{"@@locale": "en", "appTitle": "CarNow - Car Parts Marketplace", "@appTitle": {"description": "The general title of the application"}, "welcomeGuest": "Welcome, Guest", "@welcomeGuest": {"description": "Welcome message for guest users"}, "guestSubtitle": "Sign in or create an account to get the full experience.", "@guestSubtitle": {"description": "Subtitle for guest users encouraging them to sign up"}, "loginOrRegister": "Login / Register", "@loginOrRegister": {"description": "Button text for login or register"}, "categories": "Categories", "@categories": {"description": "Categories section label"}, "allCategories": "All Categories", "@allCategories": {"description": "All categories section label"}, "shopByCategory": "Shop by Category", "@shopByCategory": {"description": "Shop by category section label"}, "featuredCategories": "Featured Categories", "@featuredCategories": {"description": "Featured categories section label"}, "featuredProducts": "Featured Products", "@featuredProducts": {"description": "Featured products section label"}, "noCategoriesAvailable": "No categories available", "@noCategoriesAvailable": {"description": "Message shown when no categories are available"}, "errorLoadingCategories": "Error loading categories", "@errorLoadingCategories": {"description": "Error message shown when categories fail to load"}, "failedToLoadProducts": "Failed to load products", "@failedToLoadProducts": {"description": "Error message shown when products fail to load"}, "inputTooLong": "Input is too long", "@inputTooLong": {"description": "Error message when input exceeds maximum length"}, "invalidInput": "Invalid input detected", "@invalidInput": {"description": "Error message when dangerous content is detected"}, "securityWarning": "Potentially unsafe content detected", "@securityWarning": {"description": "Warning message for security issues"}, "invalidName": "Name contains invalid characters", "@invalidName": {"description": "Error message for invalid name characters"}, "phoneTooShort": "Phone number is too short", "@phoneTooShort": {"description": "Error message when phone number is too short"}, "phoneTooLong": "Phone number is too long", "@phoneTooLong": {"description": "Error message when phone number is too long"}, "passwordTooLong": "Password is too long", "@passwordTooLong": {"description": "Error message when password exceeds maximum length"}, "passwordNeedsUppercase": "Password must contain at least one uppercase letter", "@passwordNeedsUppercase": {"description": "Error message when password lacks uppercase letters"}, "passwordNeedsLowercase": "Password must contain at least one lowercase letter", "@passwordNeedsLowercase": {"description": "Error message when password lacks lowercase letters"}, "passwordNeedsNumber": "Password must contain at least one number", "@passwordNeedsNumber": {"description": "Error message when password lacks numbers"}, "passwordNeedsSpecialChar": "Password must contain at least one special character", "@passwordNeedsSpecialChar": {"description": "Error message when password lacks special characters"}, "passwordTooCommon": "Password is too common", "@passwordTooCommon": {"description": "Error message when password is commonly used"}, "passwordsDoNotMatch": "Passwords do not match", "@passwordsDoNotMatch": {"description": "Error message when password confirmation doesn't match"}, "invalidAmount": "Please enter a valid amount", "@invalidAmount": {"description": "Error message for invalid monetary amounts"}, "amountMustBePositive": "Amount must be positive", "@amountMustBePositive": {"description": "Error message when amount is not positive"}, "amountTooSmall": "Amount is too small", "@amountTooSmall": {"description": "Error message when amount is below minimum"}, "amountTooLarge": "Amount is too large", "@amountTooLarge": {"description": "Error message when amount exceeds maximum"}, "accountNumberTooShort": "Account number is too short", "@accountNumberTooShort": {"description": "Error message when account number is too short"}, "accountNumberTooLong": "Account number is too long", "@accountNumberTooLong": {"description": "Error message when account number is too long"}, "invalidAccountNumber": "Invalid account number format", "@invalidAccountNumber": {"description": "Error message for invalid account number format"}, "invalidVIN": "Invalid VIN format", "@invalidVIN": {"description": "Error message for invalid VIN format"}, "invalidPostalCode": "Invalid postal code format", "@invalidPostalCode": {"description": "Error message for invalid postal code format"}, "invalidURL": "Please enter a valid URL", "@invalidURL": {"description": "Error message for invalid URL format"}, "passwordStrengthNone": "No password", "@passwordStrengthNone": {"description": "Password strength indicator for no password"}, "passwordStrengthWeak": "Weak", "@passwordStrengthWeak": {"description": "Password strength indicator for weak passwords"}, "passwordStrengthMedium": "Medium", "@passwordStrengthMedium": {"description": "Password strength indicator for medium passwords"}, "passwordStrengthStrong": "Strong", "@passwordStrengthStrong": {"description": "Password strength indicator for strong passwords"}, "passwordStrengthVeryStrong": "Very Strong", "@passwordStrengthVeryStrong": {"description": "Password strength indicator for very strong passwords"}, "validationError": "Validation error occurred", "@validationError": {"description": "General validation error message"}, "securityError": "Security issue detected", "@securityError": {"description": "General security error message"}, "unknownError": "An unexpected error occurred", "@unknownError": {"description": "Unknown error message"}, "invalidImageFormat": "Invalid image format", "@invalidImageFormat": {"description": "Error message for invalid image file format"}, "invalidDocumentFormat": "Invalid document format", "@invalidDocumentFormat": {"description": "Error message for invalid document file format"}, "fileTooLarge": "File is too large", "@fileTooLarge": {"description": "Error message when file size exceeds limit"}, "mustSignInFirst": "You must sign in first", "@mustSignInFirst": {"description": "Message shown when an action requires user to be signed in"}, "outOfStock": "Out of Stock", "@outOfStock": {"description": "Label indicating a product is not available"}, "cart": "<PERSON><PERSON>", "@cart": {"description": "Shopping cart navigation label"}, "myGarage": "My Garage", "@myGarage": {"description": "My Garage navigation label"}, "home": "Home", "@home": {"description": "Home page navigation label"}, "myCars": "My Cars", "@myCars": {"description": "User's cars section label"}, "search": "Search...", "@search": {"description": "Placeholder text for search input fields"}, "inbox": "Inbox", "@inbox": {"description": "User's message inbox label"}, "selling": "Selling", "@selling": {"description": "Selling section label"}, "carNowMarketplace": "CarNow Marketplace", "@carNowMarketplace": {"description": "Marketplace title"}, "all": "All", "@all": {"description": "Filter for showing all items"}, "newItem": "New", "@newItem": {"description": "Filter for new items"}, "used": "Used", "@used": {"description": "Filter for used items"}, "featured": "Featured", "@featured": {"description": "Filter for featured items"}, "nearby": "Nearby", "@nearby": {"description": "Filter for items near user location"}, "specialOffer": "Special Offer!", "@specialOffer": {"description": "Label for special offer notification"}, "specialOfferDetails": "Get 10% off on your first order.", "@specialOfferDetails": {"description": "Details about the special offer"}, "learnMore": "Learn More", "@learnMore": {"description": "Button to learn more about an offer or feature"}, "add": "Add", "@add": {"description": "Add button label"}, "notifications": "Notifications", "@notifications": {"description": "Notifications section label"}, "store": "Store", "@store": {"description": "Store or marketplace section"}, "account": "Account", "@account": {"description": "User account section label"}, "shoppingActivity": "Shopping Activity", "@shoppingActivity": {"description": "Header for user's shopping activity section"}, "trackYourOrders": "Track your orders", "@trackYourOrders": {"description": "Subtitle for My Orders option"}, "manageYourListings": "Manage your items for sale", "@manageYourListings": {"description": "Subtitle for My Listings option"}, "viewBuyerSellerMessages": "View buyer and seller messages", "@viewBuyerSellerMessages": {"description": "Subtitle for Messages option"}, "becomeASeller": "Become a Seller", "becomeASellerSubtitle": "Start selling auto parts and accessories", "applyToSellOnCarNow": "Apply to sell on CarNow", "@applyToSellOnCarNow": {"description": "Subtitle for become a seller option"}, "settingsAndSupport": "Settings & Support", "@settingsAndSupport": {"description": "Header for settings and support section"}, "personalInformation": "Personal Information", "@personalInformation": {"description": "Title for personal information option"}, "editYourProfileDetails": "Edit your profile details", "@editYourProfileDetails": {"description": "Subtitle for personal information option"}, "accountSettings": "Account settings", "@accountSettings": {"description": "Subtitle for Settings option"}, "getHelpAndContactSupport": "Get help and contact support", "@getHelpAndContactSupport": {"description": "Subtitle for Support option"}, "signOutFromYourAccount": "Sign out from your account", "@signOutFromYourAccount": {"description": "Subtitle for Logout option"}, "signInPrompt": "Sign in or register to manage your listings", "@signInPrompt": {"description": "Prompt asking users to sign in"}, "register": "Register", "@register": {"description": "Register button label"}, "signIn": "Sign In", "@signIn": {"description": "Sign in button label"}, "myOrders": "My Orders", "@myOrders": {"description": "User's orders section label"}, "myListings": "My Listings", "@myListings": {"description": "User's listings section label"}, "messages": "Messages", "@messages": {"description": "Messages section label"}, "sellerSection": "<PERSON><PERSON>", "@sellerSection": {"description": "Seller dashboard section label"}, "subscription": "Subscription", "@subscription": {"description": "Subscription management section label"}, "subscriptionPlans": "Subscription Plans", "@subscriptionPlans": {"description": "Subscription plans screen title"}, "chooseYourPlan": "Choose Your Plan", "@chooseYourPlan": {"description": "Choose your subscription plan heading"}, "monthly": "Monthly", "@monthly": {"description": "Monthly billing cycle"}, "yearly": "Yearly", "@yearly": {"description": "Yearly billing cycle"}, "month": "month", "@month": {"description": "Month unit"}, "year": "year", "@year": {"description": "Year unit"}, "continueButton": "Continue", "@continueButton": {"description": "Continue button text"}, "selected": "Selected", "@selected": {"description": "Selected state text"}, "select": "Select", "@select": {"description": "Select button text"}, "sales": "Sales", "@sales": {"description": "Sales section label"}, "settings": "Settings", "@settings": {"description": "Settings section label"}, "support": "Support", "@support": {"description": "Support section label"}, "logout": "Logout", "@logout": {"description": "Logout button label"}, "guestUser": "Guest User", "@guestUser": {"description": "Guest user label"}, "signInToContinue": "Sign in to continue", "@signInToContinue": {"description": "Prompt to sign in to continue using the app"}, "editProfile": "Edit Profile", "@editProfile": {"description": "Edit profile button label"}, "supabaseTest": "Supabase Test", "@supabaseTest": {"description": "Supabase test feature label"}, "shoppingCart": "Shopping Cart", "@shoppingCart": {"description": "Shopping cart label"}, "clearCart": "Clear Cart", "@clearCart": {"description": "Clear cart button label"}, "clearCartConfirmationTitle": "Confirm Clear Cart", "@clearCartConfirmationTitle": {"description": "Title for cart clearing confirmation dialog"}, "clearCartConfirmationBody": "Are you sure you want to remove all items from your cart?", "@clearCartConfirmationBody": {"description": "Confirmation message when clearing cart"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button label"}, "clear": "Clear", "@clear": {"description": "Clear button label"}, "emptyCartMessage": "Your cart is empty", "@emptyCartMessage": {"description": "Message shown when shopping cart is empty"}, "emptyCartSubMessage": "Browse products and add something you like!", "@emptyCartSubMessage": {"description": "Subtext shown with empty cart message"}, "shopNow": "Shop Now", "@shopNow": {"description": "Shop now button label"}, "removeFromCart": "Remove from cart", "@removeFromCart": {"description": "Button label to remove item from cart"}, "subtotal": "Subtotal", "@subtotal": {"description": "Subtotal label in cart"}, "items": "items", "@items": {"description": "Label for item count"}, "shipping": "Shipping", "@shipping": {"description": "Shipping cost label"}, "total": "Total", "@total": {"description": "Total cost label"}, "proceedToCheckout": "Proceed to Checkout", "@proceedToCheckout": {"description": "Checkout button label"}, "clearSearch": "Clear search", "@clearSearch": {"description": "Tooltip for the clear search button"}, "noSuggestionsFound": "No search suggestions found", "@noSuggestionsFound": {"description": "Message shown when no search suggestions are available"}, "startTypingToSearch": "Start typing to search", "@startTypingToSearch": {"description": "Prompt for user to start typing in search box"}, "error": "Error", "@error": {"description": "General error label"}, "recentSearches": "Recent", "@recentSearches": {"description": "Label for recent searches tab"}, "shoppingActivitySection": "Shopping Activity", "@shoppingActivitySection": {"description": "Shopping activity section header"}, "myOrdersSubtitle": "Track your orders", "@myOrdersSubtitle": {"description": "Subtitle for my orders section"}, "myListingsSubtitle": "Manage your items for sale", "@myListingsSubtitle": {"description": "Subtitle for my listings section"}, "messagesSubtitle": "View buyer and seller messages", "@messagesSubtitle": {"description": "Subtitle for messages section"}, "settingsSection": "Settings & Support", "@settingsSection": {"description": "Settings and support section header"}, "settingsSubtitle": "Manage app preferences", "@settingsSubtitle": {"description": "Subtitle for settings section"}, "sellerToolsSection": "<PERSON><PERSON>", "@sellerToolsSection": {"description": "Seller tools section header"}, "subscriptionSubtitle": "Manage your seller plan", "@subscriptionSubtitle": {"description": "Subtitle for subscription section"}, "salesSubtitle": "View your earnings", "@salesSubtitle": {"description": "Subtitle for sales section"}, "noResultsFound": "No results found", "@noResultsFound": {"description": "Text displayed when a search yields no results"}, "save": "Save", "@save": {"description": "Button text to save changes"}, "sellerDashboard": "Seller Dashboard", "@sellerDashboard": {"description": "Seller dashboard label"}, "sellerDashboardSubtitle": "Manage products, sales, and settings", "@sellerDashboardSubtitle": {"description": "Subtitle for seller dashboard"}, "sellerProducts": "Products", "@sellerProducts": {"description": "Seller products section label"}, "sellerProductsSubtitle": "Manage your product catalog", "@sellerProductsSubtitle": {"description": "Subtitle for seller products section"}, "sellerOrders": "Orders", "@sellerOrders": {"description": "Seller orders section label"}, "sellerOrdersSubtitle": "View and manage customer orders", "@sellerOrdersSubtitle": {"description": "Subtitle for seller orders section"}, "sellerStore": "Store", "@sellerStore": {"description": "Seller store section label"}, "sellerSettings": "Settings", "@sellerSettings": {"description": "Seller settings section label"}, "sellerAnalytics": "Analytics", "@sellerAnalytics": {"description": "Seller analytics section label"}, "sellerPromotionsSubtitle": "Create and manage special offers", "@sellerPromotionsSubtitle": {"description": "Subtitle for seller promotions section"}, "sellerApprovalPending": "<PERSON><PERSON>", "@sellerApprovalPending": {"description": "Status message for pending seller approval"}, "sellerApprovalPendingDesc": "Your seller account is pending approval by our team. You'll be able to access seller tools once approved.", "@sellerApprovalPendingDesc": {"description": "Description message for pending seller approval"}, "noSellerStore": "No Store Found", "@noSellerStore": {"description": "Message shown when seller has no store"}, "noSellerStoreDesc": "You need to create a store before you can start selling products", "@noSellerStoreDesc": {"description": "Description for no store found message"}, "createStore": "Create Store", "@createStore": {"description": "Button label to create a new store"}, "revenueSummary": "Revenue Summary", "@revenueSummary": {"description": "Header for revenue summary section"}, "todayRevenue": "Today", "@todayRevenue": {"description": "Label for today's revenue"}, "weeklyRevenue": "This Week", "@weeklyRevenue": {"description": "Label for weekly revenue"}, "monthlyRevenue": "This Month", "@monthlyRevenue": {"description": "Label for monthly revenue"}, "totalRevenue": "Total Revenue", "@totalRevenue": {"description": "Label for total revenue"}, "last7DaysRevenue": "Last 7 Days Revenue", "@last7DaysRevenue": {"description": "Label for last 7 days revenue chart"}, "pendingOrders": "Pending", "@pendingOrders": {"description": "Label for pending orders"}, "completedOrders": "Completed", "@completedOrders": {"description": "Label for completed orders"}, "totalSales": "Total Sales", "@totalSales": {"description": "Label for total sales count"}, "totalProducts": "Total Products", "@totalProducts": {"description": "Label for total products count"}, "quickActions": "Quick Actions", "@quickActions": {"description": "Label for quick actions"}, "addProduct": "Add Product", "@addProduct": {"description": "Button label to add a new product"}, "manageProducts": "Manage Products", "@manageProducts": {"description": "Button label to manage products"}, "createPromotion": "Create Offer", "@createPromotion": {"description": "Button label to create a new offer"}, "managePromos": "Manage Offers", "@managePromos": {"description": "Button label to manage offers"}, "viewOrders": "View Orders", "@viewOrders": {"description": "Button label to view orders"}, "storeSettings": "Store Settings", "@storeSettings": {"description": "Label for store settings section"}, "recentOrders": "Recent Orders", "@recentOrders": {"description": "Label for recent orders section"}, "topSellingProducts": "Top Selling Products", "@topSellingProducts": {"description": "Label for top selling products section"}, "verifiedStore": "Verified Store", "@verifiedStore": {"description": "Badge for verified store status"}, "editStore": "Edit Store", "@editStore": {"description": "Button label to edit store"}, "viewAll": "View All", "@viewAll": {"description": "Button label to view all items"}, "viewMore": "View More", "@viewMore": {"description": "Button label to view more items"}, "searchProducts": "Search products...", "@searchProducts": {"description": "Placeholder for product search field"}, "active": "Active", "@active": {"description": "Status label for active items"}, "inactive": "Inactive", "@inactive": {"description": "Status label for inactive items"}, "pending": "Pending", "@pending": {"description": "Status label for pending items"}, "rejected": "Rejected", "@rejected": {"description": "Status label for rejected items"}, "noProductsYet": "No Products Yet", "@noProductsYet": {"description": "Message shown when seller has no products"}, "tapAddToCreateProduct": "Tap the add button to create your first product", "@tapAddToCreateProduct": {"description": "Instructions for adding first product"}, "noProductsMatchingFilter": "No products matching your filter", "@noProductsMatchingFilter": {"description": "Message shown when no products match the applied filter"}, "clearFilters": "Clear Filters", "@clearFilters": {"description": "Clear filters button"}, "productHiddenSuccess": "Product has been hidden", "@productHiddenSuccess": {"description": "Confirmation message when product is hidden"}, "productVisibleSuccess": "Product is now visible", "@productVisibleSuccess": {"description": "Confirmation message when product visibility is restored"}, "edit": "Edit", "@edit": {"description": "Edit action button label"}, "delete": "Delete", "@delete": {"description": "Delete button"}, "show": "Show", "@show": {"description": "Show/reveal action button label"}, "hide": "<PERSON>de", "@hide": {"description": "Hide action button label"}, "unknown": "Unknown", "@unknown": {"description": "Label for unknown status"}, "nameLabel": "Name", "@nameLabel": {"description": "Label for name input field"}, "nameRequired": "Please enter your name", "@nameRequired": {"description": "Validation message for empty name field"}, "soldCount": "{count} sold", "@soldCount": {"placeholders": {"count": {"type": "int"}}}, "promotions": "Promotions & Offers", "@promotions": {"description": "Label for promotions section"}, "noStoreFound": "No store found", "@noStoreFound": {"description": "Message when no store is found"}, "noActivePromotions": "No active promotions found", "@noActivePromotions": {"description": "Message shown when no active promotions exist"}, "showAllPromotions": "Show all promotions", "@showAllPromotions": {"description": "Button label to show all promotions"}, "showActiveOnly": "Show active only", "@showActiveOnly": {"description": "Button label to filter and show only active promotions"}, "noPromotionsYet": "No Promotions Yet", "@noPromotionsYet": {"description": "Message shown when seller has no promotions"}, "createPromotionDesc": "Create special offers and discounts for your customers", "@createPromotionDesc": {"description": "Description for promotion creation"}, "deletePromotion": "Delete Promotion", "@deletePromotion": {"description": "Button label to delete a promotion"}, "deletePromotionConfirm": "Are you sure you want to delete this promotion? This action cannot be undone.", "@deletePromotionConfirm": {"description": "Confirmation message when deleting a promotion"}, "promotionDeleted": "Promotion deleted successfully", "@promotionDeleted": {"description": "Success message when promotion is deleted"}, "expired": "Expired", "@expired": {"description": "Status label for expired items"}, "scheduled": "Scheduled", "@scheduled": {"description": "Status label for scheduled items"}, "offDiscount": "off", "@offDiscount": {"description": "Label for discount percentage (X% off)"}, "discountValue": "discount", "@discountValue": {"description": "Label for discount value"}, "products": "products", "@products": {"description": "Label for products"}, "anonymous": "Anonymous", "@anonymous": {"description": "Label for anonymous user"}, "myProducts": "My Products", "@myProducts": {"description": "Header for user's products section"}, "filterProducts": "Filter Products", "@filterProducts": {"description": "Tooltip for the filter products button"}, "editProduct": "Edit Product", "@editProduct": {"description": "Header for product editing screen"}, "productName": "Product Name", "@productName": {"description": "Label for product name field"}, "price": "Price", "@price": {"description": "Label for price field"}, "condition": "Condition", "@condition": {"description": "Condition filter"}, "description": "Description", "@description": {"description": "Label for description field"}, "productImages": "Product Images", "@productImages": {"description": "Label for product images section"}, "gallery": "Gallery", "@gallery": {"description": "Label for image gallery option."}, "camera": "Camera", "@camera": {"description": "Label for camera option."}, "noImagesSelected": "Please select at least one image", "@noImagesSelected": {"description": "Validation message for no images selected"}, "imagesSelected": "images selected", "@imagesSelected": {"description": "Label indicating number of images selected"}, "updateProduct": "Update Product", "@updateProduct": {"description": "Button label to update a product"}, "deleteProduct": "Delete Product", "@deleteProduct": {"description": "Button label to delete a product"}, "confirmDeleteProduct": "Are you sure you want to delete this product?", "@confirmDeleteProduct": {"description": "Confirmation message when deleting a product"}, "productDeleted": "Product deleted successfully", "@productDeleted": {"description": "Success message when product is deleted"}, "productAdded": "Product added successfully", "@productAdded": {"description": "Success message when product is added"}, "productUpdated": "Product updated successfully", "@productUpdated": {"description": "Success message when product is updated"}, "errorDeletingProduct": "Error deleting product", "@errorDeletingProduct": {"description": "Error message when product deletion fails"}, "errorAddingProduct": "Error adding product", "@errorAddingProduct": {"description": "Error message when product addition fails"}, "errorUpdatingProduct": "Error updating product", "@errorUpdatingProduct": {"description": "Error message when product update fails"}, "errorLoadingProducts": "Error loading products", "@errorLoadingProducts": {"description": "Error message shown when product loading fails"}, "noProductsFound": "No products found", "@noProductsFound": {"description": "Message shown when no products are available"}, "invalidPrice": "Please enter a valid price", "@invalidPrice": {"description": "Validation message for invalid price input"}, "fieldRequired": "This field is required", "@fieldRequired": {"description": "Validation message for required fields"}, "noProductsAvailable": "No products available in this category", "@noProductsAvailable": {"description": "Message shown when no products are available in a specific category"}, "phoneLabel": "Phone Number", "@phoneLabel": {"description": "Label for phone number field"}, "phoneInvalid": "Please enter a valid phone number", "@phoneInvalid": {"description": "Error message for invalid phone number"}, "addressLabel": "Address", "@addressLabel": {"description": "Label for address field"}, "addressRequired": "Please enter your address", "@addressRequired": {"description": "Error message for empty address field"}, "saveChanges": "Save Changes", "@saveChanges": {"description": "Button text to save changes"}, "discardChanges": "Discard Changes", "@discardChanges": {"description": "Button text to discard changes"}, "changesSaved": "Changes saved successfully", "@changesSaved": {"description": "Success message when changes are saved"}, "errorSavingChanges": "Error saving changes. Please try again.", "@errorSavingChanges": {"description": "Error message when saving changes fails"}, "profileUpdated": "Profile updated successfully", "@profileUpdated": {"description": "Success message when profile is updated"}, "profileUpdateFailed": "Failed to update profile information", "@profileUpdateFailed": {"description": "Error message when profile update fails"}, "phoneHint": "Enter your phone number", "@phoneHint": {"description": "Hint text for phone number field"}, "addressHint": "Enter your address", "@addressHint": {"description": "Hint text for address field"}, "resetForm": "Reset Form", "@resetForm": {"description": "Button text to reset form"}, "confirmDiscardTitle": "Discard Changes?", "@confirmDiscardTitle": {"description": "Title for discard changes confirmation dialog"}, "confirmDiscardMessage": "Are you sure you want to discard your changes?", "@confirmDiscardMessage": {"description": "Message for discard changes confirmation dialog"}, "yes": "Yes", "@yes": {"description": "Confirmation button text"}, "no": "No", "@no": {"description": "Cancel button text"}, "typeAMessage": "Type a message...", "@typeAMessage": {"description": "Placeholder for message input field"}, "newConversation": "New Conversation", "@newConversation": {"description": "Title for creating a new conversation"}, "searchUsers": "Search users...", "@searchUsers": {"description": "Placeholder for searching users"}, "createGroupChat": "Create Group Chat", "@createGroupChat": {"description": "Switch label for creating a group chat"}, "groupChatTitle": "Group Title", "@groupChatTitle": {"description": "Label for group chat title input"}, "noUsersFound": "No users found", "@noUsersFound": {"description": "Message when no users are found in search"}, "chatMessagesSubtitle": "Chat with sellers and support", "@chatMessagesSubtitle": {"description": "Subtitle for chat messages section"}, "noConversationsYet": "No conversations yet", "@noConversationsYet": {"description": "Message shown when user has no conversations"}, "startNewConversation": "Start a new conversation", "@startNewConversation": {"description": "Prompt to start a new conversation"}, "noMessagesYet": "No messages yet", "@noMessagesYet": {"description": "Message shown when conversation has no messages"}, "sendFirstMessage": "Send a message to start the conversation", "@sendFirstMessage": {"description": "Prompt to send first message"}, "retry": "Retry", "@retry": {"description": "Button text to retry sending a failed message"}, "conversationDetails": "Conversation Details", "@conversationDetails": {"description": "Title for conversation details screen"}, "viewProfile": "View Profile", "@viewProfile": {"description": "Option to view user profile"}, "deleteConversation": "Delete Conversation", "@deleteConversation": {"description": "Option to delete a conversation"}, "participants": "Participants", "@participants": {"description": "Label for conversation participants"}, "loading": "Loading...", "@loading": {"description": "Message shown during content loading"}, "help": "Help", "@help": {"description": "Help section"}, "manageSalesCategories": "Manage Sales Categories", "@manageSalesCategories": {"description": "Title for the sales categories management screen"}, "selectCategoriesDescription": "Select the categories in which you want to sell your products.", "@selectCategoriesDescription": {"description": "Description for the category selection screen"}, "categoriesSaved": "Categories saved successfully", "@categoriesSaved": {"description": "Success message for saving categories"}, "categoriesHelp": "Categories Help", "@categoriesHelp": {"description": "Title for the categories help dialog"}, "categoriesHelpText": "You can select the categories in which you want to sell your products. This will make your products appear in these categories when displayed to buyers.", "@categoriesHelpText": {"description": "Categories help text"}, "close": "Close", "@close": {"description": "Close button"}, "myCarNow": "My CarNow", "@myCarNow": {"description": "My CarNow page title"}, "welcomeToCarNow": "Welcome to CarNow", "@welcomeToCarNow": {"description": "Welcome message for unauthenticated users"}, "signInToAccessAccount": "Sign in to access your account and saved features", "@signInToAccessAccount": {"description": "Sign in prompt message"}, "createAccount": "Create Account", "@createAccount": {"description": "Create new account button"}, "carNowUser": "New Member", "@carNowUser": {"description": "Default user name"}, "newMember": "New Member", "@newMember": {"description": "Label for new members who haven't completed their profile"}, "incompleteProfile": "Profile Incomplete", "@incompleteProfile": {"description": "Label for users with incomplete profiles"}, "completeYourProfile": "Complete Your Profile", "@completeYourProfile": {"description": "Prompt for users to complete their profile"}, "profileIncomplete": "Profile Incomplete", "@profileIncomplete": {"description": "Status message for incomplete profiles"}, "memberSince": "Member since", "@memberSince": {"description": "Member since label"}, "yourUpdates": "Your Updates", "@yourUpdates": {"description": "Your updates section title"}, "watchlist": "Watchlist", "@watchlist": {"description": "Watchlist section"}, "favoritesAndLists": "Favorites & Lists", "@favoritesAndLists": {"description": "Favorites and lists subtitle"}, "bidsAndOffers": "Bids & Offers", "@bidsAndOffers": {"description": "Bids and offers section"}, "activeAuctionsAndOffers": "Active auctions and offers", "@activeAuctionsAndOffers": {"description": "Active auctions and offers subtitle"}, "shopping": "Shopping", "@shopping": {"description": "Shopping section title"}, "followWatchedItems": "Follow watched items", "@followWatchedItems": {"description": "Follow watched items subtitle"}, "savedItems": "Saved Items", "@savedItems": {"description": "Saved items section"}, "searchesSellersFeeds": "Searches, sellers, feeds", "@searchesSellersFeeds": {"description": "Searches sellers feeds subtitle"}, "buyAgain": "Buy Again", "@buyAgain": {"description": "Buy again section"}, "shopFromPreviousPurchases": "Shop from previous purchases", "@shopFromPreviousPurchases": {"description": "Shop from previous purchases subtitle"}, "purchases": "Purchases", "@purchases": {"description": "Purchases section"}, "orderHistory": "Order history", "@orderHistory": {"description": "Order history subtitle"}, "recentlyViewed": "Recently Viewed", "@recentlyViewed": {"description": "Recently viewed section"}, "itemsYouViewed": "Items you viewed", "@itemsYouViewed": {"description": "Items you viewed subtitle"}, "carNowGarage": "CarNow Garage", "@carNowGarage": {"description": "CarNow garage title"}, "addCarGetCompatibleParts": "Add your car and get compatible parts", "@addCarGetCompatibleParts": {"description": "Add car get compatible parts subtitle"}, "manageGarage": "Manage Garage", "@manageGarage": {"description": "Manage garage button"}, "addVehicle": "Add Vehicle", "@addVehicle": {"description": "Add vehicle button"}, "fitsYourRideEveryTime": "Fits your ride, every time", "@fitsYourRideEveryTime": {"description": "Fits your ride every time banner title"}, "rightPartsOrMoneyBack": "Get the right parts or your money back", "@rightPartsOrMoneyBack": {"description": "Right parts or money back banner subtitle"}, "localInstallationServices": "Local Installation Services", "@localInstallationServices": {"description": "Local installation services banner title"}, "installPartsWithExperts": "Install your new parts with our network of experts", "@installPartsWithExperts": {"description": "Install parts with experts banner subtitle"}, "browseServices": "Browse Services", "@browseServices": {"description": "Browse services button"}, "myVehicles": "My Vehicles", "@myVehicles": {"description": "My vehicles section title"}, "vehicleFormComingSoon": "Vehicle form will be implemented in a future task", "@vehicleFormComingSoon": {"description": "Vehicle form coming soon message"}, "deleteVehicle": "Delete Vehicle", "@deleteVehicle": {"description": "Delete vehicle dialog title"}, "confirmDeleteVehicle": "Are you sure you want to delete this vehicle?", "@confirmDeleteVehicle": {"description": "Confirm delete vehicle message"}, "garageSettings": "Garage <PERSON>s", "@garageSettings": {"description": "Garage settings section"}, "share": "Share", "@share": {"description": "Share button"}, "compatibilityGuarantee": "Compatibility Guarantee", "@compatibilityGuarantee": {"description": "Compatibility guarantee dialog title"}, "compatibilityGuaranteeMessage": "We guarantee that the parts you buy will fit your vehicle. If they don't fit, you can return them and get a full refund", "@compatibilityGuaranteeMessage": {"description": "Compatibility guarantee message"}, "understood": "Understood", "@understood": {"description": "Understood button"}, "sharingFeatureComingSoon": "Sharing feature coming soon!", "@sharingFeatureComingSoon": {"description": "Sharing feature coming soon message"}, "garageIsEmpty": "Your garage is empty", "@garageIsEmpty": {"description": "Garage is empty message"}, "addVehiclesToFindParts": "Add your vehicles to find compatible parts", "@addVehiclesToFindParts": {"description": "Add vehicles to find parts message"}, "contactUs": "Contact Us", "@contactUs": {"description": "Contact Us section label"}, "emailSupport": "Email Support", "@emailSupport": {"description": "Email support label"}, "phoneSupport": "Phone Support", "@phoneSupport": {"description": "Phone support label"}, "faq": "Frequently Asked Questions", "@faq": {"description": "FAQ section label"}, "noFaqsAvailable": "No FAQs available at the moment.", "@noFaqsAvailable": {"description": "Message when no FAQs are available"}, "liveChat": "Live Chat", "@liveChat": {"description": "Live chat button label"}, "savedSearches": "Saved", "@savedSearches": {"description": "Label for saved searches tab"}, "noResultsFoundFor": "No results found for '{query}'", "@noResultsFoundFor": {"description": "Message shown when no search results are found for a query", "placeholders": {"query": {"type": "String", "example": "keywords"}}}, "errorOccurred": "An error occurred", "@errorOccurred": {"description": "Generic error message"}, "noSavedItems": "You haven't saved any items yet.", "@noSavedItems": {"description": "Message shown when there are no saved items"}, "networkError": "Network error. Please check your connection.", "@networkError": {"description": "Error message for network connectivity issues."}, "serverError": "Server error", "@serverError": {"description": "Server error message"}, "timeoutError": "Connection timeout", "@timeoutError": {"description": "Timeout error message"}, "invalidCredentials": "Invalid login credentials", "@invalidCredentials": {"description": "Invalid credentials error message"}, "emailNotConfirmed": "Please confirm your email", "@emailNotConfirmed": {"description": "Email not confirmed error message"}, "userNotFound": "User not found.", "@userNotFound": {"description": "Error message when a user is not found."}, "weakPassword": "Password is too weak", "@weakPassword": {"description": "Weak password error message"}, "emailAlreadyRegistered": "Email is already registered", "@emailAlreadyRegistered": {"description": "Email already registered error message"}, "authError": "Authentication failed. Please check your credentials.", "@authError": {"description": "Error message for authentication failures."}, "duplicateEntry": "Data already exists", "@duplicateEntry": {"description": "Duplicate entry error message"}, "foreignKeyViolation": "Cannot delete this item", "@foreignKeyViolation": {"description": "Foreign key violation error message"}, "insufficientPermissions": "You don't have permission for this action", "@insufficientPermissions": {"description": "Insufficient permissions error message"}, "databaseError": "Database error", "@databaseError": {"description": "Database error message"}, "helloWorld": "Hello World!", "login": "<PERSON><PERSON>", "password": "Password", "requiredField": "This field is required", "@requiredField": {"description": "Validation error for a required field that is empty."}, "invalidEmail": "Please enter a valid email", "@invalidEmail": {"description": "Validation error for an invalid email format."}, "passwordTooShort": "Password must be at least 6 characters", "@passwordTooShort": {"description": "Validation error for a password that is too short."}, "confirmPassword": "Confirm Password", "authenticationError": "Authentication failed. Please check your credentials.", "@authenticationError": {"description": "Error message for authentication failures."}, "dataProcessingError": "Error processing data. Please try again.", "@dataProcessingError": {"description": "Error message for data processing failures."}, "unexpectedError": "An unexpected error occurred. Please try again.", "@unexpectedError": {"description": "Generic error message for unexpected issues."}, "completeProfileTitle": "Complete Your Profile", "completeProfileHeading": "One Last Step Before You Proceed!", "completeProfileMessage": "We need some additional information in your profile (like your name and phone number) to successfully complete the purchase.", "completeProfileButton": "Go to Complete Profile", "cancelButton": "Cancel", "logoutConfirmation": "Are you sure you want to log out?", "createAccountButton": "Create a New Account", "loginRequiredTitle": "<PERSON><PERSON> Required", "loginRequiredHeading": "Exclusive Feature for Members", "loginRequiredMessage": "To use this feature and get full access to our services, please log in or create a new account.", "fullName": "Full Name", "enterFullName": "Enter your full name", "phoneNumber": "Phone Number", "enterPhoneNumber": "Enter your mobile number", "location": "Location", "enterLocation": "Enter your city or area", "saveAndContinue": "Save and Continue", "nameCannotBeEmpty": "Name cannot be empty.", "nameTooShort": "Name must be at least 2 characters.", "phoneCannotBeEmpty": "Phone number cannot be empty.", "invalidPhoneNumber": "Invalid phone number format.", "locationCannotBeEmpty": "Location cannot be empty.", "locationTooShort": "Location must be at least 3 characters.", "removeImage": "Remove Image", "profileUpdatedSuccessfully": "Profile updated successfully.", "storageError": "Error saving data. Please try again.", "errorLoadingProfile": "Error loading profile.", "completeProfileWelcome": "Welcome! We need some additional information", "completeProfileDescription": "To improve your experience in the app, please complete your personal information below.", "loginButton": "<PERSON><PERSON>", "supportTicketUpdateSuccess": "Support ticket updated successfully.", "supportTicketUpdateError": "Failed to update support ticket.", "searchHint": "Search for parts, categories...", "partNamesLanguage": "Part Names Language", "partNamesLanguageDesc": "Choose the language for displaying car part names.", "selectAutomotiveTypeTitle": "Select Automotive Type", "@selectAutomotiveTypeTitle": {"description": "Title for automotive type selection screen"}, "whatAreYouListing": "What are you listing?", "@whatAreYouListing": {"description": "Main question for what user wants to list"}, "selectCategoryToContinue": "Select a category to continue with your listing", "@selectCategoryToContinue": {"description": "Instruction to select category"}, "vehicleSelectionTitle": "Vehicle", "@vehicleSelectionTitle": {"description": "Title for vehicle selection option"}, "vehicleSelectionSubtitle": "List cars, motorcycles, trucks, and other vehicles", "@vehicleSelectionSubtitle": {"description": "Description for vehicle selection option"}, "autoPartSelectionTitle": "Auto Parts", "@autoPartSelectionTitle": {"description": "Title for auto parts selection option"}, "autoPartSelectionSubtitle": "List spare parts, accessories, and components", "@autoPartSelectionSubtitle": {"description": "Description for auto parts selection option"}, "developerTools": "Developer Tools", "@developerTools": {"description": "Developer tools screen title"}, "analyticsDashboard": "Analytics Dashboard", "@analyticsDashboard": {"description": "Analytics dashboard"}, "salesReports": "Sales Reports", "@salesReports": {"description": "Sales reports"}, "inventoryManagement": "Inventory Management", "@inventoryManagement": {"description": "Inventory management"}, "advancedDashboard": "Advanced Dashboard", "@advancedDashboard": {"description": "Advanced dashboard"}, "storesListing": "Stores Listing", "@storesListing": {"description": "Stores listing"}, "warrantyList": "Warranty List", "@warrantyList": {"description": "Warranty list"}, "archivedChats": "Archived Chats", "@archivedChats": {"description": "Archived chats"}, "chatStorage": "Chat Storage", "@chatStorage": {"description": "Chat storage"}, "conversations": "Conversations", "@conversations": {"description": "Conversations"}, "enhancedSearch": "Enhanced Search", "@enhancedSearch": {"description": "Enhanced search"}, "compatiblePartsTest": "Compatible Parts (Test)", "@compatiblePartsTest": {"description": "Compatible parts test"}, "adminSubscriptionRequests": "Admin - Subscription Requests", "@adminSubscriptionRequests": {"description": "Admin subscription requests"}, "devToolsDebugOnly": "Developer tools are only available in debug mode", "@devToolsDebugOnly": {"description": "Message shown when developer tools are accessed in non-debug mode"}, "smartSearch": "Smart Search", "@smartSearch": {"description": "Smart search feature title"}, "hybridSearch": "Hybrid Search", "@hybridSearch": {"description": "Hybrid search type"}, "semanticSearch": "Semantic Search", "@semanticSearch": {"description": "Semantic search type"}, "clearHistory": "Clear History", "@clearHistory": {"description": "Clear search history"}, "startSearching": "Start Searching", "@startSearching": {"description": "Start searching message"}, "searchDescription": "Use smart search to find what you want quickly", "@searchDescription": {"description": "Search description text"}, "searchSuggestions": "Search Suggestions", "@searchSuggestions": {"description": "Search suggestions section"}, "searchHistory": "Search History", "@searchHistory": {"description": "Search history section"}, "clearSearchHistory": "Clear Search History", "@clearSearchHistory": {"description": "Clear search history button"}, "noSearchHistory": "No search history", "@noSearchHistory": {"description": "No search history message"}, "searchFailed": "Search Failed", "@searchFailed": {"description": "Search failed message"}, "searchEmpty": "No Results", "@searchEmpty": {"description": "No search results message"}, "searchEmptyMessage": "No results found. Try different keywords.", "@searchEmptyMessage": {"description": "Empty search results detailed message"}, "searchProcessingTime": "Processing time: {time}ms", "@searchProcessingTime": {"description": "Search processing time"}, "searchResultsCount": "{count} results", "@searchResultsCount": {"description": "Search results count"}, "searchInsights": "Search Insights", "@searchInsights": {"description": "Search insights section"}, "searchCategoryDistribution": "Category Distribution", "@searchCategoryDistribution": {"description": "Category distribution in search"}, "searchPopularFilters": "Popular Filters", "@searchPopularFilters": {"description": "Popular filters in search"}, "searchPriceRange": "Price Range", "@searchPriceRange": {"description": "Price range in search"}, "searchMinPrice": "<PERSON>", "@searchMinPrice": {"description": "Minimum price in search"}, "searchMaxPrice": "Max Price", "@searchMaxPrice": {"description": "Maximum price in search"}, "searchAveragePrice": "Average Price", "@searchAveragePrice": {"description": "Average price in search"}, "searchCars": "Search Cars", "@searchCars": {"description": "Search cars section"}, "searchAutoParts": "Search Auto Parts", "@searchAutoParts": {"description": "Search auto parts section"}, "carBrand": "Car Brand", "@carBrand": {"description": "Car brand filter"}, "carModel": "Car Model", "@carModel": {"description": "Car model filter"}, "carYear": "Car Year", "@carYear": {"description": "Car year filter"}, "partType": "Part Type", "@partType": {"description": "Part type filter"}, "compatibility": "Compatibility", "@compatibility": {"description": "Compatibility filter"}, "mileage": "Mileage", "@mileage": {"description": "Mileage filter"}, "fuelType": "Fuel Type", "@fuelType": {"description": "Fuel type filter"}, "transmission": "Transmission", "@transmission": {"description": "Transmission filter"}, "warranty": "Warranty", "@warranty": {"description": "Warranty filter"}, "oemPart": "OEM Part", "@oemPart": {"description": "OEM part filter"}, "sedans": "Sedans", "@sedans": {"description": "Sedans car type"}, "suvs": "SUVs", "@suvs": {"description": "SUVs car type"}, "hatchbacks": "Hatchbacks", "@hatchbacks": {"description": "Hatchbacks car type"}, "pickupTrucks": "Pickup Trucks", "@pickupTrucks": {"description": "Pickup trucks car type"}, "coupes": "Coupes", "@coupes": {"description": "Coupes car type"}, "convertibles": "Convertibles", "@convertibles": {"description": "Convertibles car type"}, "motorcycles": "Motorcycles", "@motorcycles": {"description": "Motorcycles vehicle type"}, "engineParts": "Engine Parts", "@engineParts": {"description": "Engine parts category"}, "brakeParts": "Brake Parts", "@brakeParts": {"description": "Brake parts category"}, "electricalParts": "Electrical Parts", "@electricalParts": {"description": "Electrical parts category"}, "bodyParts": "Body Parts", "@bodyParts": {"description": "Body parts category"}, "transmissionParts": "Transmission Parts", "@transmissionParts": {"description": "Transmission parts category"}, "suspensionParts": "Suspension Parts", "@suspensionParts": {"description": "Suspension parts category"}, "coolingParts": "Cooling Parts", "@coolingParts": {"description": "Cooling parts category"}, "exhaustParts": "Exhaust Parts", "@exhaustParts": {"description": "Exhaust parts category"}, "smartFilters": "Smart Filters", "@smartFilters": {"description": "Smart filters section"}, "activeFilters": "Active Filters", "@activeFilters": {"description": "Active filters section"}, "applyFilters": "Apply Filters", "@applyFilters": {"description": "Apply filters button"}, "quickSuggestions": "Quick Suggestions", "@quickSuggestions": {"description": "Quick suggestions section"}, "searchByCategory": "Search by Category", "@searchByCategory": {"description": "Search by category"}, "aiPoweredSearch": "AI-Powered Search", "@aiPoweredSearch": {"description": "AI-powered search feature"}, "intelligentSearch": "Intelligent Search", "@intelligentSearch": {"description": "Intelligent search feature"}}