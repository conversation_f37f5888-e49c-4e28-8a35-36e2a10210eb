// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'كارناو - سوق قطع غيار السيارات';

  @override
  String get welcomeGuest => 'أهلاً بك أيها الزائر';

  @override
  String get guestSubtitle =>
      'سجل الدخول أو أنشئ حسابًا جديدًا للاستفادة من كامل الميزات.';

  @override
  String get loginOrRegister => 'تسجيل الدخول / إنشاء حساب';

  @override
  String get categories => 'الفئات';

  @override
  String get allCategories => 'جميع الفئات';

  @override
  String get shopByCategory => 'تسوق حسب الفئة';

  @override
  String get featuredCategories => 'الفئات المميزة';

  @override
  String get featuredProducts => 'منتجات مميزة';

  @override
  String get noCategoriesAvailable => 'لا توجد فئات متاحة';

  @override
  String get errorLoadingCategories => 'خطأ في تحميل الفئات';

  @override
  String get failedToLoadProducts => 'فشل تحميل المنتجات';

  @override
  String get inputTooLong => 'المدخل طويل جداً';

  @override
  String get invalidInput => 'تم اكتشاف مدخل غير صالح';

  @override
  String get securityWarning => 'تم اكتشاف محتوى قد يكون غير آمن';

  @override
  String get invalidName => 'الاسم يحتوي على أحرف غير صالحة';

  @override
  String get phoneTooShort => 'رقم الهاتف قصير جداً';

  @override
  String get phoneTooLong => 'رقم الهاتف طويل جداً';

  @override
  String get passwordTooLong => 'كلمة المرور طويلة جداً';

  @override
  String get passwordNeedsUppercase =>
      'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';

  @override
  String get passwordNeedsLowercase =>
      'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';

  @override
  String get passwordNeedsNumber =>
      'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';

  @override
  String get passwordNeedsSpecialChar =>
      'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';

  @override
  String get passwordTooCommon => 'كلمة المرور شائعة جداً';

  @override
  String get passwordsDoNotMatch => 'كلمات المرور غير متطابقة';

  @override
  String get invalidAmount => 'يرجى إدخال مبلغ صالح';

  @override
  String get amountMustBePositive => 'المبلغ يجب أن يكون موجباً';

  @override
  String get amountTooSmall => 'المبلغ صغير جداً';

  @override
  String get amountTooLarge => 'المبلغ كبير جداً';

  @override
  String get accountNumberTooShort => 'رقم الحساب قصير جداً';

  @override
  String get accountNumberTooLong => 'رقم الحساب طويل جداً';

  @override
  String get invalidAccountNumber => 'تنسيق رقم الحساب غير صالح';

  @override
  String get invalidVIN => 'تنسيق رقم VIN غير صالح';

  @override
  String get invalidPostalCode => 'تنسيق الرمز البريدي غير صالح';

  @override
  String get invalidURL => 'يرجى إدخال رابط صالح';

  @override
  String get passwordStrengthNone => 'لا توجد كلمة مرور';

  @override
  String get passwordStrengthWeak => 'ضعيفة';

  @override
  String get passwordStrengthMedium => 'متوسطة';

  @override
  String get passwordStrengthStrong => 'قوية';

  @override
  String get passwordStrengthVeryStrong => 'قوية جداً';

  @override
  String get validationError => 'حدث خطأ في التحقق';

  @override
  String get securityError => 'تم اكتشاف مشكلة أمنية';

  @override
  String get unknownError => 'حدث خطأ غير متوقع';

  @override
  String get invalidImageFormat => 'تنسيق الصورة غير صالح';

  @override
  String get invalidDocumentFormat => 'تنسيق المستند غير صالح';

  @override
  String get fileTooLarge => 'الملف كبير جداً';

  @override
  String get mustSignInFirst => 'يجب عليك تسجيل الدخول أولا';

  @override
  String get outOfStock => 'نفدت الكمية';

  @override
  String get cart => 'السلة';

  @override
  String get myGarage => 'مرآبي';

  @override
  String get home => 'الرئيسية';

  @override
  String get myCars => 'سياراتي';

  @override
  String get search => 'ابحث...';

  @override
  String get inbox => 'الوارد';

  @override
  String get selling => 'البيع';

  @override
  String get carNowMarketplace => 'سوق CarNow';

  @override
  String get all => 'الكل';

  @override
  String get newItem => 'جديد';

  @override
  String get used => 'مستعمل';

  @override
  String get featured => 'مميز';

  @override
  String get nearby => 'بالقرب';

  @override
  String get specialOffer => 'عرض خاص!';

  @override
  String get specialOfferDetails => 'احصل على خصم 10% على طلبك الأول.';

  @override
  String get learnMore => 'اعرف المزيد';

  @override
  String get add => 'إضافة';

  @override
  String get notifications => 'اشعارات';

  @override
  String get store => 'متجر';

  @override
  String get account => 'حسابي';

  @override
  String get shoppingActivity => 'نشاط التسوق';

  @override
  String get trackYourOrders => 'تتبع طلباتك';

  @override
  String get manageYourListings => 'إدارة العناصر المعروضة للبيع';

  @override
  String get viewBuyerSellerMessages => 'عرض رسائل المشترين والبائعين';

  @override
  String get becomeASeller => 'كن بائعًا';

  @override
  String get becomeASellerSubtitle => 'ابدأ في بيع قطع الغيار والإكسسوارات';

  @override
  String get applyToSellOnCarNow => 'قدم طلبًا للبيع على كارناو';

  @override
  String get settingsAndSupport => 'الإعدادات والدعم';

  @override
  String get personalInformation => 'المعلومات الشخصية';

  @override
  String get editYourProfileDetails => 'تعديل تفاصيل ملفك الشخصي';

  @override
  String get accountSettings => 'إعدادات الحساب';

  @override
  String get getHelpAndContactSupport =>
      'الحصول على المساعدة والتواصل مع الدعم';

  @override
  String get signOutFromYourAccount => 'تسجيل الخروج من حسابك';

  @override
  String get signInPrompt => 'سجل الدخول أو أنشئ حساباً لإدارة إعلاناتك';

  @override
  String get register => 'تسجيل جديد';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get myOrders => 'طلباتي';

  @override
  String get myListings => 'قوائمي';

  @override
  String get messages => 'الرسائل';

  @override
  String get sellerSection => 'البائع';

  @override
  String get subscription => 'الاشتراك';

  @override
  String get subscriptionPlans => 'خطط الاشتراك';

  @override
  String get chooseYourPlan => 'اختر خطتك';

  @override
  String get monthly => 'شهرياً';

  @override
  String get yearly => 'سنوياً';

  @override
  String get month => 'شهر';

  @override
  String get year => 'سنة';

  @override
  String get continueButton => 'متابعة';

  @override
  String get selected => 'محدد';

  @override
  String get select => 'اختيار';

  @override
  String get sales => 'المبيعات';

  @override
  String get settings => 'الإعدادات';

  @override
  String get support => 'الدعم';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get guestUser => 'زائر';

  @override
  String get signInToContinue => 'سجل الدخول للمتابعة';

  @override
  String get editProfile => 'تعديل الملف الشخصي';

  @override
  String get supabaseTest => 'اختبار Supabase';

  @override
  String get shoppingCart => 'سلة التسوق';

  @override
  String get clearCart => 'إفراغ السلة';

  @override
  String get clearCartConfirmationTitle => 'تأكيد إفراغ السلة';

  @override
  String get clearCartConfirmationBody =>
      'هل أنت متأكد من رغبتك في إزالة كل العناصر من سلتك؟';

  @override
  String get cancel => 'إلغاء';

  @override
  String get clear => 'إفراغ';

  @override
  String get emptyCartMessage => 'سلتك فارغة';

  @override
  String get emptyCartSubMessage => 'تصفح المنتجات وأضف ما يعجبك!';

  @override
  String get shopNow => 'تسوق الآن';

  @override
  String get removeFromCart => 'إزالة من السلة';

  @override
  String get subtotal => 'المجموع الفرعي';

  @override
  String get items => 'عناصر';

  @override
  String get shipping => 'الشحن';

  @override
  String get total => 'المجموع الكلي';

  @override
  String get proceedToCheckout => 'المتابعة للدفع';

  @override
  String get clearSearch => 'مسح البحث';

  @override
  String get noSuggestionsFound => 'لم يتم العثور على اقتراحات';

  @override
  String get startTypingToSearch => 'ابدأ الكتابة للبحث...';

  @override
  String get error => 'حدث خطأ';

  @override
  String get recentSearches => 'عمليات البحث الأخيرة';

  @override
  String get shoppingActivitySection => 'نشاط التسوق';

  @override
  String get myOrdersSubtitle => 'تتبع طلباتك';

  @override
  String get myListingsSubtitle => 'إدارة العناصر المعروضة للبيع';

  @override
  String get messagesSubtitle => 'عرض رسائل المشترين والبائعين';

  @override
  String get settingsSection => 'الإعدادات والدعم';

  @override
  String get settingsSubtitle => 'إدارة تفضيلات التطبيق';

  @override
  String get sellerToolsSection => 'أدوات البائع';

  @override
  String get subscriptionSubtitle => 'إدارة خطة اشتراكك';

  @override
  String get salesSubtitle => 'عرض أداء مبيعاتك';

  @override
  String get noResultsFound => 'لم يتم العثور على نتائج';

  @override
  String get save => 'حفظ';

  @override
  String get sellerDashboard => 'لوحة تحكم البائع';

  @override
  String get sellerDashboardSubtitle => 'إدارة المنتجات، المبيعات، والإعدادات';

  @override
  String get sellerProducts => 'المنتجات';

  @override
  String get sellerProductsSubtitle => 'إدارة قوائم منتجاتك';

  @override
  String get sellerOrders => 'الطلبات';

  @override
  String get sellerOrdersSubtitle => 'عرض وإدارة طلبات العملاء';

  @override
  String get sellerStore => 'المتجر';

  @override
  String get sellerSettings => 'الإعدادات';

  @override
  String get sellerAnalytics => 'التحليلات';

  @override
  String get sellerPromotionsSubtitle => 'إنشاء وإدارة العروض الخاصة';

  @override
  String get sellerApprovalPending => 'موافقة البائع معلقة';

  @override
  String get sellerApprovalPendingDesc =>
      'حساب البائع الخاص بك قيد الموافقة من قبل فريقنا. ستتمكن من الوصول إلى أدوات البائع بمجرد الموافقة عليه.';

  @override
  String get noSellerStore => 'لم يتم العثور على متجر';

  @override
  String get noSellerStoreDesc =>
      'تحتاج إلى إنشاء متجر قبل البدء في بيع المنتجات';

  @override
  String get createStore => 'إنشاء متجر';

  @override
  String get revenueSummary => 'ملخص الإيرادات';

  @override
  String get todayRevenue => 'اليوم';

  @override
  String get weeklyRevenue => 'هذا الأسبوع';

  @override
  String get monthlyRevenue => 'هذا الشهر';

  @override
  String get totalRevenue => 'إجمالي الإيرادات';

  @override
  String get last7DaysRevenue => 'إيرادات آخر 7 أيام';

  @override
  String get pendingOrders => 'قيد الانتظار';

  @override
  String get completedOrders => 'مكتملة';

  @override
  String get totalSales => 'إجمالي المبيعات';

  @override
  String get totalProducts => 'إجمالي المنتجات';

  @override
  String get quickActions => 'إجراءات سريعة';

  @override
  String get addProduct => 'إضافة منتج';

  @override
  String get manageProducts => 'إدارة المنتجات';

  @override
  String get createPromotion => 'إنشاء عرض';

  @override
  String get managePromos => 'إدارة العروض';

  @override
  String get viewOrders => 'عرض الطلبات';

  @override
  String get storeSettings => 'إعدادات المتجر';

  @override
  String get recentOrders => 'الطلبات الأخيرة';

  @override
  String get topSellingProducts => 'المنتجات الأكثر مبيعًا';

  @override
  String get verifiedStore => 'متجر موثق';

  @override
  String get editStore => 'تعديل المتجر';

  @override
  String get viewAll => 'عرض الكل';

  @override
  String get viewMore => 'عرض المزيد';

  @override
  String get searchProducts => 'البحث عن منتجات...';

  @override
  String get active => 'نشط';

  @override
  String get inactive => 'غير نشط';

  @override
  String get pending => 'قيد الانتظار';

  @override
  String get rejected => 'مرفوض';

  @override
  String get noProductsYet => 'لا توجد منتجات متوفرة بعد';

  @override
  String get tapAddToCreateProduct => 'اضغط على زر الإضافة لإنشاء أول منتج لك';

  @override
  String get noProductsMatchingFilter => 'لا توجد منتجات تطابق التصفية';

  @override
  String get clearFilters => 'مسح الفلاتر';

  @override
  String get productHiddenSuccess => 'تم إخفاء المنتج';

  @override
  String get productVisibleSuccess => 'المنتج مرئي الآن';

  @override
  String get edit => 'تعديل';

  @override
  String get delete => 'حذف';

  @override
  String get show => 'إظهار';

  @override
  String get hide => 'إخفاء';

  @override
  String get unknown => 'غير معروف';

  @override
  String get nameLabel => 'الاسم';

  @override
  String get nameRequired => 'الرجاء إدخال الاسم';

  @override
  String soldCount(int count) {
    return 'تم بيع $count';
  }

  @override
  String get promotions => 'العروض والخصومات';

  @override
  String get noStoreFound => 'لم يتم العثور على متجر';

  @override
  String get noActivePromotions => 'لا توجد عروض نشطة';

  @override
  String get showAllPromotions => 'عرض جميع العروض';

  @override
  String get showActiveOnly => 'عرض النشطة فقط';

  @override
  String get noPromotionsYet => 'لا توجد عروض حتى الآن';

  @override
  String get createPromotionDesc => 'إنشاء عروض وخصومات خاصة لعملائك';

  @override
  String get deletePromotion => 'حذف العرض';

  @override
  String get deletePromotionConfirm =>
      'هل أنت متأكد من رغبتك في حذف هذا العرض؟ لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get promotionDeleted => 'تم حذف العرض بنجاح';

  @override
  String get expired => 'منتهي';

  @override
  String get scheduled => 'مجدول';

  @override
  String get offDiscount => 'خصم';

  @override
  String get discountValue => 'خصم';

  @override
  String get products => 'منتجات';

  @override
  String get anonymous => 'مجهول';

  @override
  String get myProducts => 'منتجاتي';

  @override
  String get filterProducts => 'تصفية المنتجات';

  @override
  String get editProduct => 'تعديل المنتج';

  @override
  String get productName => 'اسم المنتج';

  @override
  String get price => 'السعر';

  @override
  String get condition => 'الحالة';

  @override
  String get description => 'الوصف';

  @override
  String get productImages => 'صور المنتج';

  @override
  String get gallery => 'المعرض';

  @override
  String get camera => 'الكاميرا';

  @override
  String get noImagesSelected => 'لم يتم اختيار أي صور';

  @override
  String get imagesSelected => 'تم اختيار الصور';

  @override
  String get updateProduct => 'تحديث المنتج';

  @override
  String get deleteProduct => 'حذف المنتج';

  @override
  String get confirmDeleteProduct => 'هل أنت متأكد من رغبتك في حذف هذا المنتج؟';

  @override
  String get productDeleted => 'تم حذف المنتج بنجاح';

  @override
  String get productAdded => 'تمت إضافة المنتج بنجاح';

  @override
  String get productUpdated => 'تم تحديث المنتج بنجاح';

  @override
  String get errorDeletingProduct => 'حدث خطأ أثناء حذف المنتج';

  @override
  String get errorAddingProduct => 'حدث خطأ أثناء إضافة المنتج';

  @override
  String get errorUpdatingProduct => 'حدث خطأ أثناء تحديث المنتج';

  @override
  String get errorLoadingProducts => 'خطأ في تحميل المنتجات';

  @override
  String get noProductsFound => 'لم يتم العثور على منتجات';

  @override
  String get invalidPrice => 'الرجاء إدخال سعر صالح';

  @override
  String get fieldRequired => 'هذا الحقل مطلوب';

  @override
  String get noProductsAvailable => 'No products available in this category';

  @override
  String get phoneLabel => 'رقم الهاتف';

  @override
  String get phoneInvalid => 'الرجاء إدخال رقم هاتف صالح';

  @override
  String get addressLabel => 'العنوان';

  @override
  String get addressRequired => 'الرجاء إدخال عنوانك';

  @override
  String get saveChanges => 'حفظ التغييرات';

  @override
  String get discardChanges => 'تجاهل التغييرات';

  @override
  String get changesSaved => 'تم حفظ التغييرات بنجاح';

  @override
  String get errorSavingChanges =>
      'حدث خطأ أثناء حفظ التغييرات. الرجاء المحاولة مرة أخرى.';

  @override
  String get profileUpdated => 'تم تحديث الملف الشخصي بنجاح';

  @override
  String get profileUpdateFailed => 'فشل تحديث الملف الشخصي';

  @override
  String get phoneHint => 'أدخل رقم هاتفك';

  @override
  String get addressHint => 'أدخل عنوانك';

  @override
  String get resetForm => 'إعادة تعيين النموذج';

  @override
  String get confirmDiscardTitle => 'تجاهل التغييرات؟';

  @override
  String get confirmDiscardMessage =>
      'هل أنت متأكد من رغبتك في تجاهل التغييرات؟';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get typeAMessage => 'اكتب رسالة...';

  @override
  String get newConversation => 'محادثة جديدة';

  @override
  String get searchUsers => 'البحث عن المستخدمين...';

  @override
  String get createGroupChat => 'إنشاء محادثة جماعية';

  @override
  String get groupChatTitle => 'عنوان المجموعة';

  @override
  String get noUsersFound => 'لم يتم العثور على مستخدمين';

  @override
  String get chatMessagesSubtitle => 'الدردشة مع البائعين والدعم';

  @override
  String get noConversationsYet => 'لا توجد محادثات حتى الآن';

  @override
  String get startNewConversation => 'بدء محادثة جديدة';

  @override
  String get noMessagesYet => 'لا توجد رسائل حتى الآن';

  @override
  String get sendFirstMessage => 'أرسل رسالة لبدء المحادثة';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get conversationDetails => 'تفاصيل المحادثة';

  @override
  String get viewProfile => 'عرض الملف الشخصي';

  @override
  String get deleteConversation => 'حذف المحادثة';

  @override
  String get participants => 'المشاركون';

  @override
  String get loading => 'جاري التحميل';

  @override
  String get help => 'مساعدة';

  @override
  String get manageSalesCategories => 'إدارة فئات المبيعات';

  @override
  String get selectCategoriesDescription =>
      'اختر الفئات التي ترغب في بيع منتجاتك فيها.';

  @override
  String get categoriesSaved => 'تم حفظ الفئات بنجاح';

  @override
  String get categoriesHelp => 'مساعدة الفئات';

  @override
  String get categoriesHelpText =>
      'يمكنك اختيار الفئات التي ترغب في بيع منتجاتك فيها. سيؤدي ذلك إلى ظهور منتجاتك في هذه الفئات عند عرضها للمشترين.';

  @override
  String get close => 'إغلاق';

  @override
  String get myCarNow => 'CarNow الخاص بي';

  @override
  String get welcomeToCarNow => 'مرحباً بك في CarNow';

  @override
  String get signInToAccessAccount =>
      'سجل دخولك للوصول إلى حسابك وميزاتك المحفوظة';

  @override
  String get createAccount => 'إنشاء حساب جديد';

  @override
  String get carNowUser => 'عضو جديد';

  @override
  String get newMember => 'عضو جديد';

  @override
  String get incompleteProfile => 'الملف الشخصي غير مكتمل';

  @override
  String get completeYourProfile => 'أكمل ملفك الشخصي';

  @override
  String get profileIncomplete => 'الملف الشخصي غير مكتمل';

  @override
  String get memberSince => 'عضو منذ';

  @override
  String get yourUpdates => 'التحديثات الخاصة بك';

  @override
  String get watchlist => 'قائمة المراقبة';

  @override
  String get favoritesAndLists => 'المفضلة والقوائم';

  @override
  String get bidsAndOffers => 'العروض والمزايدات';

  @override
  String get activeAuctionsAndOffers => 'المزادات النشطة وعروض البائعين';

  @override
  String get shopping => 'التسوق';

  @override
  String get followWatchedItems => 'تابع العناصر المراقبة';

  @override
  String get savedItems => 'المحفوظات';

  @override
  String get searchesSellersFeeds => 'عمليات البحث، البائعين، التغذية';

  @override
  String get buyAgain => 'اشتري مرة أخرى';

  @override
  String get shopFromPreviousPurchases => 'تسوق من مشترياتك السابقة';

  @override
  String get purchases => 'المشتريات';

  @override
  String get orderHistory => 'تاريخ الطلبات';

  @override
  String get recentlyViewed => 'تمت مشاهدته مؤخراً';

  @override
  String get itemsYouViewed => 'العناصر التي شاهدتها';

  @override
  String get carNowGarage => 'جراج CarNow';

  @override
  String get addCarGetCompatibleParts =>
      'أضف سيارتك واحصل على قطع غيار متوافقة';

  @override
  String get manageGarage => 'إدارة الجراج';

  @override
  String get addVehicle => 'إضافة مركبة';

  @override
  String get fitsYourRideEveryTime => 'يناسب مركبتك، في كل مرة';

  @override
  String get rightPartsOrMoneyBack =>
      'احصل على القطع المناسبة أو استرد أموالك.';

  @override
  String get localInstallationServices => 'خدمات تركيب محلية';

  @override
  String get installPartsWithExperts =>
      'اطلب تركيب قطعك الجديدة بواسطة شبكة خبرائنا.';

  @override
  String get browseServices => 'تصفح الخدمات';

  @override
  String get myVehicles => 'مركباتي';

  @override
  String get vehicleFormComingSoon =>
      'نموذج المركبة سيتم تطبيقه في مهمة مستقبلية.';

  @override
  String get deleteVehicle => 'حذف المركبة';

  @override
  String get confirmDeleteVehicle =>
      'هل أنت متأكد من رغبتك في حذف هذه المركبة؟';

  @override
  String get garageSettings => 'إعدادات الجراج';

  @override
  String get share => 'مشاركة';

  @override
  String get compatibilityGuarantee => 'ضمان التوافق';

  @override
  String get compatibilityGuaranteeMessage =>
      'نضمن أن القطع التي تشتريها ستناسب مركبتك. إذا لم تكن مناسبة، يمكنك إرجاعها واسترداد أموالك كاملة.';

  @override
  String get understood => 'فهمت';

  @override
  String get sharingFeatureComingSoon => 'ميزة المشاركة قريباً!';

  @override
  String get garageIsEmpty => 'جراجك فارغ';

  @override
  String get addVehiclesToFindParts =>
      'أضف مركباتك للعثور على قطع غيار متوافقة';

  @override
  String get contactUs => 'اتصل بنا';

  @override
  String get emailSupport => 'الدعم عبر البريد الإلكتروني';

  @override
  String get phoneSupport => 'الدعم عبر الهاتف';

  @override
  String get faq => 'الأسئلة الشائعة';

  @override
  String get noFaqsAvailable => 'لا توجد أسئلة شائعة متاحة في الوقت الحالي.';

  @override
  String get liveChat => 'محادثة مباشرة';

  @override
  String get savedSearches => 'عمليات البحث المحفوظة';

  @override
  String noResultsFoundFor(String query) {
    return 'لم يتم العثور على نتائج لـ \'$query\'';
  }

  @override
  String get errorOccurred => 'حدث خطأ';

  @override
  String get noSavedItems => 'لم تقم بحفظ أي عناصر حتى الآن.';

  @override
  String get networkError => 'خطأ في الاتصال بالإنترنت';

  @override
  String get serverError => 'خطأ في الخادم';

  @override
  String get timeoutError => 'انتهت مهلة الاتصال';

  @override
  String get invalidCredentials => 'بيانات تسجيل الدخول غير صحيحة';

  @override
  String get emailNotConfirmed => 'يرجى تأكيد البريد الإلكتروني';

  @override
  String get userNotFound => 'المستخدم غير موجود';

  @override
  String get weakPassword => 'كلمة المرور ضعيفة';

  @override
  String get emailAlreadyRegistered => 'البريد الإلكتروني مسجل مسبقاً';

  @override
  String get authError => 'خطأ في المصادقة';

  @override
  String get duplicateEntry => 'البيانات موجودة مسبقاً';

  @override
  String get foreignKeyViolation => 'لا يمكن حذف هذا العنصر';

  @override
  String get insufficientPermissions => 'ليس لديك صلاحية لهذا الإجراء';

  @override
  String get databaseError => 'خطأ في قاعدة البيانات';

  @override
  String get helloWorld => 'Hello World!';

  @override
  String get login => 'Login';

  @override
  String get password => 'Password';

  @override
  String get requiredField => 'This field is required';

  @override
  String get invalidEmail => 'Please enter a valid email';

  @override
  String get passwordTooShort => 'Password must be at least 6 characters';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get authenticationError =>
      'Authentication failed. Please check your credentials.';

  @override
  String get dataProcessingError => 'Error processing data. Please try again.';

  @override
  String get unexpectedError =>
      'An unexpected error occurred. Please try again.';

  @override
  String get completeProfileTitle => 'إكمال البيانات الشخصية';

  @override
  String get completeProfileHeading => 'خطوة أخيرة قبل المتابعة!';

  @override
  String get completeProfileMessage =>
      'نحتاج بعض المعلومات الإضافية في ملفك الشخصي (مثل الاسم ورقم الهاتف) لتتمكن من إتمام عملية الشراء بنجاح.';

  @override
  String get completeProfileButton => 'الانتقال لإكمال البيانات';

  @override
  String get cancelButton => 'إلغاء';

  @override
  String get logoutConfirmation => 'هل أنت متأكد أنك تريد تسجيل الخروج؟';

  @override
  String get createAccountButton => 'إنشاء حساب جديد';

  @override
  String get loginRequiredTitle => 'التسجيل مطلوب';

  @override
  String get loginRequiredHeading => 'ميزة حصرية للأعضاء';

  @override
  String get loginRequiredMessage =>
      'للاستفادة من هذه الميزة والوصول الكامل لخدماتنا، يرجى تسجيل الدخول أو إنشاء حساب جديد.';

  @override
  String get fullName => 'الاسم الكامل';

  @override
  String get enterFullName => 'أدخل اسمك الكامل';

  @override
  String get phoneNumber => 'رقم الهاتف';

  @override
  String get enterPhoneNumber => 'أدخل رقم هاتفك';

  @override
  String get location => 'الموقع';

  @override
  String get enterLocation => 'أدخل مدينتك أو منطقتك';

  @override
  String get saveAndContinue => 'حفظ ومتابعة';

  @override
  String get nameCannotBeEmpty => 'لا يمكن ترك الاسم فارغًا.';

  @override
  String get nameTooShort => 'يجب أن يتكون الاسم من حرفين على الأقل.';

  @override
  String get phoneCannotBeEmpty => 'لا يمكن ترك رقم الهاتف فارغًا.';

  @override
  String get invalidPhoneNumber => 'صيغة رقم الهاتف غير صالحة.';

  @override
  String get locationCannotBeEmpty => 'لا يمكن ترك الموقع فارغًا.';

  @override
  String get locationTooShort => 'يجب أن يتكون الموقع من 3 أحرف على الأقل.';

  @override
  String get removeImage => 'إزالة الصورة';

  @override
  String get profileUpdatedSuccessfully => 'تم تحديث الملف الشخصي بنجاح.';

  @override
  String get storageError => 'خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى.';

  @override
  String get errorLoadingProfile => 'خطأ في تحميل الملف الشخصي.';

  @override
  String get completeProfileWelcome => 'أهلاً بك! نحتاج بعض المعلومات الإضافية';

  @override
  String get completeProfileDescription =>
      'لتحسين تجربتك في التطبيق، يرجى إكمال بياناتك الشخصية أدناه.';

  @override
  String get loginButton => 'تسجيل الدخول';

  @override
  String get supportTicketUpdateSuccess => 'تم تحديث تذكرة الدعم بنجاح.';

  @override
  String get supportTicketUpdateError => 'فشل تحديث تذكرة الدعم.';

  @override
  String get searchHint => 'ابحث عن قطع غيار، فئات...';

  @override
  String get partNamesLanguage => 'لغة أسماء القطع';

  @override
  String get partNamesLanguageDesc =>
      'اختر اللغة لعرض أسماء قطع غيار السيارات.';

  @override
  String get selectAutomotiveTypeTitle => 'اختر نوع المركبة';

  @override
  String get whatAreYouListing => 'ماذا تريد أن تبيع؟';

  @override
  String get selectCategoryToContinue => 'اختر فئة للمتابعة مع إدراجك';

  @override
  String get vehicleSelectionTitle => 'مركبة';

  @override
  String get vehicleSelectionSubtitle =>
      'أدرج سيارات، دراجات نارية، شاحنات، ومركبات أخرى';

  @override
  String get autoPartSelectionTitle => 'قطع غيار';

  @override
  String get autoPartSelectionSubtitle => 'أدرج قطع غيار، إكسسوارات، ومكونات';

  @override
  String get developerTools => 'أدوات المطور';

  @override
  String get analyticsDashboard => 'لوحة التحليلات';

  @override
  String get salesReports => 'تقارير المبيعات';

  @override
  String get inventoryManagement => 'إدارة المخزون';

  @override
  String get advancedDashboard => 'لوحة التحكم المتقدمة';

  @override
  String get storesListing => 'قائمة المتاجر';

  @override
  String get warrantyList => 'قائمة الضمانات';

  @override
  String get archivedChats => 'المحادثات المؤرشفة';

  @override
  String get chatStorage => 'تخزين المحادثات';

  @override
  String get conversations => 'المحادثات';

  @override
  String get enhancedSearch => 'البحث المحسّن';

  @override
  String get compatiblePartsTest => 'اختبار القطع المتوافقة';

  @override
  String get adminSubscriptionRequests => 'طلبات الاشتراك الإدارية';

  @override
  String get devToolsDebugOnly => 'أدوات المطور متاحة فقط في وضع التطوير';

  @override
  String get smartSearch => 'البحث الذكي';

  @override
  String get hybridSearch => 'البحث المختلط';

  @override
  String get semanticSearch => 'البحث الدلالي';

  @override
  String get clearHistory => 'مسح التاريخ';

  @override
  String get startSearching => 'ابدأ البحث';

  @override
  String get searchDescription => 'استخدم البحث الذكي للعثور على ما تريد بسرعة';

  @override
  String get searchSuggestions => 'اقتراحات البحث';

  @override
  String get searchHistory => 'تاريخ البحث';

  @override
  String get clearSearchHistory => 'مسح تاريخ البحث';

  @override
  String get noSearchHistory => 'لا يوجد تاريخ بحث';

  @override
  String get searchFailed => 'فشل البحث';

  @override
  String get searchEmpty => 'لا توجد نتائج';

  @override
  String get searchEmptyMessage =>
      'لم يتم العثور على نتائج. حاول كلمات مختلفة.';

  @override
  String searchProcessingTime(Object time) {
    return 'وقت المعالجة: ${time}ms';
  }

  @override
  String searchResultsCount(Object count) {
    return '$count نتيجة';
  }

  @override
  String get searchInsights => 'رؤى البحث';

  @override
  String get searchCategoryDistribution => 'توزيع الفئات';

  @override
  String get searchPopularFilters => 'فلاتر شائعة';

  @override
  String get searchPriceRange => 'نطاق السعر';

  @override
  String get searchMinPrice => 'السعر الأدنى';

  @override
  String get searchMaxPrice => 'السعر الأعلى';

  @override
  String get searchAveragePrice => 'متوسط السعر';

  @override
  String get searchCars => 'بحث السيارات';

  @override
  String get searchAutoParts => 'بحث قطع الغيار';

  @override
  String get carBrand => 'علامة السيارة';

  @override
  String get carModel => 'موديل السيارة';

  @override
  String get carYear => 'سنة الصنع';

  @override
  String get partType => 'نوع القطعة';

  @override
  String get compatibility => 'التوافق';

  @override
  String get mileage => 'المسافة المقطوعة';

  @override
  String get fuelType => 'نوع الوقود';

  @override
  String get transmission => 'ناقل الحركة';

  @override
  String get warranty => 'الضمان';

  @override
  String get oemPart => 'قطعة أصلية';

  @override
  String get sedans => 'سيدان';

  @override
  String get suvs => 'SUV';

  @override
  String get hatchbacks => 'هاتشباك';

  @override
  String get pickupTrucks => 'بيك أب';

  @override
  String get coupes => 'كوبيه';

  @override
  String get convertibles => 'قابلة للتحويل';

  @override
  String get motorcycles => 'دراجات نارية';

  @override
  String get engineParts => 'أجزاء المحرك';

  @override
  String get brakeParts => 'أجزاء الفرامل';

  @override
  String get electricalParts => 'أجزاء كهربائية';

  @override
  String get bodyParts => 'أجزاء الهيكل';

  @override
  String get transmissionParts => 'أجزاء ناقل الحركة';

  @override
  String get suspensionParts => 'أجزاء التعليق';

  @override
  String get coolingParts => 'أجزاء التبريد';

  @override
  String get exhaustParts => 'أجزاء العادم';

  @override
  String get smartFilters => 'فلاتر ذكية';

  @override
  String get activeFilters => 'الفلاتر النشطة';

  @override
  String get applyFilters => 'تطبيق الفلاتر';

  @override
  String get quickSuggestions => 'اقتراحات سريعة';

  @override
  String get searchByCategory => 'البحث حسب الفئة';

  @override
  String get aiPoweredSearch => 'البحث بالذكاء الاصطناعي';

  @override
  String get intelligentSearch => 'البحث الذكي';
}
