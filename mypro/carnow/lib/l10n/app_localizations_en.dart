// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'CarNow - Car Parts Marketplace';

  @override
  String get welcomeGuest => 'Welcome, Guest';

  @override
  String get guestSubtitle =>
      'Sign in or create an account to get the full experience.';

  @override
  String get loginOrRegister => 'Login / Register';

  @override
  String get categories => 'Categories';

  @override
  String get allCategories => 'All Categories';

  @override
  String get shopByCategory => 'Shop by Category';

  @override
  String get featuredCategories => 'Featured Categories';

  @override
  String get featuredProducts => 'Featured Products';

  @override
  String get noCategoriesAvailable => 'No categories available';

  @override
  String get errorLoadingCategories => 'Error loading categories';

  @override
  String get failedToLoadProducts => 'Failed to load products';

  @override
  String get inputTooLong => 'Input is too long';

  @override
  String get invalidInput => 'Invalid input detected';

  @override
  String get securityWarning => 'Potentially unsafe content detected';

  @override
  String get invalidName => 'Name contains invalid characters';

  @override
  String get phoneTooShort => 'Phone number is too short';

  @override
  String get phoneTooLong => 'Phone number is too long';

  @override
  String get passwordTooLong => 'Password is too long';

  @override
  String get passwordNeedsUppercase =>
      'Password must contain at least one uppercase letter';

  @override
  String get passwordNeedsLowercase =>
      'Password must contain at least one lowercase letter';

  @override
  String get passwordNeedsNumber => 'Password must contain at least one number';

  @override
  String get passwordNeedsSpecialChar =>
      'Password must contain at least one special character';

  @override
  String get passwordTooCommon => 'Password is too common';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get invalidAmount => 'Please enter a valid amount';

  @override
  String get amountMustBePositive => 'Amount must be positive';

  @override
  String get amountTooSmall => 'Amount is too small';

  @override
  String get amountTooLarge => 'Amount is too large';

  @override
  String get accountNumberTooShort => 'Account number is too short';

  @override
  String get accountNumberTooLong => 'Account number is too long';

  @override
  String get invalidAccountNumber => 'Invalid account number format';

  @override
  String get invalidVIN => 'Invalid VIN format';

  @override
  String get invalidPostalCode => 'Invalid postal code format';

  @override
  String get invalidURL => 'Please enter a valid URL';

  @override
  String get passwordStrengthNone => 'No password';

  @override
  String get passwordStrengthWeak => 'Weak';

  @override
  String get passwordStrengthMedium => 'Medium';

  @override
  String get passwordStrengthStrong => 'Strong';

  @override
  String get passwordStrengthVeryStrong => 'Very Strong';

  @override
  String get validationError => 'Validation error occurred';

  @override
  String get securityError => 'Security issue detected';

  @override
  String get unknownError => 'An unexpected error occurred';

  @override
  String get invalidImageFormat => 'Invalid image format';

  @override
  String get invalidDocumentFormat => 'Invalid document format';

  @override
  String get fileTooLarge => 'File is too large';

  @override
  String get mustSignInFirst => 'You must sign in first';

  @override
  String get outOfStock => 'Out of Stock';

  @override
  String get cart => 'Cart';

  @override
  String get myGarage => 'My Garage';

  @override
  String get home => 'Home';

  @override
  String get myCars => 'My Cars';

  @override
  String get search => 'Search...';

  @override
  String get inbox => 'Inbox';

  @override
  String get selling => 'Selling';

  @override
  String get carNowMarketplace => 'CarNow Marketplace';

  @override
  String get all => 'All';

  @override
  String get newItem => 'New';

  @override
  String get used => 'Used';

  @override
  String get featured => 'Featured';

  @override
  String get nearby => 'Nearby';

  @override
  String get specialOffer => 'Special Offer!';

  @override
  String get specialOfferDetails => 'Get 10% off on your first order.';

  @override
  String get learnMore => 'Learn More';

  @override
  String get add => 'Add';

  @override
  String get notifications => 'Notifications';

  @override
  String get store => 'Store';

  @override
  String get account => 'Account';

  @override
  String get shoppingActivity => 'Shopping Activity';

  @override
  String get trackYourOrders => 'Track your orders';

  @override
  String get manageYourListings => 'Manage your items for sale';

  @override
  String get viewBuyerSellerMessages => 'View buyer and seller messages';

  @override
  String get becomeASeller => 'Become a Seller';

  @override
  String get becomeASellerSubtitle =>
      'Start selling auto parts and accessories';

  @override
  String get applyToSellOnCarNow => 'Apply to sell on CarNow';

  @override
  String get settingsAndSupport => 'Settings & Support';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get editYourProfileDetails => 'Edit your profile details';

  @override
  String get accountSettings => 'Account settings';

  @override
  String get getHelpAndContactSupport => 'Get help and contact support';

  @override
  String get signOutFromYourAccount => 'Sign out from your account';

  @override
  String get signInPrompt => 'Sign in or register to manage your listings';

  @override
  String get register => 'Register';

  @override
  String get signIn => 'Sign In';

  @override
  String get myOrders => 'My Orders';

  @override
  String get myListings => 'My Listings';

  @override
  String get messages => 'Messages';

  @override
  String get sellerSection => 'Seller';

  @override
  String get subscription => 'Subscription';

  @override
  String get subscriptionPlans => 'Subscription Plans';

  @override
  String get chooseYourPlan => 'Choose Your Plan';

  @override
  String get monthly => 'Monthly';

  @override
  String get yearly => 'Yearly';

  @override
  String get month => 'month';

  @override
  String get year => 'year';

  @override
  String get continueButton => 'Continue';

  @override
  String get selected => 'Selected';

  @override
  String get select => 'Select';

  @override
  String get sales => 'Sales';

  @override
  String get settings => 'Settings';

  @override
  String get support => 'Support';

  @override
  String get logout => 'Logout';

  @override
  String get guestUser => 'Guest User';

  @override
  String get signInToContinue => 'Sign in to continue';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get supabaseTest => 'Supabase Test';

  @override
  String get shoppingCart => 'Shopping Cart';

  @override
  String get clearCart => 'Clear Cart';

  @override
  String get clearCartConfirmationTitle => 'Confirm Clear Cart';

  @override
  String get clearCartConfirmationBody =>
      'Are you sure you want to remove all items from your cart?';

  @override
  String get cancel => 'Cancel';

  @override
  String get clear => 'Clear';

  @override
  String get emptyCartMessage => 'Your cart is empty';

  @override
  String get emptyCartSubMessage =>
      'Browse products and add something you like!';

  @override
  String get shopNow => 'Shop Now';

  @override
  String get removeFromCart => 'Remove from cart';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get items => 'items';

  @override
  String get shipping => 'Shipping';

  @override
  String get total => 'Total';

  @override
  String get proceedToCheckout => 'Proceed to Checkout';

  @override
  String get clearSearch => 'Clear search';

  @override
  String get noSuggestionsFound => 'No search suggestions found';

  @override
  String get startTypingToSearch => 'Start typing to search';

  @override
  String get error => 'Error';

  @override
  String get recentSearches => 'Recent';

  @override
  String get shoppingActivitySection => 'Shopping Activity';

  @override
  String get myOrdersSubtitle => 'Track your orders';

  @override
  String get myListingsSubtitle => 'Manage your items for sale';

  @override
  String get messagesSubtitle => 'View buyer and seller messages';

  @override
  String get settingsSection => 'Settings & Support';

  @override
  String get settingsSubtitle => 'Manage app preferences';

  @override
  String get sellerToolsSection => 'Seller Tools';

  @override
  String get subscriptionSubtitle => 'Manage your seller plan';

  @override
  String get salesSubtitle => 'View your earnings';

  @override
  String get noResultsFound => 'No results found';

  @override
  String get save => 'Save';

  @override
  String get sellerDashboard => 'Seller Dashboard';

  @override
  String get sellerDashboardSubtitle => 'Manage products, sales, and settings';

  @override
  String get sellerProducts => 'Products';

  @override
  String get sellerProductsSubtitle => 'Manage your product catalog';

  @override
  String get sellerOrders => 'Orders';

  @override
  String get sellerOrdersSubtitle => 'View and manage customer orders';

  @override
  String get sellerStore => 'Store';

  @override
  String get sellerSettings => 'Settings';

  @override
  String get sellerAnalytics => 'Analytics';

  @override
  String get sellerPromotionsSubtitle => 'Create and manage special offers';

  @override
  String get sellerApprovalPending => 'Seller Approval Pending';

  @override
  String get sellerApprovalPendingDesc =>
      'Your seller account is pending approval by our team. You\'ll be able to access seller tools once approved.';

  @override
  String get noSellerStore => 'No Store Found';

  @override
  String get noSellerStoreDesc =>
      'You need to create a store before you can start selling products';

  @override
  String get createStore => 'Create Store';

  @override
  String get revenueSummary => 'Revenue Summary';

  @override
  String get todayRevenue => 'Today';

  @override
  String get weeklyRevenue => 'This Week';

  @override
  String get monthlyRevenue => 'This Month';

  @override
  String get totalRevenue => 'Total Revenue';

  @override
  String get last7DaysRevenue => 'Last 7 Days Revenue';

  @override
  String get pendingOrders => 'Pending';

  @override
  String get completedOrders => 'Completed';

  @override
  String get totalSales => 'Total Sales';

  @override
  String get totalProducts => 'Total Products';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get addProduct => 'Add Product';

  @override
  String get manageProducts => 'Manage Products';

  @override
  String get createPromotion => 'Create Offer';

  @override
  String get managePromos => 'Manage Offers';

  @override
  String get viewOrders => 'View Orders';

  @override
  String get storeSettings => 'Store Settings';

  @override
  String get recentOrders => 'Recent Orders';

  @override
  String get topSellingProducts => 'Top Selling Products';

  @override
  String get verifiedStore => 'Verified Store';

  @override
  String get editStore => 'Edit Store';

  @override
  String get viewAll => 'View All';

  @override
  String get viewMore => 'View More';

  @override
  String get searchProducts => 'Search products...';

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String get pending => 'Pending';

  @override
  String get rejected => 'Rejected';

  @override
  String get noProductsYet => 'No Products Yet';

  @override
  String get tapAddToCreateProduct =>
      'Tap the add button to create your first product';

  @override
  String get noProductsMatchingFilter => 'No products matching your filter';

  @override
  String get clearFilters => 'Clear Filters';

  @override
  String get productHiddenSuccess => 'Product has been hidden';

  @override
  String get productVisibleSuccess => 'Product is now visible';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get show => 'Show';

  @override
  String get hide => 'Hide';

  @override
  String get unknown => 'Unknown';

  @override
  String get nameLabel => 'Name';

  @override
  String get nameRequired => 'Please enter your name';

  @override
  String soldCount(int count) {
    return '$count sold';
  }

  @override
  String get promotions => 'Promotions & Offers';

  @override
  String get noStoreFound => 'No store found';

  @override
  String get noActivePromotions => 'No active promotions found';

  @override
  String get showAllPromotions => 'Show all promotions';

  @override
  String get showActiveOnly => 'Show active only';

  @override
  String get noPromotionsYet => 'No Promotions Yet';

  @override
  String get createPromotionDesc =>
      'Create special offers and discounts for your customers';

  @override
  String get deletePromotion => 'Delete Promotion';

  @override
  String get deletePromotionConfirm =>
      'Are you sure you want to delete this promotion? This action cannot be undone.';

  @override
  String get promotionDeleted => 'Promotion deleted successfully';

  @override
  String get expired => 'Expired';

  @override
  String get scheduled => 'Scheduled';

  @override
  String get offDiscount => 'off';

  @override
  String get discountValue => 'discount';

  @override
  String get products => 'products';

  @override
  String get anonymous => 'Anonymous';

  @override
  String get myProducts => 'My Products';

  @override
  String get filterProducts => 'Filter Products';

  @override
  String get editProduct => 'Edit Product';

  @override
  String get productName => 'Product Name';

  @override
  String get price => 'Price';

  @override
  String get condition => 'Condition';

  @override
  String get description => 'Description';

  @override
  String get productImages => 'Product Images';

  @override
  String get gallery => 'Gallery';

  @override
  String get camera => 'Camera';

  @override
  String get noImagesSelected => 'Please select at least one image';

  @override
  String get imagesSelected => 'images selected';

  @override
  String get updateProduct => 'Update Product';

  @override
  String get deleteProduct => 'Delete Product';

  @override
  String get confirmDeleteProduct =>
      'Are you sure you want to delete this product?';

  @override
  String get productDeleted => 'Product deleted successfully';

  @override
  String get productAdded => 'Product added successfully';

  @override
  String get productUpdated => 'Product updated successfully';

  @override
  String get errorDeletingProduct => 'Error deleting product';

  @override
  String get errorAddingProduct => 'Error adding product';

  @override
  String get errorUpdatingProduct => 'Error updating product';

  @override
  String get errorLoadingProducts => 'Error loading products';

  @override
  String get noProductsFound => 'No products found';

  @override
  String get invalidPrice => 'Please enter a valid price';

  @override
  String get fieldRequired => 'This field is required';

  @override
  String get noProductsAvailable => 'No products available in this category';

  @override
  String get phoneLabel => 'Phone Number';

  @override
  String get phoneInvalid => 'Please enter a valid phone number';

  @override
  String get addressLabel => 'Address';

  @override
  String get addressRequired => 'Please enter your address';

  @override
  String get saveChanges => 'Save Changes';

  @override
  String get discardChanges => 'Discard Changes';

  @override
  String get changesSaved => 'Changes saved successfully';

  @override
  String get errorSavingChanges => 'Error saving changes. Please try again.';

  @override
  String get profileUpdated => 'Profile updated successfully';

  @override
  String get profileUpdateFailed => 'Failed to update profile information';

  @override
  String get phoneHint => 'Enter your phone number';

  @override
  String get addressHint => 'Enter your address';

  @override
  String get resetForm => 'Reset Form';

  @override
  String get confirmDiscardTitle => 'Discard Changes?';

  @override
  String get confirmDiscardMessage =>
      'Are you sure you want to discard your changes?';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get typeAMessage => 'Type a message...';

  @override
  String get newConversation => 'New Conversation';

  @override
  String get searchUsers => 'Search users...';

  @override
  String get createGroupChat => 'Create Group Chat';

  @override
  String get groupChatTitle => 'Group Title';

  @override
  String get noUsersFound => 'No users found';

  @override
  String get chatMessagesSubtitle => 'Chat with sellers and support';

  @override
  String get noConversationsYet => 'No conversations yet';

  @override
  String get startNewConversation => 'Start a new conversation';

  @override
  String get noMessagesYet => 'No messages yet';

  @override
  String get sendFirstMessage => 'Send a message to start the conversation';

  @override
  String get retry => 'Retry';

  @override
  String get conversationDetails => 'Conversation Details';

  @override
  String get viewProfile => 'View Profile';

  @override
  String get deleteConversation => 'Delete Conversation';

  @override
  String get participants => 'Participants';

  @override
  String get loading => 'Loading...';

  @override
  String get help => 'Help';

  @override
  String get manageSalesCategories => 'Manage Sales Categories';

  @override
  String get selectCategoriesDescription =>
      'Select the categories in which you want to sell your products.';

  @override
  String get categoriesSaved => 'Categories saved successfully';

  @override
  String get categoriesHelp => 'Categories Help';

  @override
  String get categoriesHelpText =>
      'You can select the categories in which you want to sell your products. This will make your products appear in these categories when displayed to buyers.';

  @override
  String get close => 'Close';

  @override
  String get myCarNow => 'My CarNow';

  @override
  String get welcomeToCarNow => 'Welcome to CarNow';

  @override
  String get signInToAccessAccount =>
      'Sign in to access your account and saved features';

  @override
  String get createAccount => 'Create Account';

  @override
  String get carNowUser => 'New Member';

  @override
  String get newMember => 'New Member';

  @override
  String get incompleteProfile => 'Profile Incomplete';

  @override
  String get completeYourProfile => 'Complete Your Profile';

  @override
  String get profileIncomplete => 'Profile Incomplete';

  @override
  String get memberSince => 'Member since';

  @override
  String get yourUpdates => 'Your Updates';

  @override
  String get watchlist => 'Watchlist';

  @override
  String get favoritesAndLists => 'Favorites & Lists';

  @override
  String get bidsAndOffers => 'Bids & Offers';

  @override
  String get activeAuctionsAndOffers => 'Active auctions and offers';

  @override
  String get shopping => 'Shopping';

  @override
  String get followWatchedItems => 'Follow watched items';

  @override
  String get savedItems => 'Saved Items';

  @override
  String get searchesSellersFeeds => 'Searches, sellers, feeds';

  @override
  String get buyAgain => 'Buy Again';

  @override
  String get shopFromPreviousPurchases => 'Shop from previous purchases';

  @override
  String get purchases => 'Purchases';

  @override
  String get orderHistory => 'Order history';

  @override
  String get recentlyViewed => 'Recently Viewed';

  @override
  String get itemsYouViewed => 'Items you viewed';

  @override
  String get carNowGarage => 'CarNow Garage';

  @override
  String get addCarGetCompatibleParts =>
      'Add your car and get compatible parts';

  @override
  String get manageGarage => 'Manage Garage';

  @override
  String get addVehicle => 'Add Vehicle';

  @override
  String get fitsYourRideEveryTime => 'Fits your ride, every time';

  @override
  String get rightPartsOrMoneyBack => 'Get the right parts or your money back';

  @override
  String get localInstallationServices => 'Local Installation Services';

  @override
  String get installPartsWithExperts =>
      'Install your new parts with our network of experts';

  @override
  String get browseServices => 'Browse Services';

  @override
  String get myVehicles => 'My Vehicles';

  @override
  String get vehicleFormComingSoon =>
      'Vehicle form will be implemented in a future task';

  @override
  String get deleteVehicle => 'Delete Vehicle';

  @override
  String get confirmDeleteVehicle =>
      'Are you sure you want to delete this vehicle?';

  @override
  String get garageSettings => 'Garage Settings';

  @override
  String get share => 'Share';

  @override
  String get compatibilityGuarantee => 'Compatibility Guarantee';

  @override
  String get compatibilityGuaranteeMessage =>
      'We guarantee that the parts you buy will fit your vehicle. If they don\'t fit, you can return them and get a full refund';

  @override
  String get understood => 'Understood';

  @override
  String get sharingFeatureComingSoon => 'Sharing feature coming soon!';

  @override
  String get garageIsEmpty => 'Your garage is empty';

  @override
  String get addVehiclesToFindParts =>
      'Add your vehicles to find compatible parts';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get emailSupport => 'Email Support';

  @override
  String get phoneSupport => 'Phone Support';

  @override
  String get faq => 'Frequently Asked Questions';

  @override
  String get noFaqsAvailable => 'No FAQs available at the moment.';

  @override
  String get liveChat => 'Live Chat';

  @override
  String get savedSearches => 'Saved';

  @override
  String noResultsFoundFor(String query) {
    return 'No results found for \'$query\'';
  }

  @override
  String get errorOccurred => 'An error occurred';

  @override
  String get noSavedItems => 'You haven\'t saved any items yet.';

  @override
  String get networkError => 'Network error. Please check your connection.';

  @override
  String get serverError => 'Server error';

  @override
  String get timeoutError => 'Connection timeout';

  @override
  String get invalidCredentials => 'Invalid login credentials';

  @override
  String get emailNotConfirmed => 'Please confirm your email';

  @override
  String get userNotFound => 'User not found.';

  @override
  String get weakPassword => 'Password is too weak';

  @override
  String get emailAlreadyRegistered => 'Email is already registered';

  @override
  String get authError =>
      'Authentication failed. Please check your credentials.';

  @override
  String get duplicateEntry => 'Data already exists';

  @override
  String get foreignKeyViolation => 'Cannot delete this item';

  @override
  String get insufficientPermissions =>
      'You don\'t have permission for this action';

  @override
  String get databaseError => 'Database error';

  @override
  String get helloWorld => 'Hello World!';

  @override
  String get login => 'Login';

  @override
  String get password => 'Password';

  @override
  String get requiredField => 'This field is required';

  @override
  String get invalidEmail => 'Please enter a valid email';

  @override
  String get passwordTooShort => 'Password must be at least 6 characters';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get authenticationError =>
      'Authentication failed. Please check your credentials.';

  @override
  String get dataProcessingError => 'Error processing data. Please try again.';

  @override
  String get unexpectedError =>
      'An unexpected error occurred. Please try again.';

  @override
  String get completeProfileTitle => 'Complete Your Profile';

  @override
  String get completeProfileHeading => 'One Last Step Before You Proceed!';

  @override
  String get completeProfileMessage =>
      'We need some additional information in your profile (like your name and phone number) to successfully complete the purchase.';

  @override
  String get completeProfileButton => 'Go to Complete Profile';

  @override
  String get cancelButton => 'Cancel';

  @override
  String get logoutConfirmation => 'Are you sure you want to log out?';

  @override
  String get createAccountButton => 'Create a New Account';

  @override
  String get loginRequiredTitle => 'Login Required';

  @override
  String get loginRequiredHeading => 'Exclusive Feature for Members';

  @override
  String get loginRequiredMessage =>
      'To use this feature and get full access to our services, please log in or create a new account.';

  @override
  String get fullName => 'Full Name';

  @override
  String get enterFullName => 'Enter your full name';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get enterPhoneNumber => 'Enter your mobile number';

  @override
  String get location => 'Location';

  @override
  String get enterLocation => 'Enter your city or area';

  @override
  String get saveAndContinue => 'Save and Continue';

  @override
  String get nameCannotBeEmpty => 'Name cannot be empty.';

  @override
  String get nameTooShort => 'Name must be at least 2 characters.';

  @override
  String get phoneCannotBeEmpty => 'Phone number cannot be empty.';

  @override
  String get invalidPhoneNumber => 'Invalid phone number format.';

  @override
  String get locationCannotBeEmpty => 'Location cannot be empty.';

  @override
  String get locationTooShort => 'Location must be at least 3 characters.';

  @override
  String get removeImage => 'Remove Image';

  @override
  String get profileUpdatedSuccessfully => 'Profile updated successfully.';

  @override
  String get storageError => 'Error saving data. Please try again.';

  @override
  String get errorLoadingProfile => 'Error loading profile.';

  @override
  String get completeProfileWelcome =>
      'Welcome! We need some additional information';

  @override
  String get completeProfileDescription =>
      'To improve your experience in the app, please complete your personal information below.';

  @override
  String get loginButton => 'Login';

  @override
  String get supportTicketUpdateSuccess =>
      'Support ticket updated successfully.';

  @override
  String get supportTicketUpdateError => 'Failed to update support ticket.';

  @override
  String get searchHint => 'Search for parts, categories...';

  @override
  String get partNamesLanguage => 'Part Names Language';

  @override
  String get partNamesLanguageDesc =>
      'Choose the language for displaying car part names.';

  @override
  String get selectAutomotiveTypeTitle => 'Select Automotive Type';

  @override
  String get whatAreYouListing => 'What are you listing?';

  @override
  String get selectCategoryToContinue =>
      'Select a category to continue with your listing';

  @override
  String get vehicleSelectionTitle => 'Vehicle';

  @override
  String get vehicleSelectionSubtitle =>
      'List cars, motorcycles, trucks, and other vehicles';

  @override
  String get autoPartSelectionTitle => 'Auto Parts';

  @override
  String get autoPartSelectionSubtitle =>
      'List spare parts, accessories, and components';

  @override
  String get developerTools => 'Developer Tools';

  @override
  String get analyticsDashboard => 'Analytics Dashboard';

  @override
  String get salesReports => 'Sales Reports';

  @override
  String get inventoryManagement => 'Inventory Management';

  @override
  String get advancedDashboard => 'Advanced Dashboard';

  @override
  String get storesListing => 'Stores Listing';

  @override
  String get warrantyList => 'Warranty List';

  @override
  String get archivedChats => 'Archived Chats';

  @override
  String get chatStorage => 'Chat Storage';

  @override
  String get conversations => 'Conversations';

  @override
  String get enhancedSearch => 'Enhanced Search';

  @override
  String get compatiblePartsTest => 'Compatible Parts (Test)';

  @override
  String get adminSubscriptionRequests => 'Admin - Subscription Requests';

  @override
  String get devToolsDebugOnly =>
      'Developer tools are only available in debug mode';

  @override
  String get smartSearch => 'Smart Search';

  @override
  String get hybridSearch => 'Hybrid Search';

  @override
  String get semanticSearch => 'Semantic Search';

  @override
  String get clearHistory => 'Clear History';

  @override
  String get startSearching => 'Start Searching';

  @override
  String get searchDescription =>
      'Use smart search to find what you want quickly';

  @override
  String get searchSuggestions => 'Search Suggestions';

  @override
  String get searchHistory => 'Search History';

  @override
  String get clearSearchHistory => 'Clear Search History';

  @override
  String get noSearchHistory => 'No search history';

  @override
  String get searchFailed => 'Search Failed';

  @override
  String get searchEmpty => 'No Results';

  @override
  String get searchEmptyMessage => 'No results found. Try different keywords.';

  @override
  String searchProcessingTime(Object time) {
    return 'Processing time: ${time}ms';
  }

  @override
  String searchResultsCount(Object count) {
    return '$count results';
  }

  @override
  String get searchInsights => 'Search Insights';

  @override
  String get searchCategoryDistribution => 'Category Distribution';

  @override
  String get searchPopularFilters => 'Popular Filters';

  @override
  String get searchPriceRange => 'Price Range';

  @override
  String get searchMinPrice => 'Min Price';

  @override
  String get searchMaxPrice => 'Max Price';

  @override
  String get searchAveragePrice => 'Average Price';

  @override
  String get searchCars => 'Search Cars';

  @override
  String get searchAutoParts => 'Search Auto Parts';

  @override
  String get carBrand => 'Car Brand';

  @override
  String get carModel => 'Car Model';

  @override
  String get carYear => 'Car Year';

  @override
  String get partType => 'Part Type';

  @override
  String get compatibility => 'Compatibility';

  @override
  String get mileage => 'Mileage';

  @override
  String get fuelType => 'Fuel Type';

  @override
  String get transmission => 'Transmission';

  @override
  String get warranty => 'Warranty';

  @override
  String get oemPart => 'OEM Part';

  @override
  String get sedans => 'Sedans';

  @override
  String get suvs => 'SUVs';

  @override
  String get hatchbacks => 'Hatchbacks';

  @override
  String get pickupTrucks => 'Pickup Trucks';

  @override
  String get coupes => 'Coupes';

  @override
  String get convertibles => 'Convertibles';

  @override
  String get motorcycles => 'Motorcycles';

  @override
  String get engineParts => 'Engine Parts';

  @override
  String get brakeParts => 'Brake Parts';

  @override
  String get electricalParts => 'Electrical Parts';

  @override
  String get bodyParts => 'Body Parts';

  @override
  String get transmissionParts => 'Transmission Parts';

  @override
  String get suspensionParts => 'Suspension Parts';

  @override
  String get coolingParts => 'Cooling Parts';

  @override
  String get exhaustParts => 'Exhaust Parts';

  @override
  String get smartFilters => 'Smart Filters';

  @override
  String get activeFilters => 'Active Filters';

  @override
  String get applyFilters => 'Apply Filters';

  @override
  String get quickSuggestions => 'Quick Suggestions';

  @override
  String get searchByCategory => 'Search by Category';

  @override
  String get aiPoweredSearch => 'AI-Powered Search';

  @override
  String get intelligentSearch => 'Intelligent Search';
}
