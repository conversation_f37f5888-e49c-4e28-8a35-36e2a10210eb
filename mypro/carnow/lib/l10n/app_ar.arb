{"@@locale": "ar", "appTitle": "كارناو - سوق قطع غيار السيارات", "@appTitle": {"description": "العنوان العام للتطبيق"}, "welcomeGuest": "أهلاً بك أيها الزائر", "@welcomeGuest": {"description": "Welcome message for guest users"}, "guestSubtitle": "سجل الدخول أو أنشئ حسابًا جديدًا للاستفادة من كامل الميزات.", "@guestSubtitle": {"description": "Subtitle for guest users encouraging them to sign up"}, "loginOrRegister": "تسجيل الدخول / إنشاء حساب", "@loginOrRegister": {"description": "Button text for login or register"}, "categories": "الفئات", "@categories": {"description": "قسم الفئات"}, "allCategories": "جميع الفئات", "@allCategories": {"description": "علامة قسم جميع الفئات"}, "shopByCategory": "تسوق حسب الفئة", "@shopByCategory": {"description": "علامة قسم التسوق حسب الفئة"}, "noCategoriesAvailable": "لا توجد فئات متاحة", "@noCategoriesAvailable": {"description": "الرسالة التي تظهر عند عدم توفر فئات"}, "errorLoadingCategories": "خطأ في تحميل الفئات", "@errorLoadingCategories": {"description": "رسالة خطأ عند فشل تحميل الفئات"}, "failedToLoadProducts": "فشل تحميل المنتجات", "@failedToLoadProducts": {"description": "رسالة خطأ عند فشل تحميل المنتجات"}, "mustSignInFirst": "يجب عليك تسجيل الدخول أولا", "@mustSignInFirst": {"description": "رسالة تظهر عندما يتطلب إجراء ما تسجيل دخول المستخدم"}, "outOfStock": "نفدت الكمية", "@outOfStock": {"description": "علامة تشير إلى أن المنتج غير متوفر"}, "inputTooLong": "المدخل طويل جداً", "@inputTooLong": {"description": "رسالة خطأ عندما يتجاوز المدخل الحد الأقصى للطول"}, "invalidInput": "تم اكتشاف مدخل غير صالح", "@invalidInput": {"description": "رسالة خطأ عند اكتشاف مح<PERSON><PERSON>ى خطير"}, "securityWarning": "تم اكتشاف مح<PERSON>وى قد يكون غير آمن", "@securityWarning": {"description": "رسالة تحذير للمشاكل الأمنية"}, "invalidName": "الاسم يحتوي على أحرف غير صالحة", "@invalidName": {"description": "رسالة خطأ للأحرف غير الصالحة في الاسم"}, "phoneTooShort": "رقم الهاتف قصير جداً", "@phoneTooShort": {"description": "رسالة خطأ عندما يكون رقم الهاتف قصير جداً"}, "phoneTooLong": "رقم الهاتف طويل جداً", "@phoneTooLong": {"description": "رسالة خطأ عندما يكون رقم الهاتف طويل جداً"}, "passwordTooLong": "كلمة المرور طويلة جداً", "@passwordTooLong": {"description": "رسالة خطأ عندما تتجاوز كلمة المرور الحد الأقصى للطول"}, "passwordNeedsUppercase": "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل", "@passwordNeedsUppercase": {"description": "رسالة خطأ عندما تفتقر كلمة المرور للأحرف الكبيرة"}, "passwordNeedsLowercase": "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل", "@passwordNeedsLowercase": {"description": "رسالة خطأ عندما تفتقر كلمة المرور للأحرف الصغيرة"}, "passwordNeedsNumber": "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل", "@passwordNeedsNumber": {"description": "رسالة خطأ عندما تفتقر كلمة المرور للأرقام"}, "passwordNeedsSpecialChar": "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل", "@passwordNeedsSpecialChar": {"description": "رسالة خطأ عندما تفتقر كلمة المرور للرموز الخاصة"}, "passwordTooCommon": "كلمة المرور شائعة جداً", "@passwordTooCommon": {"description": "رسالة خطأ عندما تكون كلمة المرور شائعة الاستخدام"}, "passwordsDoNotMatch": "كلمات المرور غير متطابقة", "@passwordsDoNotMatch": {"description": "رسالة خطأ عندما لا يتطابق تأكيد كلمة المرور"}, "invalidAmount": "ير<PERSON>ى إدخال مبلغ صالح", "@invalidAmount": {"description": "رسالة خطأ للمبالغ المالية غير الصالحة"}, "amountMustBePositive": "المبلغ يجب أن يكون موجباً", "@amountMustBePositive": {"description": "رسالة خطأ عندما لا يكون المبلغ موجباً"}, "amountTooSmall": "المبلغ صغير جداً", "@amountTooSmall": {"description": "رسالة خطأ عندما يكون المبلغ أقل من الحد الأدنى"}, "amountTooLarge": "المبلغ كبير جداً", "@amountTooLarge": {"description": "رسالة خطأ عندما يتجاوز المبلغ الحد الأقصى"}, "accountNumberTooShort": "رقم الحساب قصير جداً", "@accountNumberTooShort": {"description": "رسالة خطأ عندما يكون رقم الحساب قصير جداً"}, "accountNumberTooLong": "رقم الحساب طويل جداً", "@accountNumberTooLong": {"description": "رسالة خطأ عندما يكون رقم الحساب طويل جداً"}, "invalidAccountNumber": "تنسيق رقم الحساب غير صالح", "@invalidAccountNumber": {"description": "رسالة خطأ لتنسيق رقم الحساب غير الصالح"}, "invalidVIN": "تنسيق رقم VIN غير صالح", "@invalidVIN": {"description": "رسالة خطأ لتنسيق رقم VIN غير الصالح"}, "invalidPostalCode": "تنسيق الرمز البريدي غير صالح", "@invalidPostalCode": {"description": "رسالة خطأ لتنسيق الرمز البريدي غير الصالح"}, "invalidURL": "ير<PERSON>ى إدخال رابط صالح", "@invalidURL": {"description": "رسالة خطأ لتنسيق الرابط غير الصالح"}, "passwordStrengthNone": "لا توجد كلمة مرور", "@passwordStrengthNone": {"description": "مؤشر قوة كلمة المرور لعدم وجود كلمة مرور"}, "passwordStrengthWeak": "ضعيفة", "@passwordStrengthWeak": {"description": "مؤشر قوة كلمة المرور للكلمات الضعيفة"}, "passwordStrengthMedium": "متوسطة", "@passwordStrengthMedium": {"description": "مؤشر قوة كلمة المرور للكلمات المتوسطة"}, "passwordStrengthStrong": "قوية", "@passwordStrengthStrong": {"description": "مؤشر قوة كلمة المرور للكلمات القوية"}, "passwordStrengthVeryStrong": "قوية جداً", "@passwordStrengthVeryStrong": {"description": "مؤشر قوة كلمة المرور للكلمات القوية جداً"}, "validationError": "حدث خطأ في التحقق", "@validationError": {"description": "رسالة خطأ عامة للتحقق"}, "securityError": "تم اكتشاف مشكلة أمنية", "@securityError": {"description": "رسالة خطأ أمنية عامة"}, "unknownError": "حد<PERSON> خطأ غير متوقع", "@unknownError": {"description": "رسالة خطأ غير معروف"}, "invalidImageFormat": "تنسيق الصورة غير صالح", "@invalidImageFormat": {"description": "رسالة خطأ لتنسيق ملف الصورة غير الصالح"}, "invalidDocumentFormat": "تنسيق المستند غير صالح", "@invalidDocumentFormat": {"description": "رسالة خطأ لتنسيق ملف المستند غير الصالح"}, "fileTooLarge": "الملف كبير جداً", "@fileTooLarge": {"description": "رسالة خطأ عندما يتجاوز حجم الملف الحد المسموح"}, "cart": "السلة", "@cart": {"description": "Shopping cart navigation label"}, "myGarage": "مرآبي", "@myGarage": {"description": "My Garage navigation label"}, "home": "الرئيسية", "@home": {"description": "Home page navigation label"}, "myCars": "سياراتي", "@myCars": {"description": "User's cars section label"}, "search": "ابحث...", "@search": {"description": "Placeholder text for search input fields"}, "inbox": "الوارد", "@inbox": {"description": "User's message inbox label"}, "selling": "البيع", "@selling": {"description": "Selling section label"}, "carNowMarketplace": "سوق CarNow", "@carNowMarketplace": {"description": "Marketplace title"}, "all": "الكل", "@all": {"description": "Filter for showing all items"}, "newItem": "جديد", "@newItem": {"description": "Filter for new items"}, "used": "مستعمل", "@used": {"description": "Filter for used items"}, "featured": "مميز", "@featured": {"description": "Filter for featured items"}, "nearby": "بالقرب", "@nearby": {"description": "Filter for items near user location"}, "specialOffer": "عرض خاص!", "@specialOffer": {"description": "Text shown for special promotional offers"}, "specialOfferDetails": "احصل على خصم 10% على طلبك الأول.", "@specialOfferDetails": {"description": "Details about the special offer"}, "learnMore": "اعرف المزيد", "@learnMore": {"description": "Button to learn more about an offer or feature"}, "add": "إضافة", "@add": {"description": "Add button label"}, "notifications": "اشعارات", "@notifications": {"description": "Notifications section label"}, "store": "متجر", "@store": {"description": "Store or marketplace section"}, "account": "حسابي", "@account": {"description": "User account section label"}, "shoppingActivity": "نشاط التسوق", "@shoppingActivity": {"description": "Header for user's shopping activity section"}, "trackYourOrders": "تتبع طلباتك", "@trackYourOrders": {"description": "Subtitle for My Orders option"}, "manageYourListings": "إدارة العناصر المعروضة للبيع", "@manageYourListings": {"description": "Subtitle for My Listings option"}, "viewBuyerSellerMessages": "عرض رسائل المشترين والبائعين", "@viewBuyerSellerMessages": {"description": "Subtitle for Messages option"}, "becomeASeller": "كن بائعًا", "becomeASellerSubtitle": "ابدأ في بيع قطع الغيار والإكسسوارات", "applyToSellOnCarNow": "قدم طلبًا للبيع على كارناو", "@applyToSellOnCarNow": {"description": "Subtitle for become a seller option"}, "settingsAndSupport": "الإعدادات والدعم", "@settingsAndSupport": {"description": "Header for settings and support section"}, "personalInformation": "المعلومات الشخصية", "@personalInformation": {"description": "Menu item for personal information screen"}, "editYourProfileDetails": "تعديل تفاصيل ملفك الشخصي", "@editYourProfileDetails": {"description": "Subtitle for personal information option"}, "accountSettings": "إعدادات الحساب", "@accountSettings": {"description": "Menu item for account settings screen"}, "getHelpAndContactSupport": "الحصول على المساعدة والتواصل مع الدعم", "@getHelpAndContactSupport": {"description": "Subtitle for Support option"}, "signOutFromYourAccount": "تسجيل الخروج من حسابك", "@signOutFromYourAccount": {"description": "Subtitle for Logout option"}, "signInPrompt": "سجل الدخول أو أنشئ حساباً لإدارة إعلاناتك", "@signInPrompt": {"description": "Prompt asking users to sign in"}, "register": "تسجيل جديد", "@register": {"description": "Register button label"}, "signIn": "تسجيل الدخول", "@signIn": {"description": "Sign in button label"}, "myOrders": "طلباتي", "@myOrders": {"description": "User's orders section label"}, "myListings": "قوائمي", "@myListings": {"description": "User's listings section label"}, "messages": "الرسائل", "@messages": {"description": "Messages section label"}, "sellerSection": "البائع", "@sellerSection": {"description": "Seller dashboard section label"}, "subscription": "الاشتراك", "@subscription": {"description": "Subscription management section label"}, "subscriptionPlans": "خطط الاشتراك", "@subscriptionPlans": {"description": "Subscription plans screen title"}, "chooseYourPlan": "ا<PERSON><PERSON><PERSON> خطتك", "@chooseYourPlan": {"description": "Choose your subscription plan heading"}, "monthly": "شهرياً", "@monthly": {"description": "Monthly billing cycle"}, "yearly": "سنوياً", "@yearly": {"description": "Yearly billing cycle"}, "month": "شهر", "@month": {"description": "Month unit"}, "year": "سنة", "@year": {"description": "Year unit"}, "continueButton": "متابعة", "@continueButton": {"description": "Continue button text"}, "selected": "<PERSON><PERSON><PERSON><PERSON>", "@selected": {"description": "Selected state text"}, "select": "اختيار", "@select": {"description": "Select button text"}, "sales": "المبيعات", "@sales": {"description": "Sales section label"}, "settings": "الإعدادات", "@settings": {"description": "Settings section label"}, "support": "الدعم", "@support": {"description": "Support section label"}, "logout": "تسجيل الخروج", "@logout": {"description": "Logout button label"}, "guestUser": "زائر", "@guestUser": {"description": "Guest user label"}, "signInToContinue": "سجل الدخول للمتابعة", "@signInToContinue": {"description": "Prompt to sign in to continue using the app"}, "editProfile": "تعديل الملف الشخصي", "@editProfile": {"description": "Edit profile button label"}, "supabaseTest": "اختبار Supabase", "@supabaseTest": {"description": "Supabase test feature label"}, "shoppingCart": "سلة التسوق", "@shoppingCart": {"description": "Shopping cart label"}, "clearCart": "إفراغ السلة", "@clearCart": {"description": "Clear cart button label"}, "clearCartConfirmationTitle": "تأ<PERSON>يد إفراغ السلة", "@clearCartConfirmationTitle": {"description": "Confirmation dialog title for clearing cart"}, "clearCartConfirmationBody": "هل أنت متأكد من رغبتك في إزالة كل العناصر من سلتك؟", "@clearCartConfirmationBody": {"description": "Confirmation dialog message for clearing cart"}, "cancel": "إلغاء", "@cancel": {"description": "Cancel button label"}, "clear": "إفراغ", "@clear": {"description": "Clear button label"}, "emptyCartMessage": "سلتك فارغة", "@emptyCartMessage": {"description": "Message shown when cart is empty"}, "myCarNow": "CarNow الخاص بي", "@myCarNow": {"description": "My CarNow page title"}, "welcomeToCarNow": "مرحباً بك في CarNow", "@welcomeToCarNow": {"description": "Welcome message for unauthenticated users"}, "signInToAccessAccount": "سجل دخولك للوصول إلى حسابك وميزاتك المحفوظة", "@signInToAccessAccount": {"description": "Sign in prompt message"}, "createAccount": "إنشاء حساب جديد", "@createAccount": {"description": "Create new account button"}, "yourUpdates": "التحديثات الخاصة بك", "@yourUpdates": {"description": "Your updates section title"}, "watchlist": "قائمة المراقبة", "@watchlist": {"description": "Watchlist feature"}, "favoritesAndLists": "المفضلة والقوائم", "@favoritesAndLists": {"description": "Favorites and lists subtitle"}, "bidsAndOffers": "العروض والمزايدات", "@bidsAndOffers": {"description": "Bids and offers feature"}, "activeAuctionsAndOffers": "المزادات النشطة وعروض البائعين", "@activeAuctionsAndOffers": {"description": "Active auctions and seller offers subtitle"}, "shopping": "التسوق", "@shopping": {"description": "Shopping section title"}, "followWatchedItems": "تابع العناصر المراقبة", "@followWatchedItems": {"description": "Follow watched items subtitle"}, "savedItems": "المحفوظات", "@savedItems": {"description": "Saved items feature"}, "searchesSellersFeeds": "عمليات البحث، البائعين، التغذية", "@searchesSellersFeeds": {"description": "Searches, sellers, feeds subtitle"}, "buyAgain": "اشتري مرة أخرى", "@buyAgain": {"description": "Buy again feature"}, "shopFromPreviousPurchases": "تسوق من مشترياتك السابقة", "@shopFromPreviousPurchases": {"description": "Shop from previous purchases subtitle"}, "purchases": "المشتريات", "@purchases": {"description": "Purchases feature"}, "orderHistory": "تاريخ الطلبات", "@orderHistory": {"description": "Order history subtitle"}, "recentlyViewed": "تمت مشاهدته مؤخراً", "@recentlyViewed": {"description": "Recently viewed feature"}, "itemsYouViewed": "العناصر التي شاهدتها", "@itemsYouViewed": {"description": "Items you viewed subtitle"}, "carNowGarage": "جر<PERSON><PERSON> CarNow", "@carNowGarage": {"description": "CarNow Garage feature"}, "addCarGetCompatibleParts": "أضف سيارتك واحصل على قطع غيار متوافقة", "@addCarGetCompatibleParts": {"description": "Add car and get compatible parts subtitle"}, "manageGarage": "إدارة الجراج", "@manageGarage": {"description": "Manage garage button"}, "memberSince": "عضو منذ", "@memberSince": {"description": "Member since label"}, "carNowUser": "عض<PERSON> جديد", "@carNowUser": {"description": "Default CarNow user name"}, "newMember": "عض<PERSON> جديد", "@newMember": {"description": "Label for new members who haven't completed their profile"}, "incompleteProfile": "الملف الشخصي غير مكتمل", "@incompleteProfile": {"description": "Label for users with incomplete profiles"}, "completeYourProfile": "أكمل ملفك الشخصي", "@completeYourProfile": {"description": "Prompt for users to complete their profile"}, "profileIncomplete": "الملف الشخصي غير مكتمل", "@profileIncomplete": {"description": "Status message for incomplete profiles"}, "fitsYourRideEveryTime": "يناسب مركبتك، في كل مرة", "@fitsYourRideEveryTime": {"description": "Guarantee message for parts compatibility"}, "rightPartsOrMoneyBack": "احصل على القطع المناسبة أو استرد أموالك.", "@rightPartsOrMoneyBack": {"description": "Money back guarantee message"}, "myVehicles": "مركباتي", "@myVehicles": {"description": "My vehicles section title"}, "addVehicle": "إضافة مركبة", "@addVehicle": {"description": "Add vehicle button"}, "localInstallationServices": "خدمات تركيب محلية", "@localInstallationServices": {"description": "Local installation services"}, "installPartsWithExperts": "اطلب تركيب قطعك الجديدة بواسطة شبكة خبرائنا.", "@installPartsWithExperts": {"description": "Install parts with experts subtitle"}, "browseServices": "تصفح الخدمات", "@browseServices": {"description": "Browse services button"}, "help": "مساعدة", "@help": {"description": "زر المساعدة"}, "garageSettings": "إعدادات الجراج", "@garageSettings": {"description": "Garage settings option"}, "share": "مشاركة", "@share": {"description": "Share option"}, "compatibilityGuarantee": "<PERSON><PERSON><PERSON> التوافق", "@compatibilityGuarantee": {"description": "Compatibility guarantee title"}, "compatibilityGuaranteeMessage": "نضمن أن القطع التي تشتريها ستناسب مركبتك. إذا لم تكن مناسبة، يمكنك إرجاعها واسترداد أموالك كاملة.", "@compatibilityGuaranteeMessage": {"description": "Compatibility guarantee explanation"}, "understood": "فهمت", "@understood": {"description": "Understood button"}, "sharingFeatureComingSoon": "ميزة المشاركة قريباً!", "@sharingFeatureComingSoon": {"description": "Sharing feature coming soon message"}, "garageIsEmpty": "جراجك فارغ", "@garageIsEmpty": {"description": "Empty garage message"}, "addVehiclesToFindParts": "أضف مركباتك للعثور على قطع غيار متوافقة", "@addVehiclesToFindParts": {"description": "Add vehicles to find parts message"}, "deleteVehicle": "<PERSON><PERSON><PERSON> المر<PERSON><PERSON>ة", "@deleteVehicle": {"description": "Delete vehicle dialog title"}, "confirmDeleteVehicle": "هل أنت متأكد من رغبتك في حذف هذه المركبة؟", "@confirmDeleteVehicle": {"description": "Confirm delete vehicle message"}, "delete": "<PERSON><PERSON><PERSON>", "@delete": {"description": "Delete button"}, "vehicleFormComingSoon": "نموذج المركبة سيتم تطبيقه في مهمة مستقبلية.", "@vehicleFormComingSoon": {"description": "Vehicle form coming soon message"}, "close": "إغلاق", "@close": {"description": "زر إغلاق"}, "emptyCartSubMessage": "تصفح المنتجات وأضف ما يعجبك!", "@emptyCartSubMessage": {"description": "Subtitle shown when cart is empty with suggestion"}, "shopNow": "تسوق الآن", "@shopNow": {"description": "Shop now button label"}, "removeFromCart": "إزالة من السلة", "@removeFromCart": {"description": "Remove from cart button label"}, "subtotal": "المجموع الفرعي", "@subtotal": {"description": "Subtotal label in cart"}, "items": "عناصر", "@items": {"description": "Items label in cart"}, "shipping": "الشحن", "@shipping": {"description": "Shipping cost label"}, "total": "المجموع الكلي", "@total": {"description": "Total cost label"}, "proceedToCheckout": "المتابعة للدفع", "@proceedToCheckout": {"description": "Proceed to checkout button label"}, "clearSearch": "<PERSON><PERSON><PERSON> البحث", "@clearSearch": {"description": "Tooltip for the clear search button"}, "shoppingActivitySection": "نشاط التسوق", "@shoppingActivitySection": {"description": "Shopping activity section header"}, "myOrdersSubtitle": "تتبع طلباتك", "@myOrdersSubtitle": {"description": "Subtitle for my orders section"}, "myListingsSubtitle": "إدارة العناصر المعروضة للبيع", "@myListingsSubtitle": {"description": "Subtitle for my listings section"}, "messagesSubtitle": "عرض رسائل المشترين والبائعين", "@messagesSubtitle": {"description": "Subtitle for messages section"}, "settingsSection": "الإعدادات والدعم", "@settingsSection": {"description": "Settings and support section header"}, "settingsSubtitle": "إدارة تفضيلات التطبيق", "@settingsSubtitle": {"description": "Subtitle for settings section"}, "supportSubtitle": "الحصول على المساعدة والتواصل مع الدعم", "@supportSubtitle": {"description": "Subtitle for support section"}, "sellerToolsSection": "أدوات البائع", "@sellerToolsSection": {"description": "Seller tools section header"}, "subscriptionSubtitle": "إدارة خطة اشتراكك", "@subscriptionSubtitle": {"description": "Subtitle for subscription section"}, "salesSubtitle": "عرض أداء مبيعاتك", "@salesSubtitle": {"description": "Subtitle for sales section"}, "noResultsFound": "لم يتم العثور على نتائج", "@noResultsFound": {"description": "النص المعروض عندما لا يسفر البحث عن نتائج"}, "save": "<PERSON><PERSON><PERSON>", "@save": {"description": "Button text to save changes"}, "sellerDashboard": "لوحة تحكم البائع", "@sellerDashboard": {"description": "Seller dashboard title"}, "sellerDashboardSubtitle": "إدارة المنتجات، المبيعات، والإعدادات", "@sellerDashboardSubtitle": {"description": "Seller dashboard subtitle"}, "sellerProducts": "المنتجات", "@sellerProducts": {"description": "Seller products section title"}, "sellerProductsSubtitle": "إدارة قوائم منتجاتك", "@sellerProductsSubtitle": {"description": "Seller products section subtitle"}, "sellerOrders": "الطلبات", "@sellerOrders": {"description": "Seller orders section title"}, "sellerOrdersSubtitle": "عرض وإدارة طلبات العملاء", "@sellerOrdersSubtitle": {"description": "Seller orders section subtitle"}, "sellerStore": "المتجر", "@sellerStore": {"description": "Seller store section title"}, "sellerSettings": "الإعدادات", "@sellerSettings": {"description": "Seller settings section title"}, "sellerAnalytics": "التحليلات", "@sellerAnalytics": {"description": "Seller analytics section title"}, "sellerPromotions": "العروض والخصومات", "@sellerPromotions": {"description": "Seller promotions section title"}, "sellerPromotionsSubtitle": "إنشاء وإدارة العروض الخاصة", "@sellerPromotionsSubtitle": {"description": "Seller promotions section subtitle"}, "sellerApprovalPending": "موافقة البائع معلقة", "@sellerApprovalPending": {"description": "Seller approval pending status message"}, "sellerApprovalPendingDesc": "حساب البائع الخاص بك قيد الموافقة من قبل فريقنا. ستتمكن من الوصول إلى أدوات البائع بمجرد الموافقة عليه.", "@sellerApprovalPendingDesc": {"description": "Seller approval pending description"}, "noSellerStore": "لم يتم العثور على متجر", "@noSellerStore": {"description": "No seller store found message"}, "noSellerStoreDesc": "تحتاج إلى إنشاء متجر قبل البدء في بيع المنتجات", "@noSellerStoreDesc": {"description": "Description shown when no seller store is found"}, "createStore": "إنشاء متجر", "@createStore": {"description": "Create store button label"}, "revenueSummary": "ملخص الإيرادات", "@revenueSummary": {"description": "Revenue summary section title"}, "todayRevenue": "اليوم", "@todayRevenue": {"description": "Today's revenue label"}, "weeklyRevenue": "هذا الأسبوع", "monthlyRevenue": "هذا الشهر", "totalRevenue": "إجمالي الإيرادات", "last7DaysRevenue": "إيرادات آخر 7 أيام", "pendingOrders": "قيد الانتظار", "completedOrders": "مكتملة", "totalSales": "إجمالي المبيعات", "addProduct": "إضافة منتج", "manageProducts": "إدارة المنتجات", "createPromotion": "إنشاء عرض", "managePromos": "إدارة العروض", "viewOrders": "عرض الطلبات", "storeSettings": "إعدادات المتجر", "recentOrders": "الطلبات الأخيرة", "topSellingProducts": "المنتجات الأكثر مبيعًا", "verifiedStore": "متجر موثق", "editStore": "تعديل المتجر", "viewMore": "عر<PERSON> المزيد", "searchProducts": "البحث عن منتجات...", "active": "نشط", "inactive": "غير نشط", "pending": "قيد الانتظار", "rejected": "مرفو<PERSON>", "filterProducts": "تصفية المنتجات", "tapAddToCreateProduct": "اضغط على زر الإضافة لإنشاء أول منتج لك", "noProductsMatchingFilter": "لا توجد منتجات تطابق التصفية", "clearFilters": "<PERSON><PERSON><PERSON> الفلا<PERSON>ر", "productHiddenSuccess": "تم إخفاء المنتج", "productVisibleSuccess": "المنتج مرئي الآن", "edit": "تعديل", "show": "إظهار", "hide": "إخفاء", "unknown": "غير معروف", "nameLabel": "الاسم", "nameRequired": "الرجاء إدخال الاسم", "soldCount": "تم بيع {count}", "@soldCount": {"placeholders": {"count": {"type": "int"}}}, "promotions": "العروض والخصومات", "@promotions": {"description": "Promotions and offers section label"}, "noStoreFound": "لم يتم العثور على متجر", "noActivePromotions": "لا توجد عروض نشطة", "showAllPromotions": "عرض جميع العروض", "showActiveOnly": "عرض النشطة فقط", "noPromotionsYet": "لا توجد عروض حتى الآن", "createPromotionDesc": "إنشاء عروض وخصومات خاصة لعملائك", "deletePromotion": "<PERSON><PERSON><PERSON> العرض", "deletePromotionConfirm": "هل أنت متأكد من رغبتك في حذف هذا العرض؟ لا يمكن التراجع عن هذا الإجراء.", "promotionDeleted": "تم حذف العرض بنجاح", "expired": "منتهي", "scheduled": "مجدول", "offDiscount": "خصم", "discountValue": "خصم", "products": "منتجات", "anonymous": "مجهول", "phoneLabel": "رقم الهاتف", "@phoneLabel": {"description": "تسمية حقل رقم الهاتف"}, "phoneInvalid": "الرجاء إدخال رقم هاتف صالح", "@phoneInvalid": {"description": "رسالة خطأ لرقم هاتف غير صالح"}, "addressLabel": "العنوان", "@addressLabel": {"description": "تسمية حقل العنوان"}, "addressRequired": "الرجاء إدخال عنوانك", "@addressRequired": {"description": "رسالة خطأ لحقل العنوان الفارغ"}, "saveChanges": "حفظ التغييرات", "@saveChanges": {"description": "نص زر حفظ التغييرات"}, "discardChanges": "تجاهل التغييرات", "@discardChanges": {"description": "نص زر تجاهل التغييرات"}, "changesSaved": "تم حفظ التغييرات بنجاح", "@changesSaved": {"description": "رسالة نجاح عند حفظ التغييرات"}, "errorSavingChanges": "حد<PERSON> خطأ أثناء حفظ التغييرات. الرجاء المحاولة مرة أخرى.", "@errorSavingChanges": {"description": "رسالة خطأ عند فشل حفظ التغييرات"}, "profileUpdated": "تم تحديث الملف الشخصي بنجاح", "@profileUpdated": {"description": "رسالة نجاح عند تحديث الملف الشخصي"}, "profileUpdateFailed": "فشل تحديث الملف الشخصي", "@profileUpdateFailed": {"description": "رسالة خطأ عند فشل تحديث الملف الشخصي"}, "phoneHint": "أدخل رقم هاتفك", "@phoneHint": {"description": "نص تلميحي لحقل رقم الهاتف"}, "addressHint": "أد<PERSON>ل عنوانك", "@addressHint": {"description": "نص تلميحي لحقل العنوان"}, "resetForm": "إعادة تعيين النموذج", "@resetForm": {"description": "نص زر إعادة تعيين النموذج"}, "confirmDiscardTitle": "تجاهل التغييرات؟", "@confirmDiscardTitle": {"description": "عنوان نافذة تأكيد تجاهل التغييرات"}, "confirmDiscardMessage": "هل أنت متأكد من رغبتك في تجاهل التغييرات؟", "@confirmDiscardMessage": {"description": "رسالة تأكيد تجاهل التغييرات"}, "yes": "نعم", "@yes": {"description": "نص زر التأكيد"}, "no": "لا", "@no": {"description": "نص زر الإلغاء"}, "myProducts": "منتجاتي", "@myProducts": {"description": "My products section title"}, "editProduct": "تعديل المنتج", "@editProduct": {"description": "Edit product button label"}, "productName": "اسم المنتج", "@productName": {"description": "Product name field label"}, "price": "السعر", "@price": {"description": "Price field label"}, "condition": "الحالة", "@condition": {"description": "Condition filter"}, "description": "الوصف", "@description": {"description": "Product description field label"}, "productImages": "صور المنتج", "@productImages": {"description": "Product images section label"}, "gallery": "المعرض", "@gallery": {"description": "Gallery option for selecting images"}, "camera": "الكاميرا", "@camera": {"description": "Camera option for capturing images"}, "noImagesSelected": "لم يتم اختيار أي صور", "@noImagesSelected": {"description": "Error message when no images are selected"}, "imagesSelected": "تم اختيار الصور", "@imagesSelected": {"description": "Message showing images have been selected"}, "updateProduct": "تحديث المنتج", "@updateProduct": {"description": "Update product button label"}, "deleteProduct": "<PERSON><PERSON><PERSON> المنتج", "@deleteProduct": {"description": "Delete product button label"}, "confirmDeleteProduct": "هل أنت متأكد من رغبتك في حذف هذا المنتج؟", "@confirmDeleteProduct": {"description": "Confirmation message for deleting product"}, "productDeleted": "تم حذف المنتج بنجاح", "@productDeleted": {"description": "Success message for product deletion"}, "productAdded": "تمت إضافة المنتج بنجاح", "@productAdded": {"description": "Success message for adding product"}, "productUpdated": "تم تحديث المنتج بنجاح", "@productUpdated": {"description": "Success message for updating product"}, "errorDeletingProduct": "حد<PERSON> خطأ أثناء حذف المنتج", "@errorDeletingProduct": {"description": "Error message for product deletion"}, "errorAddingProduct": "حد<PERSON> خطأ أثناء إضافة المنتج", "@errorAddingProduct": {"description": "Error message for adding product"}, "errorUpdatingProduct": "حد<PERSON> خطأ أثناء تحديث المنتج", "@errorUpdatingProduct": {"description": "Error message for updating product"}, "errorLoadingProducts": "خطأ في تحميل المنتجات", "@errorLoadingProducts": {"description": "رسالة خطأ تظهر عند فشل تحميل المنتجات"}, "invalidPrice": "الرجاء إدخال سعر صالح", "@invalidPrice": {"description": "Error message for invalid price"}, "fieldRequired": "هذا الحقل مطلوب", "@fieldRequired": {"description": "Error message for required fields"}, "noProductsFound": "لم يتم العثور على منتجات", "@noProductsFound": {"description": "Message shown when no products are found"}, "featuredProducts": "منتجات مميزة", "@featuredProducts": {"description": "علامة قسم المنتجات المميزة"}, "featuredCategories": "الفئات المميزة", "@featuredCategories": {"description": "علامة قسم الفئات المميزة"}, "totalProducts": "إجمالي المنتجات", "@totalProducts": {"description": "Label for total products count"}, "totalViews": "إجمالي المشاهدات", "@totalViews": {"description": "Label for total product views"}, "activeListings": "القوائم النشطة", "@activeListings": {"description": "Label for active product listings"}, "pendingListings": "القوائم المعلقة", "@pendingListings": {"description": "Label for pending product listings"}, "orders": "الطلبات", "@orders": {"description": "Label for orders section"}, "analytics": "التحليلات", "@analytics": {"description": "Label for analytics section"}, "quickActions": "إجراءات سريعة", "@quickActions": {"description": "Title for quick actions section"}, "recentActivity": "النشاط الأخير", "@recentActivity": {"description": "Title for recent activity section"}, "viewAll": "عر<PERSON> الكل", "@viewAll": {"description": "Label for view all button"}, "productStatus": "حالة المنتج", "@productStatus": {"description": "Label for product status"}, "editStatus": "تعديل الحالة", "@editStatus": {"description": "Label for edit status button"}, "bulkActions": "إجراءات متعددة", "@bulkActions": {"description": "Label for bulk actions button"}, "selectAll": "تحدي<PERSON> الكل", "@selectAll": {"description": "Label for select all checkbox"}, "selectedItems": "العناصر المحددة: {count}", "@selectedItems": {"description": "Label for selected items count", "placeholders": {"count": {"type": "int", "example": "5"}}}, "bulkDelete": "<PERSON><PERSON><PERSON> متعدد", "@bulkDelete": {"description": "Label for bulk delete button"}, "bulkChangeStatus": "تغيير الحالة", "@bulkChangeStatus": {"description": "Label for bulk change status button"}, "confirmBulkDelete": "هل أنت متأكد من حذف {count} منتجات؟", "@confirmBulkDelete": {"description": "Confirmation message for bulk delete", "placeholders": {"count": {"type": "int", "example": "5"}}}, "noActivity": "لا يوجد نشاط حديث", "@noActivity": {"description": "Message shown when there is no recent activity"}, "productViewed": "تمت مشاهدة {productName}", "@productViewed": {"description": "Message for product view activity", "placeholders": {"productName": {"type": "String", "example": "iPhone 12"}}}, "newMessage": "رسالة جديدة حول {productName}", "@newMessage": {"description": "Message for new inquiry activity", "placeholders": {"productName": {"type": "String", "example": "iPhone 12"}}}, "duplicateProduct": "نسخ المنتج", "@duplicateProduct": {"description": "Label for duplicate product action"}, "errorLoadingDashboard": "خطأ في تحميل لوحة التحكم", "@errorLoadingDashboard": {"description": "Error message when dashboard fails to load"}, "typeAMessage": "اكتب رسالة...", "@typeAMessage": {"description": "Placeholder for message input field"}, "newConversation": "محاد<PERSON>ة جديدة", "@newConversation": {"description": "Title for creating a new conversation"}, "searchUsers": "البحث عن المستخدمين...", "@searchUsers": {"description": "Placeholder for searching users"}, "createGroupChat": "إنشاء محادثة جماعية", "@createGroupChat": {"description": "Switch label for creating a group chat"}, "groupChatTitle": "عنوان المجموعة", "@groupChatTitle": {"description": "Label for group chat title input"}, "noUsersFound": "لم يتم العثور على مستخدمين", "@noUsersFound": {"description": "Message when no users are found in search"}, "noConversationsYet": "لا توجد محادثات حتى الآن", "@noConversationsYet": {"description": "Message shown when user has no conversations"}, "startNewConversation": "بدء محادثة جديدة", "@startNewConversation": {"description": "Prompt to start a new conversation"}, "noMessagesYet": "لا توجد رسائل حتى الآن", "@noMessagesYet": {"description": "Message shown when conversation has no messages"}, "sendFirstMessage": "أرسل رسالة لبدء المحادثة", "@sendFirstMessage": {"description": "Prompt to send first message"}, "retry": "إعادة المحاولة", "@retry": {"description": "Button text to retry sending a failed message"}, "conversationDetails": "تفاصيل المحادثة", "@conversationDetails": {"description": "Title for conversation details screen"}, "viewProfile": "عرض الملف الشخصي", "@viewProfile": {"description": "Option to view user profile"}, "deleteConversation": "<PERSON><PERSON><PERSON> المحادثة", "@deleteConversation": {"description": "Option to delete a conversation"}, "participants": "المشاركون", "@participants": {"description": "Label for conversation participants"}, "chatMessagesSubtitle": "الدردشة مع البائعين والدعم", "@chatMessagesSubtitle": {"description": "عنوان فرعي لقسم رسائل الدردشة"}, "loading": "جاري التحميل", "@loading": {"description": "رسالة تظهر أثناء تحميل المحتوى"}, "manageSalesCategories": "إدارة فئات المبيعات", "@manageSalesCategories": {"description": "عنوان شاشة إدارة فئات المبيعات"}, "selectCategoriesDescription": "اختر الفئات التي ترغب في بيع منتجاتك فيها.", "@selectCategoriesDescription": {"description": "وصف لشاشة اختيار الفئات"}, "categoriesSaved": "تم حفظ الفئات بنجاح", "@categoriesSaved": {"description": "رسالة نجاح حفظ الفئات"}, "categoriesHelp": "مساعدة الفئات", "@categoriesHelp": {"description": "عنوان نافذة مساعدة الفئات"}, "categoriesHelpText": "يمكنك اختيار الفئات التي ترغب في بيع منتجاتك فيها. سيؤدي ذلك إلى ظهور منتجاتك في هذه الفئات عند عرضها للمشترين.", "@categoriesHelpText": {"description": "نص مساعدة الفئات"}, "contactUs": "اتصل بنا", "@contactUs": {"description": "عنوان قسم اتصل بنا"}, "emailSupport": "الدعم عبر البريد الإلكتروني", "@emailSupport": {"description": "عنوان الدعم عبر البريد الإلكتروني"}, "phoneSupport": "الدعم عبر الهاتف", "@phoneSupport": {"description": "عنوان الدعم عبر الهاتف"}, "faq": "الأسئلة الشائعة", "@faq": {"description": "عنوان قسم الأسئلة الشائعة"}, "noFaqsAvailable": "لا توجد أسئلة شائعة متاحة في الوقت الحالي.", "@noFaqsAvailable": {"description": "رسالة عند عدم توفر أسئلة شائعة"}, "liveChat": "محادثة مباشرة", "@liveChat": {"description": "عنوان زر المحادثة المباشرة"}, "noSuggestionsFound": "لم يتم العثور على اقتراحات", "@noSuggestionsFound": {"description": "رسالة عدم وجود اقتراحات بحث"}, "startTypingToSearch": "ابدأ الكتابة للبحث...", "@startTypingToSearch": {"description": "نص الحث على بدء البحث"}, "error": "<PERSON><PERSON><PERSON>", "@error": {"description": "رسالة خطأ عامة"}, "recentSearches": "عمليات البحث الأخيرة", "@recentSearches": {"description": "عنوان قسم عمليات البحث الأخيرة"}, "noProductsYet": "لا توجد منتجات متوفرة بعد", "@noProductsYet": {"description": "رسالة عدم وجود منتجات"}, "savedSearches": "عمليات البحث المحفوظة", "@savedSearches": {"description": "تسمية لعلامة تبويب عمليات البحث المحفوظة"}, "noResultsFoundFor": "لم يتم العثور على نتائج لـ '{query}'", "@noResultsFoundFor": {"description": "الرسالة التي تظهر عند عدم العثور على نتائج بحث لاستعلام معين", "placeholders": {"query": {"type": "String", "example": "كلمات البحث"}}}, "errorOccurred": "<PERSON><PERSON><PERSON>", "@errorOccurred": {"description": "رسالة خطأ عامة"}, "noSavedItems": "لم تقم بحفظ أي عناصر حتى الآن.", "@noSavedItems": {"description": "الرسالة التي تظهر عند عدم وجود عناصر محفوظة"}, "networkError": "خطأ في الاتصال بالإنترنت", "@networkError": {"description": "رسالة خطأ الشبكة"}, "serverError": "خطأ في الخادم", "@serverError": {"description": "رسالة خطأ الخادم"}, "timeoutError": "انتهت مهلة الاتصال", "@timeoutError": {"description": "رسالة خطأ انتهاء المهلة"}, "invalidCredentials": "بيانات تسجيل الدخول غير صحيحة", "@invalidCredentials": {"description": "رسالة خطأ بيانات الدخول"}, "emailNotConfirmed": "يرجى تأكيد البريد الإلكتروني", "@emailNotConfirmed": {"description": "رسالة عدم تأكيد البريد الإلكتروني"}, "userNotFound": "المستخدم غير موجود", "@userNotFound": {"description": "رسالة عدم وجود المستخدم"}, "weakPassword": "كلمة المرور ضعيفة", "@weakPassword": {"description": "رسالة كلمة مرور ضعيفة"}, "emailAlreadyRegistered": "البريد الإلكتروني مسجل مسبقاً", "@emailAlreadyRegistered": {"description": "رسالة البريد الإلكتروني المسجل مسبقاً"}, "authError": "خطأ في المصادقة", "@authError": {"description": "رسالة خطأ المصادقة"}, "duplicateEntry": "البيانات موجودة مسبقاً", "@duplicateEntry": {"description": "رسالة البيانات المكررة"}, "foreignKeyViolation": "لا يمكن حذف هذا العنصر", "@foreignKeyViolation": {"description": "رسالة خطأ المفتاح الخارجي"}, "insufficientPermissions": "ليس لديك صلاحية لهذا الإجراء", "@insufficientPermissions": {"description": "رسالة عدم وجود صلاحيات"}, "databaseError": "خطأ في قاعدة البيانات", "@databaseError": {"description": "رسالة خطأ قاعدة البيانات"}, "completeProfile": "إكمال البيانات الشخصية", "welcomeCompleteProfile": "مرحباً! نحتاج لبعض البيانات الإضافية", "completeProfileDescription": "لتحسين تجربتك في التطبيق، يرجى إكمال بياناتك الشخصية أدناه.", "phoneRequired": "رقم الها<PERSON><PERSON> مطلوب", "locationRequired": "الموقع مطلوب", "profileCompletionNote": "ستساعدنا هذه البيانات في تقديم خدمة أفضل لك.", "createAccountButton": "إنشاء حساب جديد", "loginRequiredTitle": "التسجيل مطلوب", "loginRequiredHeading": "ميزة حصرية للأعضاء", "loginRequiredMessage": "للاستفادة من هذه الميزة والوصول الكامل لخدماتنا، يرجى تسجيل الدخول أو إنشاء حساب جديد.", "completeProfileTitle": "إكمال البيانات الشخصية", "completeProfileHeading": "خطوة أخيرة قبل المتابعة!", "completeProfileMessage": "نحتاج بعض المعلومات الإضافية في ملفك الشخصي (مثل الاسم ورقم الهاتف) لتتمكن من إتمام عملية الشراء بنجاح.", "completeProfileButton": "الانتقال لإكمال البيانات", "cancelButton": "إلغاء", "logoutConfirmation": "هل أنت متأكد أنك تريد تسجيل الخروج؟", "fullName": "الاسم الكامل", "enterFullName": "أد<PERSON>ل اسمك الكامل", "phoneNumber": "رقم الهاتف", "enterPhoneNumber": "أدخل رقم هاتفك", "location": "الموقع", "enterLocation": "أد<PERSON>ل مدينتك أو منطقتك", "saveAndContinue": "حفظ ومتابعة", "nameCannotBeEmpty": "لا يمكن ترك الاسم فارغًا.", "nameTooShort": "يجب أن يتكون الاسم من حرفين على الأقل.", "phoneCannotBeEmpty": "لا يمكن ترك رقم الهاتف فارغًا.", "invalidPhoneNumber": "صيغة رقم الهاتف غير صالحة.", "locationCannotBeEmpty": "لا يمكن ترك الموقع فارغًا.", "locationTooShort": "يجب أن يتكون الموقع من 3 أحرف على الأقل.", "removeImage": "إزالة الصورة", "profileUpdatedSuccessfully": "تم تحديث الملف الشخصي بنجاح.", "storageError": "خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى.", "errorLoadingProfile": "خطأ في تحميل الملف الشخصي.", "completeProfileWelcome": "أهلاً بك! نحتاج بعض المعلومات الإضافية", "loginButton": "تسجيل الدخول", "supportTicketUpdateSuccess": "تم تحديث تذكرة الدعم بنجاح.", "supportTicketUpdateError": "فشل تحديث تذكرة الدعم.", "searchHint": "ابحث عن قطع غيار، فئات...", "partNamesLanguage": "لغة أسماء القطع", "partNamesLanguageDesc": "اختر اللغة لعرض أسماء قطع غيار السيارات.", "selectAutomotiveTypeTitle": "اختر نوع المركبة", "whatAreYouListing": "ماذا تريد أن تبيع؟", "selectCategoryToContinue": "اختر فئة للمتابعة مع إدراجك", "vehicleSelectionTitle": "مركبة", "vehicleSelectionSubtitle": "أدرج سيارات، دراجات نارية، شاحنات، ومركبات أخرى", "autoPartSelectionTitle": "قطع غيار", "autoPartSelectionSubtitle": "أدر<PERSON> قطع غيار، إكسسوارات، ومكونات", "developerTools": "أدوات المطور", "@developerTools": {"description": "Developer tools screen title"}, "analyticsDashboard": "لوحة التحليلات", "@analyticsDashboard": {"description": "Analytics dashboard"}, "salesReports": "تقارير المبيعات", "@salesReports": {"description": "Sales reports"}, "inventoryManagement": "إدارة المخزون", "@inventoryManagement": {"description": "Inventory management"}, "advancedDashboard": "لوحة التحكم المتقدمة", "@advancedDashboard": {"description": "Advanced dashboard"}, "storesListing": "قائمة المتاجر", "@storesListing": {"description": "Stores listing"}, "warrantyList": "قائمة الضمانات", "@warrantyList": {"description": "Warranty list"}, "archivedChats": "المحادثات المؤرشفة", "@archivedChats": {"description": "Archived chats"}, "chatStorage": "تخزين المحادثات", "@chatStorage": {"description": "Chat storage"}, "conversations": "المحادثات", "@conversations": {"description": "Conversations"}, "enhancedSearch": "البحث المحسّن", "@enhancedSearch": {"description": "Enhanced search"}, "compatiblePartsTest": "اختبار القطع المتوافقة", "@compatiblePartsTest": {"description": "Compatible parts test"}, "adminSubscriptionRequests": "طلبات الاشتراك الإدارية", "@adminSubscriptionRequests": {"description": "Admin subscription requests"}, "devToolsDebugOnly": "أدوات المطور متاحة فقط في وضع التطوير", "@devToolsDebugOnly": {"description": "Message shown when developer tools are accessed in non-debug mode"}, "smartSearch": "البحث الذكي", "@smartSearch": {"description": "Smart search feature title"}, "hybridSearch": "البحث المختلط", "@hybridSearch": {"description": "Hybrid search type"}, "semanticSearch": "البح<PERSON> الدلالي", "@semanticSearch": {"description": "Semantic search type"}, "clearHistory": "م<PERSON><PERSON> التاريخ", "@clearHistory": {"description": "Clear search history"}, "startSearching": "اب<PERSON><PERSON> البحث", "@startSearching": {"description": "Start searching message"}, "searchDescription": "استخدم البحث الذكي للعثور على ما تريد بسرعة", "@searchDescription": {"description": "Search description text"}, "searchSuggestions": "اقتراحات البحث", "@searchSuggestions": {"description": "Search suggestions section"}, "searchHistory": "تاريخ البحث", "@searchHistory": {"description": "Search history section"}, "clearSearchHistory": "مسح تاريخ البحث", "@clearSearchHistory": {"description": "Clear search history button"}, "noSearchHistory": "لا يوجد تاريخ بحث", "@noSearchHistory": {"description": "No search history message"}, "searchFailed": "فشل البحث", "@searchFailed": {"description": "Search failed message"}, "searchEmpty": "لا توجد نتائج", "@searchEmpty": {"description": "No search results message"}, "searchEmptyMessage": "لم يتم العثور على نتائج. حاول كلمات مختلفة.", "@searchEmptyMessage": {"description": "Empty search results detailed message"}, "searchProcessingTime": "وقت المعالجة: {time}ms", "@searchProcessingTime": {"description": "Search processing time"}, "searchResultsCount": "{count} نتيجة", "@searchResultsCount": {"description": "Search results count"}, "searchInsights": "رؤى البحث", "@searchInsights": {"description": "Search insights section"}, "searchCategoryDistribution": "توزيع الفئات", "@searchCategoryDistribution": {"description": "Category distribution in search"}, "searchPopularFilters": "فلاتر شائعة", "@searchPopularFilters": {"description": "Popular filters in search"}, "searchPriceRange": "نطاق السعر", "@searchPriceRange": {"description": "Price range in search"}, "searchMinPrice": "السعر الأدنى", "@searchMinPrice": {"description": "Minimum price in search"}, "searchMaxPrice": "السعر الأعلى", "@searchMaxPrice": {"description": "Maximum price in search"}, "searchAveragePrice": "متوسط السعر", "@searchAveragePrice": {"description": "Average price in search"}, "searchCars": "بحث السيارات", "@searchCars": {"description": "Search cars section"}, "searchAutoParts": "بحث قطع الغيار", "@searchAutoParts": {"description": "Search auto parts section"}, "carBrand": "علامة السيارة", "@carBrand": {"description": "Car brand filter"}, "carModel": "موديل السيارة", "@carModel": {"description": "Car model filter"}, "carYear": "سنة الصنع", "@carYear": {"description": "Car year filter"}, "partType": "نوع القطعة", "@partType": {"description": "Part type filter"}, "compatibility": "التوافق", "@compatibility": {"description": "Compatibility filter"}, "mileage": "المسافة المقطوعة", "@mileage": {"description": "Mileage filter"}, "fuelType": "نوع الوقود", "@fuelType": {"description": "Fuel type filter"}, "transmission": "ناقل الحركة", "@transmission": {"description": "Transmission filter"}, "warranty": "الضمان", "@warranty": {"description": "Warranty filter"}, "oemPart": "قطعة أصلية", "@oemPart": {"description": "OEM part filter"}, "sedans": "سيدان", "@sedans": {"description": "Sedans car type"}, "suvs": "SUV", "@suvs": {"description": "SUVs car type"}, "hatchbacks": "هاتشباك", "@hatchbacks": {"description": "Hatchbacks car type"}, "pickupTrucks": "بيك أب", "@pickupTrucks": {"description": "Pickup trucks car type"}, "coupes": "كوبيه", "@coupes": {"description": "Coupes car type"}, "convertibles": "قابلة للتحويل", "@convertibles": {"description": "Convertibles car type"}, "motorcycles": "دراجات نارية", "@motorcycles": {"description": "Motorcycles vehicle type"}, "engineParts": "أجزاء المحرك", "@engineParts": {"description": "Engine parts category"}, "brakeParts": "أجزاء الفرامل", "@brakeParts": {"description": "Brake parts category"}, "electricalParts": "أجزاء كهربائية", "@electricalParts": {"description": "Electrical parts category"}, "bodyParts": "أجزاء الهيكل", "@bodyParts": {"description": "Body parts category"}, "transmissionParts": "أجزاء ناقل الحركة", "@transmissionParts": {"description": "Transmission parts category"}, "suspensionParts": "أجزاء التعليق", "@suspensionParts": {"description": "Suspension parts category"}, "coolingParts": "أجزاء التبريد", "@coolingParts": {"description": "Cooling parts category"}, "exhaustParts": "أجزاء العادم", "@exhaustParts": {"description": "Exhaust parts category"}, "smartFilters": "فلاتر ذكية", "@smartFilters": {"description": "Smart filters section"}, "activeFilters": "الفلاتر النشطة", "@activeFilters": {"description": "Active filters section"}, "@clearFilters": {"description": "Clear filters button"}, "applyFilters": "تطبيق الفلاتر", "@applyFilters": {"description": "Apply filters button"}, "quickSuggestions": "اقتراحات سريعة", "@quickSuggestions": {"description": "Quick suggestions section"}, "searchByCategory": "البح<PERSON> حسب الفئة", "@searchByCategory": {"description": "Search by category"}, "aiPoweredSearch": "البحث بالذكاء الاصطناعي", "@aiPoweredSearch": {"description": "AI-powered search feature"}, "intelligentSearch": "البحث الذكي", "@intelligentSearch": {"description": "Intelligent search feature"}}