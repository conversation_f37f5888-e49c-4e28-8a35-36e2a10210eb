import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
  ];

  /// The general title of the application
  ///
  /// In en, this message translates to:
  /// **'CarNow - Car Parts Marketplace'**
  String get appTitle;

  /// Welcome message for guest users
  ///
  /// In en, this message translates to:
  /// **'Welcome, Guest'**
  String get welcomeGuest;

  /// Subtitle for guest users encouraging them to sign up
  ///
  /// In en, this message translates to:
  /// **'Sign in or create an account to get the full experience.'**
  String get guestSubtitle;

  /// Button text for login or register
  ///
  /// In en, this message translates to:
  /// **'Login / Register'**
  String get loginOrRegister;

  /// Categories section label
  ///
  /// In en, this message translates to:
  /// **'Categories'**
  String get categories;

  /// All categories section label
  ///
  /// In en, this message translates to:
  /// **'All Categories'**
  String get allCategories;

  /// Shop by category section label
  ///
  /// In en, this message translates to:
  /// **'Shop by Category'**
  String get shopByCategory;

  /// Featured categories section label
  ///
  /// In en, this message translates to:
  /// **'Featured Categories'**
  String get featuredCategories;

  /// Featured products section label
  ///
  /// In en, this message translates to:
  /// **'Featured Products'**
  String get featuredProducts;

  /// Message shown when no categories are available
  ///
  /// In en, this message translates to:
  /// **'No categories available'**
  String get noCategoriesAvailable;

  /// Error message shown when categories fail to load
  ///
  /// In en, this message translates to:
  /// **'Error loading categories'**
  String get errorLoadingCategories;

  /// Error message shown when products fail to load
  ///
  /// In en, this message translates to:
  /// **'Failed to load products'**
  String get failedToLoadProducts;

  /// Error message when input exceeds maximum length
  ///
  /// In en, this message translates to:
  /// **'Input is too long'**
  String get inputTooLong;

  /// Error message when dangerous content is detected
  ///
  /// In en, this message translates to:
  /// **'Invalid input detected'**
  String get invalidInput;

  /// Warning message for security issues
  ///
  /// In en, this message translates to:
  /// **'Potentially unsafe content detected'**
  String get securityWarning;

  /// Error message for invalid name characters
  ///
  /// In en, this message translates to:
  /// **'Name contains invalid characters'**
  String get invalidName;

  /// Error message when phone number is too short
  ///
  /// In en, this message translates to:
  /// **'Phone number is too short'**
  String get phoneTooShort;

  /// Error message when phone number is too long
  ///
  /// In en, this message translates to:
  /// **'Phone number is too long'**
  String get phoneTooLong;

  /// Error message when password exceeds maximum length
  ///
  /// In en, this message translates to:
  /// **'Password is too long'**
  String get passwordTooLong;

  /// Error message when password lacks uppercase letters
  ///
  /// In en, this message translates to:
  /// **'Password must contain at least one uppercase letter'**
  String get passwordNeedsUppercase;

  /// Error message when password lacks lowercase letters
  ///
  /// In en, this message translates to:
  /// **'Password must contain at least one lowercase letter'**
  String get passwordNeedsLowercase;

  /// Error message when password lacks numbers
  ///
  /// In en, this message translates to:
  /// **'Password must contain at least one number'**
  String get passwordNeedsNumber;

  /// Error message when password lacks special characters
  ///
  /// In en, this message translates to:
  /// **'Password must contain at least one special character'**
  String get passwordNeedsSpecialChar;

  /// Error message when password is commonly used
  ///
  /// In en, this message translates to:
  /// **'Password is too common'**
  String get passwordTooCommon;

  /// Error message when password confirmation doesn't match
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordsDoNotMatch;

  /// Error message for invalid monetary amounts
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid amount'**
  String get invalidAmount;

  /// Error message when amount is not positive
  ///
  /// In en, this message translates to:
  /// **'Amount must be positive'**
  String get amountMustBePositive;

  /// Error message when amount is below minimum
  ///
  /// In en, this message translates to:
  /// **'Amount is too small'**
  String get amountTooSmall;

  /// Error message when amount exceeds maximum
  ///
  /// In en, this message translates to:
  /// **'Amount is too large'**
  String get amountTooLarge;

  /// Error message when account number is too short
  ///
  /// In en, this message translates to:
  /// **'Account number is too short'**
  String get accountNumberTooShort;

  /// Error message when account number is too long
  ///
  /// In en, this message translates to:
  /// **'Account number is too long'**
  String get accountNumberTooLong;

  /// Error message for invalid account number format
  ///
  /// In en, this message translates to:
  /// **'Invalid account number format'**
  String get invalidAccountNumber;

  /// Error message for invalid VIN format
  ///
  /// In en, this message translates to:
  /// **'Invalid VIN format'**
  String get invalidVIN;

  /// Error message for invalid postal code format
  ///
  /// In en, this message translates to:
  /// **'Invalid postal code format'**
  String get invalidPostalCode;

  /// Error message for invalid URL format
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid URL'**
  String get invalidURL;

  /// Password strength indicator for no password
  ///
  /// In en, this message translates to:
  /// **'No password'**
  String get passwordStrengthNone;

  /// Password strength indicator for weak passwords
  ///
  /// In en, this message translates to:
  /// **'Weak'**
  String get passwordStrengthWeak;

  /// Password strength indicator for medium passwords
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get passwordStrengthMedium;

  /// Password strength indicator for strong passwords
  ///
  /// In en, this message translates to:
  /// **'Strong'**
  String get passwordStrengthStrong;

  /// Password strength indicator for very strong passwords
  ///
  /// In en, this message translates to:
  /// **'Very Strong'**
  String get passwordStrengthVeryStrong;

  /// General validation error message
  ///
  /// In en, this message translates to:
  /// **'Validation error occurred'**
  String get validationError;

  /// General security error message
  ///
  /// In en, this message translates to:
  /// **'Security issue detected'**
  String get securityError;

  /// Unknown error message
  ///
  /// In en, this message translates to:
  /// **'An unexpected error occurred'**
  String get unknownError;

  /// Error message for invalid image file format
  ///
  /// In en, this message translates to:
  /// **'Invalid image format'**
  String get invalidImageFormat;

  /// Error message for invalid document file format
  ///
  /// In en, this message translates to:
  /// **'Invalid document format'**
  String get invalidDocumentFormat;

  /// Error message when file size exceeds limit
  ///
  /// In en, this message translates to:
  /// **'File is too large'**
  String get fileTooLarge;

  /// Message shown when an action requires user to be signed in
  ///
  /// In en, this message translates to:
  /// **'You must sign in first'**
  String get mustSignInFirst;

  /// Label indicating a product is not available
  ///
  /// In en, this message translates to:
  /// **'Out of Stock'**
  String get outOfStock;

  /// Shopping cart navigation label
  ///
  /// In en, this message translates to:
  /// **'Cart'**
  String get cart;

  /// My Garage navigation label
  ///
  /// In en, this message translates to:
  /// **'My Garage'**
  String get myGarage;

  /// Home page navigation label
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// User's cars section label
  ///
  /// In en, this message translates to:
  /// **'My Cars'**
  String get myCars;

  /// Placeholder text for search input fields
  ///
  /// In en, this message translates to:
  /// **'Search...'**
  String get search;

  /// User's message inbox label
  ///
  /// In en, this message translates to:
  /// **'Inbox'**
  String get inbox;

  /// Selling section label
  ///
  /// In en, this message translates to:
  /// **'Selling'**
  String get selling;

  /// Marketplace title
  ///
  /// In en, this message translates to:
  /// **'CarNow Marketplace'**
  String get carNowMarketplace;

  /// Filter for showing all items
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// Filter for new items
  ///
  /// In en, this message translates to:
  /// **'New'**
  String get newItem;

  /// Filter for used items
  ///
  /// In en, this message translates to:
  /// **'Used'**
  String get used;

  /// Filter for featured items
  ///
  /// In en, this message translates to:
  /// **'Featured'**
  String get featured;

  /// Filter for items near user location
  ///
  /// In en, this message translates to:
  /// **'Nearby'**
  String get nearby;

  /// Label for special offer notification
  ///
  /// In en, this message translates to:
  /// **'Special Offer!'**
  String get specialOffer;

  /// Details about the special offer
  ///
  /// In en, this message translates to:
  /// **'Get 10% off on your first order.'**
  String get specialOfferDetails;

  /// Button to learn more about an offer or feature
  ///
  /// In en, this message translates to:
  /// **'Learn More'**
  String get learnMore;

  /// Add button label
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// Notifications section label
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// Store or marketplace section
  ///
  /// In en, this message translates to:
  /// **'Store'**
  String get store;

  /// User account section label
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get account;

  /// Header for user's shopping activity section
  ///
  /// In en, this message translates to:
  /// **'Shopping Activity'**
  String get shoppingActivity;

  /// Subtitle for My Orders option
  ///
  /// In en, this message translates to:
  /// **'Track your orders'**
  String get trackYourOrders;

  /// Subtitle for My Listings option
  ///
  /// In en, this message translates to:
  /// **'Manage your items for sale'**
  String get manageYourListings;

  /// Subtitle for Messages option
  ///
  /// In en, this message translates to:
  /// **'View buyer and seller messages'**
  String get viewBuyerSellerMessages;

  /// No description provided for @becomeASeller.
  ///
  /// In en, this message translates to:
  /// **'Become a Seller'**
  String get becomeASeller;

  /// No description provided for @becomeASellerSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Start selling auto parts and accessories'**
  String get becomeASellerSubtitle;

  /// Subtitle for become a seller option
  ///
  /// In en, this message translates to:
  /// **'Apply to sell on CarNow'**
  String get applyToSellOnCarNow;

  /// Header for settings and support section
  ///
  /// In en, this message translates to:
  /// **'Settings & Support'**
  String get settingsAndSupport;

  /// Title for personal information option
  ///
  /// In en, this message translates to:
  /// **'Personal Information'**
  String get personalInformation;

  /// Subtitle for personal information option
  ///
  /// In en, this message translates to:
  /// **'Edit your profile details'**
  String get editYourProfileDetails;

  /// Subtitle for Settings option
  ///
  /// In en, this message translates to:
  /// **'Account settings'**
  String get accountSettings;

  /// Subtitle for Support option
  ///
  /// In en, this message translates to:
  /// **'Get help and contact support'**
  String get getHelpAndContactSupport;

  /// Subtitle for Logout option
  ///
  /// In en, this message translates to:
  /// **'Sign out from your account'**
  String get signOutFromYourAccount;

  /// Prompt asking users to sign in
  ///
  /// In en, this message translates to:
  /// **'Sign in or register to manage your listings'**
  String get signInPrompt;

  /// Register button label
  ///
  /// In en, this message translates to:
  /// **'Register'**
  String get register;

  /// Sign in button label
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// User's orders section label
  ///
  /// In en, this message translates to:
  /// **'My Orders'**
  String get myOrders;

  /// User's listings section label
  ///
  /// In en, this message translates to:
  /// **'My Listings'**
  String get myListings;

  /// Messages section label
  ///
  /// In en, this message translates to:
  /// **'Messages'**
  String get messages;

  /// Seller dashboard section label
  ///
  /// In en, this message translates to:
  /// **'Seller'**
  String get sellerSection;

  /// Subscription management section label
  ///
  /// In en, this message translates to:
  /// **'Subscription'**
  String get subscription;

  /// Subscription plans screen title
  ///
  /// In en, this message translates to:
  /// **'Subscription Plans'**
  String get subscriptionPlans;

  /// Choose your subscription plan heading
  ///
  /// In en, this message translates to:
  /// **'Choose Your Plan'**
  String get chooseYourPlan;

  /// Monthly billing cycle
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get monthly;

  /// Yearly billing cycle
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get yearly;

  /// Month unit
  ///
  /// In en, this message translates to:
  /// **'month'**
  String get month;

  /// Year unit
  ///
  /// In en, this message translates to:
  /// **'year'**
  String get year;

  /// Continue button text
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueButton;

  /// Selected state text
  ///
  /// In en, this message translates to:
  /// **'Selected'**
  String get selected;

  /// Select button text
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get select;

  /// Sales section label
  ///
  /// In en, this message translates to:
  /// **'Sales'**
  String get sales;

  /// Settings section label
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Support section label
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get support;

  /// Logout button label
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// Guest user label
  ///
  /// In en, this message translates to:
  /// **'Guest User'**
  String get guestUser;

  /// Prompt to sign in to continue using the app
  ///
  /// In en, this message translates to:
  /// **'Sign in to continue'**
  String get signInToContinue;

  /// Edit profile button label
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get editProfile;

  /// Supabase test feature label
  ///
  /// In en, this message translates to:
  /// **'Supabase Test'**
  String get supabaseTest;

  /// Shopping cart label
  ///
  /// In en, this message translates to:
  /// **'Shopping Cart'**
  String get shoppingCart;

  /// Clear cart button label
  ///
  /// In en, this message translates to:
  /// **'Clear Cart'**
  String get clearCart;

  /// Title for cart clearing confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Confirm Clear Cart'**
  String get clearCartConfirmationTitle;

  /// Confirmation message when clearing cart
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to remove all items from your cart?'**
  String get clearCartConfirmationBody;

  /// Cancel button label
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Clear button label
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// Message shown when shopping cart is empty
  ///
  /// In en, this message translates to:
  /// **'Your cart is empty'**
  String get emptyCartMessage;

  /// Subtext shown with empty cart message
  ///
  /// In en, this message translates to:
  /// **'Browse products and add something you like!'**
  String get emptyCartSubMessage;

  /// Shop now button label
  ///
  /// In en, this message translates to:
  /// **'Shop Now'**
  String get shopNow;

  /// Button label to remove item from cart
  ///
  /// In en, this message translates to:
  /// **'Remove from cart'**
  String get removeFromCart;

  /// Subtotal label in cart
  ///
  /// In en, this message translates to:
  /// **'Subtotal'**
  String get subtotal;

  /// Label for item count
  ///
  /// In en, this message translates to:
  /// **'items'**
  String get items;

  /// Shipping cost label
  ///
  /// In en, this message translates to:
  /// **'Shipping'**
  String get shipping;

  /// Total cost label
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get total;

  /// Checkout button label
  ///
  /// In en, this message translates to:
  /// **'Proceed to Checkout'**
  String get proceedToCheckout;

  /// Tooltip for the clear search button
  ///
  /// In en, this message translates to:
  /// **'Clear search'**
  String get clearSearch;

  /// Message shown when no search suggestions are available
  ///
  /// In en, this message translates to:
  /// **'No search suggestions found'**
  String get noSuggestionsFound;

  /// Prompt for user to start typing in search box
  ///
  /// In en, this message translates to:
  /// **'Start typing to search'**
  String get startTypingToSearch;

  /// General error label
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Label for recent searches tab
  ///
  /// In en, this message translates to:
  /// **'Recent'**
  String get recentSearches;

  /// Shopping activity section header
  ///
  /// In en, this message translates to:
  /// **'Shopping Activity'**
  String get shoppingActivitySection;

  /// Subtitle for my orders section
  ///
  /// In en, this message translates to:
  /// **'Track your orders'**
  String get myOrdersSubtitle;

  /// Subtitle for my listings section
  ///
  /// In en, this message translates to:
  /// **'Manage your items for sale'**
  String get myListingsSubtitle;

  /// Subtitle for messages section
  ///
  /// In en, this message translates to:
  /// **'View buyer and seller messages'**
  String get messagesSubtitle;

  /// Settings and support section header
  ///
  /// In en, this message translates to:
  /// **'Settings & Support'**
  String get settingsSection;

  /// Subtitle for settings section
  ///
  /// In en, this message translates to:
  /// **'Manage app preferences'**
  String get settingsSubtitle;

  /// Seller tools section header
  ///
  /// In en, this message translates to:
  /// **'Seller Tools'**
  String get sellerToolsSection;

  /// Subtitle for subscription section
  ///
  /// In en, this message translates to:
  /// **'Manage your seller plan'**
  String get subscriptionSubtitle;

  /// Subtitle for sales section
  ///
  /// In en, this message translates to:
  /// **'View your earnings'**
  String get salesSubtitle;

  /// Text displayed when a search yields no results
  ///
  /// In en, this message translates to:
  /// **'No results found'**
  String get noResultsFound;

  /// Button text to save changes
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Seller dashboard label
  ///
  /// In en, this message translates to:
  /// **'Seller Dashboard'**
  String get sellerDashboard;

  /// Subtitle for seller dashboard
  ///
  /// In en, this message translates to:
  /// **'Manage products, sales, and settings'**
  String get sellerDashboardSubtitle;

  /// Seller products section label
  ///
  /// In en, this message translates to:
  /// **'Products'**
  String get sellerProducts;

  /// Subtitle for seller products section
  ///
  /// In en, this message translates to:
  /// **'Manage your product catalog'**
  String get sellerProductsSubtitle;

  /// Seller orders section label
  ///
  /// In en, this message translates to:
  /// **'Orders'**
  String get sellerOrders;

  /// Subtitle for seller orders section
  ///
  /// In en, this message translates to:
  /// **'View and manage customer orders'**
  String get sellerOrdersSubtitle;

  /// Seller store section label
  ///
  /// In en, this message translates to:
  /// **'Store'**
  String get sellerStore;

  /// Seller settings section label
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get sellerSettings;

  /// Seller analytics section label
  ///
  /// In en, this message translates to:
  /// **'Analytics'**
  String get sellerAnalytics;

  /// Subtitle for seller promotions section
  ///
  /// In en, this message translates to:
  /// **'Create and manage special offers'**
  String get sellerPromotionsSubtitle;

  /// Status message for pending seller approval
  ///
  /// In en, this message translates to:
  /// **'Seller Approval Pending'**
  String get sellerApprovalPending;

  /// Description message for pending seller approval
  ///
  /// In en, this message translates to:
  /// **'Your seller account is pending approval by our team. You\'ll be able to access seller tools once approved.'**
  String get sellerApprovalPendingDesc;

  /// Message shown when seller has no store
  ///
  /// In en, this message translates to:
  /// **'No Store Found'**
  String get noSellerStore;

  /// Description for no store found message
  ///
  /// In en, this message translates to:
  /// **'You need to create a store before you can start selling products'**
  String get noSellerStoreDesc;

  /// Button label to create a new store
  ///
  /// In en, this message translates to:
  /// **'Create Store'**
  String get createStore;

  /// Header for revenue summary section
  ///
  /// In en, this message translates to:
  /// **'Revenue Summary'**
  String get revenueSummary;

  /// Label for today's revenue
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get todayRevenue;

  /// Label for weekly revenue
  ///
  /// In en, this message translates to:
  /// **'This Week'**
  String get weeklyRevenue;

  /// Label for monthly revenue
  ///
  /// In en, this message translates to:
  /// **'This Month'**
  String get monthlyRevenue;

  /// Label for total revenue
  ///
  /// In en, this message translates to:
  /// **'Total Revenue'**
  String get totalRevenue;

  /// Label for last 7 days revenue chart
  ///
  /// In en, this message translates to:
  /// **'Last 7 Days Revenue'**
  String get last7DaysRevenue;

  /// Label for pending orders
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pendingOrders;

  /// Label for completed orders
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completedOrders;

  /// Label for total sales count
  ///
  /// In en, this message translates to:
  /// **'Total Sales'**
  String get totalSales;

  /// Label for total products count
  ///
  /// In en, this message translates to:
  /// **'Total Products'**
  String get totalProducts;

  /// Label for quick actions
  ///
  /// In en, this message translates to:
  /// **'Quick Actions'**
  String get quickActions;

  /// Button label to add a new product
  ///
  /// In en, this message translates to:
  /// **'Add Product'**
  String get addProduct;

  /// Button label to manage products
  ///
  /// In en, this message translates to:
  /// **'Manage Products'**
  String get manageProducts;

  /// Button label to create a new offer
  ///
  /// In en, this message translates to:
  /// **'Create Offer'**
  String get createPromotion;

  /// Button label to manage offers
  ///
  /// In en, this message translates to:
  /// **'Manage Offers'**
  String get managePromos;

  /// Button label to view orders
  ///
  /// In en, this message translates to:
  /// **'View Orders'**
  String get viewOrders;

  /// Label for store settings section
  ///
  /// In en, this message translates to:
  /// **'Store Settings'**
  String get storeSettings;

  /// Label for recent orders section
  ///
  /// In en, this message translates to:
  /// **'Recent Orders'**
  String get recentOrders;

  /// Label for top selling products section
  ///
  /// In en, this message translates to:
  /// **'Top Selling Products'**
  String get topSellingProducts;

  /// Badge for verified store status
  ///
  /// In en, this message translates to:
  /// **'Verified Store'**
  String get verifiedStore;

  /// Button label to edit store
  ///
  /// In en, this message translates to:
  /// **'Edit Store'**
  String get editStore;

  /// Button label to view all items
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get viewAll;

  /// Button label to view more items
  ///
  /// In en, this message translates to:
  /// **'View More'**
  String get viewMore;

  /// Placeholder for product search field
  ///
  /// In en, this message translates to:
  /// **'Search products...'**
  String get searchProducts;

  /// Status label for active items
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// Status label for inactive items
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get inactive;

  /// Status label for pending items
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// Status label for rejected items
  ///
  /// In en, this message translates to:
  /// **'Rejected'**
  String get rejected;

  /// Message shown when seller has no products
  ///
  /// In en, this message translates to:
  /// **'No Products Yet'**
  String get noProductsYet;

  /// Instructions for adding first product
  ///
  /// In en, this message translates to:
  /// **'Tap the add button to create your first product'**
  String get tapAddToCreateProduct;

  /// Message shown when no products match the applied filter
  ///
  /// In en, this message translates to:
  /// **'No products matching your filter'**
  String get noProductsMatchingFilter;

  /// Clear filters button
  ///
  /// In en, this message translates to:
  /// **'Clear Filters'**
  String get clearFilters;

  /// Confirmation message when product is hidden
  ///
  /// In en, this message translates to:
  /// **'Product has been hidden'**
  String get productHiddenSuccess;

  /// Confirmation message when product visibility is restored
  ///
  /// In en, this message translates to:
  /// **'Product is now visible'**
  String get productVisibleSuccess;

  /// Edit action button label
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Delete button
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Show/reveal action button label
  ///
  /// In en, this message translates to:
  /// **'Show'**
  String get show;

  /// Hide action button label
  ///
  /// In en, this message translates to:
  /// **'Hide'**
  String get hide;

  /// Label for unknown status
  ///
  /// In en, this message translates to:
  /// **'Unknown'**
  String get unknown;

  /// Label for name input field
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get nameLabel;

  /// Validation message for empty name field
  ///
  /// In en, this message translates to:
  /// **'Please enter your name'**
  String get nameRequired;

  /// No description provided for @soldCount.
  ///
  /// In en, this message translates to:
  /// **'{count} sold'**
  String soldCount(int count);

  /// Label for promotions section
  ///
  /// In en, this message translates to:
  /// **'Promotions & Offers'**
  String get promotions;

  /// Message when no store is found
  ///
  /// In en, this message translates to:
  /// **'No store found'**
  String get noStoreFound;

  /// Message shown when no active promotions exist
  ///
  /// In en, this message translates to:
  /// **'No active promotions found'**
  String get noActivePromotions;

  /// Button label to show all promotions
  ///
  /// In en, this message translates to:
  /// **'Show all promotions'**
  String get showAllPromotions;

  /// Button label to filter and show only active promotions
  ///
  /// In en, this message translates to:
  /// **'Show active only'**
  String get showActiveOnly;

  /// Message shown when seller has no promotions
  ///
  /// In en, this message translates to:
  /// **'No Promotions Yet'**
  String get noPromotionsYet;

  /// Description for promotion creation
  ///
  /// In en, this message translates to:
  /// **'Create special offers and discounts for your customers'**
  String get createPromotionDesc;

  /// Button label to delete a promotion
  ///
  /// In en, this message translates to:
  /// **'Delete Promotion'**
  String get deletePromotion;

  /// Confirmation message when deleting a promotion
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this promotion? This action cannot be undone.'**
  String get deletePromotionConfirm;

  /// Success message when promotion is deleted
  ///
  /// In en, this message translates to:
  /// **'Promotion deleted successfully'**
  String get promotionDeleted;

  /// Status label for expired items
  ///
  /// In en, this message translates to:
  /// **'Expired'**
  String get expired;

  /// Status label for scheduled items
  ///
  /// In en, this message translates to:
  /// **'Scheduled'**
  String get scheduled;

  /// Label for discount percentage (X% off)
  ///
  /// In en, this message translates to:
  /// **'off'**
  String get offDiscount;

  /// Label for discount value
  ///
  /// In en, this message translates to:
  /// **'discount'**
  String get discountValue;

  /// Label for products
  ///
  /// In en, this message translates to:
  /// **'products'**
  String get products;

  /// Label for anonymous user
  ///
  /// In en, this message translates to:
  /// **'Anonymous'**
  String get anonymous;

  /// Header for user's products section
  ///
  /// In en, this message translates to:
  /// **'My Products'**
  String get myProducts;

  /// Tooltip for the filter products button
  ///
  /// In en, this message translates to:
  /// **'Filter Products'**
  String get filterProducts;

  /// Header for product editing screen
  ///
  /// In en, this message translates to:
  /// **'Edit Product'**
  String get editProduct;

  /// Label for product name field
  ///
  /// In en, this message translates to:
  /// **'Product Name'**
  String get productName;

  /// Label for price field
  ///
  /// In en, this message translates to:
  /// **'Price'**
  String get price;

  /// Condition filter
  ///
  /// In en, this message translates to:
  /// **'Condition'**
  String get condition;

  /// Label for description field
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// Label for product images section
  ///
  /// In en, this message translates to:
  /// **'Product Images'**
  String get productImages;

  /// Label for image gallery option.
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// Label for camera option.
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// Validation message for no images selected
  ///
  /// In en, this message translates to:
  /// **'Please select at least one image'**
  String get noImagesSelected;

  /// Label indicating number of images selected
  ///
  /// In en, this message translates to:
  /// **'images selected'**
  String get imagesSelected;

  /// Button label to update a product
  ///
  /// In en, this message translates to:
  /// **'Update Product'**
  String get updateProduct;

  /// Button label to delete a product
  ///
  /// In en, this message translates to:
  /// **'Delete Product'**
  String get deleteProduct;

  /// Confirmation message when deleting a product
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this product?'**
  String get confirmDeleteProduct;

  /// Success message when product is deleted
  ///
  /// In en, this message translates to:
  /// **'Product deleted successfully'**
  String get productDeleted;

  /// Success message when product is added
  ///
  /// In en, this message translates to:
  /// **'Product added successfully'**
  String get productAdded;

  /// Success message when product is updated
  ///
  /// In en, this message translates to:
  /// **'Product updated successfully'**
  String get productUpdated;

  /// Error message when product deletion fails
  ///
  /// In en, this message translates to:
  /// **'Error deleting product'**
  String get errorDeletingProduct;

  /// Error message when product addition fails
  ///
  /// In en, this message translates to:
  /// **'Error adding product'**
  String get errorAddingProduct;

  /// Error message when product update fails
  ///
  /// In en, this message translates to:
  /// **'Error updating product'**
  String get errorUpdatingProduct;

  /// Error message shown when product loading fails
  ///
  /// In en, this message translates to:
  /// **'Error loading products'**
  String get errorLoadingProducts;

  /// Message shown when no products are available
  ///
  /// In en, this message translates to:
  /// **'No products found'**
  String get noProductsFound;

  /// Validation message for invalid price input
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid price'**
  String get invalidPrice;

  /// Validation message for required fields
  ///
  /// In en, this message translates to:
  /// **'This field is required'**
  String get fieldRequired;

  /// Message shown when no products are available in a specific category
  ///
  /// In en, this message translates to:
  /// **'No products available in this category'**
  String get noProductsAvailable;

  /// Label for phone number field
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneLabel;

  /// Error message for invalid phone number
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid phone number'**
  String get phoneInvalid;

  /// Label for address field
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get addressLabel;

  /// Error message for empty address field
  ///
  /// In en, this message translates to:
  /// **'Please enter your address'**
  String get addressRequired;

  /// Button text to save changes
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get saveChanges;

  /// Button text to discard changes
  ///
  /// In en, this message translates to:
  /// **'Discard Changes'**
  String get discardChanges;

  /// Success message when changes are saved
  ///
  /// In en, this message translates to:
  /// **'Changes saved successfully'**
  String get changesSaved;

  /// Error message when saving changes fails
  ///
  /// In en, this message translates to:
  /// **'Error saving changes. Please try again.'**
  String get errorSavingChanges;

  /// Success message when profile is updated
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully'**
  String get profileUpdated;

  /// Error message when profile update fails
  ///
  /// In en, this message translates to:
  /// **'Failed to update profile information'**
  String get profileUpdateFailed;

  /// Hint text for phone number field
  ///
  /// In en, this message translates to:
  /// **'Enter your phone number'**
  String get phoneHint;

  /// Hint text for address field
  ///
  /// In en, this message translates to:
  /// **'Enter your address'**
  String get addressHint;

  /// Button text to reset form
  ///
  /// In en, this message translates to:
  /// **'Reset Form'**
  String get resetForm;

  /// Title for discard changes confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Discard Changes?'**
  String get confirmDiscardTitle;

  /// Message for discard changes confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to discard your changes?'**
  String get confirmDiscardMessage;

  /// Confirmation button text
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// Placeholder for message input field
  ///
  /// In en, this message translates to:
  /// **'Type a message...'**
  String get typeAMessage;

  /// Title for creating a new conversation
  ///
  /// In en, this message translates to:
  /// **'New Conversation'**
  String get newConversation;

  /// Placeholder for searching users
  ///
  /// In en, this message translates to:
  /// **'Search users...'**
  String get searchUsers;

  /// Switch label for creating a group chat
  ///
  /// In en, this message translates to:
  /// **'Create Group Chat'**
  String get createGroupChat;

  /// Label for group chat title input
  ///
  /// In en, this message translates to:
  /// **'Group Title'**
  String get groupChatTitle;

  /// Message when no users are found in search
  ///
  /// In en, this message translates to:
  /// **'No users found'**
  String get noUsersFound;

  /// Subtitle for chat messages section
  ///
  /// In en, this message translates to:
  /// **'Chat with sellers and support'**
  String get chatMessagesSubtitle;

  /// Message shown when user has no conversations
  ///
  /// In en, this message translates to:
  /// **'No conversations yet'**
  String get noConversationsYet;

  /// Prompt to start a new conversation
  ///
  /// In en, this message translates to:
  /// **'Start a new conversation'**
  String get startNewConversation;

  /// Message shown when conversation has no messages
  ///
  /// In en, this message translates to:
  /// **'No messages yet'**
  String get noMessagesYet;

  /// Prompt to send first message
  ///
  /// In en, this message translates to:
  /// **'Send a message to start the conversation'**
  String get sendFirstMessage;

  /// Button text to retry sending a failed message
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Title for conversation details screen
  ///
  /// In en, this message translates to:
  /// **'Conversation Details'**
  String get conversationDetails;

  /// Option to view user profile
  ///
  /// In en, this message translates to:
  /// **'View Profile'**
  String get viewProfile;

  /// Option to delete a conversation
  ///
  /// In en, this message translates to:
  /// **'Delete Conversation'**
  String get deleteConversation;

  /// Label for conversation participants
  ///
  /// In en, this message translates to:
  /// **'Participants'**
  String get participants;

  /// Message shown during content loading
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Help section
  ///
  /// In en, this message translates to:
  /// **'Help'**
  String get help;

  /// Title for the sales categories management screen
  ///
  /// In en, this message translates to:
  /// **'Manage Sales Categories'**
  String get manageSalesCategories;

  /// Description for the category selection screen
  ///
  /// In en, this message translates to:
  /// **'Select the categories in which you want to sell your products.'**
  String get selectCategoriesDescription;

  /// Success message for saving categories
  ///
  /// In en, this message translates to:
  /// **'Categories saved successfully'**
  String get categoriesSaved;

  /// Title for the categories help dialog
  ///
  /// In en, this message translates to:
  /// **'Categories Help'**
  String get categoriesHelp;

  /// Categories help text
  ///
  /// In en, this message translates to:
  /// **'You can select the categories in which you want to sell your products. This will make your products appear in these categories when displayed to buyers.'**
  String get categoriesHelpText;

  /// Close button
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// My CarNow page title
  ///
  /// In en, this message translates to:
  /// **'My CarNow'**
  String get myCarNow;

  /// Welcome message for unauthenticated users
  ///
  /// In en, this message translates to:
  /// **'Welcome to CarNow'**
  String get welcomeToCarNow;

  /// Sign in prompt message
  ///
  /// In en, this message translates to:
  /// **'Sign in to access your account and saved features'**
  String get signInToAccessAccount;

  /// Create new account button
  ///
  /// In en, this message translates to:
  /// **'Create Account'**
  String get createAccount;

  /// Default user name
  ///
  /// In en, this message translates to:
  /// **'New Member'**
  String get carNowUser;

  /// Label for new members who haven't completed their profile
  ///
  /// In en, this message translates to:
  /// **'New Member'**
  String get newMember;

  /// Label for users with incomplete profiles
  ///
  /// In en, this message translates to:
  /// **'Profile Incomplete'**
  String get incompleteProfile;

  /// Prompt for users to complete their profile
  ///
  /// In en, this message translates to:
  /// **'Complete Your Profile'**
  String get completeYourProfile;

  /// Status message for incomplete profiles
  ///
  /// In en, this message translates to:
  /// **'Profile Incomplete'**
  String get profileIncomplete;

  /// Member since label
  ///
  /// In en, this message translates to:
  /// **'Member since'**
  String get memberSince;

  /// Your updates section title
  ///
  /// In en, this message translates to:
  /// **'Your Updates'**
  String get yourUpdates;

  /// Watchlist section
  ///
  /// In en, this message translates to:
  /// **'Watchlist'**
  String get watchlist;

  /// Favorites and lists subtitle
  ///
  /// In en, this message translates to:
  /// **'Favorites & Lists'**
  String get favoritesAndLists;

  /// Bids and offers section
  ///
  /// In en, this message translates to:
  /// **'Bids & Offers'**
  String get bidsAndOffers;

  /// Active auctions and offers subtitle
  ///
  /// In en, this message translates to:
  /// **'Active auctions and offers'**
  String get activeAuctionsAndOffers;

  /// Shopping section title
  ///
  /// In en, this message translates to:
  /// **'Shopping'**
  String get shopping;

  /// Follow watched items subtitle
  ///
  /// In en, this message translates to:
  /// **'Follow watched items'**
  String get followWatchedItems;

  /// Saved items section
  ///
  /// In en, this message translates to:
  /// **'Saved Items'**
  String get savedItems;

  /// Searches sellers feeds subtitle
  ///
  /// In en, this message translates to:
  /// **'Searches, sellers, feeds'**
  String get searchesSellersFeeds;

  /// Buy again section
  ///
  /// In en, this message translates to:
  /// **'Buy Again'**
  String get buyAgain;

  /// Shop from previous purchases subtitle
  ///
  /// In en, this message translates to:
  /// **'Shop from previous purchases'**
  String get shopFromPreviousPurchases;

  /// Purchases section
  ///
  /// In en, this message translates to:
  /// **'Purchases'**
  String get purchases;

  /// Order history subtitle
  ///
  /// In en, this message translates to:
  /// **'Order history'**
  String get orderHistory;

  /// Recently viewed section
  ///
  /// In en, this message translates to:
  /// **'Recently Viewed'**
  String get recentlyViewed;

  /// Items you viewed subtitle
  ///
  /// In en, this message translates to:
  /// **'Items you viewed'**
  String get itemsYouViewed;

  /// CarNow garage title
  ///
  /// In en, this message translates to:
  /// **'CarNow Garage'**
  String get carNowGarage;

  /// Add car get compatible parts subtitle
  ///
  /// In en, this message translates to:
  /// **'Add your car and get compatible parts'**
  String get addCarGetCompatibleParts;

  /// Manage garage button
  ///
  /// In en, this message translates to:
  /// **'Manage Garage'**
  String get manageGarage;

  /// Add vehicle button
  ///
  /// In en, this message translates to:
  /// **'Add Vehicle'**
  String get addVehicle;

  /// Fits your ride every time banner title
  ///
  /// In en, this message translates to:
  /// **'Fits your ride, every time'**
  String get fitsYourRideEveryTime;

  /// Right parts or money back banner subtitle
  ///
  /// In en, this message translates to:
  /// **'Get the right parts or your money back'**
  String get rightPartsOrMoneyBack;

  /// Local installation services banner title
  ///
  /// In en, this message translates to:
  /// **'Local Installation Services'**
  String get localInstallationServices;

  /// Install parts with experts banner subtitle
  ///
  /// In en, this message translates to:
  /// **'Install your new parts with our network of experts'**
  String get installPartsWithExperts;

  /// Browse services button
  ///
  /// In en, this message translates to:
  /// **'Browse Services'**
  String get browseServices;

  /// My vehicles section title
  ///
  /// In en, this message translates to:
  /// **'My Vehicles'**
  String get myVehicles;

  /// Vehicle form coming soon message
  ///
  /// In en, this message translates to:
  /// **'Vehicle form will be implemented in a future task'**
  String get vehicleFormComingSoon;

  /// Delete vehicle dialog title
  ///
  /// In en, this message translates to:
  /// **'Delete Vehicle'**
  String get deleteVehicle;

  /// Confirm delete vehicle message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this vehicle?'**
  String get confirmDeleteVehicle;

  /// Garage settings section
  ///
  /// In en, this message translates to:
  /// **'Garage Settings'**
  String get garageSettings;

  /// Share button
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// Compatibility guarantee dialog title
  ///
  /// In en, this message translates to:
  /// **'Compatibility Guarantee'**
  String get compatibilityGuarantee;

  /// Compatibility guarantee message
  ///
  /// In en, this message translates to:
  /// **'We guarantee that the parts you buy will fit your vehicle. If they don\'t fit, you can return them and get a full refund'**
  String get compatibilityGuaranteeMessage;

  /// Understood button
  ///
  /// In en, this message translates to:
  /// **'Understood'**
  String get understood;

  /// Sharing feature coming soon message
  ///
  /// In en, this message translates to:
  /// **'Sharing feature coming soon!'**
  String get sharingFeatureComingSoon;

  /// Garage is empty message
  ///
  /// In en, this message translates to:
  /// **'Your garage is empty'**
  String get garageIsEmpty;

  /// Add vehicles to find parts message
  ///
  /// In en, this message translates to:
  /// **'Add your vehicles to find compatible parts'**
  String get addVehiclesToFindParts;

  /// Contact Us section label
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contactUs;

  /// Email support label
  ///
  /// In en, this message translates to:
  /// **'Email Support'**
  String get emailSupport;

  /// Phone support label
  ///
  /// In en, this message translates to:
  /// **'Phone Support'**
  String get phoneSupport;

  /// FAQ section label
  ///
  /// In en, this message translates to:
  /// **'Frequently Asked Questions'**
  String get faq;

  /// Message when no FAQs are available
  ///
  /// In en, this message translates to:
  /// **'No FAQs available at the moment.'**
  String get noFaqsAvailable;

  /// Live chat button label
  ///
  /// In en, this message translates to:
  /// **'Live Chat'**
  String get liveChat;

  /// Label for saved searches tab
  ///
  /// In en, this message translates to:
  /// **'Saved'**
  String get savedSearches;

  /// Message shown when no search results are found for a query
  ///
  /// In en, this message translates to:
  /// **'No results found for \'{query}\''**
  String noResultsFoundFor(String query);

  /// Generic error message
  ///
  /// In en, this message translates to:
  /// **'An error occurred'**
  String get errorOccurred;

  /// Message shown when there are no saved items
  ///
  /// In en, this message translates to:
  /// **'You haven\'t saved any items yet.'**
  String get noSavedItems;

  /// Error message for network connectivity issues.
  ///
  /// In en, this message translates to:
  /// **'Network error. Please check your connection.'**
  String get networkError;

  /// Server error message
  ///
  /// In en, this message translates to:
  /// **'Server error'**
  String get serverError;

  /// Timeout error message
  ///
  /// In en, this message translates to:
  /// **'Connection timeout'**
  String get timeoutError;

  /// Invalid credentials error message
  ///
  /// In en, this message translates to:
  /// **'Invalid login credentials'**
  String get invalidCredentials;

  /// Email not confirmed error message
  ///
  /// In en, this message translates to:
  /// **'Please confirm your email'**
  String get emailNotConfirmed;

  /// Error message when a user is not found.
  ///
  /// In en, this message translates to:
  /// **'User not found.'**
  String get userNotFound;

  /// Weak password error message
  ///
  /// In en, this message translates to:
  /// **'Password is too weak'**
  String get weakPassword;

  /// Email already registered error message
  ///
  /// In en, this message translates to:
  /// **'Email is already registered'**
  String get emailAlreadyRegistered;

  /// Error message for authentication failures.
  ///
  /// In en, this message translates to:
  /// **'Authentication failed. Please check your credentials.'**
  String get authError;

  /// Duplicate entry error message
  ///
  /// In en, this message translates to:
  /// **'Data already exists'**
  String get duplicateEntry;

  /// Foreign key violation error message
  ///
  /// In en, this message translates to:
  /// **'Cannot delete this item'**
  String get foreignKeyViolation;

  /// Insufficient permissions error message
  ///
  /// In en, this message translates to:
  /// **'You don\'t have permission for this action'**
  String get insufficientPermissions;

  /// Database error message
  ///
  /// In en, this message translates to:
  /// **'Database error'**
  String get databaseError;

  /// No description provided for @helloWorld.
  ///
  /// In en, this message translates to:
  /// **'Hello World!'**
  String get helloWorld;

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// Validation error for a required field that is empty.
  ///
  /// In en, this message translates to:
  /// **'This field is required'**
  String get requiredField;

  /// Validation error for an invalid email format.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get invalidEmail;

  /// Validation error for a password that is too short.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get passwordTooShort;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// Error message for authentication failures.
  ///
  /// In en, this message translates to:
  /// **'Authentication failed. Please check your credentials.'**
  String get authenticationError;

  /// Error message for data processing failures.
  ///
  /// In en, this message translates to:
  /// **'Error processing data. Please try again.'**
  String get dataProcessingError;

  /// Generic error message for unexpected issues.
  ///
  /// In en, this message translates to:
  /// **'An unexpected error occurred. Please try again.'**
  String get unexpectedError;

  /// No description provided for @completeProfileTitle.
  ///
  /// In en, this message translates to:
  /// **'Complete Your Profile'**
  String get completeProfileTitle;

  /// No description provided for @completeProfileHeading.
  ///
  /// In en, this message translates to:
  /// **'One Last Step Before You Proceed!'**
  String get completeProfileHeading;

  /// No description provided for @completeProfileMessage.
  ///
  /// In en, this message translates to:
  /// **'We need some additional information in your profile (like your name and phone number) to successfully complete the purchase.'**
  String get completeProfileMessage;

  /// No description provided for @completeProfileButton.
  ///
  /// In en, this message translates to:
  /// **'Go to Complete Profile'**
  String get completeProfileButton;

  /// No description provided for @cancelButton.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancelButton;

  /// No description provided for @logoutConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to log out?'**
  String get logoutConfirmation;

  /// No description provided for @createAccountButton.
  ///
  /// In en, this message translates to:
  /// **'Create a New Account'**
  String get createAccountButton;

  /// No description provided for @loginRequiredTitle.
  ///
  /// In en, this message translates to:
  /// **'Login Required'**
  String get loginRequiredTitle;

  /// No description provided for @loginRequiredHeading.
  ///
  /// In en, this message translates to:
  /// **'Exclusive Feature for Members'**
  String get loginRequiredHeading;

  /// No description provided for @loginRequiredMessage.
  ///
  /// In en, this message translates to:
  /// **'To use this feature and get full access to our services, please log in or create a new account.'**
  String get loginRequiredMessage;

  /// No description provided for @fullName.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get fullName;

  /// No description provided for @enterFullName.
  ///
  /// In en, this message translates to:
  /// **'Enter your full name'**
  String get enterFullName;

  /// No description provided for @phoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumber;

  /// No description provided for @enterPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Enter your mobile number'**
  String get enterPhoneNumber;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @enterLocation.
  ///
  /// In en, this message translates to:
  /// **'Enter your city or area'**
  String get enterLocation;

  /// No description provided for @saveAndContinue.
  ///
  /// In en, this message translates to:
  /// **'Save and Continue'**
  String get saveAndContinue;

  /// No description provided for @nameCannotBeEmpty.
  ///
  /// In en, this message translates to:
  /// **'Name cannot be empty.'**
  String get nameCannotBeEmpty;

  /// No description provided for @nameTooShort.
  ///
  /// In en, this message translates to:
  /// **'Name must be at least 2 characters.'**
  String get nameTooShort;

  /// No description provided for @phoneCannotBeEmpty.
  ///
  /// In en, this message translates to:
  /// **'Phone number cannot be empty.'**
  String get phoneCannotBeEmpty;

  /// No description provided for @invalidPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Invalid phone number format.'**
  String get invalidPhoneNumber;

  /// No description provided for @locationCannotBeEmpty.
  ///
  /// In en, this message translates to:
  /// **'Location cannot be empty.'**
  String get locationCannotBeEmpty;

  /// No description provided for @locationTooShort.
  ///
  /// In en, this message translates to:
  /// **'Location must be at least 3 characters.'**
  String get locationTooShort;

  /// No description provided for @removeImage.
  ///
  /// In en, this message translates to:
  /// **'Remove Image'**
  String get removeImage;

  /// No description provided for @profileUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully.'**
  String get profileUpdatedSuccessfully;

  /// No description provided for @storageError.
  ///
  /// In en, this message translates to:
  /// **'Error saving data. Please try again.'**
  String get storageError;

  /// No description provided for @errorLoadingProfile.
  ///
  /// In en, this message translates to:
  /// **'Error loading profile.'**
  String get errorLoadingProfile;

  /// No description provided for @completeProfileWelcome.
  ///
  /// In en, this message translates to:
  /// **'Welcome! We need some additional information'**
  String get completeProfileWelcome;

  /// No description provided for @completeProfileDescription.
  ///
  /// In en, this message translates to:
  /// **'To improve your experience in the app, please complete your personal information below.'**
  String get completeProfileDescription;

  /// No description provided for @loginButton.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get loginButton;

  /// No description provided for @supportTicketUpdateSuccess.
  ///
  /// In en, this message translates to:
  /// **'Support ticket updated successfully.'**
  String get supportTicketUpdateSuccess;

  /// No description provided for @supportTicketUpdateError.
  ///
  /// In en, this message translates to:
  /// **'Failed to update support ticket.'**
  String get supportTicketUpdateError;

  /// No description provided for @searchHint.
  ///
  /// In en, this message translates to:
  /// **'Search for parts, categories...'**
  String get searchHint;

  /// No description provided for @partNamesLanguage.
  ///
  /// In en, this message translates to:
  /// **'Part Names Language'**
  String get partNamesLanguage;

  /// No description provided for @partNamesLanguageDesc.
  ///
  /// In en, this message translates to:
  /// **'Choose the language for displaying car part names.'**
  String get partNamesLanguageDesc;

  /// Title for automotive type selection screen
  ///
  /// In en, this message translates to:
  /// **'Select Automotive Type'**
  String get selectAutomotiveTypeTitle;

  /// Main question for what user wants to list
  ///
  /// In en, this message translates to:
  /// **'What are you listing?'**
  String get whatAreYouListing;

  /// Instruction to select category
  ///
  /// In en, this message translates to:
  /// **'Select a category to continue with your listing'**
  String get selectCategoryToContinue;

  /// Title for vehicle selection option
  ///
  /// In en, this message translates to:
  /// **'Vehicle'**
  String get vehicleSelectionTitle;

  /// Description for vehicle selection option
  ///
  /// In en, this message translates to:
  /// **'List cars, motorcycles, trucks, and other vehicles'**
  String get vehicleSelectionSubtitle;

  /// Title for auto parts selection option
  ///
  /// In en, this message translates to:
  /// **'Auto Parts'**
  String get autoPartSelectionTitle;

  /// Description for auto parts selection option
  ///
  /// In en, this message translates to:
  /// **'List spare parts, accessories, and components'**
  String get autoPartSelectionSubtitle;

  /// Developer tools screen title
  ///
  /// In en, this message translates to:
  /// **'Developer Tools'**
  String get developerTools;

  /// Analytics dashboard
  ///
  /// In en, this message translates to:
  /// **'Analytics Dashboard'**
  String get analyticsDashboard;

  /// Sales reports
  ///
  /// In en, this message translates to:
  /// **'Sales Reports'**
  String get salesReports;

  /// Inventory management
  ///
  /// In en, this message translates to:
  /// **'Inventory Management'**
  String get inventoryManagement;

  /// Advanced dashboard
  ///
  /// In en, this message translates to:
  /// **'Advanced Dashboard'**
  String get advancedDashboard;

  /// Stores listing
  ///
  /// In en, this message translates to:
  /// **'Stores Listing'**
  String get storesListing;

  /// Warranty list
  ///
  /// In en, this message translates to:
  /// **'Warranty List'**
  String get warrantyList;

  /// Archived chats
  ///
  /// In en, this message translates to:
  /// **'Archived Chats'**
  String get archivedChats;

  /// Chat storage
  ///
  /// In en, this message translates to:
  /// **'Chat Storage'**
  String get chatStorage;

  /// Conversations
  ///
  /// In en, this message translates to:
  /// **'Conversations'**
  String get conversations;

  /// Enhanced search
  ///
  /// In en, this message translates to:
  /// **'Enhanced Search'**
  String get enhancedSearch;

  /// Compatible parts test
  ///
  /// In en, this message translates to:
  /// **'Compatible Parts (Test)'**
  String get compatiblePartsTest;

  /// Admin subscription requests
  ///
  /// In en, this message translates to:
  /// **'Admin - Subscription Requests'**
  String get adminSubscriptionRequests;

  /// Message shown when developer tools are accessed in non-debug mode
  ///
  /// In en, this message translates to:
  /// **'Developer tools are only available in debug mode'**
  String get devToolsDebugOnly;

  /// Smart search feature title
  ///
  /// In en, this message translates to:
  /// **'Smart Search'**
  String get smartSearch;

  /// Hybrid search type
  ///
  /// In en, this message translates to:
  /// **'Hybrid Search'**
  String get hybridSearch;

  /// Semantic search type
  ///
  /// In en, this message translates to:
  /// **'Semantic Search'**
  String get semanticSearch;

  /// Clear search history
  ///
  /// In en, this message translates to:
  /// **'Clear History'**
  String get clearHistory;

  /// Start searching message
  ///
  /// In en, this message translates to:
  /// **'Start Searching'**
  String get startSearching;

  /// Search description text
  ///
  /// In en, this message translates to:
  /// **'Use smart search to find what you want quickly'**
  String get searchDescription;

  /// Search suggestions section
  ///
  /// In en, this message translates to:
  /// **'Search Suggestions'**
  String get searchSuggestions;

  /// Search history section
  ///
  /// In en, this message translates to:
  /// **'Search History'**
  String get searchHistory;

  /// Clear search history button
  ///
  /// In en, this message translates to:
  /// **'Clear Search History'**
  String get clearSearchHistory;

  /// No search history message
  ///
  /// In en, this message translates to:
  /// **'No search history'**
  String get noSearchHistory;

  /// Search failed message
  ///
  /// In en, this message translates to:
  /// **'Search Failed'**
  String get searchFailed;

  /// No search results message
  ///
  /// In en, this message translates to:
  /// **'No Results'**
  String get searchEmpty;

  /// Empty search results detailed message
  ///
  /// In en, this message translates to:
  /// **'No results found. Try different keywords.'**
  String get searchEmptyMessage;

  /// Search processing time
  ///
  /// In en, this message translates to:
  /// **'Processing time: {time}ms'**
  String searchProcessingTime(Object time);

  /// Search results count
  ///
  /// In en, this message translates to:
  /// **'{count} results'**
  String searchResultsCount(Object count);

  /// Search insights section
  ///
  /// In en, this message translates to:
  /// **'Search Insights'**
  String get searchInsights;

  /// Category distribution in search
  ///
  /// In en, this message translates to:
  /// **'Category Distribution'**
  String get searchCategoryDistribution;

  /// Popular filters in search
  ///
  /// In en, this message translates to:
  /// **'Popular Filters'**
  String get searchPopularFilters;

  /// Price range in search
  ///
  /// In en, this message translates to:
  /// **'Price Range'**
  String get searchPriceRange;

  /// Minimum price in search
  ///
  /// In en, this message translates to:
  /// **'Min Price'**
  String get searchMinPrice;

  /// Maximum price in search
  ///
  /// In en, this message translates to:
  /// **'Max Price'**
  String get searchMaxPrice;

  /// Average price in search
  ///
  /// In en, this message translates to:
  /// **'Average Price'**
  String get searchAveragePrice;

  /// Search cars section
  ///
  /// In en, this message translates to:
  /// **'Search Cars'**
  String get searchCars;

  /// Search auto parts section
  ///
  /// In en, this message translates to:
  /// **'Search Auto Parts'**
  String get searchAutoParts;

  /// Car brand filter
  ///
  /// In en, this message translates to:
  /// **'Car Brand'**
  String get carBrand;

  /// Car model filter
  ///
  /// In en, this message translates to:
  /// **'Car Model'**
  String get carModel;

  /// Car year filter
  ///
  /// In en, this message translates to:
  /// **'Car Year'**
  String get carYear;

  /// Part type filter
  ///
  /// In en, this message translates to:
  /// **'Part Type'**
  String get partType;

  /// Compatibility filter
  ///
  /// In en, this message translates to:
  /// **'Compatibility'**
  String get compatibility;

  /// Mileage filter
  ///
  /// In en, this message translates to:
  /// **'Mileage'**
  String get mileage;

  /// Fuel type filter
  ///
  /// In en, this message translates to:
  /// **'Fuel Type'**
  String get fuelType;

  /// Transmission filter
  ///
  /// In en, this message translates to:
  /// **'Transmission'**
  String get transmission;

  /// Warranty filter
  ///
  /// In en, this message translates to:
  /// **'Warranty'**
  String get warranty;

  /// OEM part filter
  ///
  /// In en, this message translates to:
  /// **'OEM Part'**
  String get oemPart;

  /// Sedans car type
  ///
  /// In en, this message translates to:
  /// **'Sedans'**
  String get sedans;

  /// SUVs car type
  ///
  /// In en, this message translates to:
  /// **'SUVs'**
  String get suvs;

  /// Hatchbacks car type
  ///
  /// In en, this message translates to:
  /// **'Hatchbacks'**
  String get hatchbacks;

  /// Pickup trucks car type
  ///
  /// In en, this message translates to:
  /// **'Pickup Trucks'**
  String get pickupTrucks;

  /// Coupes car type
  ///
  /// In en, this message translates to:
  /// **'Coupes'**
  String get coupes;

  /// Convertibles car type
  ///
  /// In en, this message translates to:
  /// **'Convertibles'**
  String get convertibles;

  /// Motorcycles vehicle type
  ///
  /// In en, this message translates to:
  /// **'Motorcycles'**
  String get motorcycles;

  /// Engine parts category
  ///
  /// In en, this message translates to:
  /// **'Engine Parts'**
  String get engineParts;

  /// Brake parts category
  ///
  /// In en, this message translates to:
  /// **'Brake Parts'**
  String get brakeParts;

  /// Electrical parts category
  ///
  /// In en, this message translates to:
  /// **'Electrical Parts'**
  String get electricalParts;

  /// Body parts category
  ///
  /// In en, this message translates to:
  /// **'Body Parts'**
  String get bodyParts;

  /// Transmission parts category
  ///
  /// In en, this message translates to:
  /// **'Transmission Parts'**
  String get transmissionParts;

  /// Suspension parts category
  ///
  /// In en, this message translates to:
  /// **'Suspension Parts'**
  String get suspensionParts;

  /// Cooling parts category
  ///
  /// In en, this message translates to:
  /// **'Cooling Parts'**
  String get coolingParts;

  /// Exhaust parts category
  ///
  /// In en, this message translates to:
  /// **'Exhaust Parts'**
  String get exhaustParts;

  /// Smart filters section
  ///
  /// In en, this message translates to:
  /// **'Smart Filters'**
  String get smartFilters;

  /// Active filters section
  ///
  /// In en, this message translates to:
  /// **'Active Filters'**
  String get activeFilters;

  /// Apply filters button
  ///
  /// In en, this message translates to:
  /// **'Apply Filters'**
  String get applyFilters;

  /// Quick suggestions section
  ///
  /// In en, this message translates to:
  /// **'Quick Suggestions'**
  String get quickSuggestions;

  /// Search by category
  ///
  /// In en, this message translates to:
  /// **'Search by Category'**
  String get searchByCategory;

  /// AI-powered search feature
  ///
  /// In en, this message translates to:
  /// **'AI-Powered Search'**
  String get aiPoweredSearch;

  /// Intelligent search feature
  ///
  /// In en, this message translates to:
  /// **'Intelligent Search'**
  String get intelligentSearch;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
