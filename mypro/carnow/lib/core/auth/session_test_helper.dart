import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'enhanced_secure_token_storage.dart';
import 'session_persistence_diagnostics.dart';
import 'session_recovery_service.dart';

/// Session Test Helper
/// مساعد اختبار الجلسة
/// 
/// This helper provides utilities to test and debug session persistence issues
/// following Forever Plan Architecture principles.
class SessionTestHelper {
  final ProviderContainer _container;

  SessionTestHelper(this._container);

  /// Test session persistence functionality
  /// اختبار وظائف استمرارية الجلسة
  Future<SessionTestResult> testSessionPersistence() async {
    debugPrint('🧪 Starting session persistence test...');
    
    final results = <String, dynamic>{};
    final issues = <String>[];
    
    try {
      // Test 1: Storage availability
      await _testStorageAvailability(results, issues);
      
      // Test 2: Encryption credentials
      await _testEncryptionCredentials(results, issues);
      
      // Test 3: Token storage and retrieval
      await _testTokenStorageRetrieval(results, issues);
      
      // Test 4: Session diagnostics
      await _testSessionDiagnostics(results, issues);
      
      // Test 5: Session recovery
      await _testSessionRecovery(results, issues);
      
      debugPrint('✅ Session persistence test completed');
      
      return SessionTestResult(
        isSuccess: issues.isEmpty,
        results: results,
        issues: issues,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      debugPrint('❌ Session persistence test failed: $e');
      
      return SessionTestResult(
        isSuccess: false,
        results: {'error': e.toString()},
        issues: ['Critical test error: $e'],
        timestamp: DateTime.now(),
      );
    }
  }

  /// Test storage availability
  Future<void> _testStorageAvailability(
    Map<String, dynamic> results,
    List<String> issues,
  ) async {
    try {
      debugPrint('🔍 Testing storage availability...');
      
      final tokenStorage = _container.read(enhancedSecureTokenStorageProvider);
      
      // Test basic storage operations
      const testToken = 'test_token_12345';
      await tokenStorage.storeToken(testToken);
      
      final retrievedToken = await tokenStorage.getToken();
      
      if (retrievedToken == testToken) {
        results['storage_test'] = 'PASS';
        debugPrint('✅ Storage test passed');
      } else {
        results['storage_test'] = 'FAIL';
        issues.add('Storage test failed: expected $testToken, got $retrievedToken');
      }
      
      // Clean up test data
      await tokenStorage.clearAllData();
      
    } catch (e) {
      results['storage_test'] = 'ERROR';
      issues.add('Storage test error: $e');
      debugPrint('❌ Storage test error: $e');
    }
  }

  /// Test encryption credentials
  Future<void> _testEncryptionCredentials(
    Map<String, dynamic> results,
    List<String> issues,
  ) async {
    try {
      debugPrint('🔍 Testing encryption credentials...');
      
      final tokenStorage = _container.read(enhancedSecureTokenStorageProvider);
      
      // Test encryption by storing and retrieving a token
      const testToken = 'encrypted_test_token_67890';
      await tokenStorage.storeToken(testToken);
      
      final retrievedToken = await tokenStorage.getToken();
      
      if (retrievedToken == testToken) {
        results['encryption_test'] = 'PASS';
        debugPrint('✅ Encryption test passed');
      } else {
        results['encryption_test'] = 'FAIL';
        issues.add('Encryption test failed: token mismatch');
      }
      
      // Clean up
      await tokenStorage.clearAllData();
      
    } catch (e) {
      results['encryption_test'] = 'ERROR';
      issues.add('Encryption test error: $e');
      debugPrint('❌ Encryption test error: $e');
    }
  }

  /// Test token storage and retrieval
  Future<void> _testTokenStorageRetrieval(
    Map<String, dynamic> results,
    List<String> issues,
  ) async {
    try {
      debugPrint('🔍 Testing token storage and retrieval...');
      
      final tokenStorage = _container.read(enhancedSecureTokenStorageProvider);
      
      // Test access token
      const accessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
      const refreshToken = 'refresh_token_12345';
      
      // Store tokens
      await tokenStorage.storeToken(accessToken);
      await tokenStorage.storeRefreshToken(refreshToken);
      
      // Retrieve tokens
      final retrievedAccessToken = await tokenStorage.getToken();
      final retrievedRefreshToken = await tokenStorage.getRefreshToken();
      
      if (retrievedAccessToken == accessToken && retrievedRefreshToken == refreshToken) {
        results['token_storage_test'] = 'PASS';
        debugPrint('✅ Token storage test passed');
      } else {
        results['token_storage_test'] = 'FAIL';
        issues.add('Token storage test failed: token mismatch');
      }
      
      // Test session data
      final sessionData = {'user': {'id': '123', 'email': '<EMAIL>'}};
      await tokenStorage.storeSessionData(sessionData);
      
      final retrievedSessionData = await tokenStorage.getSessionData();
      
      if (retrievedSessionData['user'] != null) {
        results['session_data_test'] = 'PASS';
        debugPrint('✅ Session data test passed');
      } else {
        results['session_data_test'] = 'FAIL';
        issues.add('Session data test failed');
      }
      
      // Clean up
      await tokenStorage.clearAllData();
      
    } catch (e) {
      results['token_storage_test'] = 'ERROR';
      issues.add('Token storage test error: $e');
      debugPrint('❌ Token storage test error: $e');
    }
  }

  /// Test session diagnostics
  Future<void> _testSessionDiagnostics(
    Map<String, dynamic> results,
    List<String> issues,
  ) async {
    try {
      debugPrint('🔍 Testing session diagnostics...');
      
      final diagnostics = _container.read(sessionPersistenceDiagnosticsProvider);
      final diagnosticsResult = await diagnostics.runDiagnostics();
      
      results['diagnostics_test'] = diagnosticsResult.isHealthy ? 'PASS' : 'FAIL';
      results['diagnostics_issues'] = diagnosticsResult.issues;
      results['diagnostics_recommendations'] = diagnosticsResult.recommendations;
      
      if (!diagnosticsResult.isHealthy) {
        issues.addAll(diagnosticsResult.issues);
      }
      
      debugPrint('✅ Session diagnostics test completed');
      
    } catch (e) {
      results['diagnostics_test'] = 'ERROR';
      issues.add('Diagnostics test error: $e');
      debugPrint('❌ Diagnostics test error: $e');
    }
  }

  /// Test session recovery
  Future<void> _testSessionRecovery(
    Map<String, dynamic> results,
    List<String> issues,
  ) async {
    try {
      debugPrint('🔍 Testing session recovery...');
      
      final recoveryService = _container.read(sessionRecoveryServiceProvider);
      final recoveryResult = await recoveryService.attemptSessionRecovery();
      
      results['recovery_test'] = recoveryResult.isSuccess ? 'PASS' : 'FAIL';
      results['recovery_method'] = recoveryResult.recoveryMethod;
      results['requires_auth'] = recoveryResult.requiresReauthentication;
      
      if (!recoveryResult.isSuccess && recoveryResult.error != null) {
        issues.add('Recovery test issue: ${recoveryResult.error}');
      }
      
      debugPrint('✅ Session recovery test completed');
      
    } catch (e) {
      results['recovery_test'] = 'ERROR';
      issues.add('Recovery test error: $e');
      debugPrint('❌ Recovery test error: $e');
    }
  }

  /// Print comprehensive test report
  void printTestReport(SessionTestResult result) {
    debugPrint('\n' + '=' * 60);
    debugPrint('📊 SESSION PERSISTENCE TEST REPORT');
    debugPrint('=' * 60);
    debugPrint('🕒 Test Time: ${result.timestamp}');
    debugPrint('✅ Overall Result: ${result.isSuccess ? "PASS" : "FAIL"}');
    debugPrint('');
    
    debugPrint('📋 Test Results:');
    result.results.forEach((key, value) {
      debugPrint('  $key: $value');
    });
    
    if (result.issues.isNotEmpty) {
      debugPrint('');
      debugPrint('⚠️  Issues Found:');
      for (int i = 0; i < result.issues.length; i++) {
        debugPrint('  ${i + 1}. ${result.issues[i]}');
      }
    }
    
    debugPrint('=' * 60 + '\n');
  }

  /// Clear all session data for fresh start
  Future<void> clearAllSessionData() async {
    try {
      debugPrint('🧹 Clearing all session data...');
      
      final tokenStorage = _container.read(enhancedSecureTokenStorageProvider);
      await tokenStorage.clearAllData();
      
      debugPrint('✅ All session data cleared');
    } catch (e) {
      debugPrint('❌ Failed to clear session data: $e');
      throw Exception('Failed to clear session data: $e');
    }
  }
}

/// Session test result
class SessionTestResult {
  final bool isSuccess;
  final Map<String, dynamic> results;
  final List<String> issues;
  final DateTime timestamp;

  const SessionTestResult({
    required this.isSuccess,
    required this.results,
    required this.issues,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'SessionTestResult(success: $isSuccess, issues: ${issues.length})';
  }
}

/// Create session test helper
SessionTestHelper createSessionTestHelper(ProviderContainer container) {
  return SessionTestHelper(container);
}
