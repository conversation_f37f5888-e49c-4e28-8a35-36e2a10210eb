/// ============================================================================
/// CORE AUTHENTICATION MODELS - Forever Plan Architecture
/// ============================================================================
///
/// نماذج المصادقة الأساسية - بنية الخطة الدائمة
/// Immutable data structures using Freezed for type safety
///
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ✅ Uses Freezed for immutable data structures
/// ✅ Comprehensive JSON serialization and validation
/// ✅ Type-safe sealed classes for all auth states
/// ✅ Production-ready error handling
/// ============================================================================
// ignore_for_file: invalid_annotation_target

library;

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'auth_models.freezed.dart';
part 'auth_models.g.dart';

// =============================================================================
// ENUMS - Authentication Provider Types
// =============================================================================

/// Authentication provider types
enum AuthProvider {
  @JsonValue('email')
  email,
  @JsonValue('google')
  google,
  @JsonValue('apple')
  apple,
}

/// Authentication operation types
enum AuthOperation {
  signIn,
  signUp,
  signOut,
  refreshToken,
  verifyEmail,
  resetPassword,
}

/// Authentication error types for comprehensive error handling
/// أنواع أخطاء المصادقة للمعالجة الشاملة للأخطاء
enum AuthErrorType {
  @JsonValue('unknown')
  unknown,
  @JsonValue('network')
  network,
  @JsonValue('invalid_credentials')
  invalidCredentials,
  @JsonValue('user_not_found')
  userNotFound,
  @JsonValue('email_already_exists')
  emailAlreadyExists,
  @JsonValue('weak_password')
  weakPassword,
  @JsonValue('invalid_email')
  invalidEmail,
  @JsonValue('session_expired')
  sessionExpired,
  @JsonValue('rate_limit_exceeded')
  rateLimitExceeded,
  @JsonValue('server_error')
  serverError,
  @JsonValue('oauth_error')
  oauthError,
  @JsonValue('verification_required')
  verificationRequired,
  @JsonValue('network_error')
  networkError,
  @JsonValue('email_not_verified')
  emailNotVerified,
  @JsonValue('account_disabled')
  accountDisabled,
  @JsonValue('service_unavailable')
  serviceUnavailable,
  @JsonValue('too_many_attempts')
  tooManyAttempts,
  @JsonValue('security_violation')
  securityViolation,
}

// =============================================================================
// USER MODEL - Production-Ready with Comprehensive Validation
// =============================================================================

/// Enhanced User model for the unified authentication system
/// Includes comprehensive validation, helper methods, and type safety
@freezed
abstract class User with _$User {
  const factory User({
    /// Unique user identifier from backend
    required String id,
    
    /// User's email address (validated)
    required String email,
    
    /// User's first name
    @JsonKey(name: 'first_name') required String firstName,
    
    /// User's last name
    @JsonKey(name: 'last_name') required String lastName,
    
    /// User's display name (computed from first + last name)
    @JsonKey(name: 'display_name') String? displayName,
    
    /// User's phone number (optional)
    @JsonKey(name: 'phone_number') String? phoneNumber,
    
    /// User's avatar/profile picture URL
    @JsonKey(name: 'avatar_url') String? avatarUrl,
    
    /// Whether the user account is active
    @JsonKey(name: 'is_active') @Default(true) bool isActive,
    
    /// Whether the user's email is verified
    @JsonKey(name: 'email_verified') @Default(false) bool emailVerified,
    
    /// Authentication provider (email, google)
    @JsonKey(name: 'auth_provider') @Default(AuthProvider.email) AuthProvider authProvider,
    
    /// User's preferred language/locale
    @JsonKey(name: 'locale') @Default('en') String locale,
    
    /// User's timezone
    @JsonKey(name: 'timezone') String? timezone,
    
    /// Last login timestamp
    @JsonKey(name: 'last_login_at') DateTime? lastLoginAt,
    
    /// Account creation timestamp
    @JsonKey(name: 'created_at') required DateTime createdAt,
    
    /// Last update timestamp
    @JsonKey(name: 'updated_at') required DateTime updatedAt,
  }) = _User;
  
  const User._();

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  
  /// Helper method to get full name
  String get fullName => displayName ?? '$firstName $lastName'.trim();
  
  /// Helper method to get initials
  String get initials {
    final first = firstName.isNotEmpty ? firstName[0].toUpperCase() : '';
    final last = lastName.isNotEmpty ? lastName[0].toUpperCase() : '';
    return '$first$last';
  }
  
  /// Helper method to check if user has complete profile
  bool get hasCompleteProfile {
    return firstName.isNotEmpty && 
           lastName.isNotEmpty && 
           emailVerified && 
           isActive;
  }
  
  /// Helper method to get display avatar (URL or initials)
  String get displayAvatar => avatarUrl ?? initials;
}

// =============================================================================
// AUTH STATE - Comprehensive State Management
// =============================================================================

/// Enhanced Authentication state with comprehensive type safety
/// Covers all possible authentication states in the application
@freezed
abstract class AuthState with _$AuthState {
  /// Initial state when app starts
  const factory AuthState.initial() = AuthStateInitial;
  
  /// Loading state during authentication operations
  const factory AuthState.loading({
    /// Optional message to show during loading
    String? message,
    /// Type of operation being performed
    @Default(AuthOperation.signIn) AuthOperation operation,
  }) = AuthStateLoading;
  
  /// Successfully authenticated state
  const factory AuthState.authenticated({
    /// Authenticated user information
    required User user,
    /// JWT access token
    required String token,
    /// Optional refresh token
    String? refreshToken,
    /// Token expiry timestamp
    @JsonKey(name: 'token_expiry') DateTime? tokenExpiry,
    /// Session start time
    @JsonKey(name: 'session_start') DateTime? sessionStart,
  }) = AuthStateAuthenticated;
  
  /// Not authenticated state
  const factory AuthState.unauthenticated({
    /// Optional reason for being unauthenticated
    String? reason,
  }) = AuthStateUnauthenticated;
  
  /// Error state with detailed error information
  const factory AuthState.error({
    /// Error message
    required String message,
    /// Error code for programmatic handling
    String? errorCode,
    /// Type of error
    @Default(AuthErrorType.unknown) AuthErrorType errorType,
    /// Whether the error is recoverable
    @Default(true) bool isRecoverable,
    /// Original exception if available
    Object? originalException,
  }) = AuthStateError;
  
  /// Email verification pending state
  const factory AuthState.emailVerificationPending({
    /// User's email that needs verification
    required String email,
    /// When verification email was sent
    DateTime? sentAt,
  }) = AuthStateEmailVerificationPending;
  
  /// Session expired state
  const factory AuthState.sessionExpired({
    /// When the session expired
    DateTime? expiredAt,
    /// Whether auto-refresh was attempted
    @Default(false) bool autoRefreshAttempted,
  }) = AuthStateSessionExpired;
}

// =============================================================================
// AUTH RESULT - Comprehensive Result Types
// =============================================================================

/// Enhanced Authentication results with comprehensive type safety
/// Covers all possible authentication operation results
@freezed
abstract class AuthResult with _$AuthResult {
  /// Successful authentication result
  const factory AuthResult.success({
    /// Authenticated user information
    required User user,
    /// JWT access token
    required String token,
    /// Optional refresh token
    String? refreshToken,
    /// Token expiry timestamp
    DateTime? tokenExpiry,
    /// Additional metadata
    Map<String, dynamic>? metadata,
  }) = AuthResultSuccess;
  
  /// Failed authentication result
  const factory AuthResult.failure({
    /// Error message
    required String error,
    /// Error code for programmatic handling
    String? errorCode,
    /// Type of error
    @Default(AuthErrorType.unknown) AuthErrorType errorType,
    /// Whether the error is recoverable
    @Default(true) bool isRecoverable,
    /// Additional error details
    Map<String, dynamic>? details,
  }) = AuthResultFailure;
  
  /// Cancelled authentication result (user cancelled)
  const factory AuthResult.cancelled({
    /// Optional reason for cancellation
    String? reason,
  }) = AuthResultCancelled;
  
  /// Pending result (requires additional action)
  const factory AuthResult.pending({
    /// Message describing what action is needed
    required String message,
    /// Type of pending action
    @Default(AuthOperation.verifyEmail) AuthOperation pendingAction,
    /// Additional data for the pending action
    Map<String, dynamic>? actionData,
  }) = AuthResultPending;
}

/// Authentication data model for API responses
@freezed
abstract class AuthData with _$AuthData {
  const factory AuthData({
    required User user,
    @JsonKey(name: 'access_token') required String accessToken,
    @JsonKey(name: 'refresh_token') String? refreshToken,
    @JsonKey(name: 'expires_in') required int expiresIn,
  }) = _AuthData;

  factory AuthData.fromJson(Map<String, dynamic> json) =>
      _$AuthDataFromJson(json);
}

/// Login request model
@freezed
abstract class LoginRequest with _$LoginRequest {
  const factory LoginRequest({
    required String email,
    required String password,
  }) = _LoginRequest;

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);
}

/// Registration request model
@freezed
abstract class RegisterRequest with _$RegisterRequest {
  const factory RegisterRequest({
    required String name,
    required String email,
    required String password,
  }) = _RegisterRequest;

  factory RegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$RegisterRequestFromJson(json);
}

/// Google OAuth request model
@freezed
abstract class GoogleAuthRequest with _$GoogleAuthRequest {
  const factory GoogleAuthRequest({
    @JsonKey(name: 'id_token') required String idToken,
  }) = _GoogleAuthRequest;

  factory GoogleAuthRequest.fromJson(Map<String, dynamic> json) =>
      _$GoogleAuthRequestFromJson(json);
}

/// Token refresh request model
@freezed
abstract class RefreshTokenRequest with _$RefreshTokenRequest {
  const factory RefreshTokenRequest({
    @JsonKey(name: 'refresh_token') required String refreshToken,
  }) = _RefreshTokenRequest;

  factory RefreshTokenRequest.fromJson(Map<String, dynamic> json) =>
      _$RefreshTokenRequestFromJson(json);
}

/// API response wrapper for authentication operations
@freezed
abstract class AuthResponse with _$AuthResponse {
  const factory AuthResponse({
    required bool success,
    Map<String, dynamic>? data,
    String? error,
    @JsonKey(name: 'error_code') String? errorCode,
  }) = _AuthResponse;

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);
}
