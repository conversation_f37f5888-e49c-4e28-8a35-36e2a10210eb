/// ============================================================================
/// TOKEN STORAGE MIGRATION - Forever Plan Architecture
/// ============================================================================
///
/// أداة ترحيل تخزين الرموز المميزة - بنية الخطة الدائمة
/// Migration utility to move tokens from old storage keys to new v2 keys
///
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ✅ Migrates tokens from carnow_access_token to carnow_access_token_v2
/// ✅ Preserves user sessions during app updates
/// ✅ Handles encryption format changes
/// ✅ Automatic cleanup of old storage keys
/// ============================================================================
library;

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'enhanced_secure_token_storage.dart';

part 'token_storage_migration.g.dart';

/// Migration service for token storage
class TokenStorageMigration {
  static const String _migrationCompleteKey = 'carnow_migration_v2_complete';
  
  // Old storage keys (v1)
  static const String _oldAccessTokenKey = 'carnow_access_token';
  static const String _oldRefreshTokenKey = 'carnow_refresh_token';
  static const String _oldTokenExpiryKey = 'carnow_token_expiry';
  static const String _oldSessionDataKey = 'carnow_session_data';
  
  final FlutterSecureStorage _secureStorage;
  final EnhancedSecureTokenStorage _newStorage;
  
  TokenStorageMigration({
    FlutterSecureStorage? secureStorage,
    EnhancedSecureTokenStorage? newStorage,
  }) : _secureStorage = secureStorage ?? const FlutterSecureStorage(
         aOptions: AndroidOptions(encryptedSharedPreferences: true),
         iOptions: IOSOptions(accessibility: KeychainAccessibility.first_unlock_this_device),
       ),
       _newStorage = newStorage ?? EnhancedSecureTokenStorage();

  /// Check if migration is needed and perform it
  Future<bool> migrateIfNeeded() async {
    try {
      // Check if migration already completed
      final migrationComplete = await _secureStorage.read(key: _migrationCompleteKey);
      if (migrationComplete == 'true') {
        debugPrint('🔄 Token storage migration already completed');
        return true;
      }

      debugPrint('🔄 Starting token storage migration from v1 to v2...');
      
      // Check if old tokens exist
      final oldAccessToken = await _secureStorage.read(key: _oldAccessTokenKey);
      final oldRefreshToken = await _secureStorage.read(key: _oldRefreshTokenKey);
      final oldTokenExpiry = await _secureStorage.read(key: _oldTokenExpiryKey);
      final oldSessionData = await _secureStorage.read(key: _oldSessionDataKey);
      
      if (oldAccessToken == null && oldRefreshToken == null) {
        debugPrint('🔄 No old tokens found, marking migration as complete');
        await _markMigrationComplete();
        return true;
      }

      debugPrint('🔄 Found old tokens, migrating to v2 storage...');
      
      // Migrate access token
      if (oldAccessToken != null) {
        DateTime? expiryDate;
        if (oldTokenExpiry != null) {
          try {
            expiryDate = DateTime.parse(oldTokenExpiry);
          } catch (e) {
            debugPrint('⚠️ Failed to parse old token expiry: $e');
          }
        }
        
        await _newStorage.storeToken(oldAccessToken, expiryDate: expiryDate);
        debugPrint('✅ Migrated access token');
      }
      
      // Migrate refresh token
      if (oldRefreshToken != null) {
        await _newStorage.storeRefreshToken(oldRefreshToken);
        debugPrint('✅ Migrated refresh token');
      }
      
      // Migrate session data
      if (oldSessionData != null) {
        try {
          final sessionMap = jsonDecode(oldSessionData) as Map<String, dynamic>;
          await _newStorage.storeSessionData(sessionMap);
          debugPrint('✅ Migrated session data');
        } catch (e) {
          debugPrint('⚠️ Failed to migrate session data: $e');
        }
      }
      
      // Clean up old storage keys
      await _cleanupOldStorage();
      
      // Mark migration as complete
      await _markMigrationComplete();
      
      debugPrint('🎯 Token storage migration completed successfully');
      return true;
      
    } catch (e) {
      debugPrint('❌ Token storage migration failed: $e');
      return false;
    }
  }
  
  /// Clean up old storage keys
  Future<void> _cleanupOldStorage() async {
    try {
      final oldKeys = [
        _oldAccessTokenKey,
        _oldRefreshTokenKey,
        _oldTokenExpiryKey,
        _oldSessionDataKey,
      ];
      
      for (final key in oldKeys) {
        try {
          await _secureStorage.delete(key: key);
          debugPrint('🧹 Cleaned up old key: $key');
        } catch (e) {
          debugPrint('⚠️ Failed to clean up key $key: $e');
        }
      }
    } catch (e) {
      debugPrint('❌ Failed to cleanup old storage: $e');
    }
  }
  
  /// Mark migration as complete
  Future<void> _markMigrationComplete() async {
    try {
      await _secureStorage.write(key: _migrationCompleteKey, value: 'true');
    } catch (e) {
      debugPrint('❌ Failed to mark migration complete: $e');
    }
  }
  
  /// Force reset migration status (for testing)
  Future<void> resetMigrationStatus() async {
    try {
      await _secureStorage.delete(key: _migrationCompleteKey);
      debugPrint('🔄 Migration status reset');
    } catch (e) {
      debugPrint('❌ Failed to reset migration status: $e');
    }
  }
}

// =============================================================================
// RIVERPOD PROVIDERS
// =============================================================================

/// Provider for token storage migration
@riverpod
TokenStorageMigration tokenStorageMigration(Ref ref) {
  final newStorage = ref.read(enhancedSecureTokenStorageProvider);
  return TokenStorageMigration(newStorage: newStorage);
}
