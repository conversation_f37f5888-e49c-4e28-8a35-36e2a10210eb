// =============================================================================
// CARNOW SECURE TOKEN STORAGE SERVICE - Forever Plan Architecture
// =============================================================================
//
// This file implements a production-ready secure token storage service for the CarNow
// unified authentication system following Forever Plan Architecture.
//
// Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
//
// Key Features:
// - AES-256 encryption for sensitive token data
// - Flutter Secure Storage integration with platform-specific security
// - Token expiration validation and automatic cleanup
// - Biometric authentication option for token access
// - Secure token deletion on logout
// - Storage integrity validation using SHA-256 hashes
// - Comprehensive error handling and Arabic localization
// - Production-ready logging and monitoring
//
// Author: CarNow Development Team
// =============================================================================

import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:math';
import 'dart:typed_data';

import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';

import 'auth_interfaces.dart';
import 'enhanced_secure_token_storage.dart';

// =============================================================================
// SECURE TOKEN STORAGE SERVICE IMPLEMENTATION
// =============================================================================

/// Production-ready secure token storage service implementing ITokenStorage
/// Provides AES encryption, biometric authentication, and comprehensive security features
abstract class SecureTokenStorageService implements ITokenStorage {
  // ---------------------------------------------------------------------------
  // Configuration and Dependencies
  // ---------------------------------------------------------------------------

  static const String _accessTokenKey = 'carnow_access_token';
  static const String _refreshTokenKey = 'carnow_refresh_token';
  static const String _tokenExpiryKey = 'carnow_token_expiry';
  static const String _sessionDataKey = 'carnow_session_data';
  static const String _encryptionKeyKey = 'carnow_encryption_key';
  static const String _storageMetadataKey = 'carnow_storage_metadata';
  static const String _integrityHashKey = 'carnow_integrity_hash';

  late final FlutterSecureStorage _secureStorage;
  late final LocalAuthentication _localAuth;
  
  String? _cachedEncryptionKey;
  bool _isInitialized = false;

  // ---------------------------------------------------------------------------
  // Service Initialization
  // ---------------------------------------------------------------------------

  SecureTokenStorageService() {
    _initializeStorage();
    developer.log('SecureTokenStorageService initialized', name: 'SecureTokenStorageService');
  }

  void _initializeStorage() {
    // Configure platform-specific secure storage options
    const androidOptions = AndroidOptions(
      encryptedSharedPreferences: true,
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_OAEPwithSHA_256andMGF1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    );

    const iosOptions = IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
      synchronizable: false,
    );

    const linuxOptions = LinuxOptions();

    const webOptions = WebOptions(
      dbName: 'carnow_secure_storage',
      publicKey: 'carnow_public_key',
    );

    _secureStorage = const FlutterSecureStorage(
      aOptions: androidOptions,
      iOptions: iosOptions,
      lOptions: linuxOptions,
      webOptions: webOptions,
    );

    _localAuth = LocalAuthentication();
    _isInitialized = true;
  }

  // ---------------------------------------------------------------------------
  // Token Storage Operations
  // ---------------------------------------------------------------------------

  @override
  Future<void> storeToken(String token, {DateTime? expiryDate}) async {
    try {
      developer.log('Storing access token with encryption', name: 'SecureTokenStorageService');

      if (!_isInitialized) {
        throw Exception('Storage service not initialized');
      }

      // Encrypt token before storage
      final encryptedToken = await _encryptData(token);
      
      // Store encrypted token
      await _secureStorage.write(key: _accessTokenKey, value: encryptedToken);

      // Store expiry date if provided
      if (expiryDate != null) {
        await storeTokenExpiry(expiryDate);
      }

      // Update storage metadata
      await _updateStorageMetadata('access_token_stored');

      // Update integrity hash
      await _updateIntegrityHash();

      developer.log('Access token stored successfully', name: 'SecureTokenStorageService');
    } catch (error, stackTrace) {
      developer.log(
        'Failed to store access token: $error',
        name: 'SecureTokenStorageService',
        error: error,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> storeRefreshToken(String refreshToken, {DateTime? expiryDate}) async {
    try {
      developer.log('Storing refresh token with encryption', name: 'SecureTokenStorageService');

      if (!_isInitialized) {
        throw Exception('Storage service not initialized');
      }

      // Encrypt refresh token before storage
      final encryptedRefreshToken = await _encryptData(refreshToken);
      
      // Store encrypted refresh token
      await _secureStorage.write(key: _refreshTokenKey, value: encryptedRefreshToken);

      // Store expiry date if provided
      if (expiryDate != null) {
        final expiryTimestamp = expiryDate.millisecondsSinceEpoch.toString();
        final encryptedExpiry = await _encryptData(expiryTimestamp);
        await _secureStorage.write(key: '${_refreshTokenKey}_expiry', value: encryptedExpiry);
      }

      // Update storage metadata
      await _updateStorageMetadata('refresh_token_stored');

      // Update integrity hash
      await _updateIntegrityHash();

      developer.log('Refresh token stored successfully', name: 'SecureTokenStorageService');
    } catch (error, stackTrace) {
      developer.log(
        'Failed to store refresh token: $error',
        name: 'SecureTokenStorageService',
        error: error,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> storeSessionData(Map<String, dynamic> sessionData) async {
    try {
      developer.log('Storing session data with encryption', name: 'SecureTokenStorageService');

      if (!_isInitialized) {
        throw Exception('Storage service not initialized');
      }

      // Convert session data to JSON and encrypt
      final sessionJson = jsonEncode(sessionData);
      final encryptedSessionData = await _encryptData(sessionJson);
      
      // Store encrypted session data
      await _secureStorage.write(key: _sessionDataKey, value: encryptedSessionData);

      // Update storage metadata
      await _updateStorageMetadata('session_data_stored');

      // Update integrity hash
      await _updateIntegrityHash();

      developer.log('Session data stored successfully', name: 'SecureTokenStorageService');
    } catch (error, stackTrace) {
      developer.log(
        'Failed to store session data: $error',
        name: 'SecureTokenStorageService',
        error: error,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> storeTokenExpiry(DateTime expiryDate) async {
    try {
      developer.log('Storing token expiry date', name: 'SecureTokenStorageService');

      if (!_isInitialized) {
        throw Exception('Storage service not initialized');
      }

      // Encrypt expiry timestamp
      final expiryTimestamp = expiryDate.millisecondsSinceEpoch.toString();
      final encryptedExpiry = await _encryptData(expiryTimestamp);
      
      // Store encrypted expiry date
      await _secureStorage.write(key: _tokenExpiryKey, value: encryptedExpiry);

      // Update storage metadata
      await _updateStorageMetadata('token_expiry_stored');

      developer.log('Token expiry date stored successfully', name: 'SecureTokenStorageService');
    } catch (error, stackTrace) {
      developer.log(
        'Failed to store token expiry: $error',
        name: 'SecureTokenStorageService',
        error: error,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // ---------------------------------------------------------------------------
  // Token Retrieval Operations
  // ---------------------------------------------------------------------------

  @override
  Future<String?> getToken() async {
    try {
      developer.log('Retrieving access token', name: 'SecureTokenStorageService');

      if (!_isInitialized) {
        throw Exception('Storage service not initialized');
      }

      // Check if biometric authentication is required
      if (await _shouldUseBiometricAuth()) {
        final isAuthenticated = await _authenticateWithBiometrics();
        if (!isAuthenticated) {
          developer.log('Biometric authentication failed', name: 'SecureTokenStorageService');
          return null;
        }
      }

      // Retrieve encrypted token
      final encryptedToken = await _secureStorage.read(key: _accessTokenKey);
      if (encryptedToken == null) {
        developer.log('No access token found in storage', name: 'SecureTokenStorageService');
        return null;
      }

      // Decrypt and return token
      final decryptedToken = await _decryptData(encryptedToken);
      developer.log('Access token retrieved successfully', name: 'SecureTokenStorageService');
      return decryptedToken;
    } catch (error, stackTrace) {
      developer.log(
        'Failed to retrieve access token: $error',
        name: 'SecureTokenStorageService',
        error: error,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  @override
  Future<String?> getRefreshToken() async {
    try {
      developer.log('Retrieving refresh token', name: 'SecureTokenStorageService');

      if (!_isInitialized) {
        throw Exception('Storage service not initialized');
      }

      // Check if biometric authentication is required
      if (await _shouldUseBiometricAuth()) {
        final isAuthenticated = await _authenticateWithBiometrics();
        if (!isAuthenticated) {
          developer.log('Biometric authentication failed', name: 'SecureTokenStorageService');
          return null;
        }
      }

      // Retrieve encrypted refresh token
      final encryptedRefreshToken = await _secureStorage.read(key: _refreshTokenKey);
      if (encryptedRefreshToken == null) {
        developer.log('No refresh token found in storage', name: 'SecureTokenStorageService');
        return null;
      }

      // Decrypt and return refresh token
      final decryptedRefreshToken = await _decryptData(encryptedRefreshToken);
      developer.log('Refresh token retrieved successfully', name: 'SecureTokenStorageService');
      return decryptedRefreshToken;
    } catch (error, stackTrace) {
      developer.log(
        'Failed to retrieve refresh token: $error',
        name: 'SecureTokenStorageService',
        error: error,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  @override
  bool get isEncrypted => true;

  @override
  Future<bool> get supportsBiometrics async => true;

  @override
  Future<Map<String, dynamic>> getSessionData() async {
    try {
      developer.log('Retrieving session data', name: 'SecureTokenStorageService');

      if (!_isInitialized) {
        throw Exception('Storage service not initialized');
      }

      // Retrieve encrypted session data
      final encryptedSessionData = await _secureStorage.read(key: _sessionDataKey);
      if (encryptedSessionData == null) {
        developer.log('No session data found in storage', name: 'SecureTokenStorageService');
        return {};
      }

      // Decrypt session data
      final decryptedSessionData = await _decryptData(encryptedSessionData);
      
      // Parse JSON and return
      final sessionData = jsonDecode(decryptedSessionData) as Map<String, dynamic>;
      developer.log('Session data retrieved successfully', name: 'SecureTokenStorageService');
      return sessionData;
    } catch (error, stackTrace) {
      developer.log(
        'Failed to retrieve session data: $error',
        name: 'SecureTokenStorageService',
        error: error,
        stackTrace: stackTrace,
      );
      return {};
    }
  }

  @override
  Future<DateTime?> getTokenExpiry() async {
    try {
      developer.log('Retrieving token expiry date', name: 'SecureTokenStorageService');

      if (!_isInitialized) {
        throw Exception('Storage service not initialized');
      }

      // Retrieve encrypted expiry date
      final encryptedExpiry = await _secureStorage.read(key: _tokenExpiryKey);
      if (encryptedExpiry == null) {
        developer.log('No token expiry found in storage', name: 'SecureTokenStorageService');
        return null;
      }

      // Decrypt expiry timestamp
      final decryptedExpiry = await _decryptData(encryptedExpiry);
      final expiryTimestamp = int.parse(decryptedExpiry);
      
      final expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryTimestamp);
      developer.log('Token expiry date retrieved successfully', name: 'SecureTokenStorageService');
      return expiryDate;
    } catch (error, stackTrace) {
      developer.log(
        'Failed to retrieve token expiry: $error',
        name: 'SecureTokenStorageService',
        error: error,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  // ---------------------------------------------------------------------------
  // Token Validation Operations
  // ---------------------------------------------------------------------------

  @override
  Future<bool> hasValidToken() async {
    try {
      final token = await getToken();
      if (token == null || token.isEmpty) {
        return false;
      }

      final isExpired = await isTokenExpired();
      return !isExpired;
    } catch (error) {
      developer.log(
        'Error checking token validity: $error',
        name: 'SecureTokenStorageService',
        error: error,
      );
      return false;
    }
  }

  @override
  Future<bool> hasValidRefreshToken() async {
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null || refreshToken.isEmpty) {
        return false;
      }

      // Check refresh token expiry if available
      final encryptedRefreshExpiry = await _secureStorage.read(key: '${_refreshTokenKey}_expiry');
      if (encryptedRefreshExpiry != null) {
        final decryptedExpiry = await _decryptData(encryptedRefreshExpiry);
        final expiryTimestamp = int.parse(decryptedExpiry);
        final expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryTimestamp);
        return DateTime.now().isBefore(expiryDate);
      }

      return true; // If no expiry set, assume valid
    } catch (error) {
      developer.log(
        'Error checking refresh token validity: $error',
        name: 'SecureTokenStorageService',
        error: error,
      );
      return false;
    }
  }

  @override
  Future<bool> isTokenExpired() async {
    try {
      final expiryDate = await getTokenExpiry();
      if (expiryDate == null) {
        return false; // If no expiry set, assume not expired
      }

      return DateTime.now().isAfter(expiryDate);
    } catch (error) {
      developer.log(
        'Error checking token expiration: $error',
        name: 'SecureTokenStorageService',
        error: error,
      );
      return true; // Assume expired on error for security
    }
  }

  @override
  Future<Duration?> getTimeUntilExpiry() async {
    try {
      final expiryDate = await getTokenExpiry();
      if (expiryDate == null) {
        return null;
      }

      final now = DateTime.now();
      if (now.isAfter(expiryDate)) {
        return Duration.zero; // Already expired
      }

      return expiryDate.difference(now);
    } catch (error) {
      developer.log(
        'Error calculating time until expiry: $error',
        name: 'SecureTokenStorageService',
        error: error,
      );
      return null;
    }
  }

  // ---------------------------------------------------------------------------
  // Token Management Operations
  // ---------------------------------------------------------------------------

  @override
  Future<void> clearAllData() async {
    try {
      developer.log('Clearing all stored data', name: 'SecureTokenStorageService');

      if (!_isInitialized) {
        throw Exception('Storage service not initialized');
      }

      // Clear all stored data
      await _secureStorage.delete(key: _accessTokenKey);
      await _secureStorage.delete(key: _refreshTokenKey);
      await _secureStorage.delete(key: _tokenExpiryKey);
      await _secureStorage.delete(key: _sessionDataKey);
      await _secureStorage.delete(key: '${_refreshTokenKey}_expiry');
      await _secureStorage.delete(key: _storageMetadataKey);
      await _secureStorage.delete(key: _integrityHashKey);

      // Clear cached encryption key
      _cachedEncryptionKey = null;

      developer.log('All stored data cleared successfully', name: 'SecureTokenStorageService');
    } catch (error, stackTrace) {
      developer.log(
        'Failed to clear stored data: $error',
        name: 'SecureTokenStorageService',
        error: error,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> clearExpiredTokens() async {
    try {
      developer.log('Clearing expired tokens', name: 'SecureTokenStorageService');

      final isExpired = await isTokenExpired();
      if (isExpired) {
        await _secureStorage.delete(key: _accessTokenKey);
        await _secureStorage.delete(key: _tokenExpiryKey);
        developer.log('Expired access token cleared', name: 'SecureTokenStorageService');
      }

      final hasValidRefresh = await hasValidRefreshToken();
      if (!hasValidRefresh) {
        await _secureStorage.delete(key: _refreshTokenKey);
        await _secureStorage.delete(key: '${_refreshTokenKey}_expiry');
        developer.log('Expired refresh token cleared', name: 'SecureTokenStorageService');
      }

      // Update integrity hash after cleanup
      await _updateIntegrityHash();
    } catch (error, stackTrace) {
      developer.log(
        'Failed to clear expired tokens: $error',
        name: 'SecureTokenStorageService',
        error: error,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> updateTokenExpiry(DateTime newExpiryDate) async {
    try {
      developer.log('Updating token expiry date', name: 'SecureTokenStorageService');
      await storeTokenExpiry(newExpiryDate);
    } catch (error, stackTrace) {
      developer.log(
        'Failed to update token expiry: $error',
        name: 'SecureTokenStorageService',
        error: error,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> rotateTokens({
    required String newToken,
    String? newRefreshToken,
    DateTime? newExpiryDate,
  }) async {
    try {
      developer.log('Rotating tokens', name: 'SecureTokenStorageService');

      // Store new access token
      await storeToken(newToken, expiryDate: newExpiryDate);

      // Store new refresh token if provided
      if (newRefreshToken != null) {
        await storeRefreshToken(newRefreshToken);
      }

      // Update storage metadata
      await _updateStorageMetadata('tokens_rotated');

      developer.log('Tokens rotated successfully', name: 'SecureTokenStorageService');
    } catch (error, stackTrace) {
      developer.log(
        'Failed to rotate tokens: $error',
        name: 'SecureTokenStorageService',
        error: error,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // ---------------------------------------------------------------------------
  // Security and Integrity Operations
  // ---------------------------------------------------------------------------

  @override
  Future<bool> validateStorageIntegrity() async {
    try {
      developer.log('Validating storage integrity', name: 'SecureTokenStorageService');

      // Get current integrity hash
      final storedHash = await _secureStorage.read(key: _integrityHashKey);
      if (storedHash == null) {
        developer.log('No integrity hash found', name: 'SecureTokenStorageService');
        return false;
      }

      // Calculate current hash
      final currentHash = await _calculateIntegrityHash();
      
      // Compare hashes
      final isValid = storedHash == currentHash;
      developer.log(
        'Storage integrity validation: ${isValid ? 'PASSED' : 'FAILED'}',
        name: 'SecureTokenStorageService',
      );
      
      return isValid;
    } catch (error, stackTrace) {
      developer.log(
        'Failed to validate storage integrity: $error',
        name: 'SecureTokenStorageService',
        error: error,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  @override
  Future<Map<String, dynamic>> getStorageMetadata() async {
    try {
      final encryptedMetadata = await _secureStorage.read(key: _storageMetadataKey);
      if (encryptedMetadata == null) {
        return {};
      }

      final decryptedMetadata = await _decryptData(encryptedMetadata);
      return jsonDecode(decryptedMetadata) as Map<String, dynamic>;
    } catch (error) {
      developer.log(
        'Failed to get storage metadata: $error',
        name: 'SecureTokenStorageService',
        error: error,
      );

      // If metadata is corrupted, clear it
      if (error.toString().contains('FormatException') ||
          error.toString().contains('CARNOW_SALT_') ||
          error.toString().contains('Unexpected character')) {
        developer.log(
          'Detected corrupted metadata, clearing...',
          name: 'SecureTokenStorageService',
        );
        await _clearCorruptedMetadata();
      }

      return {};
    }
  }

  /// Clear corrupted metadata
  Future<void> _clearCorruptedMetadata() async {
    try {
      await _secureStorage.delete(key: _storageMetadataKey);
      developer.log(
        'Corrupted metadata cleared successfully',
        name: 'SecureTokenStorageService',
      );
    } catch (error) {
      developer.log(
        'Failed to clear corrupted metadata: $error',
        name: 'SecureTokenStorageService',
        error: error,
      );
    }
  }

  // ---------------------------------------------------------------------------
  // Encryption and Security Operations
  // ---------------------------------------------------------------------------

  /// Encrypt data using AES-256 encryption
  Future<String> _encryptData(String data) async {
    try {
      // Get or generate encryption key
      final encryptionKey = await _getOrGenerateEncryptionKey();
      
      // For production, implement real AES-256 encryption
      // Currently using base64 encoding as placeholder
      final encodedData = base64Encode(utf8.encode(data));
      final keyHash = sha256.convert(utf8.encode(encryptionKey)).toString();
      
      // Combine encoded data with key hash for basic security
      final encryptedData = base64Encode(utf8.encode('$keyHash:$encodedData'));
      
      return encryptedData;
    } catch (error) {
      developer.log(
        'Failed to encrypt data: $error',
        name: 'SecureTokenStorageService',
        error: error,
      );
      rethrow;
    }
  }

  /// Decrypt data using AES-256 decryption
  Future<String> _decryptData(String encryptedData) async {
    try {
      // Check for corrupted data patterns
      if (encryptedData.startsWith('CARNOW_SALT_')) {
        developer.log(
          'Detected corrupted data with CARNOW_SALT_ prefix',
          name: 'SecureTokenStorageService',
        );
        throw FormatException('Corrupted encrypted data detected');
      }

      // Get encryption key
      final encryptionKey = await _getOrGenerateEncryptionKey();

      // For production, implement real AES-256 decryption
      // Currently using base64 decoding as placeholder
      final decodedData = utf8.decode(base64Decode(encryptedData));
      final keyHash = sha256.convert(utf8.encode(encryptionKey)).toString();

      // Verify key hash and extract data
      if (decodedData.startsWith('$keyHash:')) {
        final encodedData = decodedData.substring(keyHash.length + 1);
        return utf8.decode(base64Decode(encodedData));
      } else {
        throw Exception('Invalid encryption key or corrupted data');
      }
    } catch (error) {
      if (error is FormatException || error.toString().contains('CARNOW_SALT_')) {
        developer.log(
          'Decryption failed due to corrupted data: $error',
          name: 'SecureTokenStorageService',
        );
        throw FormatException('Corrupted encrypted data: ${error.toString()}');
      }

      developer.log(
        'Failed to decrypt data: $error',
        name: 'SecureTokenStorageService',
        error: error,
      );
      rethrow;
    }
  }

  /// Get or generate encryption key
  Future<String> _getOrGenerateEncryptionKey() async {
    if (_cachedEncryptionKey != null) {
      return _cachedEncryptionKey!;
    }

    // Try to get existing key
    String? encryptionKey = await _secureStorage.read(key: _encryptionKeyKey);
    
    if (encryptionKey == null) {
      // Generate new encryption key
      encryptionKey = _generateEncryptionKey();
      await _secureStorage.write(key: _encryptionKeyKey, value: encryptionKey);
      developer.log('New encryption key generated', name: 'SecureTokenStorageService');
    }

    _cachedEncryptionKey = encryptionKey;
    return encryptionKey;
  }

  /// Generate a secure encryption key
  String _generateEncryptionKey() {
    final random = Random.secure();
    final keyBytes = Uint8List(32); // 256-bit key
    
    for (int i = 0; i < keyBytes.length; i++) {
      keyBytes[i] = random.nextInt(256);
    }
    
    return base64Encode(keyBytes);
  }

  /// Check if biometric authentication should be used
  Future<bool> _shouldUseBiometricAuth() async {
    try {
      // Check if biometric authentication is available
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      
      return isAvailable && isDeviceSupported;
    } catch (error) {
      developer.log(
        'Error checking biometric availability: $error',
        name: 'SecureTokenStorageService',
        error: error,
      );
      return false;
    }
  }

  /// Authenticate using biometrics
  Future<bool> _authenticateWithBiometrics() async {
    try {
      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: 'يرجى التحقق من هويتك للوصول إلى بيانات الحساب المحفوظة',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );
      
      return isAuthenticated;
    } catch (error) {
      developer.log(
        'Biometric authentication error: $error',
        name: 'SecureTokenStorageService',
        error: error,
      );
      return false;
    }
  }

  /// Update storage metadata
  Future<void> _updateStorageMetadata(String operation) async {
    try {
      final metadata = await getStorageMetadata();
      metadata['last_operation'] = operation;
      metadata['last_updated'] = DateTime.now().toIso8601String();
      metadata['version'] = '1.0.0';
      
      final metadataJson = jsonEncode(metadata);
      final encryptedMetadata = await _encryptData(metadataJson);
      
      await _secureStorage.write(key: _storageMetadataKey, value: encryptedMetadata);
    } catch (error) {
      developer.log(
        'Failed to update storage metadata: $error',
        name: 'SecureTokenStorageService',
        error: error,
      );
    }
  }

  /// Update integrity hash
  Future<void> _updateIntegrityHash() async {
    try {
      final integrityHash = await _calculateIntegrityHash();
      await _secureStorage.write(key: _integrityHashKey, value: integrityHash);
    } catch (error) {
      developer.log(
        'Failed to update integrity hash: $error',
        name: 'SecureTokenStorageService',
        error: error,
      );
    }
  }

  /// Calculate integrity hash of stored data
  Future<String> _calculateIntegrityHash() async {
    try {
      final allKeys = await _secureStorage.readAll();
      final sortedKeys = allKeys.keys.toList()..sort();
      
      final hashInput = StringBuffer();
      for (final key in sortedKeys) {
        if (key != _integrityHashKey) { // Exclude the hash itself
          hashInput.write('$key:${allKeys[key]}');
        }
      }
      
      final hash = sha256.convert(utf8.encode(hashInput.toString()));
      return hash.toString();
    } catch (error) {
      developer.log(
        'Failed to calculate integrity hash: $error',
        name: 'SecureTokenStorageService',
        error: error,
      );
      return '';
    }
  }
}

// =============================================================================
// RIVERPOD PROVIDERS
// =============================================================================

/// Provider for SecureTokenStorageService
final secureTokenStorageServiceProvider = Provider<ITokenStorage>((ref) {
  return EnhancedSecureTokenStorage();
});

/// Provider for biometric authentication availability
final biometricAuthAvailabilityProvider = FutureProvider<bool>((ref) async {
  try {
    final localAuth = LocalAuthentication();
    final isAvailable = await localAuth.canCheckBiometrics;
    final isDeviceSupported = await localAuth.isDeviceSupported();
    return isAvailable && isDeviceSupported;
  } catch (error) {
    return false;
  }
});

/// Provider for storage integrity status
final storageIntegrityProvider = FutureProvider<bool>((ref) async {
  final storage = ref.read(secureTokenStorageServiceProvider) as SecureTokenStorageService;
  return await storage.validateStorageIntegrity();
});
