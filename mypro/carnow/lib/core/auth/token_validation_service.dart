/// ============================================================================
/// TOKEN VALIDATION SERVICE - Forever Plan Architecture
/// ============================================================================
///
/// خدمة التحقق من الرموز المميزة - بنية الخطة الدائمة
/// Robust token validation with circuit breaker pattern and exponential backoff
///
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ✅ Circuit breaker pattern to prevent infinite retry loops
/// ✅ Exponential backoff for failed validation attempts
/// ✅ Comprehensive error handling and recovery
/// ✅ Production-ready logging and monitoring
/// ============================================================================
library;

import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'auth_models.dart';
import 'auth_interfaces.dart';
import 'enhanced_secure_token_storage.dart';
import '../networking/simple_api_client.dart';

part 'token_validation_service.freezed.dart';
part 'token_validation_service.g.dart';

// =============================================================================
// CIRCUIT BREAKER MODELS
// =============================================================================

/// Circuit breaker states for token validation
enum CircuitBreakerState {
  /// Circuit is closed - normal operation
  closed,

  /// Circuit is open - failing fast
  open,

  /// Circuit is half-open - testing recovery
  halfOpen,
}

/// Circuit breaker configuration
@freezed
abstract class CircuitBreakerConfig with _$CircuitBreakerConfig {
  const factory CircuitBreakerConfig({
    /// Maximum number of failures before opening circuit
    @Default(3) int failureThreshold,

    /// Timeout duration when circuit is open
    @Default(Duration(seconds: 30)) Duration timeout,

    /// Maximum retry attempts in half-open state
    @Default(1) int maxRetryAttempts,

    /// Reset timeout after successful operation
    @Default(Duration(seconds: 60)) Duration resetTimeout,
  }) = _CircuitBreakerConfig;
}

/// Token validation result with circuit breaker context
@freezed
abstract class TokenValidationResult with _$TokenValidationResult {
  /// Successful validation
  const factory TokenValidationResult.success({
    required bool isValid,
    required String token,
    DateTime? expiryDate,
    Map<String, dynamic>? metadata,
  }) = TokenValidationSuccess;

  /// Failed validation
  const factory TokenValidationResult.failure({
    required String error,
    required AuthErrorType errorType,
    @Default(false) bool isRecoverable,
    int? retryAfterSeconds,
    Map<String, dynamic>? details,
  }) = TokenValidationFailure;

  /// Circuit breaker is open - failing fast
  const factory TokenValidationResult.circuitOpen({
    required String reason,
    required Duration retryAfter,
    required int failureCount,
  }) = TokenValidationCircuitOpen;
}

/// Exponential backoff configuration
@freezed
abstract class BackoffConfig with _$BackoffConfig {
  const factory BackoffConfig({
    /// Initial delay for first retry
    @Default(Duration(milliseconds: 500)) Duration initialDelay,

    /// Maximum delay between retries
    @Default(Duration(seconds: 30)) Duration maxDelay,

    /// Multiplier for exponential backoff
    @Default(2.0) double multiplier,

    /// Maximum number of retry attempts
    @Default(5) int maxAttempts,

    /// Jitter factor to avoid thundering herd
    @Default(0.1) double jitterFactor,
  }) = _BackoffConfig;
}

// =============================================================================
// TOKEN VALIDATION SERVICE
// =============================================================================

/// Enhanced token validation service with circuit breaker and exponential backoff
/// Provides robust token handling with failure recovery mechanisms
@riverpod
class TokenValidationService extends _$TokenValidationService {
  // Circuit breaker state management
  CircuitBreakerState _circuitState = CircuitBreakerState.closed;
  int _failureCount = 0;
  DateTime? _lastFailureTime;
  DateTime? _circuitOpenTime;

  // Configuration
  late final CircuitBreakerConfig _circuitConfig;
  late final BackoffConfig _backoffConfig;

  // Dependencies
  late final ITokenStorage _tokenStorage;
  late final SimpleApiClient _apiClient;

  @override
  TokenValidationService build() {
    // Initialize configuration
    _circuitConfig = const CircuitBreakerConfig();
    _backoffConfig = const BackoffConfig();

    // Get dependencies
    _tokenStorage = ref.read(enhancedSecureTokenStorageProvider);
    _apiClient = ref.read(simpleApiClientProvider);

    return this;
  }

  // ---------------------------------------------------------------------------
  // PUBLIC API
  // ---------------------------------------------------------------------------

  /// Validate stored authentication token with circuit breaker protection
  /// Returns [TokenValidationResult] with validation status and error details
  Future<TokenValidationResult> validateStoredToken() async {
    try {
      // Check circuit breaker state
      final circuitCheck = _checkCircuitBreaker();
      if (circuitCheck != null) {
        return circuitCheck;
      }

      // Get stored token
      final token = await _tokenStorage.getToken();
      if (token == null) {
        return const TokenValidationResult.failure(
          error: 'No stored token found',
          errorType: AuthErrorType.sessionExpired,
          isRecoverable: true,
        );
      }

      // Validate token with retry logic
      return await _validateTokenWithRetry(token);
    } catch (error, stackTrace) {
      debugPrint('🔴 TokenValidationService - Unexpected error: $error');
      debugPrint('Stack trace: $stackTrace');

      return TokenValidationResult.failure(
        error: 'Unexpected validation error: $error',
        errorType: AuthErrorType.unknown,
        isRecoverable: false,
        details: {'stackTrace': stackTrace.toString()},
      );
    }
  }

  /// Validate specific token with circuit breaker protection
  /// [token] - Token to validate
  /// Returns [TokenValidationResult] with validation status
  Future<TokenValidationResult> validateToken(String token) async {
    try {
      // Check circuit breaker state
      final circuitCheck = _checkCircuitBreaker();
      if (circuitCheck != null) {
        return circuitCheck;
      }

      // Validate token with retry logic
      return await _validateTokenWithRetry(token);
    } catch (error, stackTrace) {
      debugPrint('🔴 TokenValidationService - Token validation error: $error');

      return TokenValidationResult.failure(
        error: 'Token validation failed: $error',
        errorType: AuthErrorType.unknown,
        isRecoverable: false,
        details: {'stackTrace': stackTrace.toString()},
      );
    }
  }

  /// Refresh token with circuit breaker protection
  /// Returns [TokenValidationResult] with new token or error details
  Future<TokenValidationResult> refreshToken() async {
    try {
      // Check circuit breaker state
      final circuitCheck = _checkCircuitBreaker();
      if (circuitCheck != null) {
        return circuitCheck;
      }

      // Get refresh token
      final refreshToken = await _tokenStorage.getRefreshToken();
      if (refreshToken == null) {
        return const TokenValidationResult.failure(
          error: 'No refresh token available',
          errorType: AuthErrorType.sessionExpired,
          isRecoverable: false,
        );
      }

      // Refresh token with retry logic
      return await _refreshTokenWithRetry(refreshToken);
    } catch (error, stackTrace) {
      debugPrint('🔴 TokenValidationService - Token refresh error: $error');

      return TokenValidationResult.failure(
        error: 'Token refresh failed: $error',
        errorType: AuthErrorType.unknown,
        isRecoverable: false,
        details: {'stackTrace': stackTrace.toString()},
      );
    }
  }

  /// Reset circuit breaker state (for manual recovery)
  void resetCircuitBreaker() {
    debugPrint('🔄 TokenValidationService - Resetting circuit breaker');
    _circuitState = CircuitBreakerState.closed;
    _failureCount = 0;
    _lastFailureTime = null;
    _circuitOpenTime = null;
  }

  /// Get current circuit breaker status
  Map<String, dynamic> getCircuitBreakerStatus() {
    return {
      'state': _circuitState.name,
      'failureCount': _failureCount,
      'lastFailureTime': _lastFailureTime?.toIso8601String(),
      'circuitOpenTime': _circuitOpenTime?.toIso8601String(),
      'isHealthy':
          _circuitState == CircuitBreakerState.closed && _failureCount == 0,
    };
  }

  // ---------------------------------------------------------------------------
  // CIRCUIT BREAKER LOGIC
  // ---------------------------------------------------------------------------

  /// Check circuit breaker state and return early result if circuit is open
  TokenValidationResult? _checkCircuitBreaker() {
    final now = DateTime.now();

    switch (_circuitState) {
      case CircuitBreakerState.closed:
        // Normal operation
        return null;

      case CircuitBreakerState.open:
        // Check if timeout has passed
        if (_circuitOpenTime != null &&
            now.difference(_circuitOpenTime!) >= _circuitConfig.timeout) {
          debugPrint(
            '🔄 TokenValidationService - Circuit breaker moving to half-open',
          );
          _circuitState = CircuitBreakerState.halfOpen;
          return null;
        }

        // Circuit is still open
        final retryAfter =
            _circuitConfig.timeout - now.difference(_circuitOpenTime!);
        return TokenValidationResult.circuitOpen(
          reason: 'Circuit breaker is open due to repeated failures',
          retryAfter: retryAfter,
          failureCount: _failureCount,
        );

      case CircuitBreakerState.halfOpen:
        // Allow one attempt to test recovery
        return null;
    }
  }

  /// Record successful operation and update circuit breaker state
  void _recordSuccess() {
    if (_circuitState == CircuitBreakerState.halfOpen) {
      debugPrint(
        '✅ TokenValidationService - Circuit breaker recovered, closing circuit',
      );
      _circuitState = CircuitBreakerState.closed;
      _failureCount = 0;
      _lastFailureTime = null;
      _circuitOpenTime = null;
    } else if (_circuitState == CircuitBreakerState.closed) {
      // Reset failure count on successful operation
      _failureCount = 0;
      _lastFailureTime = null;
    }
  }

  /// Record failure and update circuit breaker state
  void _recordFailure() {
    _failureCount++;
    _lastFailureTime = DateTime.now();

    if (_circuitState == CircuitBreakerState.halfOpen) {
      // Failed in half-open state, go back to open
      debugPrint(
        '🔴 TokenValidationService - Circuit breaker failed in half-open, reopening',
      );
      _circuitState = CircuitBreakerState.open;
      _circuitOpenTime = DateTime.now();
    } else if (_failureCount >= _circuitConfig.failureThreshold) {
      // Exceeded failure threshold, open circuit
      debugPrint(
        '🔴 TokenValidationService - Circuit breaker opened due to failures: $_failureCount',
      );
      _circuitState = CircuitBreakerState.open;
      _circuitOpenTime = DateTime.now();
    }
  }

  // ---------------------------------------------------------------------------
  // TOKEN VALIDATION WITH RETRY
  // ---------------------------------------------------------------------------

  /// Validate token with exponential backoff retry logic
  Future<TokenValidationResult> _validateTokenWithRetry(String token) async {
    int attempt = 0;
    Duration delay = _backoffConfig.initialDelay;

    while (attempt < _backoffConfig.maxAttempts) {
      try {
        // Attempt token validation
        final result = await _performTokenValidation(token);

        // Record success and return result
        _recordSuccess();
        return result;
      } catch (error) {
        attempt++;
        _recordFailure();

        debugPrint(
          '🔴 TokenValidationService - Validation attempt $attempt failed: $error',
        );

        // If this was the last attempt, return failure
        if (attempt >= _backoffConfig.maxAttempts) {
          return TokenValidationResult.failure(
            error: 'Token validation failed after $attempt attempts: $error',
            errorType: _categorizeError(error),
            isRecoverable: _isRecoverableError(error),
            retryAfterSeconds: delay.inSeconds,
            details: {'attempts': attempt, 'lastError': error.toString()},
          );
        }

        // Wait before next attempt with exponential backoff
        await _waitWithJitter(delay);
        delay = Duration(
          milliseconds: math.min(
            (delay.inMilliseconds * _backoffConfig.multiplier).round(),
            _backoffConfig.maxDelay.inMilliseconds,
          ),
        );
      }
    }

    // This should never be reached, but included for completeness
    return const TokenValidationResult.failure(
      error: 'Token validation failed - maximum attempts exceeded',
      errorType: AuthErrorType.unknown,
      isRecoverable: false,
    );
  }

  /// Refresh token with exponential backoff retry logic
  Future<TokenValidationResult> _refreshTokenWithRetry(
    String refreshToken,
  ) async {
    int attempt = 0;
    Duration delay = _backoffConfig.initialDelay;

    while (attempt < _backoffConfig.maxAttempts) {
      try {
        // Attempt token refresh
        final result = await _performTokenRefresh(refreshToken);

        // Record success and return result
        _recordSuccess();
        return result;
      } catch (error) {
        attempt++;
        _recordFailure();

        debugPrint(
          '🔴 TokenValidationService - Refresh attempt $attempt failed: $error',
        );

        // If this was the last attempt, return failure
        if (attempt >= _backoffConfig.maxAttempts) {
          return TokenValidationResult.failure(
            error: 'Token refresh failed after $attempt attempts: $error',
            errorType: _categorizeError(error),
            isRecoverable: _isRecoverableError(error),
            retryAfterSeconds: delay.inSeconds,
            details: {'attempts': attempt, 'lastError': error.toString()},
          );
        }

        // Wait before next attempt with exponential backoff
        await _waitWithJitter(delay);
        delay = Duration(
          milliseconds: math.min(
            (delay.inMilliseconds * _backoffConfig.multiplier).round(),
            _backoffConfig.maxDelay.inMilliseconds,
          ),
        );
      }
    }

    return const TokenValidationResult.failure(
      error: 'Token refresh failed - maximum attempts exceeded',
      errorType: AuthErrorType.unknown,
      isRecoverable: false,
    );
  }

  // ---------------------------------------------------------------------------
  // CORE VALIDATION OPERATIONS
  // ---------------------------------------------------------------------------

  /// Perform actual token validation via API
  Future<TokenValidationResult> _performTokenValidation(String token) async {
    try {
      // Validate token format first
      if (!_isValidTokenFormat(token)) {
        throw Exception('Invalid token format');
      }

      // Call backend API to validate token
      final response = await _apiClient.get(
        '/api/v1/auth/validate?token=$token',
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final isValid = data['valid'] == true;
        final expiryDate = data['expires_at'] != null
            ? DateTime.parse(data['expires_at'])
            : null;

        return TokenValidationResult.success(
          isValid: isValid,
          token: token,
          expiryDate: expiryDate,
          metadata: data,
        );
      } else {
        throw Exception(
          'Token validation failed: ${response.error ?? "Unknown error"}',
        );
      }
    } catch (error) {
      debugPrint('🔴 TokenValidationService - Token validation error: $error');
      rethrow;
    }
  }

  /// Perform actual token refresh via API
  Future<TokenValidationResult> _performTokenRefresh(
    String refreshToken,
  ) async {
    try {
      // Call backend API to refresh token
      final response = await _apiClient.post(
        '/api/v1/auth/refresh',
        data: {'refresh_token': refreshToken},
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final newToken = data['access_token'] as String;
        final expiryDate = data['expires_at'] != null
            ? DateTime.parse(data['expires_at'])
            : null;

        // Store new token
        await _tokenStorage.storeToken(newToken, expiryDate: expiryDate);
        if (data['refresh_token'] != null) {
          await _tokenStorage.storeRefreshToken(data['refresh_token']);
        }

        return TokenValidationResult.success(
          isValid: true,
          token: newToken,
          expiryDate: expiryDate,
          metadata: data,
        );
      } else {
        throw Exception(
          'Token refresh failed: ${response.error ?? "Unknown error"}',
        );
      }
    } catch (error) {
      debugPrint('🔴 TokenValidationService - Token refresh error: $error');
      rethrow;
    }
  }

  // ---------------------------------------------------------------------------
  // UTILITY METHODS
  // ---------------------------------------------------------------------------

  /// Wait with jitter to avoid thundering herd problem
  Future<void> _waitWithJitter(Duration delay) async {
    final jitter =
        (math.Random().nextDouble() - 0.5) * 2 * _backoffConfig.jitterFactor;
    final jitteredDelay = Duration(
      milliseconds: (delay.inMilliseconds * (1 + jitter)).round(),
    );
    await Future.delayed(jitteredDelay);
  }

  /// Validate token format (basic JWT structure check)
  bool _isValidTokenFormat(String token) {
    if (token.isEmpty) return false;

    // Basic JWT format check (3 parts separated by dots)
    final parts = token.split('.');
    if (parts.length != 3) return false;

    // Check that each part is base64-encoded
    for (final part in parts) {
      if (part.isEmpty) return false;
      try {
        // Try to decode base64 (will throw if invalid)
        final padded = part.padRight((part.length + 3) ~/ 4 * 4, '=');
        Uri.parse('data:;base64,$padded');
      } catch (e) {
        return false;
      }
    }

    return true;
  }

  /// Categorize error type for proper handling
  AuthErrorType _categorizeError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') || errorString.contains('connection')) {
      return AuthErrorType.network;
    } else if (errorString.contains('timeout')) {
      return AuthErrorType.serviceUnavailable;
    } else if (errorString.contains('unauthorized') ||
        errorString.contains('invalid token')) {
      return AuthErrorType.sessionExpired;
    } else if (errorString.contains('rate limit')) {
      return AuthErrorType.rateLimitExceeded;
    } else {
      return AuthErrorType.unknown;
    }
  }

  /// Check if error is recoverable (can be retried)
  bool _isRecoverableError(dynamic error) {
    final errorType = _categorizeError(error);

    switch (errorType) {
      case AuthErrorType.network:
      case AuthErrorType.serviceUnavailable:
      case AuthErrorType.rateLimitExceeded:
        return true;
      case AuthErrorType.sessionExpired:
      case AuthErrorType.invalidCredentials:
        return false;
      default:
        return true; // Default to recoverable for unknown errors
    }
  }
}

// =============================================================================
// TOKEN VALIDATION SERVICE - READY FOR USE
// =============================================================================
//
// The TokenValidationService is available via the generated provider:
// - tokenValidationServiceProvider
//
// Usage:
// final tokenService = ref.read(tokenValidationServiceProvider);
// final result = await tokenService.validateToken(token);
