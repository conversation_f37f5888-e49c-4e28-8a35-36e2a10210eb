import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../services/websocket_service.dart';

part 'websocket_provider.g.dart';

final _logger = Logger('WebSocketProvider');

/// WebSocket Service Provider
@riverpod
WebSocketService webSocketService(Ref ref) {
  return WebSocketService.instance;
}

/// WebSocket Connection Provider
@riverpod
class WebSocketConnection extends _$WebSocketConnection {
  @override
  Future<bool> build() async {
    final wsService = ref.read(webSocketServiceProvider);

    // Connect without authentication for now
    try {
      _logger.info('Connecting to WebSocket without auth');
      await wsService.connect();

      // Listen to connection status
      wsService.connectionStream.listen((isConnected) {
        state = AsyncValue.data(isConnected);
      });

      return true;
    } catch (e) {
      _logger.severe('Failed to connect to WebSocket', e);
      state = AsyncValue.error(e, StackTrace.current);
      return false;
    }
  }

  /// Disconnect from WebSocket
  void _disconnect() {
    final wsService = ref.read(webSocketServiceProvider);
    wsService.disconnect();
    state = const AsyncValue.data(false);
  }

  /// Manually reconnect
  Future<void> reconnect() async {
    final wsService = ref.read(webSocketServiceProvider);
    try {
      await wsService.connect();
    } catch (e) {
      _logger.severe('Failed to reconnect to WebSocket', e);
    }
  }

  /// Disconnect manually
  void disconnect() {
    _disconnect();
  }
}

/// WebSocket Messages Stream Provider
@riverpod
Stream<WebSocketMessage> webSocketMessages(Ref ref) {
  final wsService = ref.watch(webSocketServiceProvider);
  return wsService.messageStream;
}

/// Cart Updates Stream Provider
@riverpod
Stream<WebSocketMessage> cartUpdatesStream(Ref ref) {
  final wsService = ref.watch(webSocketServiceProvider);
  return wsService.subscribeToCartUpdates();
}

/// Order Updates Stream Provider
@riverpod
Stream<WebSocketMessage> orderUpdatesStream(Ref ref) {
  final wsService = ref.watch(webSocketServiceProvider);
  return wsService.subscribeToOrderUpdates();
}

/// Inventory Updates Stream Provider
@riverpod
Stream<WebSocketMessage> inventoryUpdatesStream(Ref ref) {
  final wsService = ref.watch(webSocketServiceProvider);
  return wsService.subscribeToInventoryUpdates();
}

/// WebSocket Connection Status Provider
@riverpod
bool webSocketConnectionStatus(Ref ref) {
  final connectionAsync = ref.watch(webSocketConnectionProvider);
  return connectionAsync.when(
    data: (isConnected) => isConnected,
    loading: () => false,
    error: (_, _) => false,
  );
}
