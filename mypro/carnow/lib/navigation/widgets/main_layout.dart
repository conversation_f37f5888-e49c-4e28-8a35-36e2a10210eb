import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../features/cart/providers/cart_provider.dart';

import '../../l10n/app_localizations.dart';
import '../providers/navigation_provider.dart';
import '../../core/auth/auth_provider_initializer.dart';
import '../../core/auth/auth_models.dart';

class MainLayout extends ConsumerWidget {
  const MainLayout({required this.navigationShell, super.key});

  final StatefulNavigationShell navigationShell;

  void _onTap(int index) {
    navigationShell.goBranch(
      index,
      initialLocation: index == navigationShell.currentIndex,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final navigationItems = ref.watch(navigationItemsProvider(l10n));
    final cartItems = ref.watch(cartProvider);
    
    // Watch authentication state for dynamic UI
    final authState = ref.watch(safeAuthStateProvider);
    bool isAuthenticated = false;
    if (authState is AuthStateAuthenticated) {
      isAuthenticated = true;
    }

    final cs = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'CarNow',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        centerTitle: true,
        elevation: 0,
        backgroundColor: cs.surface,
        foregroundColor: cs.onSurface,
        surfaceTintColor: cs.surfaceTint,
        actions: [
          // Search icon
          IconButton(
            icon: const Icon(Icons.search_outlined),
            onPressed: () => context.push('/search'),
            tooltip: 'البحث',
          ),
          
          // Account button (only for authenticated users)
          if (isAuthenticated) ...[
            IconButton(
              icon: const Icon(Icons.account_circle_outlined),
              onPressed: () => context.push('/account'),
              tooltip: 'الحساب',
            ),
          ],
          
          // Notifications
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () => context.push('/notifications'),
            tooltip: 'الإشعارات',
          ),
          
          // Cart with badge
          _CartIconButton(cartItems: cartItems, colorScheme: cs),
          
          const SizedBox(width: 4),
        ],
      ),
      body: navigationShell,
      bottomNavigationBar: NavigationBar(
        backgroundColor: cs.surface,
        surfaceTintColor: cs.surfaceTint,
        indicatorColor: cs.secondaryContainer,
        selectedIndex: navigationShell.currentIndex,
        onDestinationSelected: _onTap,
        destinations: navigationItems
            .map(
              (item) => NavigationDestination(
                icon: item.icon,
                selectedIcon: item.activeIcon,
                label: item.label,
              ),
            )
            .toList(),
      ),
    );
  }

  // Title mapping removed for now to eliminate reflection on internal
  // go_router properties that changed between versions.
}

class _CartIconButton extends ConsumerWidget {
  const _CartIconButton({required this.cartItems, required this.colorScheme});

  final AsyncValue<List<dynamic>> cartItems;
  final ColorScheme colorScheme;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return IconButton(
      icon: Stack(
        children: [
          const Icon(Icons.shopping_cart_outlined),
          if (cartItems.asData?.value.isNotEmpty ?? false)
            Positioned(
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(1),
                decoration: BoxDecoration(
                  color: colorScheme.error,
                  borderRadius: BorderRadius.circular(6),
                ),
                constraints: const BoxConstraints(minWidth: 12, minHeight: 12),
                child: Text(
                  '${cartItems.asData!.value.length}',
                  style: const TextStyle(color: Colors.white, fontSize: 8),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
      onPressed: () {
        context.push('/cart');
      },
    );
  }
}
