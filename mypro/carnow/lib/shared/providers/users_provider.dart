import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../core/models/user_model.dart';
import '../../core/networking/simple_api_client.dart';

// تمت إزالة currentUserProvider لتجنب التضارب مع النظام الموحد
// Removed currentUserProvider to avoid conflicts with unified system
// استخدم lib/core/auth/unified_auth_provider.dart currentUserProvider بدلاً منه
// Use lib/core/auth/unified_auth_provider.dart currentUserProvider instead

final usersProvider = FutureProvider<List<UserModel>>((ref) async {
  try {
    final apiClient = ref.watch(simpleApiClientProvider);

    final response = await apiClient.getApi<Map<String, dynamic>>('/users');

    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to fetch users: ${response.message}');
    }

    final data = response.data!;
    final List<dynamic> usersJson =
        data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
        ? data as List<dynamic>
        : throw Exception('Invalid users response format');

    return usersJson
        .map((json) => UserModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    throw Exception('Failed to fetch users: $e');
  }
});
