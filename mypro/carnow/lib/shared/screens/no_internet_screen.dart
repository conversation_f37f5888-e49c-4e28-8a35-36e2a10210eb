/// شاشة انقطاع الاتصال بالإنترنت
///
/// تُعرض هذه الشاشة تلقائيًا عندما يفقد الجهاز اتصاله بالإنترنت.
/// توفر للمستخدم ملاحظات واضحة حول حالة الاتصال،
/// وتتضمن آلية لإعادة محاولة الاتصال تلقائيًا أو يدويًا بمجرد عودة الشبكة.
library;

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../../core/theme/app_theme.dart';
import 'package:carnow/shared/widgets/primary_button.dart';

class NoInternetScreen extends HookConsumerWidget {
  const NoInternetScreen({super.key, this.onRetry, this.showAppBar = true});

  final VoidCallback? onRetry;
  final bool showAppBar;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isRetrying = useState(false);
    final connectionStatus = useState<ConnectivityResult?>(null);

    // Monitor connectivity changes
    useEffect(() {
      late StreamSubscription<List<ConnectivityResult>> subscription;

      // Check initial connectivity
      Connectivity().checkConnectivity().then((results) {
        connectionStatus.value = results.isNotEmpty
            ? results.first
            : ConnectivityResult.none;
      });

      // Listen to connectivity changes
      subscription = Connectivity().onConnectivityChanged.listen((results) {
        connectionStatus.value = results.isNotEmpty
            ? results.first
            : ConnectivityResult.none;

        // Auto-retry when connection is restored
        final result = results.isNotEmpty
            ? results.first
            : ConnectivityResult.none;
        if (result != ConnectivityResult.none && onRetry != null) {
          Future.delayed(const Duration(seconds: 1), () {
            if (context.mounted) {
              onRetry!();
            }
          });
        }
      });

      return () => subscription.cancel();
    });

    Future<void> handleRetry() async {
      if (isRetrying.value) return;

      isRetrying.value = true;

      try {
        // Check connectivity
        final results = await Connectivity().checkConnectivity();
        connectionStatus.value = results.isNotEmpty
            ? results.first
            : ConnectivityResult.none;
        final result = results.isNotEmpty
            ? results.first
            : ConnectivityResult.none;

        if (result != ConnectivityResult.none) {
          // Connection is available, trigger retry callback
          if (onRetry != null) {
            onRetry!();
          } else {
            // If no callback, just show success message
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم استعادة الاتصال بالإنترنت'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          }
        } else {
          // Still no connection
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('لا يزال الاتصال بالإنترنت غير متوفر'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في فحص الاتصال: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (context.mounted) {
          isRetrying.value = false;
        }
      }
    }

    String getConnectionStatusText() {
      switch (connectionStatus.value) {
        case ConnectivityResult.wifi:
          return 'متصل بالواي فاي';
        case ConnectivityResult.mobile:
          return 'متصل ببيانات الجوال';
        case ConnectivityResult.ethernet:
          return 'متصل بالإيثرنت';
        case ConnectivityResult.none:
          return 'غير متصل بالإنترنت';
        case null:
          return 'جاري فحص الاتصال...';
        default:
          return 'حالة الاتصال غير معروفة';
      }
    }

    Color getStatusColor() {
      switch (connectionStatus.value) {
        case ConnectivityResult.wifi:
        case ConnectivityResult.mobile:
        case ConnectivityResult.ethernet:
          return Colors.green;
        case ConnectivityResult.none:
          return Colors.red;
        case null:
        default:
          return Colors.orange;
      }
    }

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: showAppBar
          ? AppBar(
              title: const Text('مشكلة في الاتصال'),
              centerTitle: true,
              elevation: 0,
              backgroundColor: Colors.transparent,
            )
          : null,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingL),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Animated Icon
              TweenAnimationBuilder<double>(
                duration: const Duration(seconds: 2),
                tween: Tween(begin: 0, end: 1),
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: 0.8 + (value * 0.2),
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: colorScheme.errorContainer,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.wifi_off_rounded,
                        size: 60,
                        color: colorScheme.error,
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: AppTheme.spacingXL),

              // Title
              Text(
                'لا يوجد اتصال بالإنترنت',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppTheme.spacingM),

              // Description
              Text(
                'يبدو أن جهازك غير متصل بالإنترنت. تحقق من اتصالك وحاول مرة أخرى.',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurface.withAlpha((0.7 * 255).toInt()),
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppTheme.spacingL),

              // Connection Status
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingM,
                  vertical: AppTheme.spacingS,
                ),
                decoration: BoxDecoration(
                  color: getStatusColor().withAlpha((0.1 * 255).toInt()),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: getStatusColor().withAlpha((0.3 * 255).toInt()),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: getStatusColor(),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingS),
                    Text(
                      getConnectionStatusText(),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: getStatusColor(),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppTheme.spacingXL),

              // Retry Button
              PrimaryButton(
                onPressed: handleRetry,
                isLoading: isRetrying.value,
                text: 'إعادة المحاولة',
              ),

              const SizedBox(height: AppTheme.spacingL),

              // Help Section
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingM),
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest.withAlpha(
                    (0.5 * 255).toInt(),
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: colorScheme.outline.withAlpha((0.2 * 255).toInt()),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.help_outline,
                          color: colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: AppTheme.spacingS),
                        Text(
                          'نصائح لحل المشكلة:',
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppTheme.spacingS),

                    // Tips List
                    ...[
                      '• تأكد من تشغيل الواي فاي أو بيانات الجوال',
                      '• تحقق من إعدادات الشبكة في جهازك',
                      '• جرب إعادة تشغيل جهاز الراوتر',
                      '• تأكد من وجود رصيد كافي (للبيانات الخلوية)',
                    ].map(
                      (tip) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Align(
                          alignment: Alignment.centerRight,
                          child: Text(
                            tip,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppTheme.spacingL),

              // Auto-retry indicator
              if (connectionStatus.value != ConnectivityResult.none)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingM,
                    vertical: AppTheme.spacingS,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withAlpha((0.1 * 255).toInt()),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.green,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingS),
                      Text(
                        'تم استعادة الاتصال، جاري المحاولة...',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.green.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
