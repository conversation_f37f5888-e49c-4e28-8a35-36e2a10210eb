import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:carnow/core/widgets/error_display_widget.dart';
import 'package:carnow/core/services/global_error_handler.dart';
import 'package:carnow/core/errors/app_error.dart';
import 'package:carnow/features/products/providers/product_providers.dart';

/// Demo screen showcasing Phase 3.1.1 error handling features
class ErrorHandlingDemoScreen extends ConsumerWidget {
  const ErrorHandlingDemoScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productsAsync = ref.watch(productsProvider({'page': 1}));
    const AppError? error = null; // Simplified for demo

    return Scaffold(
      appBar: AppBar(
        title: const Text('Error Handling Demo'),
        actions: [
          IconButton(
            onPressed: () => ref.refresh(productsProvider({'page': 1})),
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Column(
        children: [
          // Offline indicator
          const OfflineIndicator(),

          // Main content
          Expanded(
            child: LoadingWithError(
              asyncValue: productsAsync,
              onRetry: () => ref.refresh(productsProvider({'page': 1})),
              builder: (products) => ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // Success state
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Products Loaded Successfully',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                                  color: Colors.green.shade700,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 8),
                          Text('Found ${products.length} products'),
                          const SizedBox(height: 16),
                          ...products.map(
                            (product) => ListTile(
                              title: Text(product.nameEn),
                              subtitle: Text(product.descriptionEn ?? 'No description'),
                              trailing: Text(
                                product.price != null ? '\$${product.price!.toStringAsFixed(2)}' : 'N/A',
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Demo buttons for different error scenarios
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Error Handling Demos',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 16),

                          // Network error demo
                          ElevatedButton(
                            onPressed: () =>
                                _showNetworkErrorDemo(context, ref),
                            child: const Text('Demo: Network Error'),
                          ),
                          const SizedBox(height: 8),

                          // Validation error demo
                          ElevatedButton(
                            onPressed: () =>
                                _showValidationErrorDemo(context, ref),
                            child: const Text('Demo: Validation Error'),
                          ),
                          const SizedBox(height: 8),

                          // Authentication error demo
                          ElevatedButton(
                            onPressed: () => _showAuthErrorDemo(context, ref),
                            child: const Text('Demo: Auth Error'),
                          ),
                          const SizedBox(height: 8),

                          // Server error demo
                          ElevatedButton(
                            onPressed: () =>
                                _showServerErrorDemo(context, ref),
                            child: const Text('Demo: Server Error'),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Error information card
                  if (error != null)
                    Card(
                      color: Colors.red.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Current Error State',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(
                                    color: Colors.red.shade700,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            const SizedBox(height: 8),
                            SelectableText.rich(
                              TextSpan(
                                children: [
                                  const TextSpan(
                                    text: 'Type: ',
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  TextSpan(text: error.type.toString()),
                                  const TextSpan(text: '\n'),
                                  const TextSpan(
                                    text: 'Message: ',
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  TextSpan(text: error.message),
                                  const TextSpan(text: '\n'),
                                  const TextSpan(
                                    text: 'Code: ',
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  TextSpan(text: error.code ?? 'N/A'),
                                ],
                              ),
                              style: TextStyle(color: Colors.red.shade700),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateProductDemo(context, ref),
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showNetworkErrorDemo(BuildContext context, WidgetRef ref) {
    final error = AppError.network(
      message: 'Failed to connect to server',
      code: 'CONNECTION_ERROR',
      data: {'endpoint': '/api/v1/products', 'timeout': '30s'},
    );

    final errorHandler = ref.read(globalErrorHandlerProvider);
    errorHandler.showErrorToUser(
      context,
      error,
      onRetry: () => ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Retrying network operation...')),
      ),
    );
  }

  void _showValidationErrorDemo(BuildContext context, WidgetRef ref) {
    final error = AppError.validation(
      message: 'Product name is required and must be at least 3 characters',
      code: 'VALIDATION_ERROR',
      data: {'field': 'name', 'minLength': 3},
    );

    final errorHandler = ref.read(globalErrorHandlerProvider);
    errorHandler.showErrorToUser(context, error);
  }

  void _showAuthErrorDemo(BuildContext context, WidgetRef ref) {
    final error = AppError.authentication(
      message: 'Your session has expired. Please log in again.',
      code: 'SESSION_EXPIRED',
    );

    final errorHandler = ref.read(globalErrorHandlerProvider);
    errorHandler.showErrorToUser(context, error);
  }

  void _showServerErrorDemo(BuildContext context, WidgetRef ref) {
    final error = AppError.server(
      message: 'Internal server error occurred',
      code: 'INTERNAL_SERVER_ERROR',
      data: {'statusCode': 500, 'timestamp': DateTime.now().toIso8601String()},
    );

    final errorHandler = ref.read(globalErrorHandlerProvider);
    errorHandler.showErrorToUser(
      context,
      error,
      onRetry: () => ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Retrying server operation...')),
      ),
    );
  }

  void _showCreateProductDemo(BuildContext context, WidgetRef ref) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Creating product...'),
          ],
        ),
      ),
    );

    // Simulate product creation with error handling
    // Note: Enhanced provider demo disabled - using simplified version
    /*
    final result = await ref
        .read(enhancedProductProviderProvider.notifier)
        .createProduct(
          ProductModel(
            id: '',
            name: 'Demo Product',
            description: 'This is a demo product created with error handling',
            price: 99.99,
            categoryId: 'demo_category',
            sellerId: 'demo_seller',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

    // Close loading dialog
    if (context.mounted) {
      Navigator.of(context).pop();
    }

    // Handle result
    result.when(
      success: (product) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Product created successfully: ${product.name ?? "Unknown"}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      },
      failure: (error) {
        if (context.mounted) {
          final errorHandler = ref.read(globalErrorHandlerProvider);
          errorHandler.showErrorToUser(
            context,
            error,
            onRetry: () => _showCreateProductDemo(context, ref),
          );
        }
      },
    );
    */
    
    // Simplified demo message
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Demo functionality simplified - enhanced providers not available'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }
}
