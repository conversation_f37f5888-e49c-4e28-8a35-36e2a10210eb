/// الشاشة الرئيسية للتطبيق (الحاوية الأساسية)
///
/// تمثل هذه الشاشة الهيكل الأساسي للتطبيق بعد تسجيل الدخول.
/// تحتوي على شريط التنقل السفلي الذي يتيح للمستخدم التنقل بين
/// الأقسام الرئيسية مثل الصفحة الرئيسية، الفئات، المفضلة، والحساب الشخصي.
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class MainScreen extends ConsumerStatefulWidget {
  const MainScreen({super.key});

  @override
  ConsumerState<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends ConsumerState<MainScreen> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(title: const Text('CarNow')),
      body: IndexedStack(
        index: _currentIndex,
        children: const [
          Center(child: Text('الرئيسية')),
          Center(child: Text('الفئات')),
          Center(child: Text('البحث')),
          Center(child: Text('الجراج')),
          Center(child: Text('المتجر')),
          Center(child: Text('الحساب')),
        ],
      ),
      bottomNavigationBar: NavigationBar(
        backgroundColor: cs.surface,
        surfaceTintColor: cs.surfaceTint,
        indicatorColor: cs.secondaryContainer,
        selectedIndex: _currentIndex,
        onDestinationSelected: (index) {
          setState(() => _currentIndex = index);
        },
        destinations: const [
          NavigationDestination(icon: Icon(Icons.home), label: 'الرئيسية'),
          NavigationDestination(icon: Icon(Icons.category), label: 'الفئات'),
          NavigationDestination(icon: Icon(Icons.search), label: 'البحث'),
          NavigationDestination(
            icon: Icon(Icons.directions_car),
            label: 'الجراج',
          ),
          NavigationDestination(icon: Icon(Icons.store), label: 'المتجر'),
          NavigationDestination(icon: Icon(Icons.person), label: 'الحساب'),
        ],
      ),
    );
  }
}
