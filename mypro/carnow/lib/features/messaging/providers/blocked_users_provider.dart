import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/unified_auth_provider.dart';
import '../models/blocked_user_model.dart';

part 'blocked_users_provider.g.dart';

final _logger = Logger('BlockedUsersProvider');

/// مزود قائمة المستخدمين المحظورين
@riverpod
Future<List<BlockedUserModel>> blockedUsers(Ref ref) async {
  try {
    final currentUser = ref.read(currentUserProvider);
    final userId = currentUser?.id;

    if (userId == null) {
      _logger.warning('User not authenticated for blocked users');
      return [];
    }

    _logger.info('Fetching blocked users for user: $userId');

    // جلب البيانات من Go backend API
    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi('/users/$userId/blocked');

    final blockedUsers = (response.data as List)
        .map((dynamic item) {
          try {
            final data = item is Map<String, dynamic>
                ? item
                : Map<String, dynamic>.from(item as Map<dynamic, dynamic>);

            final dynamic blockedUserData = data['blocked_user'];
            Map<String, dynamic>? blockedUserMap;

            if (blockedUserData is Map) {
              blockedUserMap = blockedUserData is Map<String, dynamic>
                  ? blockedUserData
                  : Map<String, dynamic>.from(blockedUserData);
            }

            return BlockedUserModel(
              id: data['id']?.toString() ?? '',
              userId: data['user_id']?.toString() ?? '',
              blockedUserId: data['blocked_user_id']?.toString() ?? '',
              blockedUserName: blockedUserMap?['full_name'] as String?,
              blockedUserAvatar: blockedUserMap?['avatar_url']?.toString(),
              reason: data['reason']?.toString(),
              blockedAt: data['blocked_at'] is String
                  ? DateTime.tryParse(data['blocked_at'] as String) ??
                        DateTime.now()
                  : (data['blocked_at'] is int
                        ? DateTime.fromMillisecondsSinceEpoch(
                            data['blocked_at'] as int,
                          )
                        : (data['blocked_at'] as DateTime?) ?? DateTime.now()),
              metadata: data['metadata'] is Map
                  ? (data['metadata'] is Map<String, dynamic>
                        ? data['metadata'] as Map<String, dynamic>
                        : Map<String, dynamic>.from(
                            data['metadata'] as Map<dynamic, dynamic>,
                          ))
                  : <String, dynamic>{},
            );
          } catch (e) {
            _logger.warning('Error parsing blocked user data: $e');
            return null;
          }
        })
        .whereType<BlockedUserModel>()
        .toList();

    _logger.info('Fetched ${blockedUsers.length} blocked users');
    return blockedUsers;
  } catch (e, stack) {
    _logger.severe('Error fetching blocked users: $e', e, stack);
    rethrow;
  }
}

/// مزود إدارة المستخدمين المحظورين
@riverpod
class BlockedUsersManager extends _$BlockedUsersManager {
  @override
  AsyncValue<void> build() => const AsyncValue.data(null);

  /// حظر مستخدم
  Future<void> blockUser({
    required String blockedUserId,
    String? reason,
    String? blockedUserName,
  }) async {
    state = const AsyncValue.loading();

    try {
      final currentUser = ref.read(currentUserProvider);
      final userId = currentUser?.id;

      if (userId == null) {
        throw Exception('يجب تسجيل الدخول لحظر المستخدمين');
      }

      _logger.info('Blocking user: $blockedUserId');

      // محاكاة العملية (يمكن استبدالها بـ Supabase لاحقاً)
      await Future<void>.delayed(const Duration(seconds: 1));

      // FIXED: No more direct Supabase calls - should use Go API backend
      // TODO: Replace with API call to /api/v1/users/block endpoint
      // Example: await apiClient.post('/users/block', data: {...});
      // For now, just simulate the operation

      // تحديث قائمة المستخدمين المحظورين
      ref.invalidate(blockedUsersProvider);

      state = const AsyncValue.data(null);
      _logger.info('User blocked successfully: $blockedUserId');
    } catch (e, stack) {
      _logger.severe('Error blocking user: $e', e, stack);
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  /// إلغاء حظر مستخدم
  Future<void> unblockUser(String blockedUserId) async {
    state = const AsyncValue.loading();

    try {
      final currentUser = ref.read(currentUserProvider);
      final userId = currentUser?.id;

      if (userId == null) {
        throw Exception('يجب تسجيل الدخول لإلغاء حظر المستخدمين');
      }

      _logger.info('Unblocking user: $blockedUserId');

    // ✅ Forever Plan Architecture: Flutter UI Only → Go API → Supabase Data
    // ❌ NO MOCK DATA - Use real Go backend integration
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.delete<Map<String, dynamic>>(
      '/api/v1/users/blocked/$blockedUserId',
    );
    
    if (!response.isSuccess) {
      throw Exception('فشل في إلغاء حظر المستخدم: ${response.message}');
    }
    
      // تحديث قائمة المستخدمين المحظورين
      ref.invalidate(blockedUsersProvider);

      state = const AsyncValue.data(null);
      _logger.info('User unblocked successfully: $blockedUserId');
    } catch (e, stack) {
      _logger.severe('Error unblocking user: $e', e, stack);
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  /// حظر عدة مستخدمين
  Future<void> blockMultipleUsers(List<String> userIds) async {
    state = const AsyncValue.loading();

    try {
      final currentUser = ref.read(currentUserProvider);
      final userId = currentUser?.id;

      if (userId == null) {
        throw Exception('يجب تسجيل الدخول لحظر المستخدمين');
      }

      _logger.info('Blocking multiple users: ${userIds.length}');

      // محاكاة العملية
      await Future<void>.delayed(const Duration(seconds: 2));

      // تحديث قائمة المستخدمين المحظورين
      ref.invalidate(blockedUsersProvider);

      state = const AsyncValue.data(null);
      _logger.info('Multiple users blocked successfully');
    } catch (e, stack) {
      _logger.severe('Error blocking multiple users: $e', e, stack);
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  /// إلغاء حظر عدة مستخدمين
  Future<void> unblockMultipleUsers(List<String> userIds) async {
    state = const AsyncValue.loading();

    try {
      final currentUser = ref.read(currentUserProvider);
      final userId = currentUser?.id;

      if (userId == null) {
        throw Exception('يجب تسجيل الدخول لإلغاء حظر المستخدمين');
      }

      _logger.info('Unblocking multiple users: ${userIds.length}');

      // محاكاة العملية
      await Future<void>.delayed(const Duration(seconds: 2));

      // تحديث قائمة المستخدمين المحظورين
      ref.invalidate(blockedUsersProvider);

      state = const AsyncValue.data(null);
      _logger.info('Multiple users unblocked successfully');
    } catch (e, stack) {
      _logger.severe('Error unblocking multiple users: $e', e, stack);
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }
}
