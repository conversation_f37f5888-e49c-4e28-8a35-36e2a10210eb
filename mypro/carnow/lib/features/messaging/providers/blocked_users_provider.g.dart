// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'blocked_users_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$blockedUsersHash() => r'f65cef7633afa5ba832575b330728395dad28472';

/// مزود قائمة المستخدمين المحظورين
///
/// Copied from [blockedUsers].
@ProviderFor(blockedUsers)
final blockedUsersProvider =
    AutoDisposeFutureProvider<List<BlockedUserModel>>.internal(
      blockedUsers,
      name: r'blockedUsersProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$blockedUsersHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BlockedUsersRef = AutoDisposeFutureProviderRef<List<BlockedUserModel>>;
String _$blockedUsersManagerHash() =>
    r'7cb8470fbeb4aa2df8578bdfc713296f649a8f02';

/// مزود إدارة المستخدمين المحظورين
///
/// Copied from [BlockedUsersManager].
@ProviderFor(BlockedUsersManager)
final blockedUsersManagerProvider =
    AutoDisposeNotifierProvider<BlockedUsersManager, AsyncValue<void>>.internal(
      BlockedUsersManager.new,
      name: r'blockedUsersManagerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$blockedUsersManagerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BlockedUsersManager = AutoDisposeNotifier<AsyncValue<void>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
