import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/networking/simple_api_client.dart';
import '../models/chat_models.dart';

part 'archived_chats_provider.g.dart';

final _logger = Logger('ArchivedChatsProvider');

/// مزود المحادثات المؤرشفة
@riverpod
Future<List<ChatModel>> archivedChats(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);

    _logger.info('Fetching archived chats from Go backend');

    final response = await apiClient.get('/chats/archived');
    final data = response.data as Map<String, dynamic>;
    final chats = (data['chats'] as List)
        .map((chatData) => ChatModel.fromJson(chatData as Map<String, dynamic>))
        .toList();

    _logger.info('Fetched ${chats.length} archived chats');
    return chats;
  } catch (e, stack) {
    _logger.severe('Error fetching archived chats: $e', e, stack);
    rethrow;
  }
}

/// مزود إدارة المحادثات المؤرشفة
@riverpod
class ArchivedChatsManager extends _$ArchivedChatsManager {
  @override
  AsyncValue<void> build() => const AsyncValue.data(null);

  /// إلغاء أرشفة محادثة
  Future<void> unarchiveChat(String chatId) async {
    state = const AsyncValue.loading();

    try {
      final apiClient = ref.read(simpleApiClientProvider);

      await apiClient.put('/chats/$chatId/unarchive');

      // تحديث قائمة المحادثات المؤرشفة
      ref.invalidate(archivedChatsProvider);

      state = const AsyncValue.data(null);
      _logger.info('Unarchived chat: $chatId');
    } catch (e, stack) {
      _logger.severe('Error unarchiving chat: $e', e, stack);
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  /// حذف محادثة مؤرشفة نهائياً
  Future<void> deleteArchivedChat(String chatId) async {
    state = const AsyncValue.loading();

    try {
      final apiClient = ref.read(simpleApiClientProvider);
      await apiClient.delete('/chats/$chatId');

      // تحديث قائمة المحادثات المؤرشفة
      ref.invalidate(archivedChatsProvider);

      state = const AsyncValue.data(null);
      _logger.info('Deleted archived chat: $chatId');
    } catch (e, stack) {
      _logger.severe('Error deleting archived chat: $e', e, stack);
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }
}
