import 'dart:async';
import 'dart:io';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../models/chat_models.dart';
import '../services/chat_service.dart';

part 'chat_providers.g.dart';

final _logger = Logger('ChatProvider');

/// مزود قائمة المحادثات - FIXED: Converted to @riverpod pattern
@riverpod
Future<List<ChatModel>> userChats(Ref ref) async {
  try {
    // Use currentUserProvider for user access
    final userId = ref.read(currentUserProvider)?.id;

    if (userId == null) {
      _logger.warning('User not authenticated for chats');
      return [];
    }

    final chats = await ChatService.getUserChats(userId);
    _logger.info('Fetched ${chats.length} chats for user');
    return chats;
  } catch (e, stack) {
    _logger.severe('Error fetching user chats: $e', e, stack);
    rethrow;
  }
}

/// مزود رسائل المحادثة - FIXED: Converted to @riverpod pattern
@riverpod
Future<List<MessageModel>> chatMessages(
  Ref ref,
  String chatId,
) async {
  try {
    final messages = await ChatService.getChatMessages(chatId);
    _logger.info('Fetched ${messages.length} messages for chat: $chatId');
    return messages;
  } catch (e, stack) {
    _logger.severe('Error fetching chat messages: $e', e, stack);
    rethrow;
  }
}

/// مزود إرسال الرسائل
@riverpod
class MessageSender extends _$MessageSender {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  /// إرسال رسالة نصية
  Future<MessageModel> sendTextMessage({
    required String chatId,
    required String content,
    String? replyToId,
  }) async {
    state = const AsyncValue.loading();

    try {
      // Use currentUserProvider for user access
      final senderId = ref.read(currentUserProvider)?.id;

      if (senderId == null) {
        throw Exception('يجب تسجيل الدخول لإرسال رسالة');
      }

      final message = await ChatService.sendTextMessage(
        chatId: chatId,
        senderId: senderId,
        content: content,
        replyToId: replyToId,
      );

      // تحديث قائمة المحادثات
      ref
        ..invalidate(userChatsProvider)
        ..invalidate(chatMessagesProvider(chatId));

      state = const AsyncValue.data(null);
      return message;
    } catch (e, stack) {
      _logger.severe('Error sending text message: $e', e, stack);
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  /// إرسال رسالة مع مرفق
  Future<MessageModel> sendMessageWithAttachment({
    required String chatId,
    required File file,
    required AttachmentType attachmentType,
    String? content,
    String? replyToId,
  }) async {
    state = const AsyncValue.loading();

    try {
      // Use currentUserProvider for user access
      final senderId = ref.read(currentUserProvider)?.id;

      if (senderId == null) {
        throw Exception('يجب تسجيل الدخول لإرسال رسالة');
      }

      final message = await ChatService.sendMessageWithAttachment(
        chatId: chatId,
        senderId: senderId,
        file: file,
        attachmentType: attachmentType,
        content: content,
        replyToId: replyToId,
      );

      // تحديث قائمة المحادثات
      ref
        ..invalidate(userChatsProvider)
        ..invalidate(chatMessagesProvider(chatId));

      state = const AsyncValue.data(null);
      return message;
    } catch (e, stack) {
      _logger.severe('Error sending message with attachment: $e', e, stack);
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }
}

/// مزود البحث في المحادثات - FIXED: Converted to @riverpod pattern
@riverpod
Future<List<ChatModel>> chatSearch(Ref ref, String query) async {
  if (query.trim().isEmpty) {
    return [];
  }

  try {
    // Use currentUserProvider for user access
    final userId = ref.read(currentUserProvider)?.id;

    if (userId == null) {
      return [];
    }

    // Mock search for now
    final allChats = await ref.read(userChatsProvider.future);
    return allChats
        .where(
          (chat) =>
              chat.productName?.toLowerCase().contains(query.toLowerCase()) ==
              true,
        )
        .toList();
  } catch (e, stack) {
    _logger.severe('Error searching chats: $e', e, stack);
    return [];
  }
}

/// مزود إنشاء محادثة جديدة
@riverpod
class ChatCreator extends _$ChatCreator {
  @override
  AsyncValue<ChatModel?> build() {
    return const AsyncValue.data(null);
  }

  /// إنشاء محادثة جديدة
  Future<ChatModel> createNewChat({
    required String otherUserId,
    String? productId,
    String? initialMessage,
  }) async {
    state = const AsyncValue.loading();

    try {
      final currentUser = ref.read(currentUserProvider);
      final currentUserId = currentUser?.id;

      if (currentUserId == null) {
        throw Exception('يجب تسجيل الدخول لإنشاء محادثة');
      }

      // Mock implementation for now
      // TODO: Replace with actual API call
      final newChat = ChatModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        buyerId: currentUserId,
        sellerId: otherUserId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        productId: productId,
      );

      // تحديث قائمة المحادثات
      ref.invalidate(userChatsProvider);

      state = AsyncValue.data(newChat);
      return newChat;
    } catch (e, stack) {
      _logger.severe('Error creating new chat: $e', e, stack);
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }
}

/// مزود أرشفة المحادثة
@riverpod
class ChatArchiver extends _$ChatArchiver {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  /// أرشفة محادثة
  Future<void> archiveChat(String chatId) async {
    state = const AsyncValue.loading();

    try {
      // Mock implementation for now
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(milliseconds: 500));

      // تحديث قائمة المحادثات
      ref.invalidate(userChatsProvider);

      state = const AsyncValue.data(null);
      _logger.info('Chat archived successfully: $chatId');
    } catch (e, stack) {
      _logger.severe('Error archiving chat: $e', e, stack);
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  /// حظر محادثة
  Future<void> blockChat(String chatId) async {
    state = const AsyncValue.loading();

    try {
      // Mock implementation for now
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(milliseconds: 500));

      // تحديث قائمة المحادثات
      ref.invalidate(userChatsProvider);

      state = const AsyncValue.data(null);
      _logger.info('Chat blocked successfully: $chatId');
    } catch (e, stack) {
      _logger.severe('Error blocking chat: $e', e, stack);
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  /// حذف محادثة
  Future<void> deleteChat(String chatId) async {
    state = const AsyncValue.loading();

    try {
      // Mock implementation for now
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(milliseconds: 500));

      // تحديث قائمة المحادثات
      ref.invalidate(userChatsProvider);

      state = const AsyncValue.data(null);
      _logger.info('Chat deleted successfully: $chatId');
    } catch (e, stack) {
      _logger.severe('Error deleting chat: $e', e, stack);
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }
}
