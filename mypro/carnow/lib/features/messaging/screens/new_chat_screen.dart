import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/theme/app_theme.dart';
import '../../account/models/user_model.dart';
import '../../../core/providers/users_provider.dart';
import '../../../core/widgets/app_error_widget.dart';
import '../../../core/widgets/loading_indicators.dart';
import '../providers/chat_providers.dart';

/// شاشة بدء محادثة جديدة
class NewChatScreen extends HookConsumerWidget {
  const NewChatScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchController = useTextEditingController();
    final searchQuery = useState('');

    final usersAsync = ref.watch(usersNotifierProvider);
    final currentUser = ref.read(currentUserProvider);

    // تصفية المستخدمين بناءً على البحث واستبعاد المستخدم الحالي
    final filteredUsers = usersAsync.when(
      data: (users) {
        var filtered = users
            .where(
              (user) =>
                  user.authId != currentUser?.id && // استبعاد المستخدم الحالي
                  !user.isDeleted,
            )
            .toList();

        if (searchQuery.value.isNotEmpty) {
          final query = searchQuery.value.toLowerCase();
          filtered = filtered
              .where(
                (user) =>
                    (user.name?.toLowerCase().contains(query) ?? false) ||
                    (user.email?.toLowerCase().contains(query) ?? false),
              )
              .toList();
        }

        return filtered;
      },
      loading: () => <UserModel>[],
      error: (error, _) => <UserModel>[],
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('بدء محادثة جديدة'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: searchController,
              decoration: InputDecoration(
                hintText: 'البحث عن مستخدم...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surfaceContainerHighest
                    .withAlpha((0.5 * 255).toInt()),
              ),
              onChanged: (value) {
                searchQuery.value = value;
              },
            ),
          ),

          // قائمة المستخدمين
          Expanded(
            child: usersAsync.when(
              data: (users) {
                if (filteredUsers.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.person_search,
                          size: 64,
                          color: Theme.of(context).colorScheme.onSurface
                              .withAlpha((0.5 * 255).toInt()),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          searchQuery.value.isEmpty
                              ? 'لا يوجد مستخدمون متاحون'
                              : 'لا توجد نتائج للبحث',
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurfaceVariant,
                              ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: filteredUsers.length,
                  itemBuilder: (context, index) {
                    final user = filteredUsers[index];
                    return _UserListItem(
                      user: user,
                      onTap: () => _startChat(context, ref, user),
                    );
                  },
                );
              },
              loading: LoadingIndicators.primary,
              error: (error, stack) => AppErrorWidget(
                message: 'Failed to load users',
                details: error.toString(),
                stackTrace: stack,
                onRetry: () => ref.refresh(usersNotifierProvider),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _startChat(
    BuildContext context,
    WidgetRef ref,
    UserModel user,
  ) async {
    try {
      // إظهار مؤشر التحميل
      unawaited(
        showDialog<void>(
          context: context,
          barrierDismissible: false,
          builder: (context) =>
              const Center(child: CircularProgressIndicator()),
        ),
      );

      // إنشاء المحادثة
      await ref
          .read(chatCreatorProvider.notifier)
          .createNewChat(otherUserId: user.authId);

      if (context.mounted) {
        // إغلاق مؤشر التحميل
        Navigator.of(context).pop();

        // العودة إلى شاشة المحادثات
        Navigator.of(context).pop();

        // إظهار رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم بدء محادثة مع ${user.name ?? 'المستخدم'}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        // إغلاق مؤشر التحميل
        Navigator.of(context).pop();

        // إظهار رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في بدء المحادثة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// عنصر قائمة المستخدم
class _UserListItem extends StatelessWidget {
  const _UserListItem({required this.user, required this.onTap});
  final UserModel user;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) => ListTile(
    leading: CircleAvatar(
      backgroundColor: Theme.of(context).colorScheme.primary,
      backgroundImage: user.profileImageUrl != null
          ? NetworkImage(user.profileImageUrl!)
          : null,
      child: user.profileImageUrl == null
          ? Text(
              (user.name?.isNotEmpty == true ? user.name![0] : '?')
                  .toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            )
          : null,
    ),
    title: Text(
      user.name ?? 'عضو جديد',
      style: const TextStyle(fontWeight: FontWeight.w500),
    ),
    subtitle: Text(
      user.email ?? 'لا يوجد بريد إلكتروني',
      style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant),
    ),
    trailing: Icon(
      Icons.chat_bubble_outline,
      color: Theme.of(context).colorScheme.primary,
    ),
    onTap: onTap,
  );
}
