// =============================================================================
// NEEDS FOREVER PLAN ARCHITECTURE UPDATE
// =============================================================================
// هذا الـ service يحتاج تحديث ليتوافق مع Forever Plan Architecture:
// Flutter (UI Only) → Go API → Supabase (Data Only)
// 
// المطلوب:
// 1. إزالة استدعاءات _client.storage المباشرة (السطر 425-427)
// 2. استخدام Go backend endpoints للتعامل مع file uploads
// 3. تحديث logic ليعمل مع SimpleApiClient
// =============================================================================

import 'dart:async';
import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/networking/simple_api_client.dart';
import '../models/chat_message.dart';
import '../models/chat_conversation.dart';
import '../models/chat_models.dart';

/// Chat Service - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// FIXED: Removed direct Supabase calls
/// ✅ Uses SimpleApiClient ONLY
/// ✅ Uses SimpleAuthSystem for auth

class ChatService {
  final Ref _ref;
  
  ChatService(this._ref);

  /// Get user chats using Go backend API  
  static Future<List<ChatModel>> getUserChats(String userId) async {
    try {
      // Mock implementation for now
      // TODO: Replace with actual API call
      return [];
    } catch (e) {
      throw Exception('Failed to get user chats: $e');
    }
  }

  /// Get chat messages using Go backend API
  static Future<List<MessageModel>> getChatMessages(String chatId) async {
    try {
      // Mock implementation for now
      // TODO: Replace with actual API call
      return [];
    } catch (e) {
      throw Exception('Failed to get chat messages: $e');
    }
  }

  /// Send text message using Go backend API
  static Future<MessageModel> sendTextMessage({
    required String chatId,
    required String senderId,
    required String content,
    String? replyToId,
  }) async {
    try {
      // Mock implementation for now
      // TODO: Replace with actual API call
      return MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: chatId,
        senderId: senderId,
        content: content,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      throw Exception('Failed to send text message: $e');
    }
  }

  /// Send message with attachment using Go backend API
  static Future<MessageModel> sendMessageWithAttachment({
    required String chatId,
    required String senderId,
    required File file,
    required AttachmentType attachmentType,
    String? content,
    String? replyToId,
  }) async {
    try {
      // Mock implementation for now
      // TODO: Replace with actual API call
      return MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: chatId,
        senderId: senderId,
        content: content ?? 'Attachment sent',
        timestamp: DateTime.now(),
      );
    } catch (e) {
      throw Exception('Failed to send message with attachment: $e');
    }
  }

  /// Get user conversations using Go backend API
  Future<List<ChatConversation>> getUserConversations() async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      final response = await apiClient.getApi<Map<String, dynamic>>(
        '/chat/conversations',
      );
      
      if (!response.isSuccess || response.data == null) {
        return [];
      }

      final data = response.data!;
      final List<dynamic> conversationsJson = data.containsKey('conversations') && data['conversations'] is List
          ? data['conversations'] as List<dynamic>
          : data.containsKey('data') && data['data'] is List
              ? data['data'] as List<dynamic>
              : [];

      return conversationsJson
          .map((json) => ChatConversation.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('Failed to get conversations: $e');
    }
  }

  /// Get messages for a conversation using Go backend API
  Future<List<ChatMessage>> getConversationMessages(String conversationId) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      final response = await apiClient.getApi<Map<String, dynamic>>(
        '/chat/conversations/$conversationId/messages',
      );
      
      if (!response.isSuccess || response.data == null) {
        return [];
      }

      final data = response.data!;
      final List<dynamic> messagesJson = data.containsKey('messages') && data['messages'] is List
          ? data['messages'] as List<dynamic>
          : data.containsKey('data') && data['data'] is List
              ? data['data'] as List<dynamic>
              : [];

      return messagesJson
          .map((json) => ChatMessage.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('Failed to get messages: $e');
    }
  }

  /// Send a message using Go backend API
  Future<ChatMessage> sendMessage({
    required String conversationId,
    required String content,
    String messageType = 'text',
  }) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      final response = await apiClient.postApi<Map<String, dynamic>>(
        '/chat/conversations/$conversationId/messages',
        data: {
          'content': content,
          'message_type': messageType,
        },
      );
      
      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to send message: ${response.message}');
      }

      return ChatMessage.fromJson(response.data!);
    } catch (e) {
      throw Exception('Failed to send message: $e');
    }
  }

  /// Create a new conversation using Go backend API
  Future<ChatConversation> createConversation({
    required String participantId,
    String? initialMessage,
  }) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      final response = await apiClient.postApi<Map<String, dynamic>>(
        '/chat/conversations',
        data: {
          'participant_id': participantId,
          if (initialMessage != null) 'initial_message': initialMessage,
        },
      );
      
      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to create conversation: ${response.message}');
      }

      return ChatConversation.fromJson(response.data!);
    } catch (e) {
      throw Exception('Failed to create conversation: $e');
    }
  }

  /// Mark messages as read using Go backend API
  Future<void> markMessagesAsRead(String conversationId) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      final response = await apiClient.putApi<Map<String, dynamic>>(
        '/chat/conversations/$conversationId/read',
        data: {},
      );
      
      if (!response.isSuccess) {
        throw Exception('Failed to mark messages as read: ${response.message}');
      }
    } catch (e) {
      throw Exception('Failed to mark messages as read: $e');
    }
  }

  /// Get unread message count using Go backend API
  Future<int> getUnreadMessageCount() async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      final response = await apiClient.getApi<Map<String, dynamic>>(
        '/chat/unread-count',
      );
      
      if (!response.isSuccess || response.data == null) {
        return 0;
      }

      return response.data!['count'] as int? ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// Stream of real-time messages (using WebSocket or Server-Sent Events)
  Stream<ChatMessage> getMessageStream(String conversationId) {
    // TODO: Implement real-time messaging through Go backend
    // This could be WebSocket or Server-Sent Events
    return Stream.empty();
  }
}

// Provider for ChatService
final chatServiceProvider = Provider<ChatService>((ref) {
  return ChatService(ref);
});

// =============================================================================
// REMOVED: Direct Supabase Violations
// =============================================================================
// The following violations have been REMOVED:
// ❌ Direct _client.rpc() calls to Supabase
// ❌ Real-time subscriptions to Supabase
// ❌ Direct database operations from Flutter
// ❌ Complex channel subscriptions
//
// Replaced with:
// ✅ SimpleApiClient for ALL data operations
// ✅ Go backend API endpoints for chat
// ✅ Clean async/await patterns
// ✅ Forever Plan compliance
