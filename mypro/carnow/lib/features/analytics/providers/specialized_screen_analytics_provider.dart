import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/networking/simple_api_client.dart';
import '../models/specialized_screen_analytics_model.dart';

part 'specialized_screen_analytics_provider.g.dart';

@riverpod
Future<List<SpecializedScreenAnalytics>> specializedScreenAnalytics(
  Ref ref,
) async {
  final apiClient = ref.watch(simpleApiClientProvider);

  try {
    // Call Go backend API for analytics data
    final response = await apiClient.get('/api/v1/analytics/specialized-screen-analytics');
    
    final data = response.data as Map<String, dynamic>;
    final analyticsList = <SpecializedScreenAnalytics>[];
    
    // If backend returns data in the expected format
    if (data['success'] == true && data['data'] != null) {
      final events = data['data'] as List;
      
      // Process the data to get screen usage statistics
      final Map<String, int> screenCounts = {};
      final Map<String, DateTime> lastUsed = {};

      for (final event in events) {
        final properties = event['properties'] as Map<String, dynamic>;
        final screenType = properties['screen_type'] as String;
        final createdAt = DateTime.parse(event['created_at'] as String);

        screenCounts[screenType] = (screenCounts[screenType] ?? 0) + 1;

        if (lastUsed[screenType] == null ||
            createdAt.isAfter(lastUsed[screenType]!)) {
          lastUsed[screenType] = createdAt;
        }
      }

      // Convert to analytics models
      analyticsList.addAll(screenCounts.entries.map((entry) {
        return SpecializedScreenAnalytics(
          screenType: entry.key,
          viewCount: entry.value,
          lastUsed: lastUsed[entry.key] ?? DateTime.now(),
          popularityScore: _calculatePopularityScore(
            entry.value,
            lastUsed[entry.key],
          ),
        );
      }));
    }

    // Sort by popularity score
    analyticsList.sort(
      (a, b) => b.popularityScore.compareTo(a.popularityScore),
    );

    return analyticsList;
  } catch (e) {
    throw Exception('Failed to fetch specialized screen analytics: $e');
  }
}

@riverpod
Future<Map<String, dynamic>> specializedScreenSummary(Ref ref) async {
  final analytics = await ref.watch(specializedScreenAnalyticsProvider.future);

  final totalViews = analytics.fold<int>(
    0,
    (sum, item) => sum + item.viewCount,
  );
  final mostUsedScreen = analytics.isNotEmpty ? analytics.first : null;
  final leastUsedScreen = analytics.isNotEmpty ? analytics.last : null;

  return {
    'totalViews': totalViews,
    'totalScreenTypes': analytics.length,
    'mostUsedScreen': mostUsedScreen?.screenType,
    'mostUsedScreenViews': mostUsedScreen?.viewCount ?? 0,
    'leastUsedScreen': leastUsedScreen?.screenType,
    'leastUsedScreenViews': leastUsedScreen?.viewCount ?? 0,
    'averageViewsPerScreen': analytics.isNotEmpty
        ? totalViews / analytics.length
        : 0,
  };
}

/// Calculate popularity score based on view count and recency
double _calculatePopularityScore(int viewCount, DateTime? lastUsed) {
  if (lastUsed == null) return viewCount.toDouble();

  final daysSinceLastUse = DateTime.now().difference(lastUsed).inDays;
  final recencyMultiplier = daysSinceLastUse <= 1
      ? 1.5
      : daysSinceLastUse <= 7
      ? 1.2
      : daysSinceLastUse <= 14
      ? 1.0
      : 0.8;

  return viewCount * recencyMultiplier;
}
