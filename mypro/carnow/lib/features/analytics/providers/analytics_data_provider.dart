import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/services/analytics_api_service.dart';
import '../../../core/networking/simple_api_client.dart';
import '../models/analytics_models.dart';

part 'analytics_data_provider.g.dart';

/// Provider for general analytics data dashboard
/// FIXED: No more direct Supabase calls - now uses Go API backend
@riverpod
Future<AnalyticsData> analyticsData(Ref ref) async {
  final analyticsService = ref.read(analyticsApiServiceProvider);

  try {
    // Get all analytics data through API service
    final overviewData = await analyticsService.getOverviewData();
    final usersData = await analyticsService.getUsersData();
    final productsData = await analyticsService.getProductsData();
    final salesData = await analyticsService.getSalesData();
    final engagementData = await analyticsService.getEngagementData();

    return AnalyticsData(
      overview: OverviewData(
        totalUsers: overviewData['totalUsers'] ?? 0,
        totalSales: (overviewData['totalSales'] ?? 0.0).toDouble(),
        totalOrders: overviewData['totalOrders'] ?? 0,
        conversionRate: (overviewData['conversionRate'] ?? 0.0).toDouble(),
        userGrowth: (overviewData['userGrowth'] ?? 0.0).toDouble(),
        salesGrowth: (overviewData['salesGrowth'] ?? 0.0).toDouble(),
        ordersGrowth: (overviewData['ordersGrowth'] ?? 0.0).toDouble(),
        conversionGrowth: 0.0, // TODO: Add to API response
      ),
      users: const UsersData(
        activity: [],
        sessionsStats: SessionStats(
          averageDuration: Duration.zero,
          bounceRate: 0,
          pagesPerSession: 0,
        ),
        demographics: Demographics(
          ageGroups: {},
          genderDistribution: {},
          locations: {},
        ),
        geographic: [],
      ),
      products: const ProductsData(
        topCategories: [],
        topProducts: [],
        inventory: InventoryData(
          totalProducts: 0,
          lowStock: 0,
          outOfStock: 0,
          totalValue: 0,
        ),
      ),
      sales: const SalesData(
        monthly: [],
        daily: [],
        revenue: RevenueData(
          total: 0,
          growth: 0,
          forecast: 0,
        ),
      ),
      engagement: const EngagementData(
        bounceRate: 0,
        sessionDuration: Duration.zero,
        pageViews: 0,
        interactions: [],
      ),
    );
  } catch (e) {
    throw Exception('خطأ في تحميل بيانات الإحصائيات: $e');
  }
}

/// Provider for specialized screen analytics
@riverpod
Future<Map<String, dynamic>> specializedScreenAnalytics(
  Ref ref,
  String screenName,
) async {
  final apiClient = ref.read(simpleApiClientProvider);
  
  try {
    final response = await apiClient.get('/analytics/screen/$screenName');
    return response.data as Map<String, dynamic>;
  } catch (e) {
    throw Exception('خطأ في تحميل إحصائيات الشاشة: $e');
  }
}

/// Provider for inventory analytics
@riverpod
Future<Map<String, dynamic>> inventoryAnalytics(Ref ref) async {
  final analyticsService = ref.read(analyticsApiServiceProvider);
  
  try {
    final productsData = await analyticsService.getProductsData();
    
    return {
      'totalProducts': productsData['totalProducts'] ?? 0,
      'activeProducts': productsData['activeProducts'] ?? 0,
      'lowStockItems': productsData['lowStockItems'] ?? 0,
      'outOfStockItems': productsData['outOfStockItems'] ?? 0,
      'recentlyAdded': productsData['topProducts'] ?? [],
    };
  } catch (e) {
    throw Exception('خطأ في تحميل إحصائيات المخزون: $e');
  }
}
