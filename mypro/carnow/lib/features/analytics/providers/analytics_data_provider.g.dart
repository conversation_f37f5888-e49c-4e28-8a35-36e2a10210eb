// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics_data_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$analyticsDataHash() => r'07b1b3942dbe757dbab7f575c66053a6f9e383ce';

/// Provider for general analytics data dashboard
/// FIXED: No more direct Supabase calls - now uses Go API backend
///
/// Copied from [analyticsData].
@ProviderFor(analyticsData)
final analyticsDataProvider = AutoDisposeFutureProvider<AnalyticsData>.internal(
  analyticsData,
  name: r'analyticsDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$analyticsDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AnalyticsDataRef = AutoDisposeFutureProviderRef<AnalyticsData>;
String _$specializedScreenAnalyticsHash() =>
    r'728c509854aa9852e36d2f354c9a72aa726e8d88';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for specialized screen analytics
///
/// Copied from [specializedScreenAnalytics].
@ProviderFor(specializedScreenAnalytics)
const specializedScreenAnalyticsProvider = SpecializedScreenAnalyticsFamily();

/// Provider for specialized screen analytics
///
/// Copied from [specializedScreenAnalytics].
class SpecializedScreenAnalyticsFamily
    extends Family<AsyncValue<Map<String, dynamic>>> {
  /// Provider for specialized screen analytics
  ///
  /// Copied from [specializedScreenAnalytics].
  const SpecializedScreenAnalyticsFamily();

  /// Provider for specialized screen analytics
  ///
  /// Copied from [specializedScreenAnalytics].
  SpecializedScreenAnalyticsProvider call(String screenName) {
    return SpecializedScreenAnalyticsProvider(screenName);
  }

  @override
  SpecializedScreenAnalyticsProvider getProviderOverride(
    covariant SpecializedScreenAnalyticsProvider provider,
  ) {
    return call(provider.screenName);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'specializedScreenAnalyticsProvider';
}

/// Provider for specialized screen analytics
///
/// Copied from [specializedScreenAnalytics].
class SpecializedScreenAnalyticsProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>> {
  /// Provider for specialized screen analytics
  ///
  /// Copied from [specializedScreenAnalytics].
  SpecializedScreenAnalyticsProvider(String screenName)
    : this._internal(
        (ref) => specializedScreenAnalytics(
          ref as SpecializedScreenAnalyticsRef,
          screenName,
        ),
        from: specializedScreenAnalyticsProvider,
        name: r'specializedScreenAnalyticsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$specializedScreenAnalyticsHash,
        dependencies: SpecializedScreenAnalyticsFamily._dependencies,
        allTransitiveDependencies:
            SpecializedScreenAnalyticsFamily._allTransitiveDependencies,
        screenName: screenName,
      );

  SpecializedScreenAnalyticsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.screenName,
  }) : super.internal();

  final String screenName;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>> Function(
      SpecializedScreenAnalyticsRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SpecializedScreenAnalyticsProvider._internal(
        (ref) => create(ref as SpecializedScreenAnalyticsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        screenName: screenName,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>> createElement() {
    return _SpecializedScreenAnalyticsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SpecializedScreenAnalyticsProvider &&
        other.screenName == screenName;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, screenName.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SpecializedScreenAnalyticsRef
    on AutoDisposeFutureProviderRef<Map<String, dynamic>> {
  /// The parameter `screenName` of this provider.
  String get screenName;
}

class _SpecializedScreenAnalyticsProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>>
    with SpecializedScreenAnalyticsRef {
  _SpecializedScreenAnalyticsProviderElement(super.provider);

  @override
  String get screenName =>
      (origin as SpecializedScreenAnalyticsProvider).screenName;
}

String _$inventoryAnalyticsHash() =>
    r'd2f6b564157dffa5ccc4c5420ce04d07f25ee4f7';

/// Provider for inventory analytics
///
/// Copied from [inventoryAnalytics].
@ProviderFor(inventoryAnalytics)
final inventoryAnalyticsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      inventoryAnalytics,
      name: r'inventoryAnalyticsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$inventoryAnalyticsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef InventoryAnalyticsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
