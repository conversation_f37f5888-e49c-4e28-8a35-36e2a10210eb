import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';

import '../../../core/widgets/app_error_widget.dart';
import '../../../core/widgets/loading_indicators.dart';
import '../../favorites/providers/favorites_provider.dart';
import '../../parts/providers/part_provider.dart';
import 'package:carnow/core/theme/app_colors.dart';
import '../../../core/utils/app_styles.dart';
import '../../products/models/product_model.dart';
import '../providers/auction_provider.dart';
import '../widgets/auction_timer_widget.dart';
import '../widgets/bid_history_widget.dart';
import '../models/bid_model.dart';

/// شاشة تفاصيل المزاد (النسخة المحدثة)
///
/// تعرض هذه الشاشة جميع المعلومات الحيوية لمنتج معروض في مزاد.
/// تشمل معرض صور للمنتج، مؤقت للعد التنازلي لنهاية المزاد، إحصائيات المزاد،
/// السعر الحالي، سجل المزايدات السابقة، ووصف تفصيلي للمنتج.
/// كما تتيح للمستخدمين المؤهلين تقديم مزايدات جديدة.
class AuctionDetailScreen extends ConsumerStatefulWidget {
  const AuctionDetailScreen({required this.partId, super.key});
  final String partId;

  @override
  ConsumerState<AuctionDetailScreen> createState() =>
      _AuctionDetailScreenState();
}

class _AuctionDetailScreenState extends ConsumerState<AuctionDetailScreen>
    with TickerProviderStateMixin {
  late final AnimationController _slideController;

  Color get _shadowColor =>
      Color.lerp(Colors.transparent, Colors.grey, 0.1) ?? Colors.transparent;

  final PageController _imageController = PageController();
  final TextEditingController _bidController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initAnimations();
  }

  void _initAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _slideController.dispose();
    _imageController.dispose();
    _bidController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final partAsync = ref.watch(partProviderProvider(widget.partId));
    final bidsAsync = ref.watch(auctionBidsProvider(widget.partId));

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: partAsync.when(
        data: (ProductModel? part) {
          if (part == null) {
            return const Scaffold(
              body: Center(child: Text('لم يتم العثور على المنتج')),
            );
          }

          final isAuction = part.isAuction;
          final hasEnded = part.hasAuctionEnded;

          return CustomScrollView(
            slivers: [
              _buildSliverAppBar(part, ref, context),
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    _buildImageCarousel(part),
                    _buildProductInfo(part),
                    if (isAuction) ...[
                      if (!hasEnded) _buildAuctionTimer(part),
                      _buildBidStats(),
                      _buildCurrentBidSection(bidsAsync),
                      _buildBidHistory(),
                    ],
                    _buildSellerInfo(part),
                    _buildProductDetails(part),
                    _buildCompatibilityInfo(part),
                    const SizedBox(height: 100),
                  ],
                ),
              ),
            ],
          );
        },
        loading: () => const Scaffold(body: Center(child: LoadingSpinner())),
        error: (Object error, StackTrace stackTrace) => Scaffold(
          appBar: AppBar(title: const Text('خطأ')),
          body: Center(
            child: AppErrorWidget(
              message: 'خطأ في تحميل تفاصيل المنتج',
              details: error.toString(),
              stackTrace: stackTrace,
              onRetry: () =>
                  ref.invalidate(partProviderProvider(widget.partId)),
            ),
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildSliverAppBar(
    ProductModel part,
    WidgetRef ref,
    BuildContext context,
  ) {
    final isFav = ref.watch(isFavoriteProvider(widget.partId));

    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          part.name,
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(color: Colors.white),
        ),
        background: part.images.isNotEmpty
            ? CachedNetworkImage(imageUrl: part.images.first, fit: BoxFit.cover)
            : Container(color: Colors.grey.shade200),
      ),
      actions: [
        IconButton(
          icon: Icon(isFav ? Icons.favorite : Icons.favorite_border),
          color: isFav
              ? Colors.red
              : Theme.of(context).appBarTheme.actionsIconTheme?.color,
          onPressed: () {
            ref
                .read(favoritesNotifierProvider.notifier)
                .toggleFavorite(widget.partId);
          },
        ),
        IconButton(icon: const Icon(Icons.share), onPressed: _shareAuction),
      ],
    );
  }

  Widget _buildImageCarousel(ProductModel part) {
    final images = part.images;
    if (images.isEmpty) {
      return Container(
        height: 250,
        color: Colors.grey.shade200,
        child: const Center(child: Icon(Icons.image_not_supported, size: 50)),
      );
    }

    return SizedBox(
      height: 250,
      child: PageView.builder(
        controller: _imageController,
        itemCount: images.length,
        onPageChanged: (index) {
          setState(() {
            // Update current image index
          });
        },
        itemBuilder: (context, index) => CachedNetworkImage(
          imageUrl: images[index],
          fit: BoxFit.cover,
          placeholder: (context, url) =>
              const Center(child: CircularProgressIndicator()),
          errorWidget: (context, url, error) => const Icon(Icons.error),
        ),
      ),
    );
  }

  Widget _buildAuctionTimer(ProductModel part) {
    if (part.auctionEndDate == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: _shadowColor,
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: AuctionTimerWidget(auctionEndDate: part.auctionEndDate!),
    );
  }

  Widget _buildBidStats() => Container(
    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: _shadowColor,
          spreadRadius: 1,
          blurRadius: 10,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatItem('المزايدات', '0'),
        _buildStatItem('المشاهدات', '0'),
        _buildStatItem('المتابعين', '0'),
      ],
    ),
  );

  Widget _buildStatItem(String label, String value) => Column(
    children: [
      Text(
        value,
        style: AppStyles.headingMedium.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
        ),
      ),
      const SizedBox(height: 4),
      Text(
        label,
        style: AppStyles.bodySmall.copyWith(color: Colors.grey.shade600),
      ),
    ],
  );

  Widget _buildBidHistory() {
    final shadowColor =
        Color.lerp(Colors.transparent, Colors.grey, 0.1) ?? Colors.transparent;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: shadowColor,
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'سجل المزايدات',
            style: AppStyles.headingMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          BidHistoryWidget(partId: widget.partId),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() => Container(
    margin: const EdgeInsets.all(16),
    child: FloatingActionButton.extended(
      onPressed: () {
        // Handle bid button press
      },
      label: const Text('قدم عرضاً'),
      icon: const Icon(Icons.gavel),
      backgroundColor: AppColors.primary,
    ),
  );

  Widget _buildConditionBadge(String? condition) {
    if (condition == null) {
      return const SizedBox.shrink();
    }

    Color badgeColor;
    switch (condition.toLowerCase()) {
      case 'new':
        badgeColor = Colors.green;
        break;
      case 'used':
        badgeColor = Colors.orange;
        break;
      case 'refurbished':
        badgeColor = Colors.blue;
        break;
      default:
        badgeColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: badgeColor.withAlpha(25),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: badgeColor),
      ),
      child: Text(
        condition,
        style: TextStyle(color: badgeColor, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildPriceSection(ProductModel part) => Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const SizedBox(height: 16),
      Text(
        'السعر',
        style: AppStyles.bodyLarge.copyWith(color: Colors.grey.shade600),
      ),
      const SizedBox(height: 4),
      Text(
        '${part.price.toStringAsFixed(0)} د.ل',
        style: AppStyles.headingLarge.copyWith(
          color: AppColors.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    ],
  );

  Widget _buildDetailRow(String label, String? value) {
    if (value == null || value.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: AppStyles.bodyMedium.copyWith(color: Colors.grey.shade600),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              textAlign: TextAlign.end,
              style: AppStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  void _shareAuction() {
    SharePlus.instance.share(
      ShareParams(text: 'Check out this auction on CarNow!'),
    );
  }

  void _contactSeller() {
    // Implement contact seller functionality
  }

  Widget _buildProductInfo(ProductModel part) => Container(
    margin: const EdgeInsets.all(16),
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withAlpha(25),
          spreadRadius: 1,
          blurRadius: 10,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    part.name,
                    style: AppStyles.headingLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  if (part.brand != null) ...[
                    Text(
                      'الماركة: ${part.brand}',
                      style: AppStyles.bodyMedium.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                  if (part.model != null) ...[
                    Text(
                      'الموديل: ${part.model}',
                      style: AppStyles.bodyMedium.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            _buildConditionBadge(part.condition as String),
          ],
        ),
        const SizedBox(height: 16),
        if (part.description != null) ...[
          Text(
            'الوصف',
            style: AppStyles.headingMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            part.description!,
            style: AppStyles.bodyMedium.copyWith(height: 1.5),
          ),
          const SizedBox(height: 16),
        ],
        _buildPriceSection(part),
      ],
    ),
  );

  Widget _buildCurrentBidSection(AsyncValue<List<BidModel>> bidsAsync) =>
      Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha(25),
              spreadRadius: 1,
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: bidsAsync.when(
          data: (bids) {
            if (bids.isEmpty) {
              return Column(
                children: [
                  Text(
                    'لا توجد عطاءات حتى الآن',
                    style: AppStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'كن أول من يضع عطاءاً!',
                    style: AppStyles.bodyMedium.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              );
            }

            final highestBid = bids.first;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أعلى عطاء حالي',
                  style: AppStyles.bodyMedium.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      '${highestBid.amount?.toStringAsFixed(0) ?? '0'} د.ل',
                      style: AppStyles.headingLarge.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      DateFormat(
                        'HH:mm - dd/MM/yyyy',
                      ).format(highestBid.createdAt ?? DateTime.now()),
                      style: AppStyles.bodySmall.copyWith(
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Text(
            'خطأ في تحميل العطاءات',
            style: AppStyles.bodyMedium.copyWith(color: Colors.red),
          ),
        ),
      );

  Widget _buildSellerInfo(ProductModel part) => Container(
    margin: const EdgeInsets.all(16),
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withAlpha(25),
          spreadRadius: 1,
          blurRadius: 10,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات البائع',
          style: AppStyles.headingMedium.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            const CircleAvatar(radius: 25, child: Icon(Icons.person)),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'البائع',
                    style: AppStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'متجر قطع الغيار',
                    style: AppStyles.bodyMedium.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: _contactSeller,
              icon: const Icon(Icons.message),
              color: AppColors.primary,
            ),
          ],
        ),
      ],
    ),
  );

  Widget _buildProductDetails(ProductModel part) => Container(
    margin: const EdgeInsets.all(16),
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withAlpha(25),
          spreadRadius: 1,
          blurRadius: 10,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تفاصيل المنتج',
          style: AppStyles.headingMedium.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildDetailRow('الماركة', part.brand),
        _buildDetailRow('الموديل', part.model),
        if (part.description != null)
          _buildDetailRow('الوصف', part.description),
      ],
    ),
  );

  Widget _buildCompatibilityInfo(ProductModel part) => Container(
    margin: const EdgeInsets.all(16),
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withAlpha(25),
          spreadRadius: 1,
          blurRadius: 10,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات التوافق',
          style: AppStyles.headingMedium.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        const Text(
          'متوافق مع العديد من السيارات',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 13,
            fontWeight: FontWeight.w500,
            height: 1.4,
            letterSpacing: 0.3,
            color: Color(0xFF757575),
          ),
        ),
      ],
    ),
  );
}
