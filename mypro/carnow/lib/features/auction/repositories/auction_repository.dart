import 'dart:async';

import '../../../core/repositories/base_repository.dart';
import '../../../core/errors/result.dart';
import '../models/bid_model.dart';

/// Repository for managing auction and bid data
/// ✅ Updated to use Forever Plan Architecture
class AuctionRepository extends BaseRepository {
  AuctionRepository(super.ref);

  @override
  String get apiPath => '/api/v1/auctions';

  /// Place a bid on a part
  Future<Result<BidModel>> placeBid({
    required int partId,
    required String userId,
    required double amount,
  }) => executeWithErrorHandling(() async {
    final response = await apiClient.post(
      '/api/v1/auctions/bids',
      data: {
        'part_id': partId,
        'user_id': userId,
        'amount': amount,
      },
    );

    return BidModel.fromJson(response.data as Map<String, dynamic>);
  }, operationName: 'placeBid');

  /// Get all bids for a specific part
  Future<Result<List<BidModel>>> getBidsForPart(int partId) => 
    getAll<BidModel>(
      fromJson: BidModel.fromJson,
      queryParams: {'part_id': partId.toString()},
    );

  /// Get user's bid history
  Future<Result<List<BidModel>>> getUserBidHistory(String userId) =>
    getAll<BidModel>(
      fromJson: BidModel.fromJson,
      queryParams: {'user_id': userId},
    );

  /// Get highest bid for a part
  Future<Result<BidModel?>> getHighestBid(int partId) => 
    executeWithErrorHandling(() async {
    final response = await apiClient.get(
      '/api/v1/auctions/parts/$partId/highest-bid',
    );

    if (response.data == null) return null;
    return BidModel.fromJson(response.data as Map<String, dynamic>);
  }, operationName: 'getHighestBid');

  /// Check if user can bid on a part
  Future<Result<bool>> canUserBid({
    required int partId,
    required String userId,
    required double amount,
  }) => executeWithErrorHandling(() async {
    final response = await apiClient.get(
      '/api/v1/auctions/parts/$partId/can-bid',
      queryParameters: {
        'user_id': userId,
        'amount': amount.toString(),
      },
    );

    return response.data['can_bid'] as bool;
  }, operationName: 'canUserBid');

  /// Get auction status for a part
  Future<Result<Map<String, dynamic>>> getAuctionStatus(int partId) =>
    executeWithErrorHandling(() async {
    final response = await apiClient.get(
      '/api/v1/auctions/parts/$partId/status',
    );

    return response.data as Map<String, dynamic>;
  }, operationName: 'getAuctionStatus');

  /// Get bid statistics for a part
  Future<Result<Map<String, dynamic>>> getBidStatistics(int partId) =>
    executeWithErrorHandling(() async {
    final response = await apiClient.get(
      '/api/v1/auctions/parts/$partId/statistics',
    );

    return response.data as Map<String, dynamic>;
  }, operationName: 'getBidStatistics');

  /// Withdraw a bid (if allowed)
  Future<Result<void>> withdrawBid(int bidId) => 
    executeWithErrorHandling(() async {
    await apiClient.delete('/api/v1/auctions/bids/$bidId');
  }, operationName: 'withdrawBid');

  /// Get active auctions
  Future<Result<List<Map<String, dynamic>>>> getActiveAuctions({
    int? limit,
    int? offset,
  }) => executeWithErrorHandling(() async {
    final response = await apiClient.get(
      '/api/v1/auctions/active',
      queryParameters: {
        if (limit != null) 'limit': limit.toString(),
        if (offset != null) 'offset': offset.toString(),
      },
    );

    return (response.data as List<dynamic>).cast<Map<String, dynamic>>();
  }, operationName: 'getActiveAuctions');

  /// Get ending soon auctions
  Future<Result<List<Map<String, dynamic>>>> getEndingSoonAuctions({
    int hoursThreshold = 24,
  }) => executeWithErrorHandling(() async {
    final response = await apiClient.get(
      '/api/v1/auctions/ending-soon',
      queryParameters: {'hours_threshold': hoursThreshold.toString()},
    );

    return (response.data as List<dynamic>).cast<Map<String, dynamic>>();
  }, operationName: 'getEndingSoonAuctions');

  /// Get auction history for a part
  Future<Result<List<BidModel>>> getAuctionHistory(int partId) =>
    getAll<BidModel>(
      fromJson: BidModel.fromJson,
      queryParams: {
        'part_id': partId.toString(),
        'include_history': 'true',
      },
    );

  /// Search auctions
  Future<Result<List<Map<String, dynamic>>>> searchAuctions({
    String? query,
    String? category,
    double? minPrice,
    double? maxPrice,
  }) => executeWithErrorHandling(() async {
    final response = await apiClient.get(
      '/api/v1/auctions/search',
      queryParameters: {
        if (query != null) 'q': query,
        if (category != null) 'category': category,
        if (minPrice != null) 'min_price': minPrice.toString(),
        if (maxPrice != null) 'max_price': maxPrice.toString(),
      },
    );

    return (response.data as List<dynamic>).cast<Map<String, dynamic>>();
  }, operationName: 'searchAuctions');
}
