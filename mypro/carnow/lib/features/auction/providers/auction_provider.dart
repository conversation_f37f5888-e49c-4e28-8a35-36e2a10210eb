import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/networking/simple_api_client.dart';
import '../../products/models/product_model.dart';
import '../models/bid_model.dart';

part 'auction_provider.g.dart';

final _logger = Logger('AuctionProvider');

/// Auction Providers
/// Following Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)

/// Provider for fetching all auctions (products with auction listing type)
@riverpod
Future<List<ProductModel>> auctions(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.get('/api/v1/auctions');

    if (response.isSuccess && response.data != null) {
      final List<dynamic> auctionsData = response.data['auctions'] ?? [];
      return auctionsData.map((json) => ProductModel.fromJson(json)).toList();
    }

    return [];
  } catch (e) {
    _logger.severe('Failed to fetch auctions: $e');
    throw Exception('Failed to fetch auctions: $e');
  }
}

/// Provider for fetching auctions by category
@riverpod
Future<List<ProductModel>> auctionsByCategory(Ref ref, String category) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.get(
      '/api/v1/auctions/category/$category',
    );

    if (response.isSuccess && response.data != null) {
      final List<dynamic> auctionsData = response.data['auctions'] ?? [];
      return auctionsData.map((json) => ProductModel.fromJson(json)).toList();
    }

    return [];
  } catch (e) {
    _logger.severe('Failed to fetch auctions by category: $e');
    throw Exception('Failed to fetch auctions by category: $e');
  }
}

/// Provider for fetching a single auction by ID
@riverpod
Future<ProductModel?> auction(Ref ref, String auctionId) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.get('/api/v1/auctions/$auctionId');

    if (response.isSuccess && response.data != null) {
      return ProductModel.fromJson(response.data);
    }

    return null;
  } catch (e) {
    _logger.severe('Failed to fetch auction: $e');
    throw Exception('Failed to fetch auction: $e');
  }
}

/// Provider for fetching bids for a specific auction
@riverpod
Future<List<BidModel>> auctionBids(Ref ref, String auctionId) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.get('/api/v1/auctions/$auctionId/bids');

    if (response.isSuccess && response.data != null) {
      final List<dynamic> bidsData = response.data['bids'] ?? [];
      return bidsData.map((json) => BidModel.fromJson(json)).toList();
    }

    return [];
  } catch (e) {
    _logger.severe('Failed to fetch auction bids: $e');
    throw Exception('Failed to fetch auction bids: $e');
  }
}

/// Provider for placing a bid on an auction
@riverpod
Future<bool> placeBid(Ref ref, String auctionId, double bidAmount) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.post(
      '/api/v1/auctions/$auctionId/bids',
      data: {
        'amount': bidAmount,
      },
    );

    return response.isSuccess;
  } catch (e) {
    _logger.severe('Failed to place bid: $e');
    throw Exception('Failed to place bid: $e');
  }
}

/// Provider for the highest bid for a specific auction
@riverpod
Future<BidModel?> highestBid(Ref ref, String auctionId) async {
  try {
    final bids = await ref.watch(auctionBidsProvider(auctionId).future);
    return (bids.isNotEmpty) ? bids.first : null;
  } catch (e) {
    _logger.severe('Failed to get highest bid: $e');
    return null;
  }
}

/// Provider to check if the current user is the highest bidder
/// Forever Plan: Uses Go backend for data, SimpleAuthSystem for user info
@riverpod
Future<bool> isCurrentUserHighestBidder(Ref ref, String auctionId) async {
  try {
    final currentUser = ref.read(currentUserProvider);
    
    if (currentUser == null) {
      return false;
    }

    final highestBidResult = await ref.watch(highestBidProvider(auctionId).future);
    return highestBidResult?.userId == currentUser.id;
  } catch (e) {
    _logger.severe('Failed to check highest bidder: $e');
    return false;
  }
}

/// Provider that tracks time remaining for an auction
@riverpod
Stream<Duration> auctionTimeRemaining(Ref ref, String auctionId) {
  final controller = StreamController<Duration>.broadcast();

  Timer.periodic(const Duration(seconds: 1), (timer) {
    // TODO: Get actual auction end time from backend
    // For now, create a simple countdown from 1 hour
    final endTime = DateTime.now().add(const Duration(hours: 1));
    final now = DateTime.now();
    final difference = endTime.difference(now);

    if (difference.isNegative) {
      if (!controller.isClosed) {
        controller.add(Duration.zero);
      }
      timer.cancel();
    } else {
      if (!controller.isClosed) {
        controller.add(difference);
      }
    }
  });

  ref.onDispose(() {
    if (!controller.isClosed) {
      controller.close();
    }
  });

  return controller.stream;
}

/// Simple auction stats model
class AuctionStats {
  final int bidCount;
  final int bidders;

  const AuctionStats({
    this.bidCount = 0,
    this.bidders = 0,
  });
}

/// Provider for auction stats
@riverpod
Future<AuctionStats> auctionStats(Ref ref, String auctionId) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.get('/api/v1/auctions/$auctionId/stats');

    if (response.isSuccess && response.data != null) {
      final data = response.data;
      return AuctionStats(
        bidCount: data['bid_count'] ?? 0,
        bidders: data['unique_bidders'] ?? 0,
      );
    }

    return const AuctionStats();
  } catch (e) {
    _logger.severe('Failed to get auction stats: $e');
    return const AuctionStats();
  }
}

/// Provider for ending auctions that have expired
@riverpod
Future<bool> endExpiredAuctions(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.post('/api/v1/auctions/end-expired');

    return response.isSuccess;
  } catch (e) {
    _logger.severe('Failed to end expired auctions: $e');
    throw Exception('Failed to end expired auctions: $e');
  }
}

/// Provider for creating a bid on an auction
@riverpod
Future<bool> createBid(Ref ref, String auctionId, double amount) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.post(
      '/api/v1/auctions/$auctionId/bids',
      data: {
        'amount': amount,
      },
    );

    if (response.isSuccess) {
      // Invalidate related providers to refresh data
      ref.invalidate(auctionBidsProvider(auctionId));
      ref.invalidate(auctionProvider(auctionId));
      return true;
    }

    return false;
  } catch (e) {
    _logger.severe('Failed to create bid: $e');
    throw Exception('Failed to create bid: $e');
  }
}

/// Provider for bid submission with optimistic updates
@riverpod  
Future<void> submitBid(Ref ref, String auctionId, double bidAmount) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.post(
      '/api/v1/auctions/$auctionId/bids',
      data: {
        'amount': bidAmount,
      },
    );

    if (!response.isSuccess) {
      throw Exception('Failed to submit bid');
    }

    // Refresh auction data
    ref.invalidate(auctionBidsProvider(auctionId));
    ref.invalidate(auctionProvider(auctionId));
  } catch (e) {
    _logger.severe('Failed to submit bid: $e');
    throw Exception('Failed to submit bid: $e');
  }
}

/// Provider for auction service
@riverpod
AuctionService auctionService(Ref ref) => AuctionService(ref);

/// Clean AuctionService that follows Forever Plan
class AuctionService {
  const AuctionService(this.ref);
  final Ref ref;

  /// Place a bid on an auction
  Future<bool> placeBid(String auctionId, double amount) async {
    try {
      final currentUser = ref.read(currentUserProvider);

      if (currentUser == null) {
        throw Exception('User must be logged in to place a bid');
      }

      // Validate bid amount against current highest bid
      final currentBids = await ref.read(auctionBidsProvider(auctionId).future);
      if (currentBids.isNotEmpty) {
        final highestAmount = currentBids.first.amount ?? 0;
        if (amount <= highestAmount) {
          throw Exception('Bid must be higher than current highest bid');
        }
      }

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.post(
        '/api/v1/auctions/$auctionId/bids',
        data: {
          'amount': amount,
          'user_id': currentUser.id,
        },
      );

      if (response.isSuccess) {
        // Refresh auction data
        ref.invalidate(auctionBidsProvider(auctionId));
        ref.invalidate(auctionProvider(auctionId));
        return true;
      }

      return false;
    } catch (e) {
      _logger.severe('Failed to place bid: $e');
      rethrow;
    }
  }
}
