import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:carnow/core/theme/app_colors.dart';
import '../../../shared/utils/app_styles.dart';
import '../providers/auction_provider.dart';

class BidStatsWidget extends ConsumerWidget {
  const BidStatsWidget({
    required this.partId,
    super.key,
    this.isCompact = false,
  });
  final String partId;
  final bool isCompact;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bidsAsync = ref.watch(auctionBidsProvider(partId));

    return bidsAsync.when(
      data: (bids) {
        final bidCount = bids.length;

        if (isCompact) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha((0.1 * 255).toInt()),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.gavel, size: 14, color: AppColors.primary),
                const SizedBox(width: 4),
                Text(
                  bidCount.toString(),
                  style: AppStyles.caption.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          );
        }

        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.primary.withAlpha((05 * 255).toInt()),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'إجمالي العطاءات',
                    style: AppStyles.bodyMedium.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      bidCount.toString(),
                      style: AppStyles.bodyMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              if (bids.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'أعلى عطاء',
                      style: AppStyles.bodySmall.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      '${bids.first.amount?.toStringAsFixed(0) ?? '0'} د.ل',
                      style: AppStyles.bodyMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
      },
      loading: () => Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.primary.withAlpha((05 * 255).toInt()),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Center(
          child: SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      ),
      error: (error, stack) => Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red.withAlpha((05 * 255).toInt()),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          'خطأ في تحميل البيانات',
          style: AppStyles.bodySmall.copyWith(color: Colors.red),
        ),
      ),
    );
  }
}
