/// شاشة تفاصيل المنتج
///
/// تعرض صور المنتج ومواصفاته الكاملة والسعر والحالة، مع إمكانية الدردشة مع
/// البائع أو إضافة المنتج للمفضلة.
/// تحتوي على معرض للصور، السعر، الوصف الكامل، المواصفات الفنية،
/// معلومات عن البائع، وتقييمات المنتج من المشترين الآخرين.
/// كما توفر أزرارًا لإضافة المنتج إلى السلة أو قائمة المفضلة.
library;

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/widgets/loading_indicators.dart';
import '../../../../shared/widgets/error_message.dart';
import '../../../../core/auth/unified_auth_provider.dart';
import '../../../../core/models/product_model.dart';
import '../../providers/products_provider.dart';

import 'specialized/specialized_product_details_factory.dart';

class ProductDetailsScreen extends HookConsumerWidget {
  const ProductDetailsScreen({super.key, this.productId, this.product});
  final String? productId;
  final ProductModel? product;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (product == null && productId == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('خطأ')),
        body: const Center(child: Text('لم يتم توفير معرف المنتج')),
      );
    }

    // PERFORMANCE FIX: Use RepaintBoundary to isolate expensive repaints
    final productDetailsAsync = ref.watch(productProvider(productId!));

    return productDetailsAsync.when(
      data: (listing) {
        if (listing == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('خطأ')),
            body: const Center(child: Text('لم يتم العثور على المنتج')),
          );
        }

        final currentUser = ref.watch(currentUserProvider);
        final currentUserId = currentUser?.id;
        final isOwner =
            currentUserId != null && currentUserId == listing.sellerId;

        return RepaintBoundary(
          child: SpecializedProductDetailsFactory.createProductDetailsScreen(
            product: listing,
            isOwner: isOwner,
          ),
        );
      },
      loading: () => Scaffold(body: LoadingIndicators.primary()),
      error: (error, stackTrace) => Scaffold(
        appBar: AppBar(title: const Text('خطأ')),
        body: ErrorMessage(message: 'فشل في تحميل المنتج: ${error.toString()}'),
      ),
    );
  }
}
