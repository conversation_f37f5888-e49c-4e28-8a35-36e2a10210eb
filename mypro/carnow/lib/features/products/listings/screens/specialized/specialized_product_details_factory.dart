import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../core/models/enums.dart';
import '../../../../../core/models/product_model.dart';
import '../../../../../core/auth/unified_auth_provider.dart';
import '../../../providers/products_provider.dart';

import 'auto_parts_details_screen.dart';
import 'electronics_details_screen.dart';
import 'vehicles_details_screen.dart';
import 'tools_details_screen.dart';
import 'accessories_details_screen.dart';
import 'maintenance_details_screen.dart';

/// مصنع الشاشات المتخصصة لتفاصيل المنتجات
/// يحدد أي شاشة متخصصة يجب عرضها بناءً على نوع المنتج
class SpecializedProductDetailsFactory {
  /// إنشاء الشاشة المناسبة بناءً على نوع المنتج
  static Widget createProductDetailsScreen({
    required ProductModel product,
    required bool isOwner,
  }) {
    // تحديد نوع المنتج
    final productType = _determineProductType(product);

    // تتبع استخدام الشاشات المتخصصة
    _trackSpecializedScreenUsage(productType, product.id);

    switch (productType) {
      case ProductType.autoParts:
        return AutoPartsDetailsScreen(product: product, isOwner: isOwner);

      case ProductType.electronics:
        return ElectronicsDetailsScreen(product: product, isOwner: isOwner);

      case ProductType.vehicles:
        return VehiclesDetailsScreen(product: product, isOwner: isOwner);

      case ProductType.tools:
        return ToolsDetailsScreen(product: product, isOwner: isOwner);

      case ProductType.accessories:
        return AccessoriesDetailsScreen(product: product, isOwner: isOwner);

      case ProductType.maintenance:
        return MaintenanceDetailsScreen(product: product, isOwner: isOwner);

      case ProductType.other:
        // استخدام الشاشة العامة للأنواع الأخرى
        return _ProductDetailsView(listing: product, isOwner: isOwner);
    }
  }

  /// تحديد نوع المنتج بناءً على البيانات المتاحة
  static ProductType _determineProductType(ProductModel product) {
    // إذا كان نوع المنتج محدد مباشرة
    if (product.productType != null) {
      return product.productType!;
    }

    // إذا كان المنتج قطعة غيار
    if (product.isPart ||
        product.partNumber != null ||
        product.compatibleVehicles.isNotEmpty) {
      return ProductType.autoParts;
    }

    // تحديد النوع بناءً على نوع السيارات
    if (product.automotiveType != null) {
      switch (product.automotiveType!) {
        case AutomotiveType.vehicles:
          return ProductType.vehicles;
        case AutomotiveType.electricalElectronic:
          return ProductType.electronics;
        default:
          return ProductType.autoParts;
      }
    }

    // إذا كان يحتوي على معلومات سيارة (موديل، سنة، إلخ)
    if (product.yearFrom != null ||
        product.yearTo != null ||
        (product.brand != null && product.model != null)) {
      return ProductType.vehicles;
    }

    // افتراضي: قطع غيار
    return ProductType.autoParts;
  }

  /// تتبع استخدام الشاشات المتخصصة لأغراض التحليل
  static void _trackSpecializedScreenUsage(
    ProductType productType,
    String productId,
  ) {
    // TODO: Implement analytics tracking with proper Ref context
    // Analytics tracking requires WidgetRef context - move to widget level
    final screenName = _getScreenNameForProductType(productType);
    
    // Log for debugging purposes
    debugPrint('Specialized screen view: $screenName for product: $productId');
  }

  /// الحصول على اسم الشاشة بناءً على نوع المنتج
  static String _getScreenNameForProductType(ProductType productType) {
    switch (productType) {
      case ProductType.vehicles:
        return 'vehicles_details_screen';
      case ProductType.autoParts:
        return 'auto_parts_details_screen';
      case ProductType.electronics:
        return 'electronics_details_screen';
      case ProductType.tools:
        return 'tools_details_screen';
      case ProductType.accessories:
        return 'accessories_details_screen';
      case ProductType.maintenance:
        return 'maintenance_details_screen';
      case ProductType.other:
        return 'general_details_screen';
    }
  }
}

/// Wrapper widget that fetches product data and creates the appropriate specialized screen
class ProductDetailsWrapper extends ConsumerWidget {
  const ProductDetailsWrapper({super.key, required this.productId});

  final String productId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productAsync = ref.watch(productProvider(productId));

    return productAsync.when(
      data: (product) {
        if (product == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('Product Not Found')),
            body: const Center(child: Text('المنتج غير موجود')),
          );
        }

        // Enhanced authentication logic
        final currentUser = ref.watch(currentUserProvider);
        final isOwner =
            currentUser != null && currentUser.id == product.sellerId;

        return SpecializedProductDetailsFactory.createProductDetailsScreen(
          product: product,
          isOwner: isOwner,
        );
      },
      loading: () => Scaffold(
        appBar: AppBar(title: const Text('Loading...')),
        body: const Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('خطأ في تحميل المنتج: ${error.toString()}'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.invalidate(productProvider(productId)),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// إعادة تصدير الشاشة الأساسية للاستخدام الداخلي
class _ProductDetailsView extends StatelessWidget {
  const _ProductDetailsView({required this.listing, required this.isOwner});

  final ProductModel listing;
  final bool isOwner;

  @override
  Widget build(BuildContext context) {
    // هنا يمكن إضافة التصميم العام أو إعادة توجيه للشاشة الأساسية
    return const Scaffold(body: Center(child: Text('شاشة عامة - قيد التطوير')));
  }
}
