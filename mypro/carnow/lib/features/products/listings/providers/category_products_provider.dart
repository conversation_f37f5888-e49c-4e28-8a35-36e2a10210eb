import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../services/product_api_service.dart';
import '../../models/product_model.dart';
import '../../models/product_filter_model.dart';

part 'category_products_provider.g.dart';

final _logger = Logger('CategoryProductsProvider');

/// مزود يقوم بتحميل المنتجات حسب الفئة والمعايير الأخرى
/// يدعم الخطة التوسعية للتطبيق بالسماح بتصفية المنتجات حسب الفئات المتعددة
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<ProductModel>> categoryProducts(
  Ref ref, {
  String? categorySlug,
  bool featured = false,
  String? condition,
  int limit = 10,
}) async {
  try {
    _logger.info('Fetching products for category: $categorySlug');

    final apiService = ref.watch(productApiServiceProvider);

    // Get all products and filter locally for now
    final products = await apiService.getProductsSimple();
    
    // Filter products based on category
    List<ProductModel> filteredProducts = products;
    
    if (categorySlug != null) {
      filteredProducts = products.where((product) => 
        product.categoryId == categorySlug
      ).toList();
    }
    
    // Apply featured filter if requested
    if (featured) {
      filteredProducts = filteredProducts.where((product) => 
        product.isFeatured == true
      ).toList();
    }
    
    // Apply limit
    if (filteredProducts.length > limit) {
      filteredProducts = filteredProducts.take(limit).toList();
    }

    _logger.info(
      'Successfully fetched ${filteredProducts.length} products for category: $categorySlug',
    );
    return filteredProducts;
  } catch (e, stack) {
    _logger.severe('Error fetching category products', e, stack);
    rethrow;
  }
}

/// مزود للمنتجات الشائعة (بدون تحديد فئة)
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<ProductModel>> popularProducts(Ref ref, {int limit = 10}) async {
  try {
    _logger.info('Fetching popular products');

    final apiService = ref.watch(productApiServiceProvider);

    // Get all products and take the first ones as "popular"
    final products = await apiService.getProductsSimple();
    
    // Take the first products as popular (backend could implement popularity ranking)
    final popularProducts = products.take(limit).toList();

    _logger.info('Successfully fetched ${popularProducts.length} popular products');
    return popularProducts;
  } catch (e, stack) {
    _logger.severe('Error fetching popular products', e, stack);
    rethrow;
  }
}

/// مزود للبحث في المنتجات
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<ProductModel>> searchProducts(
  Ref ref, {
  required String query,
  String? categorySlug,
  int limit = 20,
}) async {
  try {
    _logger.info('Searching products for: $query in category: $categorySlug');

    final apiService = ref.watch(productApiServiceProvider);

    // Use the search method from API service
    final products = await apiService.searchProducts(query);

    // Filter by category if specified
    List<ProductModel> filteredProducts = products;
    if (categorySlug != null) {
      filteredProducts = products.where((product) => 
        product.categoryId == categorySlug
      ).toList();
    }
    
    // Apply limit
    if (filteredProducts.length > limit) {
      filteredProducts = filteredProducts.take(limit).toList();
    }

    _logger.info(
      'Successfully found ${filteredProducts.length} products for search: $query',
    );
    return filteredProducts;
  } catch (e, stack) {
    _logger.severe('Error searching products', e, stack);
    rethrow;
  }
}
