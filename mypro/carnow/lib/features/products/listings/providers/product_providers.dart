import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../services/product_api_service.dart';
import '../../models/product_model.dart';

part 'product_providers.g.dart';

// Logger for product providers
final _logger = Logger('ProductProviders');

/// Renamed provider: Fetches all active products with pagination
/// Forever Plan: Uses HTTP calls to Go backend only
final productsProvider = FutureProvider<List<ProductModel>>((ref) async {
  try {
    _logger.info('Fetching products from Go backend');
    final apiService = ref.watch(productApiServiceProvider);
    final products = await apiService.getProductsSimple();
    return products.take(20).toList();
  } catch (e) {
    _logger.severe('Error fetching products: $e');
    rethrow;
  }
});

/// Fetches products with pagination parameters - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<ProductModel>> paginatedProducts(Ref ref, Map<String, int> params) async {
  final page = params['page'] ?? 1;
  final pageSize = params['pageSize'] ?? 20;
  
  try {
    _logger.info('Fetching paginated products: page $page, limit $pageSize');
    final apiService = ref.watch(productApiServiceProvider);
    final products = await apiService.getProductsSimple();
    
    // Handle pagination locally for now
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= products.length) {
      return <ProductModel>[];
    }
    
    return products.skip(startIndex).take(pageSize).toList();
  } catch (e) {
    _logger.severe('Error fetching paginated products: $e');
    rethrow;
  }
}

/// Fetches a specific product by ID - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<ProductModel?> productDetails(Ref ref, String id) async {
  try {
    _logger.info('Fetching product details for ID: $id');
    final apiService = ref.watch(productApiServiceProvider);
    return await apiService.getProductById(id);
  } catch (e) {
    _logger.severe('Error fetching product details: $e');
    rethrow;
  }
}

/// Fetches all products for a specific user - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<ProductModel>> userProducts(Ref ref, String userId) async {
  try {
    _logger.info('Fetching user products for user: $userId');
    final apiService = ref.watch(productApiServiceProvider);
    return await apiService.getProductsByUserId(userId);
  } catch (e) {
    _logger.severe('Error fetching user products: $e');
    rethrow;
  }
}

/// Fetches user products with pagination - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<ProductModel>> paginatedUserProducts(Ref ref, Map<String, dynamic> params) async {
  final userId = params['userId'] as String;
  final page = params['page'] as int? ?? 1;
  final pageSize = params['pageSize'] as int? ?? 20;
  
  try {
    _logger.info('Fetching paginated user products for user: $userId');
    final apiService = ref.watch(productApiServiceProvider);
    final products = await apiService.getProductsByUserId(userId);
    
    // Handle pagination locally for now
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= products.length) {
      return <ProductModel>[];
    }
    
    return products.skip(startIndex).take(pageSize).toList();
  } catch (e) {
    _logger.severe('Error fetching paginated user products: $e');
    rethrow;
  }
}

/// Car brands provider - Note: This might need backend support for vehicle data
/// Forever Plan: For now keeping minimal, should be moved to Go backend
final carBrandsProvider = FutureProvider<List<String>>((ref) async {
  try {
    _logger.info('Car brands feature needs backend implementation');
    // TODO: Implement car brands endpoint in Go backend
    return <String>[]; // Empty for now until backend supports this
  } catch (e) {
    _logger.severe('Error fetching car brands: $e');
    rethrow;
  }
});

/// Car models provider - FIXED: Converted to @riverpod pattern
/// Forever Plan: For now keeping minimal, should be moved to Go backend
@riverpod
Future<List<String>> carModels(Ref ref, int brandId) async {
  try {
    _logger.info('Car models feature needs backend implementation');
    // TODO: Implement car models endpoint in Go backend
    return <String>[]; // Empty for now until backend supports this
  } catch (e) {
    _logger.severe('Error fetching car models for brand $brandId: $e');
    rethrow;
  }
}
