// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paginated_products_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$filteredProductsHash() => r'c76880ce60ba5afa942ed5a9aed839287f9d16db';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// مزود للمنتجات المفلترة
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [filteredProducts].
@ProviderFor(filteredProducts)
const filteredProductsProvider = FilteredProductsFamily();

/// مزود للمنتجات المفلترة
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [filteredProducts].
class FilteredProductsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// مزود للمنتجات المفلترة
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [filteredProducts].
  const FilteredProductsFamily();

  /// مزود للمنتجات المفلترة
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [filteredProducts].
  FilteredProductsProvider call({
    ProductFilter? filter,
    String? category,
    bool featured = false,
    int limit = 20,
  }) {
    return FilteredProductsProvider(
      filter: filter,
      category: category,
      featured: featured,
      limit: limit,
    );
  }

  @override
  FilteredProductsProvider getProviderOverride(
    covariant FilteredProductsProvider provider,
  ) {
    return call(
      filter: provider.filter,
      category: provider.category,
      featured: provider.featured,
      limit: provider.limit,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'filteredProductsProvider';
}

/// مزود للمنتجات المفلترة
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [filteredProducts].
class FilteredProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// مزود للمنتجات المفلترة
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [filteredProducts].
  FilteredProductsProvider({
    ProductFilter? filter,
    String? category,
    bool featured = false,
    int limit = 20,
  }) : this._internal(
         (ref) => filteredProducts(
           ref as FilteredProductsRef,
           filter: filter,
           category: category,
           featured: featured,
           limit: limit,
         ),
         from: filteredProductsProvider,
         name: r'filteredProductsProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$filteredProductsHash,
         dependencies: FilteredProductsFamily._dependencies,
         allTransitiveDependencies:
             FilteredProductsFamily._allTransitiveDependencies,
         filter: filter,
         category: category,
         featured: featured,
         limit: limit,
       );

  FilteredProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.filter,
    required this.category,
    required this.featured,
    required this.limit,
  }) : super.internal();

  final ProductFilter? filter;
  final String? category;
  final bool featured;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(FilteredProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FilteredProductsProvider._internal(
        (ref) => create(ref as FilteredProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        filter: filter,
        category: category,
        featured: featured,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _FilteredProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FilteredProductsProvider &&
        other.filter == filter &&
        other.category == category &&
        other.featured == featured &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, filter.hashCode);
    hash = _SystemHash.combine(hash, category.hashCode);
    hash = _SystemHash.combine(hash, featured.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FilteredProductsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `filter` of this provider.
  ProductFilter? get filter;

  /// The parameter `category` of this provider.
  String? get category;

  /// The parameter `featured` of this provider.
  bool get featured;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _FilteredProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with FilteredProductsRef {
  _FilteredProductsProviderElement(super.provider);

  @override
  ProductFilter? get filter => (origin as FilteredProductsProvider).filter;
  @override
  String? get category => (origin as FilteredProductsProvider).category;
  @override
  bool get featured => (origin as FilteredProductsProvider).featured;
  @override
  int get limit => (origin as FilteredProductsProvider).limit;
}

String _$categoryProductsHash() => r'0ea96f144b44dc150e4f4b5b55875899725752c6';

/// مزود للمنتجات حسب الفئات
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [categoryProducts].
@ProviderFor(categoryProducts)
const categoryProductsProvider = CategoryProductsFamily();

/// مزود للمنتجات حسب الفئات
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [categoryProducts].
class CategoryProductsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// مزود للمنتجات حسب الفئات
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [categoryProducts].
  const CategoryProductsFamily();

  /// مزود للمنتجات حسب الفئات
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [categoryProducts].
  CategoryProductsProvider call({
    String? categorySlug,
    bool featured = false,
    String? condition,
    int limit = 10,
  }) {
    return CategoryProductsProvider(
      categorySlug: categorySlug,
      featured: featured,
      condition: condition,
      limit: limit,
    );
  }

  @override
  CategoryProductsProvider getProviderOverride(
    covariant CategoryProductsProvider provider,
  ) {
    return call(
      categorySlug: provider.categorySlug,
      featured: provider.featured,
      condition: provider.condition,
      limit: provider.limit,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categoryProductsProvider';
}

/// مزود للمنتجات حسب الفئات
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [categoryProducts].
class CategoryProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// مزود للمنتجات حسب الفئات
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [categoryProducts].
  CategoryProductsProvider({
    String? categorySlug,
    bool featured = false,
    String? condition,
    int limit = 10,
  }) : this._internal(
         (ref) => categoryProducts(
           ref as CategoryProductsRef,
           categorySlug: categorySlug,
           featured: featured,
           condition: condition,
           limit: limit,
         ),
         from: categoryProductsProvider,
         name: r'categoryProductsProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$categoryProductsHash,
         dependencies: CategoryProductsFamily._dependencies,
         allTransitiveDependencies:
             CategoryProductsFamily._allTransitiveDependencies,
         categorySlug: categorySlug,
         featured: featured,
         condition: condition,
         limit: limit,
       );

  CategoryProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categorySlug,
    required this.featured,
    required this.condition,
    required this.limit,
  }) : super.internal();

  final String? categorySlug;
  final bool featured;
  final String? condition;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(CategoryProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategoryProductsProvider._internal(
        (ref) => create(ref as CategoryProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categorySlug: categorySlug,
        featured: featured,
        condition: condition,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _CategoryProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryProductsProvider &&
        other.categorySlug == categorySlug &&
        other.featured == featured &&
        other.condition == condition &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categorySlug.hashCode);
    hash = _SystemHash.combine(hash, featured.hashCode);
    hash = _SystemHash.combine(hash, condition.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategoryProductsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `categorySlug` of this provider.
  String? get categorySlug;

  /// The parameter `featured` of this provider.
  bool get featured;

  /// The parameter `condition` of this provider.
  String? get condition;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _CategoryProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with CategoryProductsRef {
  _CategoryProductsProviderElement(super.provider);

  @override
  String? get categorySlug => (origin as CategoryProductsProvider).categorySlug;
  @override
  bool get featured => (origin as CategoryProductsProvider).featured;
  @override
  String? get condition => (origin as CategoryProductsProvider).condition;
  @override
  int get limit => (origin as CategoryProductsProvider).limit;
}

String _$paginatedProductsHash() => r'7f9342d5146b9b59772c6d60e76733b3212e6d94';

/// مزود المنتجات المُصفح باستخدام Riverpod
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [PaginatedProducts].
@ProviderFor(PaginatedProducts)
final paginatedProductsProvider =
    AutoDisposeAsyncNotifierProvider<
      PaginatedProducts,
      List<ProductModel>
    >.internal(
      PaginatedProducts.new,
      name: r'paginatedProductsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$paginatedProductsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PaginatedProducts = AutoDisposeAsyncNotifier<List<ProductModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
