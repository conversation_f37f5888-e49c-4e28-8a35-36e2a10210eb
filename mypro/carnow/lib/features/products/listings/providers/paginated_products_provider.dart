import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../models/product_model.dart';
import '../../models/product_filter_model.dart';
import '../../services/product_api_service.dart';
import 'filter_provider.dart';

part 'paginated_products_provider.g.dart';

// Define logger for this provider
final _logger = Logger('PaginatedProducts');

/// مزود المنتجات المُصفح باستخدام Riverpod
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
class PaginatedProducts extends _$PaginatedProducts {
  static const int _pageSize = 10;
  
  bool get hasMore => _hasNextPage;
  bool _hasNextPage = true;
  int _currentPage = 1;
  bool _isLoadingMore = false;
  bool get isLoadingMore => _isLoadingMore;

  @override
  Future<List<ProductModel>> build() async {
    ref.onDispose(() {
      _logger.info('PaginatedProducts disposed');
    });

    _logger.info('PaginatedProducts: Loading initial data from Go backend');
    return _loadInitialData();
  }

  /// تحميل البيانات الأولية من Go backend
  Future<List<ProductModel>> _loadInitialData() async {
    try {
      _logger.info('Loading initial products from Go backend');
      
      final apiService = ref.read(productApiServiceProvider);
      final products = await apiService.getProductsSimple();

      _logger.info('Successfully loaded ${products.length} products from Go backend');
      
      // For now, we'll load all products and handle pagination locally
      // In the future, backend can implement proper pagination
      _hasNextPage = products.length > _pageSize;
      return products.take(_pageSize).toList();
    } catch (e, s) {
      _logger.severe('Error loading initial products', e, s);
      
      // If it's a backend error, provide helpful message
      if (e.toString().contains('Failed to count products') || 
          e.toString().contains('SERVER_ERROR')) {
        _logger.warning('⚠️ Backend is temporarily unavailable, showing empty product list');
        _hasNextPage = false;
        return <ProductModel>[];
      }
      
      rethrow;
    }
  }

  /// تحديث البيانات
  Future<void> refresh() async {
    _isLoadingMore = false;
    _currentPage = 1;
    _hasNextPage = true;

    // إعادة بناء المزود
    ref.invalidateSelf();
    _logger.info('PaginatedProducts refreshed');
  }

  /// تحميل المزيد من البيانات
  Future<void> loadMore() async {
    if (_isLoadingMore || !_hasNextPage) return;

    _isLoadingMore = true;
    
    try {
      await _loadMoreData();
    } finally {
      _isLoadingMore = false;
    }
  }

  Future<void> _loadMoreData() async {
    try {
      final currentProducts = state.value!;
      final nextPage = _currentPage + 1;
      final filter = ref.read(selectedFilterProvider);

      final apiService = ref.read(productApiServiceProvider);
      final allProducts = await apiService.getProductsSimple();

      // Filter by category if specified
      List<ProductModel> filteredProducts = allProducts;
      if (filter.isNotEmpty && filter != 'All') {
        filteredProducts = allProducts.where((product) => 
          product.categoryId == filter
        ).toList();
      }

      // Calculate pagination
      final startIndex = (nextPage - 1) * _pageSize;
      final endIndex = startIndex + _pageSize;
      
      if (startIndex >= filteredProducts.length) {
        _hasNextPage = false;
        return;
      }

      final newProducts = filteredProducts.skip(startIndex).take(_pageSize).toList();
      
      if (newProducts.isEmpty) {
        _hasNextPage = false;
      } else {
        _currentPage = nextPage;
        final updatedProducts = [...currentProducts, ...newProducts];
        state = AsyncValue.data(updatedProducts);
        _hasNextPage = endIndex < filteredProducts.length;
      }
    } catch (e, s) {
      _logger.severe('Error loading more products', e, s);
      rethrow;
    }
  }
}

/// مزود للمنتجات المفلترة
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<ProductModel>> filteredProducts(
  Ref ref, {
  ProductFilter? filter,
  String? category,
  bool featured = false,
  int limit = 20,
}) async {
  _logger.info(
    'filteredProducts: Loading with filter: $filter, category: $category, featured: $featured',
  );

  final apiService = ref.read(productApiServiceProvider);

  try {
    final products = await apiService.getProductsSimple();
    
    // Apply filters locally
    List<ProductModel> filteredProducts = products;
    
    // Filter by category
    if (category != null) {
      filteredProducts = filteredProducts.where((product) => 
        product.categoryId == category
      ).toList();
    }
    
    // Filter by featured
    if (featured) {
      filteredProducts = filteredProducts.where((product) => 
        product.isFeatured == true
      ).toList();
    }
    
    // Apply limit
    if (filteredProducts.length > limit) {
      filteredProducts = filteredProducts.take(limit).toList();
    }

    _logger.info('filteredProducts: Loaded ${filteredProducts.length} products');
    return filteredProducts;
  } catch (e, stack) {
    _logger.severe('Error loading filtered products', e, stack);
    rethrow;
  }
}

/// مزود للمنتجات حسب الفئات
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<ProductModel>> categoryProducts(
  Ref ref, {
  String? categorySlug,
  bool featured = false,
  String? condition,
  int limit = 10,
}) async {
  _logger.info(
    'categoryProducts: Loading with categorySlug: $categorySlug, featured: $featured',
  );

  final apiService = ref.read(productApiServiceProvider);

  try {
    final products = await apiService.getProductsSimple();
    
    // Apply filters locally
    List<ProductModel> filteredProducts = products;
    
    // Filter by category
    if (categorySlug != null) {
      filteredProducts = filteredProducts.where((product) => 
        product.categoryId == categorySlug
      ).toList();
    }
    
    // Filter by featured
    if (featured) {
      filteredProducts = filteredProducts.where((product) => 
        product.isFeatured == true
      ).toList();
    }
    
    // Apply limit
    if (filteredProducts.length > limit) {
      filteredProducts = filteredProducts.take(limit).toList();
    }

    _logger.info('categoryProducts: Loaded ${filteredProducts.length} products');
    return filteredProducts;
  } catch (e, stack) {
    _logger.severe('Error loading category products', e, stack);
    rethrow;
  }
}
