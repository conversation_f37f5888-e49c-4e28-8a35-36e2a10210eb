import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:carnow/core/models/product.dart';
import 'package:carnow/core/services/api_client.dart';
import 'package:carnow/features/products/providers/category_providers.dart';

// Provider for adding products
final addProductProvider = StateNotifierProvider<AddProductNotifier, AsyncValue<void>>((ref) {
  final apiClient = ref.read(apiClientProvider);
  return AddProductNotifier(apiClient);
});

class AddProductNotifier extends StateNotifier<AsyncValue<void>> {
  final ApiClient _apiClient;

  AddProductNotifier(this._apiClient) : super(const AsyncValue.data(null));

  Future<void> addProduct({
    required Map<String, dynamic> productData,
    required List<Map<String, dynamic>> attributesData,
  }) async {
    state = const AsyncValue.loading();

    try {
      // First, create the product
      final productResponse = await _apiClient.post('/api/v1/products', productData);
      
      if (!productResponse.isSuccess) {
        throw Exception('Failed to create product: ${productResponse.error ?? 'Unknown error'}');
      }

      final productId = productResponse.data['id'] as String;

      // Then, add the attributes
      for (final attributeData in attributesData) {
        final attributeResponse = await _apiClient.post(
          '/api/v1/product-attributes',
          {
            'product_id': productId,
            'category_attribute_id': attributeData['category_attribute_id'],
            'attribute_value': attributeData['attribute_value'],
          },
        );

        if (!attributeResponse.isSuccess) {
          throw Exception('Failed to add product attribute: ${attributeResponse.error ?? 'Unknown error'}');
        }
      }

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }
}

// Provider for products list with pagination
final productsProvider = FutureProvider.family<List<Product>, Map<String, dynamic>>((ref, params) async {
  final apiClient = ref.read(apiClientProvider);
  
  final page = params['page'] as int? ?? 1;
  final limit = params['limit'] as int? ?? 20;
  final categoryId = params['category_id'] as String?;
  
  final endpoint = categoryId != null 
      ? '/api/v1/products?main_category_id=$categoryId&page=$page&limit=$limit'
      : '/api/v1/products?page=$page&limit=$limit';
      
  final response = await apiClient.get(endpoint);
  
  if (!response.isSuccess) {
    throw Exception('Failed to load products: ${response.error ?? 'Unknown error'}');
  }
  
  final List<dynamic> data = response.data['data'] ?? [];
  return data.map((json) => Product.fromJson(json)).toList();
});

// Provider for products by category
final productsByCategoryProvider = FutureProvider.family<List<Product>, String>((ref, mainCategoryId) async {
  final products = await ref.watch(productsProvider({'category_id': mainCategoryId}).future);
  return products;
});

// Provider for product search
final productSearchProvider = StateNotifierProvider<ProductSearchNotifier, AsyncValue<List<Product>>>((ref) {
  final apiClient = ref.read(apiClientProvider);
  return ProductSearchNotifier(apiClient);
});

class ProductSearchNotifier extends StateNotifier<AsyncValue<List<Product>>> {
  final ApiClient _apiClient;

  ProductSearchNotifier(this._apiClient) : super(const AsyncValue.data([]));

  Future<void> searchProducts({
    String? query,
    String? mainCategoryId,
    Map<String, String>? filters,
    int? page,
    int? limit,
  }) async {
    state = const AsyncValue.loading();

    try {
      final response = await _apiClient.searchProducts(
        query: query,
        mainCategoryId: mainCategoryId,
        filters: filters,
        page: page,
        limit: limit,
      );

      if (!response.isSuccess) {
        throw Exception('Search failed: ${response.error ?? 'Unknown error'}');
      }

      final List<dynamic> data = response.data['data'] ?? [];
      final products = data.map((json) => Product.fromJson(json)).toList();

      state = AsyncValue.data(products);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void clearSearch() {
    state = const AsyncValue.data([]);
  }
}

// Provider for product details
final productDetailsProvider = FutureProvider.family<Product?, String>((ref, productId) async {
  final apiClient = ref.read(apiClientProvider);
  final response = await apiClient.get('/api/v1/products/$productId');
  
  if (!response.isSuccess) {
    throw Exception('Failed to load product details: ${response.error ?? 'Unknown error'}');
  }
  
  return Product.fromJson(response.data);
});

// Provider for product attributes
final productAttributesProvider = FutureProvider.family<Map<String, String>, String>((ref, productId) async {
  final apiClient = ref.read(apiClientProvider);
  final response = await apiClient.get('/api/v1/products/$productId/attributes');
  
  if (!response.isSuccess) {
    throw Exception('Failed to load product attributes: ${response.error ?? 'Unknown error'}');
  }
  
  final List<dynamic> data = response.data['data'] ?? [];
  final Map<String, String> attributes = {};
  
  for (final item in data) {
    attributes[item['attribute_name_en']] = item['attribute_value'];
  }
  
  return attributes;
});

// Provider for featured products
final featuredProductsProvider = FutureProvider<List<Product>>((ref) async {
  final apiClient = ref.read(apiClientProvider);
  final response = await apiClient.get('/api/v1/products/featured');
  
  if (!response.isSuccess) {
    throw Exception('Failed to load featured products: ${response.error ?? 'Unknown error'}');
  }
  
  final List<dynamic> data = response.data['data'] ?? [];
  return data.map((json) => Product.fromJson(json)).toList();
});

// Provider for bestseller products
final bestsellerProductsProvider = FutureProvider<List<Product>>((ref) async {
  final apiClient = ref.read(apiClientProvider);
  final response = await apiClient.get('/api/v1/products/bestsellers');
  
  if (!response.isSuccess) {
    throw Exception('Failed to load bestseller products: ${response.error ?? 'Unknown error'}');
  }
  
  final List<dynamic> data = response.data['data'] ?? [];
  return data.map((json) => Product.fromJson(json)).toList();
}); 