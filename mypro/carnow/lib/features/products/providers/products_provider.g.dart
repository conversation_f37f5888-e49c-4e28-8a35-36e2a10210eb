// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'products_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$productsHash() => r'f9d48a46cd65678b883984113b64c1b79735b2fe';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider to fetch a paginated, filterable list of products.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [products].
@ProviderFor(products)
const productsProvider = ProductsFamily();

/// Provider to fetch a paginated, filterable list of products.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [products].
class ProductsFamily extends Family<AsyncValue<ProductsResult>> {
  /// Provider to fetch a paginated, filterable list of products.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [products].
  const ProductsFamily();

  /// Provider to fetch a paginated, filterable list of products.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [products].
  ProductsProvider call({
    required int page,
    int limit = 20,
    ProductFilter? filter,
  }) {
    return ProductsProvider(page: page, limit: limit, filter: filter);
  }

  @override
  ProductsProvider getProviderOverride(covariant ProductsProvider provider) {
    return call(
      page: provider.page,
      limit: provider.limit,
      filter: provider.filter,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productsProvider';
}

/// Provider to fetch a paginated, filterable list of products.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [products].
class ProductsProvider extends AutoDisposeFutureProvider<ProductsResult> {
  /// Provider to fetch a paginated, filterable list of products.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [products].
  ProductsProvider({required int page, int limit = 20, ProductFilter? filter})
    : this._internal(
        (ref) => products(
          ref as ProductsRef,
          page: page,
          limit: limit,
          filter: filter,
        ),
        from: productsProvider,
        name: r'productsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productsHash,
        dependencies: ProductsFamily._dependencies,
        allTransitiveDependencies: ProductsFamily._allTransitiveDependencies,
        page: page,
        limit: limit,
        filter: filter,
      );

  ProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.page,
    required this.limit,
    required this.filter,
  }) : super.internal();

  final int page;
  final int limit;
  final ProductFilter? filter;

  @override
  Override overrideWith(
    FutureOr<ProductsResult> Function(ProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductsProvider._internal(
        (ref) => create(ref as ProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        page: page,
        limit: limit,
        filter: filter,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProductsResult> createElement() {
    return _ProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductsProvider &&
        other.page == page &&
        other.limit == limit &&
        other.filter == filter;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);
    hash = _SystemHash.combine(hash, filter.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductsRef on AutoDisposeFutureProviderRef<ProductsResult> {
  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `limit` of this provider.
  int get limit;

  /// The parameter `filter` of this provider.
  ProductFilter? get filter;
}

class _ProductsProviderElement
    extends AutoDisposeFutureProviderElement<ProductsResult>
    with ProductsRef {
  _ProductsProviderElement(super.provider);

  @override
  int get page => (origin as ProductsProvider).page;
  @override
  int get limit => (origin as ProductsProvider).limit;
  @override
  ProductFilter? get filter => (origin as ProductsProvider).filter;
}

String _$productByIdHash() => r'19c33c2551819039395396e60ae3c8c2227ed369';

/// Provider to fetch a single product by its ID.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [productById].
@ProviderFor(productById)
const productByIdProvider = ProductByIdFamily();

/// Provider to fetch a single product by its ID.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [productById].
class ProductByIdFamily extends Family<AsyncValue<ProductModel?>> {
  /// Provider to fetch a single product by its ID.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [productById].
  const ProductByIdFamily();

  /// Provider to fetch a single product by its ID.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [productById].
  ProductByIdProvider call(String productId) {
    return ProductByIdProvider(productId);
  }

  @override
  ProductByIdProvider getProviderOverride(
    covariant ProductByIdProvider provider,
  ) {
    return call(provider.productId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productByIdProvider';
}

/// Provider to fetch a single product by its ID.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [productById].
class ProductByIdProvider extends AutoDisposeFutureProvider<ProductModel?> {
  /// Provider to fetch a single product by its ID.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [productById].
  ProductByIdProvider(String productId)
    : this._internal(
        (ref) => productById(ref as ProductByIdRef, productId),
        from: productByIdProvider,
        name: r'productByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productByIdHash,
        dependencies: ProductByIdFamily._dependencies,
        allTransitiveDependencies: ProductByIdFamily._allTransitiveDependencies,
        productId: productId,
      );

  ProductByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productId,
  }) : super.internal();

  final String productId;

  @override
  Override overrideWith(
    FutureOr<ProductModel?> Function(ProductByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductByIdProvider._internal(
        (ref) => create(ref as ProductByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productId: productId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProductModel?> createElement() {
    return _ProductByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductByIdProvider && other.productId == productId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductByIdRef on AutoDisposeFutureProviderRef<ProductModel?> {
  /// The parameter `productId` of this provider.
  String get productId;
}

class _ProductByIdProviderElement
    extends AutoDisposeFutureProviderElement<ProductModel?>
    with ProductByIdRef {
  _ProductByIdProviderElement(super.provider);

  @override
  String get productId => (origin as ProductByIdProvider).productId;
}

String _$featuredProductsHash() => r'187e720b65c60a615f288102d5e5bfc6f98f33d0';

/// Provider to fetch featured products.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [featuredProducts].
@ProviderFor(featuredProducts)
const featuredProductsProvider = FeaturedProductsFamily();

/// Provider to fetch featured products.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [featuredProducts].
class FeaturedProductsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// Provider to fetch featured products.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [featuredProducts].
  const FeaturedProductsFamily();

  /// Provider to fetch featured products.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [featuredProducts].
  FeaturedProductsProvider call({int limit = 10}) {
    return FeaturedProductsProvider(limit: limit);
  }

  @override
  FeaturedProductsProvider getProviderOverride(
    covariant FeaturedProductsProvider provider,
  ) {
    return call(limit: provider.limit);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'featuredProductsProvider';
}

/// Provider to fetch featured products.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [featuredProducts].
class FeaturedProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// Provider to fetch featured products.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [featuredProducts].
  FeaturedProductsProvider({int limit = 10})
    : this._internal(
        (ref) => featuredProducts(ref as FeaturedProductsRef, limit: limit),
        from: featuredProductsProvider,
        name: r'featuredProductsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$featuredProductsHash,
        dependencies: FeaturedProductsFamily._dependencies,
        allTransitiveDependencies:
            FeaturedProductsFamily._allTransitiveDependencies,
        limit: limit,
      );

  FeaturedProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.limit,
  }) : super.internal();

  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(FeaturedProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FeaturedProductsProvider._internal(
        (ref) => create(ref as FeaturedProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _FeaturedProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FeaturedProductsProvider && other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FeaturedProductsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `limit` of this provider.
  int get limit;
}

class _FeaturedProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with FeaturedProductsRef {
  _FeaturedProductsProviderElement(super.provider);

  @override
  int get limit => (origin as FeaturedProductsProvider).limit;
}

String _$recentProductsHash() => r'68fa546c2a5a8bc8289dee3f14d647537c5da583';

/// Provider to fetch recent products.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [recentProducts].
@ProviderFor(recentProducts)
const recentProductsProvider = RecentProductsFamily();

/// Provider to fetch recent products.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [recentProducts].
class RecentProductsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// Provider to fetch recent products.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [recentProducts].
  const RecentProductsFamily();

  /// Provider to fetch recent products.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [recentProducts].
  RecentProductsProvider call({int limit = 10}) {
    return RecentProductsProvider(limit: limit);
  }

  @override
  RecentProductsProvider getProviderOverride(
    covariant RecentProductsProvider provider,
  ) {
    return call(limit: provider.limit);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'recentProductsProvider';
}

/// Provider to fetch recent products.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [recentProducts].
class RecentProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// Provider to fetch recent products.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [recentProducts].
  RecentProductsProvider({int limit = 10})
    : this._internal(
        (ref) => recentProducts(ref as RecentProductsRef, limit: limit),
        from: recentProductsProvider,
        name: r'recentProductsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$recentProductsHash,
        dependencies: RecentProductsFamily._dependencies,
        allTransitiveDependencies:
            RecentProductsFamily._allTransitiveDependencies,
        limit: limit,
      );

  RecentProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.limit,
  }) : super.internal();

  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(RecentProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: RecentProductsProvider._internal(
        (ref) => create(ref as RecentProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _RecentProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RecentProductsProvider && other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin RecentProductsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `limit` of this provider.
  int get limit;
}

class _RecentProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with RecentProductsRef {
  _RecentProductsProviderElement(super.provider);

  @override
  int get limit => (origin as RecentProductsProvider).limit;
}

String _$sellerProductsHash() => r'be595f2d7101ee1cd629a93d3833c0a627588264';

/// Provider to fetch products for a specific seller.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [sellerProducts].
@ProviderFor(sellerProducts)
const sellerProductsProvider = SellerProductsFamily();

/// Provider to fetch products for a specific seller.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [sellerProducts].
class SellerProductsFamily extends Family<AsyncValue<ProductsResult>> {
  /// Provider to fetch products for a specific seller.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [sellerProducts].
  const SellerProductsFamily();

  /// Provider to fetch products for a specific seller.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [sellerProducts].
  SellerProductsProvider call({
    required String sellerId,
    int page = 1,
    int limit = 20,
  }) {
    return SellerProductsProvider(sellerId: sellerId, page: page, limit: limit);
  }

  @override
  SellerProductsProvider getProviderOverride(
    covariant SellerProductsProvider provider,
  ) {
    return call(
      sellerId: provider.sellerId,
      page: provider.page,
      limit: provider.limit,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sellerProductsProvider';
}

/// Provider to fetch products for a specific seller.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [sellerProducts].
class SellerProductsProvider extends AutoDisposeFutureProvider<ProductsResult> {
  /// Provider to fetch products for a specific seller.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [sellerProducts].
  SellerProductsProvider({
    required String sellerId,
    int page = 1,
    int limit = 20,
  }) : this._internal(
         (ref) => sellerProducts(
           ref as SellerProductsRef,
           sellerId: sellerId,
           page: page,
           limit: limit,
         ),
         from: sellerProductsProvider,
         name: r'sellerProductsProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$sellerProductsHash,
         dependencies: SellerProductsFamily._dependencies,
         allTransitiveDependencies:
             SellerProductsFamily._allTransitiveDependencies,
         sellerId: sellerId,
         page: page,
         limit: limit,
       );

  SellerProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sellerId,
    required this.page,
    required this.limit,
  }) : super.internal();

  final String sellerId;
  final int page;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<ProductsResult> Function(SellerProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SellerProductsProvider._internal(
        (ref) => create(ref as SellerProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sellerId: sellerId,
        page: page,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProductsResult> createElement() {
    return _SellerProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SellerProductsProvider &&
        other.sellerId == sellerId &&
        other.page == page &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sellerId.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SellerProductsRef on AutoDisposeFutureProviderRef<ProductsResult> {
  /// The parameter `sellerId` of this provider.
  String get sellerId;

  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _SellerProductsProviderElement
    extends AutoDisposeFutureProviderElement<ProductsResult>
    with SellerProductsRef {
  _SellerProductsProviderElement(super.provider);

  @override
  String get sellerId => (origin as SellerProductsProvider).sellerId;
  @override
  int get page => (origin as SellerProductsProvider).page;
  @override
  int get limit => (origin as SellerProductsProvider).limit;
}

String _$categoryProductsHash() => r'abfae37390b1428245bcefcb0babd58b9c924b2f';

/// Provider to fetch products for a specific category.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [categoryProducts].
@ProviderFor(categoryProducts)
const categoryProductsProvider = CategoryProductsFamily();

/// Provider to fetch products for a specific category.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [categoryProducts].
class CategoryProductsFamily extends Family<AsyncValue<ProductsResult>> {
  /// Provider to fetch products for a specific category.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [categoryProducts].
  const CategoryProductsFamily();

  /// Provider to fetch products for a specific category.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [categoryProducts].
  CategoryProductsProvider call({
    required String categoryId,
    int page = 1,
    int limit = 20,
  }) {
    return CategoryProductsProvider(
      categoryId: categoryId,
      page: page,
      limit: limit,
    );
  }

  @override
  CategoryProductsProvider getProviderOverride(
    covariant CategoryProductsProvider provider,
  ) {
    return call(
      categoryId: provider.categoryId,
      page: provider.page,
      limit: provider.limit,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categoryProductsProvider';
}

/// Provider to fetch products for a specific category.
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [categoryProducts].
class CategoryProductsProvider
    extends AutoDisposeFutureProvider<ProductsResult> {
  /// Provider to fetch products for a specific category.
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [categoryProducts].
  CategoryProductsProvider({
    required String categoryId,
    int page = 1,
    int limit = 20,
  }) : this._internal(
         (ref) => categoryProducts(
           ref as CategoryProductsRef,
           categoryId: categoryId,
           page: page,
           limit: limit,
         ),
         from: categoryProductsProvider,
         name: r'categoryProductsProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$categoryProductsHash,
         dependencies: CategoryProductsFamily._dependencies,
         allTransitiveDependencies:
             CategoryProductsFamily._allTransitiveDependencies,
         categoryId: categoryId,
         page: page,
         limit: limit,
       );

  CategoryProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
    required this.page,
    required this.limit,
  }) : super.internal();

  final String categoryId;
  final int page;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<ProductsResult> Function(CategoryProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategoryProductsProvider._internal(
        (ref) => create(ref as CategoryProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
        page: page,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProductsResult> createElement() {
    return _CategoryProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryProductsProvider &&
        other.categoryId == categoryId &&
        other.page == page &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategoryProductsRef on AutoDisposeFutureProviderRef<ProductsResult> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;

  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _CategoryProductsProviderElement
    extends AutoDisposeFutureProviderElement<ProductsResult>
    with CategoryProductsRef {
  _CategoryProductsProviderElement(super.provider);

  @override
  String get categoryId => (origin as CategoryProductsProvider).categoryId;
  @override
  int get page => (origin as CategoryProductsProvider).page;
  @override
  int get limit => (origin as CategoryProductsProvider).limit;
}

String _$productsByCategoryHash() =>
    r'27c64a846154f2db3524224358a2cca1182b5c08';

/// Provider to fetch products by category using ProductApiService - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [productsByCategory].
@ProviderFor(productsByCategory)
const productsByCategoryProvider = ProductsByCategoryFamily();

/// Provider to fetch products by category using ProductApiService - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [productsByCategory].
class ProductsByCategoryFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// Provider to fetch products by category using ProductApiService - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [productsByCategory].
  const ProductsByCategoryFamily();

  /// Provider to fetch products by category using ProductApiService - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [productsByCategory].
  ProductsByCategoryProvider call(String categoryId) {
    return ProductsByCategoryProvider(categoryId);
  }

  @override
  ProductsByCategoryProvider getProviderOverride(
    covariant ProductsByCategoryProvider provider,
  ) {
    return call(provider.categoryId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productsByCategoryProvider';
}

/// Provider to fetch products by category using ProductApiService - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [productsByCategory].
class ProductsByCategoryProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// Provider to fetch products by category using ProductApiService - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [productsByCategory].
  ProductsByCategoryProvider(String categoryId)
    : this._internal(
        (ref) => productsByCategory(ref as ProductsByCategoryRef, categoryId),
        from: productsByCategoryProvider,
        name: r'productsByCategoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productsByCategoryHash,
        dependencies: ProductsByCategoryFamily._dependencies,
        allTransitiveDependencies:
            ProductsByCategoryFamily._allTransitiveDependencies,
        categoryId: categoryId,
      );

  ProductsByCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
  }) : super.internal();

  final String categoryId;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(ProductsByCategoryRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductsByCategoryProvider._internal(
        (ref) => create(ref as ProductsByCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _ProductsByCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductsByCategoryProvider &&
        other.categoryId == categoryId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductsByCategoryRef
    on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;
}

class _ProductsByCategoryProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with ProductsByCategoryRef {
  _ProductsByCategoryProviderElement(super.provider);

  @override
  String get categoryId => (origin as ProductsByCategoryProvider).categoryId;
}

String _$productHash() => r'87ebc7e31dea4d5c78dda2a76e510eb5556f113e';

/// A provider to fetch a single product by its ID using ProductApiService - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [product].
@ProviderFor(product)
const productProvider = ProductFamily();

/// A provider to fetch a single product by its ID using ProductApiService - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [product].
class ProductFamily extends Family<AsyncValue<ProductModel?>> {
  /// A provider to fetch a single product by its ID using ProductApiService - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [product].
  const ProductFamily();

  /// A provider to fetch a single product by its ID using ProductApiService - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [product].
  ProductProvider call(String id) {
    return ProductProvider(id);
  }

  @override
  ProductProvider getProviderOverride(covariant ProductProvider provider) {
    return call(provider.id);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productProvider';
}

/// A provider to fetch a single product by its ID using ProductApiService - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
///
/// Copied from [product].
class ProductProvider extends AutoDisposeFutureProvider<ProductModel?> {
  /// A provider to fetch a single product by its ID using ProductApiService - FIXED: Converted to @riverpod pattern
  /// Forever Plan: Uses HTTP calls to Go backend only
  ///
  /// Copied from [product].
  ProductProvider(String id)
    : this._internal(
        (ref) => product(ref as ProductRef, id),
        from: productProvider,
        name: r'productProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productHash,
        dependencies: ProductFamily._dependencies,
        allTransitiveDependencies: ProductFamily._allTransitiveDependencies,
        id: id,
      );

  ProductProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final String id;

  @override
  Override overrideWith(
    FutureOr<ProductModel?> Function(ProductRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductProvider._internal(
        (ref) => create(ref as ProductRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProductModel?> createElement() {
    return _ProductProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductRef on AutoDisposeFutureProviderRef<ProductModel?> {
  /// The parameter `id` of this provider.
  String get id;
}

class _ProductProviderElement
    extends AutoDisposeFutureProviderElement<ProductModel?>
    with ProductRef {
  _ProductProviderElement(super.provider);

  @override
  String get id => (origin as ProductProvider).id;
}

String _$productFilterNotifierHash() =>
    r'f39bf84f64eb2ce69a5f0f53e25eea4cc81a5ec2';

/// See also [ProductFilterNotifier].
@ProviderFor(ProductFilterNotifier)
final productFilterNotifierProvider =
    AutoDisposeNotifierProvider<ProductFilterNotifier, ProductFilter>.internal(
      ProductFilterNotifier.new,
      name: r'productFilterNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$productFilterNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ProductFilterNotifier = AutoDisposeNotifier<ProductFilter>;
String _$currentPageHash() => r'8bb6adf6126fa2d63e61b401b7639373fb93f4ba';

/// Provider for managing current page state
///
/// Copied from [CurrentPage].
@ProviderFor(CurrentPage)
final currentPageProvider =
    AutoDisposeNotifierProvider<CurrentPage, int>.internal(
      CurrentPage.new,
      name: r'currentPageProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentPageHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CurrentPage = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
