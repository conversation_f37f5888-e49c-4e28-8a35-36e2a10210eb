import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'dart:async';

import '../../../core/models/product_model.dart';
import '../../../core/services/cache_service.dart';
import '../../../core/services/analytics_service.dart';
import '../services/product_api_service.dart';

part 'cached_products_provider.g.dart';

final _logger = Logger();

/// Enhanced products provider with caching for frequently accessed products
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<ProductModel?> cachedProduct(Ref ref, String productId) async {
  const cacheTtl = Duration(minutes: 15); // Cache for 15 minutes
  final cacheKey = 'product_$productId';

  try {
    // Try to get from cache first
    final cacheService = await CacheService.getInstance();
    final cachedData = await cacheService.getObject(cacheKey);

    if (cachedData != null) {
      _logger.i('Product $productId loaded from cache');

      // Track cache hit
      unawaited(
        ref.read(analyticsServiceProvider).logEvent(
          'product_cache_hit',
          entityType: 'product',
          entityId: productId,
          properties: {'cache_key': cacheKey},
        ),
      );

      return ProductModel.fromJson(cachedData);
    }

    // Cache miss - fetch from API service
    _logger.i('Product $productId not in cache, fetching from API');
    final apiService = ref.watch(productApiServiceProvider);
    final product = await apiService.getProductById(productId);

    if (product != null) {
      // Cache the product
      await cacheService.setObject(
        key: cacheKey,
        object: product.toJson(),
        ttl: cacheTtl,
      );

      // Track cache miss and subsequent cache
      unawaited(
        ref.read(analyticsServiceProvider).logEvent(
          'product_cache_miss',
          entityType: 'product',
          entityId: productId,
          properties: {
            'cache_key': cacheKey,
            'cached_for_minutes': cacheTtl.inMinutes,
          },
        ),
      );

      _logger.i('Product $productId cached successfully');
    }

    return product;
  } catch (e) {
    _logger.e('Error in cachedProduct for $productId: $e');

    // Fallback to regular API service
    final apiService = ref.watch(productApiServiceProvider);
    return apiService.getProductById(productId);
  }
}

/// Provider for frequently accessed products (based on analytics)
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<ProductModel>> frequentlyAccessedProducts(Ref ref) async {
  try {
    _logger.i('Fetching frequently accessed products');
    
    // Get frequently accessed product IDs from analytics
    final productIds = await _getFrequentlyAccessedProductIds();
    
    if (productIds.isEmpty) {
      _logger.i('No frequently accessed products found, returning recent products');
      final apiService = ref.watch(productApiServiceProvider);
      final products = await apiService.getProductsSimple();
      return products.take(5).toList();
    }

    // Fetch products by IDs
    final apiService = ref.watch(productApiServiceProvider);
    final products = <ProductModel>[];
    
    for (final productId in productIds) {
      try {
        final product = await apiService.getProductById(productId);
        if (product != null) {
          products.add(product);
        }
      } catch (e) {
        _logger.w('Failed to fetch frequently accessed product $productId: $e');
      }
    }

    _logger.i('Loaded ${products.length} frequently accessed products');
    return products;
  } catch (e) {
    _logger.e('Error fetching frequently accessed products: $e');
    
    // Fallback to recent products
    final apiService = ref.watch(productApiServiceProvider);
    final products = await apiService.getProductsSimple();
    return products.take(5).toList();
  }
}

/// Provider for popular products with caching
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<ProductModel>> popularProductsWithCache(Ref ref) async {
  const cacheKey = 'popular_products';
  const cacheTtl = Duration(hours: 1); // Cache for 1 hour

  try {
    final cacheService = await CacheService.getInstance();
    final cachedData = await cacheService.getList(cacheKey);

    if (cachedData != null && cachedData.isNotEmpty) {
      _logger.i('Popular products loaded from cache');
      return cachedData.map(ProductModel.fromJson).toList();
    }

    // Cache miss - fetch popular products from API
    final apiService = ref.watch(productApiServiceProvider);
    final products = await apiService.getProductsSimple();
    final popularProducts = products.take(10).toList();

    // Cache the results
    if (popularProducts.isNotEmpty) {
      await cacheService.setList(
        key: cacheKey,
        list: popularProducts.map((p) => p.toJson()).toList(),
        ttl: cacheTtl,
      );

      _logger.i('Popular products cached successfully');
    }

    return popularProducts;
  } catch (e) {
    _logger.e('Error fetching popular products with cache: $e');

    // Fallback to regular API service
    final apiService = ref.watch(productApiServiceProvider);
    final products = await apiService.getProductsSimple();
    return products.take(10).toList();
  }
}

/// Provider to manage cache invalidation
@riverpod
class CacheManager extends _$CacheManager {
  @override
  bool build() => false;

  /// Clear product cache for a specific product
  Future<void> invalidateProductCache(String productId) async {
    try {
      final cacheService = await CacheService.getInstance();
      final cacheKey = 'product_$productId';

      await cacheService.remove(cacheKey);

      _logger.i('Cache invalidated for product $productId');

      // Invalidate related providers
      ref.invalidate(cachedProductProvider(productId));

      unawaited(
        ref.read(analyticsServiceProvider).logEvent(
          'product_cache_invalidated',
          entityType: 'product',
          entityId: productId,
          properties: {'reason': 'manual_invalidation'},
        ),
      );
    } catch (e) {
      _logger.e('Error invalidating cache for product $productId: $e');
    }
  }

  /// Clear all product caches
  Future<void> clearAllProductCaches() async {
    try {
      final cacheService = await CacheService.getInstance();

      // Clear specific cache keys
      await Future.wait([
        cacheService.remove('frequently_accessed_products'),
        cacheService.remove('popular_products'),
      ]);

      // Invalidate related providers
      ref.invalidate(frequentlyAccessedProductsProvider);
      ref.invalidate(popularProductsWithCacheProvider);

      _logger.i('All product caches cleared');

      unawaited(
        ref.read(analyticsServiceProvider).logEvent(
          'all_product_caches_cleared',
          properties: {'timestamp': DateTime.now().toIso8601String()},
        ),
      );
    } catch (e) {
      _logger.e('Error clearing all product caches: $e');
    }
  }

  /// Preload frequently accessed products into cache
  Future<void> preloadFrequentProducts() async {
    try {
      _logger.i('Starting preload of frequent products');

      final productIds = await _getFrequentlyAccessedProductIds();

      for (final productId in productIds.take(10)) {
        // Preload top 10
        try {
          await ref.read(cachedProductProvider(productId).future);
        } catch (e) {
          _logger.w('Failed to preload product $productId: $e');
        }
      }

      _logger.i('Finished preloading frequent products');

      unawaited(
        ref.read(analyticsServiceProvider).logEvent(
          'products_preloaded',
          properties: {
            'product_count': productIds.length,
            'preloaded_count': productIds.take(10).length,
          },
        ),
      );
    } catch (e) {
      _logger.e('Error preloading frequent products: $e');
    }
  }
}

/// Helper function to get frequently accessed product IDs
/// Forever Plan: This could be enhanced to call Go backend analytics endpoint
Future<List<String>> _getFrequentlyAccessedProductIds() async {
  try {
    // TODO: This should call Go backend analytics endpoint for product access stats
    // For now, return empty list to use fallback
    return [];
  } catch (e) {
    _logger.e('Error getting frequently accessed product IDs: $e');
    return [];
  }
}
