import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:carnow/core/models/category.dart';
import 'package:carnow/core/services/api_client.dart';

// Provider for API client
final apiClientProvider = Provider<ApiClient>((ref) {
  return ApiClient();
});

// Provider for main categories
final mainCategoriesProvider = FutureProvider<List<MainCategory>>((ref) async {
  final apiClient = ref.read(apiClientProvider);
  final response = await apiClient.get('/api/v1/main-categories');
  
  if (!response.isSuccess) {
    throw Exception('Failed to load main categories: ${response.error ?? 'Unknown error'}');
  }
  
  final List<dynamic> data = response.data['data'] ?? [];
  return data.map((json) => MainCategory.fromJson(json)).toList();
});

// Provider for category attributes
final categoryAttributesProvider = FutureProvider.family<List<CategoryAttribute>, String>((ref, mainCategoryId) async {
  final apiClient = ref.read(apiClientProvider);
  final response = await apiClient.get('/api/v1/category-attributes/$mainCategoryId');
  
  if (!response.isSuccess) {
    throw Exception('Failed to load category attributes: ${response.error ?? 'Unknown error'}');
  }
  
  final List<dynamic> data = response.data['data'] ?? [];
  return data.map((json) => CategoryAttribute.fromJson(json)).toList();
});

// Provider for specific main category
final mainCategoryProvider = FutureProvider.family<MainCategory?, String>((ref, categoryId) async {
  final categories = await ref.watch(mainCategoriesProvider.future);
  try {
    return categories.firstWhere((category) => category.id == categoryId);
  } catch (e) {
    return null;
  }
});

// Provider for adding category attributes
final addCategoryAttributeProvider = StateNotifierProvider<AddCategoryAttributeNotifier, AsyncValue<void>>((ref) {
  final apiClient = ref.read(apiClientProvider);
  return AddCategoryAttributeNotifier(apiClient);
});

class AddCategoryAttributeNotifier extends StateNotifier<AsyncValue<void>> {
  final ApiClient _apiClient;

  AddCategoryAttributeNotifier(this._apiClient) : super(const AsyncValue.data(null));

  Future<void> addAttribute(Map<String, dynamic> attributeData) async {
    state = const AsyncValue.loading();

    try {
      final response = await _apiClient.post('/api/v1/category-attributes', attributeData);
      
      if (!response.isSuccess) {
        throw Exception('Failed to add category attribute: ${response.error ?? 'Unknown error'}');
      }

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }
}

// Provider for updating category attributes
final updateCategoryAttributeProvider = StateNotifierProvider<UpdateCategoryAttributeNotifier, AsyncValue<void>>((ref) {
  final apiClient = ref.read(apiClientProvider);
  return UpdateCategoryAttributeNotifier(apiClient);
});

class UpdateCategoryAttributeNotifier extends StateNotifier<AsyncValue<void>> {
  final ApiClient _apiClient;

  UpdateCategoryAttributeNotifier(this._apiClient) : super(const AsyncValue.data(null));

  Future<void> updateAttribute(String attributeId, Map<String, dynamic> attributeData) async {
    state = const AsyncValue.loading();

    try {
      final response = await _apiClient.put('/api/v1/category-attributes/$attributeId', attributeData);
      
      if (!response.isSuccess) {
        throw Exception('Failed to update category attribute: ${response.error ?? 'Unknown error'}');
      }

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }
}

// Provider for deleting category attributes
final deleteCategoryAttributeProvider = StateNotifierProvider<DeleteCategoryAttributeNotifier, AsyncValue<void>>((ref) {
  final apiClient = ref.read(apiClientProvider);
  return DeleteCategoryAttributeNotifier(apiClient);
});

class DeleteCategoryAttributeNotifier extends StateNotifier<AsyncValue<void>> {
  final ApiClient _apiClient;

  DeleteCategoryAttributeNotifier(this._apiClient) : super(const AsyncValue.data(null));

  Future<void> deleteAttribute(String attributeId) async {
    state = const AsyncValue.loading();

    try {
      final response = await _apiClient.delete('/api/v1/category-attributes/$attributeId');
      
      if (!response.isSuccess) {
        throw Exception('Failed to delete category attribute: ${response.error ?? 'Unknown error'}');
      }

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }
}

// Provider for categories by part category
final categoriesByPartCategoryProvider = FutureProvider.family<List<MainCategory>, String>((ref, partCategoryId) async {
  final apiClient = ref.read(apiClientProvider);
  final response = await apiClient.get('/api/v1/main-categories?part_category_id=$partCategoryId');
  
  if (!response.isSuccess) {
    throw Exception('Failed to load categories by part category: ${response.error ?? 'Unknown error'}');
  }
  
  final List<dynamic> data = response.data['data'] ?? [];
  return data.map((json) => MainCategory.fromJson(json)).toList();
});

// Provider for category hierarchy
final categoryHierarchyProvider = FutureProvider.family<Map<String, dynamic>, String>((ref, mainCategoryId) async {
  final apiClient = ref.read(apiClientProvider);
  final response = await apiClient.get('/api/v1/categories/$mainCategoryId/hierarchy');
  
  if (!response.isSuccess) {
    throw Exception('Failed to load category hierarchy: ${response.error ?? 'Unknown error'}');
  }
  
  return response.data;
}); 