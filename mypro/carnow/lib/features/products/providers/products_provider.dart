import 'dart:async';

import 'package:logger/logger.dart';
import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/models/products_result_model.dart';
import '../../../core/models/product_model.dart';
import '../models/product_filter_model.dart';
import '../models/product_sort_by.dart';
import '../services/product_api_service.dart';

part 'products_provider.g.dart';

final _logger = Logger(
  printer: PrettyPrinter(methodCount: 0, errorMethodCount: 5, lineLength: 50),
);

/// Provider to fetch a paginated, filterable list of products.
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<ProductsResult> products(
  Ref ref, {
  required int page,
  int limit = 20,
  ProductFilter? filter,
}) async {
  final apiService = ref.watch(productApiServiceProvider);
  final products = await apiService.getProductsSimple();
  return ProductsResult(
    products: products,
    totalCount: products.length,
    currentPage: page,
    totalPages: 1,
    hasMore: false,
  );
}

/// Provider to fetch a single product by its ID.
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<ProductModel?> productById(Ref ref, String productId) async {
  final apiService = ref.watch(productApiServiceProvider);
  return apiService.getProductById(productId);
}

/// Provider to fetch featured products.
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<ProductModel>> featuredProducts(Ref ref, {int limit = 10}) async {
  final apiService = ref.watch(productApiServiceProvider);
  final products = await apiService.getProductsSimple();
  return products.take(limit).toList();
}

/// Provider to fetch recent products.
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<ProductModel>> recentProducts(Ref ref, {int limit = 10}) async {
  try {
    _logger.i('Fetching recent products with limit: $limit');
    final apiService = ref.watch(productApiServiceProvider);
    final products = await apiService.getProductsSimple();
    final recentProducts = products.take(limit).toList();
    _logger.i(
      'Recent products fetched successfully - Count: ${recentProducts.length}',
    );
    return recentProducts;
  } catch (e, st) {
    _logger.e('Error fetching recent products', error: e, stackTrace: st);
    rethrow;
  }
}

/// Provider to fetch products for a specific seller.
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<ProductsResult> sellerProducts(
  Ref ref, {
  required String sellerId,
  int page = 1,
  int limit = 20,
}) async {
  try {
    _logger.i('Fetching products for seller: $sellerId');
    final apiService = ref.watch(productApiServiceProvider);
    final products = await apiService.getProductsByUserId(sellerId);

    _logger.i(
      'Seller products fetched successfully - Count: ${products.length}',
    );
    return ProductsResult(
      products: products,
      totalCount: products.length,
      currentPage: page,
      totalPages: 1,
      hasMore: false,
    );
  } catch (e, st) {
    _logger.e(
      'Error fetching seller products for $sellerId',
      error: e,
      stackTrace: st,
    );
    rethrow;
  }
}

/// Provider to fetch products for a specific category.
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<ProductsResult> categoryProducts(
  Ref ref, {
  required String categoryId,
  int page = 1,
  int limit = 20,
}) async {
  try {
    _logger.i('Fetching products for category: $categoryId');
    final apiService = ref.watch(productApiServiceProvider);
    final products = await apiService.getProductsByCategory(categoryId);
    _logger.i(
      'Category products fetched successfully - Count: ${products.length}',
    );
    return ProductsResult(
      products: products,
      totalCount: products.length,
      currentPage: page,
      totalPages: 1,
      hasMore: false,
    );
  } catch (e, st) {
    _logger.e(
      'Error fetching category products for $categoryId',
      error: e,
      stackTrace: st,
    );
    rethrow;
  }
}

@riverpod
class ProductFilterNotifier extends _$ProductFilterNotifier {
  @override
  ProductFilter build() {
    return const ProductFilter();
  }

  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }

  void setCategory(String? categoryId) {
    state = state.copyWith(categoryId: categoryId);
  }

  void setPriceRange(double? min, double? max) {
    state = state.copyWith(minPrice: min, maxPrice: max);
  }

  void setSortBy(ProductSortBy sortBy) {
    state = state.copyWith(sortBy: sortBy.queryParameter);
  }

  void clearFilters() {
    state = const ProductFilter();
  }
}

/// Provider for managing current page state
@riverpod
class CurrentPage extends _$CurrentPage {
  @override
  int build() => 1;

  void setPage(int page) {
    state = page;
  }

  void nextPage() {
    state = state + 1;
  }

  void previousPage() {
    if (state > 1) {
      state = state - 1;
    }
  }

  void reset() {
    state = 1;
  }
}

/// Provider to fetch products by category using ProductApiService - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<ProductModel>> productsByCategory(ProductsByCategoryRef ref, String categoryId) async {
  final apiService = ref.watch(productApiServiceProvider);
  return apiService.getProductsByCategory(categoryId);
}

/// A provider to fetch a single product by its ID using ProductApiService - FIXED: Converted to @riverpod pattern
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<ProductModel?> product(ProductRef ref, String id) async {
  final apiService = ref.watch(productApiServiceProvider);
  return apiService.getProductById(id);
}
