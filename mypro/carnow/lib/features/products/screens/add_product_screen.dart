import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:carnow/core/models/category.dart';
import 'package:carnow/features/products/providers/product_providers.dart';
import 'package:carnow/features/products/providers/category_providers.dart';
import 'package:carnow/shared/widgets/custom_text_field.dart';
import 'package:carnow/shared/widgets/custom_dropdown.dart';
import 'package:carnow/shared/widgets/custom_button.dart';
import 'package:carnow/shared/widgets/loading_indicator.dart';
import 'package:carnow/core/utils/validators.dart';

class AddProductScreen extends ConsumerStatefulWidget {
  const AddProductScreen({super.key});

  @override
  ConsumerState<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends ConsumerState<AddProductScreen> {
  final _formKey = GlobalKey<FormState>();
  
  // Controllers for basic product information
  final _nameEnController = TextEditingController();
  final _nameArController = TextEditingController();
  final _descriptionEnController = TextEditingController();
  final _descriptionArController = TextEditingController();
  final _manufacturerPartNumberController = TextEditingController();
  final _brandController = TextEditingController();
  final _priceController = TextEditingController();
  final _salePriceController = TextEditingController();
  final _stockQuantityController = TextEditingController();
  final _minOrderQuantityController = TextEditingController();
  final _weightController = TextEditingController();
  final _dimensionsController = TextEditingController();
  final _countryOfOriginController = TextEditingController();
  final _surfaceFinishController = TextEditingController();
  final _manufacturerWarrantyController = TextEditingController();
  final _oemPartNumbersController = TextEditingController();

  // Selected values
  String? _selectedMainCategoryId;
  String? _selectedConditionType;
  String? _selectedFitmentType;
  String? _selectedCurrency;

  // Dynamic attribute controllers and values
  final Map<String, TextEditingController> _attributeControllers = {};
  final Map<String, String?> _selectedAttributeValues = {};

  // Form state
  bool _isLoading = false;
  String? _errorMessage;

  // Constants
  static const List<String> _conditionTypes = ['new', 'used', 'refurbished'];
  static const List<String> _fitmentTypes = ['direct_fit', 'universal', 'custom'];
  static const List<String> _currencies = ['USD', 'EUR', 'SAR', 'AED'];

  @override
  void dispose() {
    _nameEnController.dispose();
    _nameArController.dispose();
    _descriptionEnController.dispose();
    _descriptionArController.dispose();
    _manufacturerPartNumberController.dispose();
    _brandController.dispose();
    _priceController.dispose();
    _salePriceController.dispose();
    _stockQuantityController.dispose();
    _minOrderQuantityController.dispose();
    _weightController.dispose();
    _dimensionsController.dispose();
    _countryOfOriginController.dispose();
    _surfaceFinishController.dispose();
    _manufacturerWarrantyController.dispose();
    _oemPartNumbersController.dispose();
    
    // Dispose attribute controllers
    for (final controller in _attributeControllers.values) {
      controller.dispose();
    }
    
    super.dispose();
  }

  void _onMainCategoryChanged(String? categoryId) {
    setState(() {
      _selectedMainCategoryId = categoryId;
      // Clear previous attribute controllers
      for (final controller in _attributeControllers.values) {
        controller.dispose();
      }
      _attributeControllers.clear();
      _selectedAttributeValues.clear();
    });
  }

  void _initializeAttributeControllers(List<CategoryAttribute> attributes) {
    for (final attribute in attributes) {
      if (!_attributeControllers.containsKey(attribute.id)) {
        _attributeControllers[attribute.id] = TextEditingController();
        if (attribute.defaultValue != null) {
          _attributeControllers[attribute.id]!.text = attribute.defaultValue!;
          _selectedAttributeValues[attribute.id] = attribute.defaultValue;
        }
      }
    }
  }

  List<String> _getSelectOptions(CategoryAttribute attribute) {
    // This would typically come from the backend
    // For now, we'll provide some common options based on attribute name
    switch (attribute.attributeNameEn.toLowerCase()) {
      case 'metal type':
        return ['Gold', 'Silver', 'Platinum', 'White Gold', 'Rose Gold'];
      case 'size':
        return ['XS', 'S', 'M', 'L', 'XL', 'XXL'];
      case 'color':
        return ['Red', 'Blue', 'Green', 'Black', 'White', 'Yellow'];
      case 'fuel type':
        return ['Gasoline', 'Diesel', 'Electric', 'Hybrid'];
      case 'engine size':
        return ['1.0L', '1.5L', '2.0L', '2.5L', '3.0L', '3.5L', '4.0L'];
      case 'cylinders':
        return ['3', '4', '6', '8', '10', '12'];
      case 'fabric type':
        return ['Cotton', 'Linen', 'Silk', 'Polyester', 'Wool'];
      case 'collar type':
        return ['Regular', 'Italian', 'Button-down', 'Spread'];
      case 'length':
        return ['Short', 'Regular', 'Long'];
      case 'karat':
        return ['10K', '14K', '18K', '22K', '24K'];
      case 'ring size':
        return ['5', '6', '7', '8', '9', '10', '11', '12'];
      case 'gemstone type':
        return ['Diamond', 'Ruby', 'Emerald', 'Sapphire', 'Pearl'];
      default:
        return ['Option 1', 'Option 2', 'Option 3'];
    }
  }

  Widget _buildAttributeField(CategoryAttribute attribute) {
    final controller = _attributeControllers[attribute.id];
    if (controller == null) return const SizedBox.shrink();

    switch (attribute.attributeType) {
      case 'text':
        return CustomTextField(
          controller: controller,
          labelText: attribute.attributeNameAr,
          hintText: 'أدخل ${attribute.attributeNameAr}',
          validator: attribute.isRequired ? Validators.required : null,
        );
        
      case 'number':
        return CustomTextField(
          controller: controller,
          labelText: attribute.attributeNameAr,
          hintText: 'أدخل ${attribute.attributeNameAr}',
          keyboardType: TextInputType.number,
          validator: attribute.isRequired ? Validators.required : null,
        );
        
      case 'select':
        return CustomDropdown<String>(
          value: _selectedAttributeValues[attribute.id],
          items: _getSelectOptions(attribute).map((option) {
            return DropdownMenuItem(
              value: option,
              child: Text(option),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedAttributeValues[attribute.id] = value;
              controller.text = value ?? '';
            });
          },
          labelText: attribute.attributeNameAr,
          validator: attribute.isRequired ? Validators.required : null,
        );
        
      case 'boolean':
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Text(
                    attribute.attributeNameAr,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ),
              Switch(
                value: controller.text.toLowerCase() == 'true',
                onChanged: (value) {
                  controller.text = value.toString();
                  setState(() {});
                },
              ),
              Padding(
                padding: const EdgeInsets.only(right: 12),
                child: Text(
                  controller.text.toLowerCase() == 'true' ? 'نعم' : 'لا',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
        );
        
      default:
        return CustomTextField(
          controller: controller,
          labelText: attribute.attributeNameAr,
          hintText: 'أدخل ${attribute.attributeNameAr}',
          validator: attribute.isRequired ? Validators.required : null,
        );
    }
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(top: 24, bottom: 16),
      child: Row(
        children: [
          Icon(icon, color: Theme.of(context).primaryColor),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedMainCategoryId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار تصنيف المنتج')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Prepare product data
      final productData = {
        'main_category_id': _selectedMainCategoryId,
        'name_en': _nameEnController.text.trim(),
        'name_ar': _nameArController.text.trim(),
        'description_en': _descriptionEnController.text.trim().isEmpty 
            ? null 
            : _descriptionEnController.text.trim(),
        'description_ar': _descriptionArController.text.trim().isEmpty 
            ? null 
            : _descriptionArController.text.trim(),
        'manufacturer_part_number': _manufacturerPartNumberController.text.trim().isEmpty 
            ? null 
            : _manufacturerPartNumberController.text.trim(),
        'brand': _brandController.text.trim().isEmpty 
            ? null 
            : _brandController.text.trim(),
        'condition_type': _selectedConditionType ?? 'new',
        'fitment_type': _selectedFitmentType,
        'price': _priceController.text.trim().isEmpty 
            ? null 
            : double.tryParse(_priceController.text.trim()),
        'sale_price': _salePriceController.text.trim().isEmpty 
            ? null 
            : double.tryParse(_salePriceController.text.trim()),
        'currency': _selectedCurrency ?? 'USD',
        'stock_quantity': int.tryParse(_stockQuantityController.text.trim()) ?? 0,
        'min_order_quantity': int.tryParse(_minOrderQuantityController.text.trim()) ?? 1,
        'weight_kg': _weightController.text.trim().isEmpty 
            ? null 
            : double.tryParse(_weightController.text.trim()),
        'dimensions_cm': _dimensionsController.text.trim().isEmpty 
            ? null 
            : _dimensionsController.text.trim(),
        'country_of_origin': _countryOfOriginController.text.trim().isEmpty 
            ? null 
            : _countryOfOriginController.text.trim(),
        'surface_finish': _surfaceFinishController.text.trim().isEmpty 
            ? null 
            : _surfaceFinishController.text.trim(),
        'manufacturer_warranty': _manufacturerWarrantyController.text.trim().isEmpty 
            ? null 
            : _manufacturerWarrantyController.text.trim(),
        'oem_part_numbers': _oemPartNumbersController.text.trim().isEmpty 
            ? null 
            : _oemPartNumbersController.text.trim().split(',').map((e) => e.trim()).toList(),
      };

      // Prepare attributes data
      final attributesData = <Map<String, dynamic>>[];
      for (final entry in _attributeControllers.entries) {
        final attributeId = entry.key;
        final controller = entry.value;
        final value = controller.text.trim();
        
        if (value.isNotEmpty) {
          attributesData.add({
            'category_attribute_id': attributeId,
            'attribute_value': value,
          });
        }
      }

      // Add product using provider
      await ref.read(addProductProvider.notifier).addProduct(
        productData: productData,
        attributesData: attributesData,
      );

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة المنتج بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
        
        // Navigate back
        Navigator.of(context).pop();
      }
    } catch (error) {
      setState(() {
        _errorMessage = error.toString();
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة المنتج: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final mainCategoriesAsync = ref.watch(mainCategoriesProvider);
    final categoryAttributesAsync = _selectedMainCategoryId != null
        ? ref.watch(categoryAttributesProvider(_selectedMainCategoryId!))
        : const AsyncValue.data(<CategoryAttribute>[]);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة منتج جديد'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Basic Product Information Section
                    _buildSectionHeader('المعلومات الأساسية', Icons.info_outline),
                    
                    // Product Name
                    Row(
                      children: [
                        Expanded(
                          child: CustomTextField(
                            controller: _nameEnController,
                            labelText: 'اسم المنتج (إنجليزي)',
                            hintText: 'أدخل اسم المنتج بالإنجليزية',
                            validator: Validators.required,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: CustomTextField(
                            controller: _nameArController,
                            labelText: 'اسم المنتج (عربي)',
                            hintText: 'أدخل اسم المنتج بالعربية',
                            validator: Validators.required,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Description
                    Row(
                      children: [
                        Expanded(
                          child: CustomTextField(
                            controller: _descriptionEnController,
                            labelText: 'الوصف (إنجليزي)',
                            hintText: 'أدخل وصف المنتج بالإنجليزية',
                            maxLines: 3,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: CustomTextField(
                            controller: _descriptionArController,
                            labelText: 'الوصف (عربي)',
                            hintText: 'أدخل وصف المنتج بالعربية',
                            maxLines: 3,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Category Selection Section
                    _buildSectionHeader('تصنيف المنتج', Icons.category),
                    
                    mainCategoriesAsync.when(
                      data: (categories) => CustomDropdown<String>(
                        value: _selectedMainCategoryId,
                        items: categories.map((category) {
                          return DropdownMenuItem(
                            value: category.id,
                            child: Text(category.nameAr),
                          );
                        }).toList(),
                        onChanged: _onMainCategoryChanged,
                        labelText: 'تصنيف المنتج',
                        validator: Validators.required,
                      ),
                      loading: () => const LoadingIndicator(),
                      error: (error, stack) => Text('خطأ في تحميل التصنيفات: $error'),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Product Details Section
                    _buildSectionHeader('تفاصيل المنتج', Icons.details),
                    
                    // Manufacturer Part Number & Brand
                    Row(
                      children: [
                        Expanded(
                          child: CustomTextField(
                            controller: _manufacturerPartNumberController,
                            labelText: 'رقم الجزء من المصنع',
                            hintText: 'MPN-123456',
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: CustomTextField(
                            controller: _brandController,
                            labelText: 'العلامة التجارية',
                            hintText: 'اسم العلامة التجارية',
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Condition & Fitment
                    Row(
                      children: [
                        Expanded(
                          child: CustomDropdown<String>(
                            value: _selectedConditionType,
                            items: _conditionTypes.map((type) {
                              return DropdownMenuItem(
                                value: type,
                                child: Text(type == 'new' ? 'جديد' : 
                                          type == 'used' ? 'مستعمل' : 'مجدول'),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedConditionType = value;
                              });
                            },
                            labelText: 'حالة المنتج',
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: CustomDropdown<String>(
                            value: _selectedFitmentType,
                            items: _fitmentTypes.map((type) {
                              return DropdownMenuItem(
                                value: type,
                                child: Text(type == 'direct_fit' ? 'ملائم مباشرة' : 
                                          type == 'universal' ? 'عالمي' : 'مخصص'),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedFitmentType = value;
                              });
                            },
                            labelText: 'نوع الملاءمة',
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Price & Currency
                    Row(
                      children: [
                        Expanded(
                          child: CustomTextField(
                            controller: _priceController,
                            labelText: 'السعر',
                            hintText: '0.00',
                            keyboardType: TextInputType.number,
                            prefixIcon: const Icon(Icons.attach_money),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: CustomDropdown<String>(
                            value: _selectedCurrency,
                            items: _currencies.map((currency) {
                              return DropdownMenuItem(
                                value: currency,
                                child: Text(currency),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedCurrency = value;
                              });
                            },
                            labelText: 'العملة',
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Sale Price
                    CustomTextField(
                      controller: _salePriceController,
                      labelText: 'سعر البيع (اختياري)',
                      hintText: '0.00',
                      keyboardType: TextInputType.number,
                      prefixIcon: const Icon(Icons.local_offer),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Stock & Min Order
                    Row(
                      children: [
                        Expanded(
                          child: CustomTextField(
                            controller: _stockQuantityController,
                            labelText: 'الكمية المتوفرة',
                            hintText: '0',
                            keyboardType: TextInputType.number,
                            validator: Validators.required,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: CustomTextField(
                            controller: _minOrderQuantityController,
                            labelText: 'الحد الأدنى للطلب',
                            hintText: '1',
                            keyboardType: TextInputType.number,
                            validator: Validators.required,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Weight & Dimensions
                    Row(
                      children: [
                        Expanded(
                          child: CustomTextField(
                            controller: _weightController,
                            labelText: 'الوزن (كجم)',
                            hintText: '0.0',
                            keyboardType: TextInputType.number,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: CustomTextField(
                            controller: _dimensionsController,
                            labelText: 'الأبعاد (سم)',
                            hintText: '10x5x2',
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Country & Surface Finish
                    Row(
                      children: [
                        Expanded(
                          child: CustomTextField(
                            controller: _countryOfOriginController,
                            labelText: 'بلد المنشأ',
                            hintText: 'ألمانيا',
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: CustomTextField(
                            controller: _surfaceFinishController,
                            labelText: 'نهاية السطح',
                            hintText: 'مصقول، مطلي',
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Warranty & OEM Numbers
                    Row(
                      children: [
                        Expanded(
                          child: CustomTextField(
                            controller: _manufacturerWarrantyController,
                            labelText: 'ضمان المصنع',
                            hintText: 'سنتان',
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: CustomTextField(
                            controller: _oemPartNumbersController,
                            labelText: 'أرقام OEM (مفصولة بفواصل)',
                            hintText: '123456, 789012',
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Dynamic Attributes Section
                    if (_selectedMainCategoryId != null) ...[
                      _buildSectionHeader('خصائص المنتج', Icons.tune),
                      
                      categoryAttributesAsync.when(
                        data: (attributes) {
                          _initializeAttributeControllers(attributes);
                          
                          if (attributes.isEmpty) {
                            return const Card(
                              child: Padding(
                                padding: EdgeInsets.all(16),
                                child: Text(
                                  'لا توجد خصائص محددة لهذا التصنيف',
                                  style: TextStyle(
                                    fontStyle: FontStyle.italic,
                                    color: Colors.grey,
                                  ),
                                ),
                              ),
                            );
                          }
                          
                          return Column(
                            children: attributes.map((attribute) {
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: _buildAttributeField(attribute),
                              );
                            }).toList(),
                          );
                        },
                        loading: () => const LoadingIndicator(),
                        error: (error, stack) => Text('خطأ في تحميل الخصائص: $error'),
                      ),
                    ],
                    
                    // Error message
                    if (_errorMessage != null) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          border: Border.all(color: Colors.red.shade200),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.error, color: Colors.red.shade600),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(color: Colors.red.shade700),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                    
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
            
            // Save Button
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: CustomButton(
                onPressed: _isLoading ? null : _saveProduct,
                text: 'حفظ المنتج',
                isLoading: _isLoading,
                fullWidth: true,
              ),
            ),
          ],
        ),
      ),
    );
  }
} 