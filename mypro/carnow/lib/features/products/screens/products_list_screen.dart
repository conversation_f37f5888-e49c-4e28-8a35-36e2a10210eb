import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:carnow/core/models/product.dart';
import 'package:carnow/features/products/providers/product_providers.dart';
import 'package:carnow/shared/widgets/loading_indicator.dart';
import 'package:carnow/shared/widgets/custom_text_field.dart';
import 'package:carnow/shared/widgets/custom_dropdown.dart';
import 'package:carnow/shared/widgets/custom_button.dart';

class ProductsListScreen extends ConsumerStatefulWidget {
  final String? mainCategoryId;
  final String? categoryName;

  const ProductsListScreen({
    Key? key,
    this.mainCategoryId,
    this.categoryName,
  }) : super(key: key);

  @override
  ConsumerState<ProductsListScreen> createState() => _ProductsListScreenState();
}

class _ProductsListScreenState extends ConsumerState<ProductsListScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  Map<String, String> _filters = {};
  int _currentPage = 1;
  final int _itemsPerPage = 20;
  bool _isLoadingMore = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String value) {
    setState(() {
      _searchQuery = value;
      _currentPage = 1; // Reset to first page when searching
    });
    
    // Trigger search
    ref.read(productSearchProvider.notifier).searchProducts(
      query: value.isEmpty ? null : value,
      mainCategoryId: widget.mainCategoryId,
      filters: _filters.isEmpty ? null : _filters,
      page: _currentPage,
      limit: _itemsPerPage,
    );
  }

  void _addFilter(String key, String value) {
    setState(() {
      _filters[key] = value;
      _currentPage = 1; // Reset to first page when filtering
    });
    
    _performSearch();
  }

  void _removeFilter(String key) {
    setState(() {
      _filters.remove(key);
      _currentPage = 1; // Reset to first page when removing filter
    });
    
    _performSearch();
  }

  void _clearAllFilters() {
    setState(() {
      _filters.clear();
      _currentPage = 1;
    });
    
    _performSearch();
  }

  void _performSearch() {
    ref.read(productSearchProvider.notifier).searchProducts(
      query: _searchQuery.isEmpty ? null : _searchQuery,
      mainCategoryId: widget.mainCategoryId,
      filters: _filters.isEmpty ? null : _filters,
      page: _currentPage,
      limit: _itemsPerPage,
    );
  }

  void _loadMore() {
    if (_isLoadingMore) return;
    
    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });
    
    ref.read(productSearchProvider.notifier).searchProducts(
      query: _searchQuery.isEmpty ? null : _searchQuery,
      mainCategoryId: widget.mainCategoryId,
      filters: _filters.isEmpty ? null : _filters,
      page: _currentPage,
      limit: _itemsPerPage,
    ).then((_) {
      setState(() {
        _isLoadingMore = false;
      });
    });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => _FilterDialog(
        currentFilters: _filters,
        onApplyFilters: (filters) {
          setState(() {
            _filters = filters;
            _currentPage = 1;
          });
          _performSearch();
          Navigator.of(context).pop();
        },
      ),
    );
  }

  String _getConditionText(String condition) {
    switch (condition.toLowerCase()) {
      case 'new':
        return 'جديد';
      case 'used':
        return 'مستعمل';
      case 'refurbished':
        return 'مجدول';
      default:
        return condition;
    }
  }

  Widget _buildProductCard(Product product) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      child: InkWell(
        onTap: () {
          // Navigate to product details
          Navigator.pushNamed(
            context,
            '/product-details',
            arguments: product.id,
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Image Placeholder
              Container(
                height: 120,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Icon(
                    Icons.image,
                    size: 48,
                    color: Colors.grey,
                  ),
                ),
              ),
              
              const SizedBox(height: 12),
              
              // Product Name
              Text(
                product.nameAr,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 4),
              
              // Brand
              if (product.brand != null) ...[
                Text(
                  product.brand!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 4),
              ],
              
              // Condition
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getConditionColor(product.conditionType),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getConditionText(product.conditionType),
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              
              const SizedBox(height: 8),
              
              // Price
              Row(
                children: [
                  if (product.salePrice != null && product.salePrice! < (product.price ?? 0)) ...[
                    Text(
                      '${product.salePrice!.toStringAsFixed(2)} ${product.currency}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${product.price!.toStringAsFixed(2)} ${product.currency}',
                      style: TextStyle(
                        fontSize: 14,
                        decoration: TextDecoration.lineThrough,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ] else ...[
                    Text(
                      '${(product.price ?? 0).toStringAsFixed(2)} ${product.currency}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Stock Status
              Row(
                children: [
                  Icon(
                    product.stockQuantity > 0 ? Icons.check_circle : Icons.cancel,
                    size: 16,
                    color: product.stockQuantity > 0 ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    product.stockQuantity > 0 
                        ? 'متوفر (${product.stockQuantity})'
                        : 'غير متوفر',
                    style: TextStyle(
                      fontSize: 12,
                      color: product.stockQuantity > 0 ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getConditionColor(String condition) {
    switch (condition.toLowerCase()) {
      case 'new':
        return Colors.green;
      case 'used':
        return Colors.orange;
      case 'refurbished':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    final productsAsync = ref.watch(productSearchProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.categoryName ?? 'المنتجات'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey.shade50,
            child: CustomTextField(
              controller: _searchController,
              labelText: 'البحث في المنتجات',
              hintText: 'ابحث عن منتج...',
              prefixIcon: const Icon(Icons.search),
              onChanged: _onSearchChanged,
            ),
          ),
          
          // Active Filters
          if (_filters.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: Colors.blue.shade50,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Text(
                        'الفلاتر النشطة:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: _clearAllFilters,
                        child: const Text('مسح الكل'),
                      ),
                    ],
                  ),
                  Wrap(
                    spacing: 8,
                    children: _filters.entries.map((entry) {
                      return Chip(
                        label: Text('${entry.key}: ${entry.value}'),
                        onDeleted: () => _removeFilter(entry.key),
                        deleteIcon: const Icon(Icons.close, size: 16),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ],
          
          // Products List
          Expanded(
            child: productsAsync.when(
              data: (products) {
                if (products.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'لا توجد منتجات',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  );
                }
                
                return RefreshIndicator(
                  onRefresh: () async {
                    _currentPage = 1;
                    _performSearch();
                  },
                  child: ListView.builder(
                    itemCount: products.length + (_isLoadingMore ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index == products.length) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(16),
                            child: LoadingIndicator(),
                          ),
                        );
                      }
                      
                      final product = products[index];
                      return _buildProductCard(product);
                    },
                  ),
                );
              },
              loading: () => const Center(child: LoadingIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'خطأ في تحميل المنتجات',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.red.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      style: const TextStyle(color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    CustomButton(
                      onPressed: _performSearch,
                      text: 'إعادة المحاولة',
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/add-product');
        },
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }
}

class _FilterDialog extends StatefulWidget {
  final Map<String, String> currentFilters;
  final Function(Map<String, String>) onApplyFilters;

  const _FilterDialog({
    required this.currentFilters,
    required this.onApplyFilters,
  });

  @override
  State<_FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<_FilterDialog> {
  late Map<String, String> _filters;
  
  // Filter controllers
  final _minPriceController = TextEditingController();
  final _maxPriceController = TextEditingController();
  String? _selectedCondition;
  String? _selectedBrand;

  @override
  void initState() {
    super.initState();
    _filters = Map.from(widget.currentFilters);
    
    // Initialize controllers with current values
    _minPriceController.text = _filters['min_price'] ?? '';
    _maxPriceController.text = _filters['max_price'] ?? '';
    _selectedCondition = _filters['condition'];
    _selectedBrand = _filters['brand'];
  }

  @override
  void dispose() {
    _minPriceController.dispose();
    _maxPriceController.dispose();
    super.dispose();
  }

  void _applyFilters() {
    final newFilters = <String, String>{};
    
    if (_minPriceController.text.isNotEmpty) {
      newFilters['min_price'] = _minPriceController.text;
    }
    
    if (_maxPriceController.text.isNotEmpty) {
      newFilters['max_price'] = _maxPriceController.text;
    }
    
    if (_selectedCondition != null) {
      newFilters['condition'] = _selectedCondition!;
    }
    
    if (_selectedBrand != null) {
      newFilters['brand'] = _selectedBrand!;
    }
    
    widget.onApplyFilters(newFilters);
  }

  void _clearFilters() {
    setState(() {
      _filters.clear();
      _minPriceController.clear();
      _maxPriceController.clear();
      _selectedCondition = null;
      _selectedBrand = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تصفية المنتجات'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Price Range
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _minPriceController,
                    labelText: 'السعر الأدنى',
                    hintText: '0',
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: CustomTextField(
                    controller: _maxPriceController,
                    labelText: 'السعر الأعلى',
                    hintText: '1000',
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Condition
            CustomDropdown<String>(
              value: _selectedCondition,
              items: const [
                DropdownMenuItem(value: 'new', child: Text('جديد')),
                DropdownMenuItem(value: 'used', child: Text('مستعمل')),
                DropdownMenuItem(value: 'refurbished', child: Text('مجدول')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedCondition = value;
                });
              },
              labelText: 'حالة المنتج',
            ),
            
            const SizedBox(height: 16),
            
            // Brand
            CustomTextField(
              controller: TextEditingController(text: _selectedBrand ?? ''),
              labelText: 'العلامة التجارية',
              hintText: 'أدخل اسم العلامة التجارية',
              onChanged: (value) {
                _selectedBrand = value.isEmpty ? null : value;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _clearFilters,
          child: const Text('مسح'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _applyFilters,
          child: const Text('تطبيق'),
        ),
      ],
    );
  }
} 