import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:carnow/core/models/category.dart';
import 'package:carnow/features/products/providers/category_providers.dart';
import 'package:carnow/shared/widgets/custom_text_field.dart';
import 'package:carnow/shared/widgets/custom_dropdown.dart';
import 'package:carnow/shared/widgets/custom_button.dart';
import 'package:carnow/shared/widgets/loading_indicator.dart';
import 'package:carnow/core/utils/validators.dart';

class ManageCategoryAttributesScreen extends ConsumerStatefulWidget {
  final String mainCategoryId;
  final String categoryName;

  const ManageCategoryAttributesScreen({
    Key? key,
    required this.mainCategoryId,
    required this.categoryName,
  }) : super(key: key);

  @override
  ConsumerState<ManageCategoryAttributesScreen> createState() => _ManageCategoryAttributesScreenState();
}

class _ManageCategoryAttributesScreenState extends ConsumerState<ManageCategoryAttributesScreen> {
  final _formKey = GlobalKey<FormState>();
  final _attributeNameEnController = TextEditingController();
  final _attributeNameArController = TextEditingController();
  
  String _selectedAttributeType = 'text';
  bool _isRequired = false;
  bool _isSearchable = true;
  bool _isFilterable = true;
  int _sortOrder = 0;

  @override
  void dispose() {
    _attributeNameEnController.dispose();
    _attributeNameArController.dispose();
    super.dispose();
  }

  Future<void> _addAttribute() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      final attributeData = {
        'main_category_id': widget.mainCategoryId,
        'attribute_name_en': _attributeNameEnController.text.trim(),
        'attribute_name_ar': _attributeNameArController.text.trim(),
        'attribute_type': _selectedAttributeType,
        'is_required': _isRequired,
        'is_searchable': _isSearchable,
        'is_filterable': _isFilterable,
        'sort_order': _sortOrder,
      };

      // Add attribute logic here
      // await ref.read(addCategoryAttributeProvider.notifier).addAttribute(attributeData);

      // Clear form
      _attributeNameEnController.clear();
      _attributeNameArController.clear();
      setState(() {
        _selectedAttributeType = 'text';
        _isRequired = false;
        _isSearchable = true;
        _isFilterable = true;
        _sortOrder = 0;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة الخاصية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة الخاصية: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final attributesAsync = ref.watch(categoryAttributesProvider(widget.mainCategoryId));

    return Scaffold(
      appBar: AppBar(
        title: Text('خصائص ${widget.categoryName}'),
        centerTitle: true,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Add Attribute Form
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إضافة خاصية جديدة',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Attribute Name
                  Row(
                    children: [
                      Expanded(
                        child: CustomTextField(
                          controller: _attributeNameEnController,
                          labelText: 'اسم الخاصية (إنجليزي)',
                          validator: Validators.required,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: CustomTextField(
                          controller: _attributeNameArController,
                          labelText: 'اسم الخاصية (عربي)',
                          validator: Validators.required,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Attribute Type
                  CustomDropdown<String>(
                    value: _selectedAttributeType,
                    items: const [
                      DropdownMenuItem(value: 'text', child: Text('نص')),
                      DropdownMenuItem(value: 'number', child: Text('رقم')),
                      DropdownMenuItem(value: 'select', child: Text('قائمة منسدلة')),
                      DropdownMenuItem(value: 'boolean', child: Text('صح/خطأ')),
                    ],
                    onChanged: (value) => setState(() => _selectedAttributeType = value!),
                    labelText: 'نوع الخاصية',
                  ),
                  const SizedBox(height: 16),

                  // Options
                  Row(
                    children: [
                      Expanded(
                        child: CheckboxListTile(
                          title: const Text('مطلوب'),
                          value: _isRequired,
                          onChanged: (value) => setState(() => _isRequired = value!),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      Expanded(
                        child: CheckboxListTile(
                          title: const Text('قابل للبحث'),
                          value: _isSearchable,
                          onChanged: (value) => setState(() => _isSearchable = value!),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      Expanded(
                        child: CheckboxListTile(
                          title: const Text('قابل للتصفية'),
                          value: _isFilterable,
                          onChanged: (value) => setState(() => _isFilterable = value!),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Sort Order
                  CustomTextField(
                    labelText: 'ترتيب العرض',
                    keyboardType: TextInputType.number,
                    controller: TextEditingController(text: _sortOrder.toString()),
                  ),
                  const SizedBox(height: 16),

                  CustomButton(
                    onPressed: _addAttribute,
                    text: 'إضافة الخاصية',
                    fullWidth: true,
                  ),
                ],
              ),
            ),
          ),

          // Attributes List
          Expanded(
            child: attributesAsync.when(
              data: (attributes) {
                if (attributes.isEmpty) {
                  return const Center(
                    child: Text('لا توجد خصائص لهذا التصنيف'),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: attributes.length,
                  itemBuilder: (context, index) {
                    final attribute = attributes[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        title: Text(attribute.attributeNameAr),
                        subtitle: Text(
                          '${attribute.attributeNameEn} (${attribute.attributeType})',
                          style: TextStyle(
                            color: Theme.of(context).textTheme.bodySmall?.color,
                          ),
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (attribute.isRequired)
                              const Icon(Icons.star, color: Colors.orange, size: 16),
                            if (attribute.isSearchable)
                              const Icon(Icons.search, color: Colors.blue, size: 16),
                            if (attribute.isFilterable)
                              const Icon(Icons.filter_list, color: Colors.green, size: 16),
                            const SizedBox(width: 8),
                            Text('${attribute.sortOrder}'),
                          ],
                        ),
                        onTap: () {
                          // Edit attribute logic
                        },
                      ),
                    );
                  },
                );
              },
              loading: () => const LoadingIndicator(),
              error: (error, stack) => Center(
                child: Text('خطأ: $error'),
              ),
            ),
          ),
        ],
      ),
    );
  }
} 