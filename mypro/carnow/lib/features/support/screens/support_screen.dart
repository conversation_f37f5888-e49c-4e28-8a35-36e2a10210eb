/// شاشة الدعم الفني والمساعدة
///
/// تعمل كبوابة مركزية للمستخدمين للحصول على المساعدة.
/// تعرض معلومات الاتصال بفريق الدعم، وقائمة بالأسئلة الأكثر شيوعًا (FAQ)
/// لتوفير حلول سريعة، بالإضافة إلى خيار بدء محادثة مباشرة مع فريق الدعم.
library;

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/theme/app_theme.dart';
import '../../../l10n/app_localizations.dart';
import '../models/faq_model.dart';

// Provider for FAQs - جلب البيانات من قاعدة البيانات
final faqProvider = FutureProvider<List<FaqModel>>((ref) async {
  try {
    // DEPRECATED: Direct Supabase calls removed - use Go backend API instead
    // FAQs should be fetched from Go backend
    throw UnimplementedError('Use SimpleApiClient for FAQs instead');
  } catch (e) {
    // في حالة الخطأ، إرجاع قائمة فارغة
    return <FaqModel>[];
  }
});

class SupportScreen extends HookConsumerWidget {
  const SupportScreen({super.key});

  Future<void> _launchEmail(String email) async {
    final emailLaunchUri = Uri(scheme: 'mailto', path: email);
    if (await canLaunchUrl(emailLaunchUri)) {
      await launchUrl(emailLaunchUri);
    } else {
      // Handle error: could not launch email app
      // You could show a SnackBar or dialog here
    }
  }

  Future<void> _launchPhone(String phoneNumber) async {
    final phoneLaunchUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(phoneLaunchUri)) {
      await launchUrl(phoneLaunchUri);
    } else {
      // Handle error
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final faqs = ref.watch(faqProvider);
    final theme = Theme.of(context);

    // Expansion state is now handled by the ExpansionTile widget

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.support),
        elevation: 1, // Add a slight elevation for better separation
      ),
      body: SingleChildScrollView(
        // Use SingleChildScrollView for potentially long content
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle(context, l10n.contactUs),
            const SizedBox(height: AppTheme.spacingS), // Reduced padding
            Card(
              elevation: 0.5,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusM),
                side: BorderSide(
                  color: theme.colorScheme.outline.withAlpha(
                    (0.12 * 255).toInt(),
                  ),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    leading: Icon(
                      Icons.email_outlined,
                      color: theme.colorScheme.primary,
                    ),
                    title: Text(
                      l10n.emailSupport,
                      style: theme.textTheme.titleMedium,
                    ),
                    subtitle: const Text('<EMAIL>'),
                    onTap: () => _launchEmail('<EMAIL>'),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingM,
                      vertical: AppTheme.spacingS,
                    ),
                  ),
                  const Divider(
                    height: 1,
                    indent: AppTheme.spacingM,
                    endIndent: AppTheme.spacingM,
                  ),
                  ListTile(
                    leading: Icon(
                      Icons.phone_outlined,
                      color: theme.colorScheme.primary,
                    ),
                    title: Text(
                      l10n.phoneSupport,
                      style: theme.textTheme.titleMedium,
                    ),
                    subtitle: const Text('+966 11 123 4567'),
                    onTap: () => _launchPhone('+966111234567'),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingM,
                      vertical: AppTheme.spacingS,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppTheme.spacingXL),
            _buildSectionTitle(context, l10n.faq),
            const SizedBox(height: AppTheme.spacingS),
            faqs.when(
              data: (faqList) {
                if (faqList.isEmpty) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: AppTheme.spacingL,
                    ),
                    child: Center(
                      child: Text(
                        l10n.noFaqsAvailable,
                        textAlign: TextAlign.center,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                  );
                }
                return ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: faqList.length,
                  itemBuilder: (context, index) {
                    final faq = faqList[index];
                    return ExpansionTile(
                      title: Text(
                        faq.question,
                        style: theme.textTheme.titleMedium,
                      ),
                      tilePadding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingM,
                        vertical: AppTheme.spacingXS / 2,
                      ),
                      childrenPadding: const EdgeInsets.fromLTRB(
                        AppTheme.spacingM,
                        0,
                        AppTheme.spacingM,
                        AppTheme.spacingS,
                      ),
                      iconColor: theme.colorScheme.primary,
                      collapsedIconColor: theme.colorScheme.onSurfaceVariant,
                      children: <Widget>[
                        Align(
                          alignment: Alignment.centerRight,
                          child: Text(
                            faq.answer,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                              height: 1.5,
                            ),
                            textAlign: TextAlign.start,
                          ),
                        ),
                      ],
                    );
                  },
                  separatorBuilder: (context, index) => const Divider(
                    height: 1,
                    indent: AppTheme.spacingM,
                    endIndent: AppTheme.spacingM,
                  ),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Text(
                  'خطأ في تحميل الأسئلة الشائعة',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.error,
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: AppTheme.spacingXL,
            ), // More space before the button
            FilledButton(
              onPressed: () {
                context.push('/messaging');
              },
              style: FilledButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                minimumSize: const Size(
                  double.infinity,
                  56,
                ), // Made button taller
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingL,
                  vertical: AppTheme.spacingM,
                ), // Adjusted padding
                shape: const StadiumBorder(), // Pill-shaped button
                elevation: 3,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.chat_bubble_outline_rounded,
                    size: 22,
                  ), // More rounded icon
                  const SizedBox(
                    width: AppTheme.spacingM,
                  ), // Space between icon and text
                  Text(
                    l10n.liveChat,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.bold, // Make text bold
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppTheme.spacingL), // Padding at the bottom
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) => Padding(
    padding: const EdgeInsets.only(
      bottom: AppTheme.spacingM,
      top: AppTheme.spacingS,
    ),
    child: Text(
      title,
      style: Theme.of(
        context,
      ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600),
    ),
  );
}
