/// شاشة محادثة تذكرة الدعم
///
/// توفر واجهة محادثة نصية بين المستخدم وفريق الدعم الفني بخصوص تذكرة محددة.
/// تتيح للمستخدم إرسال واستقبال الرسائل، ومتابعة سجل المحادثة
/// لحل المشكلة التي يواجهها.
library;

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/theme/app_theme.dart';
import '../models/support_ticket_model.dart';
import '../providers/support_ticket_provider.dart';
import '../providers/support_provider.dart';
import 'package:carnow/shared/widgets/error_message.dart';

class SupportTicketChatScreen extends HookConsumerWidget {
  const SupportTicketChatScreen({required this.ticketId, super.key});
  final int ticketId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // final l10n = AppLocalizations.of(context)!; // MODIFIED - REMOVED
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final ticketAsync = ref.watch(supportTicketByIdProvider(ticketId.toString()));
    final messagesAsync = ref.watch(supportTicketMessagesProvider(ticketId.toString()));

    final messageController = useTextEditingController();
    final scrollController = useScrollController();
    final isSending = useState(false);

    // Auto-scroll to bottom when new messages arrive
    useEffect(() {
      if (messagesAsync.hasValue && scrollController.hasClients) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          scrollController.animateTo(
            scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        });
      }
      return null;
    });

    return Scaffold(
      appBar: AppBar(
        title: ticketAsync.when(
          data: (ticket) => ticket != null
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('تذكرة #${ticket.id}'),
                    Text(
                      ticket.subject,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onPrimary.withValues(alpha: 0.80),
                      ),
                    ),
                  ],
                )
              : const Text('تذكرة الدعم'),
          loading: () => const Text('تذكرة الدعم'),
          error: (_, _) => const Text('تذكرة الدعم'),
        ),
        elevation: 0,
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        actions: [
          ticketAsync.when(
            data: (ticket) => ticket != null && ticket.isActive
                ? PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'close') {
                        _showCloseTicketDialog(context, ref, ticket);
                      } else if (value == 'reopen') {
                        _reopenTicket(context, ref, ticket);
                      }
                    },
                    itemBuilder: (context) => [
                      if (ticket.status != TicketStatus.resolved &&
                          ticket.status != TicketStatus.closed)
                        PopupMenuItem(
                          value: 'close',
                          child: Row(
                            children: [
                              Icon(
                                Icons.check_circle_outline,
                                color: colorScheme.primary,
                              ),
                              const SizedBox(width: AppTheme.spacingS),
                              const Text('إغلاق التذكرة'),
                            ],
                          ),
                        ),
                      if (ticket.status == TicketStatus.resolved ||
                          ticket.status == TicketStatus.closed)
                        PopupMenuItem(
                          value: 'reopen',
                          child: Row(
                            children: [
                              Icon(Icons.refresh, color: colorScheme.secondary),
                              const SizedBox(width: AppTheme.spacingS),
                              const Text('إعادة فتح التذكرة'),
                            ],
                          ),
                        ),
                    ],
                  )
                : const SizedBox.shrink(),
            loading: () => const SizedBox.shrink(),
            error: (_, _) => const SizedBox.shrink(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Ticket status and info
          ticketAsync.when(
            data: (ticket) => ticket != null
                ? _TicketHeader(ticket: ticket)
                : const SizedBox.shrink(),
            loading: () => const SizedBox.shrink(),
            error: (_, _) => const SizedBox.shrink(),
          ),

          // Messages
          Expanded(
            child: messagesAsync.when(
              data: (messages) {
                if (messages.isEmpty) {
                  return _EmptyMessagesView(
                    onSendFirstMessage: () {
                      FocusScope.of(context).requestFocus(FocusNode());
                    },
                  );
                }

                return ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.all(AppTheme.spacingM),
                  itemCount: messages.length,
                  itemBuilder: (context, index) {
                    final message = messages[index];
                    return _MessageBubble(
                      message: message,
                      isLast: index == messages.length - 1,
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stackTrace) =>
                  Center(child: ErrorMessage(message: error.toString())),
            ),
          ),

          // Message input
          ticketAsync.when(
            data: (ticket) => ticket != null && ticket.isActive
                ? _MessageInput(
                    controller: messageController,
                    isSending: isSending.value,
                    onSend: () => _sendMessage(
                      context,
                      ref,
                      messageController,
                      isSending,
                    ),
                  )
                : _ClosedTicketMessage(ticket: ticket),
            loading: () => const SizedBox.shrink(),
            error: (_, _) => const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Future<void> _sendMessage(
    BuildContext context,
    WidgetRef ref,
    TextEditingController controller,
    ValueNotifier<bool> isSending,
  ) async {
    final message = controller.text.trim();
    if (message.isEmpty || isSending.value) return;

    isSending.value = true;
    controller.clear();

    try {
      await ref
          .read(supportTicketActionsProvider.notifier)
          .addMessage(
            ticketId: ticketId.toString(),
            message: message,
          );
    } catch (e) {
      if (context.mounted) {
        final colorScheme = Theme.of(context).colorScheme;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إرسال الرسالة: ${e.toString()}'),
            backgroundColor: colorScheme.error,
          ),
        );
      }
      // Restore message if sending failed
      controller.text = message;
    } finally {
      isSending.value = false;
    }
  }

  void _showCloseTicketDialog(
    BuildContext context,
    WidgetRef ref,
    SupportTicketModel ticket,
  ) {
    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        final theme = Theme.of(context);
        final colorScheme = theme.colorScheme;
        return AlertDialog(
          title: const Text('إغلاق التذكرة'),
          content: const Text('هل أنت متأكد من أنك تريد إغلاق هذه التذكرة؟'),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusL),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            FilledButton(
              onPressed: () async {
                Navigator.of(context).pop();
                try {
                  await ref
                      .read(supportTicketActionsProvider.notifier)
                      .updateTicketStatus(
                        ticketId: ticket.id,
                        status: 'closed',
                      );
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم إغلاق التذكرة بنجاح'),
                        backgroundColor: AppTheme.success,
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('فشل في إغلاق التذكرة: ${e.toString()}'),
                        backgroundColor: colorScheme.error,
                      ),
                    );
                  }
                }
              },
              style: FilledButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
              ),
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _reopenTicket(
    BuildContext context,
    WidgetRef ref,
    SupportTicketModel ticket,
  ) async {
    final colorScheme = Theme.of(context).colorScheme;
    try {
      await ref
          .read(supportTicketActionsProvider.notifier)
          .updateTicketStatus(
            ticketId: ticket.id,
            status: 'open',
          );
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إعادة فتح التذكرة'),
            backgroundColor: AppTheme.success,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إعادة فتح التذكرة: ${e.toString()}'),
            backgroundColor: colorScheme.error,
          ),
        );
      }
    }
  }
}

class _TicketHeader extends StatelessWidget {
  const _TicketHeader({required this.ticket});
  final SupportTicketModel ticket;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final statusInfo = _getStatusInfo(ticket.status, colorScheme);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingL,
        vertical: AppTheme.spacingS,
      ),
      color: statusInfo['color']!.withValues(alpha: 0.1),
      child: Row(
        children: [
          Icon(statusInfo['icon'], color: statusInfo['color'], size: 20),
          const SizedBox(width: AppTheme.spacingM),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                statusInfo['text']!,
                style: theme.textTheme.labelLarge?.copyWith(
                  color: statusInfo['color'],
                ),
              ),
              if (ticket.updatedAt != null)
                Text(
                  'آخر تحديث: ${timeago.format(ticket.updatedAt!, locale: 'ar')}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: statusInfo['color']?.withValues(alpha: 0.80),
                  ),
                ),
            ],
          ),
          const Spacer(),
          Text(
            'الأولوية: ${_getPriorityText(ticket.priority)}',
            style: theme.textTheme.labelMedium?.copyWith(
              color: statusInfo['color'],
            ),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getStatusInfo(
    TicketStatus? status,
    ColorScheme colorScheme,
  ) {
    switch (status) {
      case TicketStatus.open:
        return {
          'text': 'مفتوحة',
          'icon': Icons.open_in_new,
          'color': colorScheme.primary,
        };
      case TicketStatus.pending:
        return {
          'text': 'قيد الانتظار',
          'icon': Icons.hourglass_top,
          'color': colorScheme.secondary,
        };
      case TicketStatus.resolved:
        return {
          'text': 'تم حلها',
          'icon': Icons.check_circle,
          'color': AppTheme.success,
        };
      case TicketStatus.closed:
        return {
          'text': 'مغلقة',
          'icon': Icons.lock_outline,
          'color': colorScheme.onSurfaceVariant,
        };
      default:
        return {
          'text': 'غير معروف',
          'icon': Icons.help_outline,
          'color': colorScheme.onSurfaceVariant,
        };
    }
  }

  String _getPriorityText(TicketPriority? priority) {
    switch (priority) {
      case TicketPriority.low:
        return 'منخفضة';
      case TicketPriority.medium:
        return 'متوسطة';
      case TicketPriority.high:
        return 'عالية';
      case TicketPriority.urgent:
        return 'عاجلة';
      default:
        return 'غير محددة';
    }
  }
}

class _MessageBubble extends StatelessWidget {
  const _MessageBubble({required this.message, required this.isLast});
  final SupportTicketMessageModel message;
  final bool isLast;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final isUserMessage = message.isFromUser;

    final bubbleColor = isUserMessage
        ? colorScheme.primaryContainer
        : colorScheme.secondaryContainer.withValues(alpha: 0.50);
    final textColor = isUserMessage
        ? colorScheme.onPrimaryContainer
        : colorScheme.onSecondaryContainer;
    final alignment = isUserMessage
        ? CrossAxisAlignment.end
        : CrossAxisAlignment.start;
    final bubbleRadius = BorderRadius.only(
      topLeft: const Radius.circular(AppTheme.radiusL),
      topRight: const Radius.circular(AppTheme.radiusL),
      bottomLeft: isUserMessage
          ? const Radius.circular(AppTheme.radiusL)
          : const Radius.circular(AppTheme.radiusS),
      bottomRight: isUserMessage
          ? const Radius.circular(AppTheme.radiusS)
          : const Radius.circular(AppTheme.radiusL),
    );

    return Container(
      margin: EdgeInsets.only(
        top: AppTheme.spacingS,
        bottom: isLast ? AppTheme.spacingS : 0,
        left: isUserMessage ? 60 : 0,
        right: isUserMessage ? 0 : 60,
      ),
      child: Column(
        crossAxisAlignment: alignment,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingM,
              vertical: AppTheme.spacingS,
            ),
            decoration: BoxDecoration(
              color: bubbleColor,
              borderRadius: bubbleRadius,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  message.message,
                  style: theme.textTheme.bodyLarge?.copyWith(color: textColor),
                  textAlign: isUserMessage ? TextAlign.right : TextAlign.left,
                ),
                const SizedBox(height: AppTheme.spacingXS),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      message.createdAt != null
                          ? timeago.format(
                              message.createdAt!,
                              locale: 'ar_short',
                            )
                          : '',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: textColor.withValues(alpha: 0.70),
                      ),
                    ),
                    if (isUserMessage)
                      const SizedBox(width: AppTheme.spacingXS),
                    if (isUserMessage)
                      Icon(
                        message.isRead ? Icons.done_all : Icons.done,
                        size: 14,
                        color: message.isRead
                            ? colorScheme.primary
                            : textColor.withValues(alpha: 0.70),
                      ),
                  ],
                ),
              ],
            ),
          ),
          if (message.sender?['name'] != null)
            Padding(
              padding: const EdgeInsets.only(
                top: AppTheme.spacingXS,
                left: AppTheme.spacingS,
                right: AppTheme.spacingS,
              ),
              child: Text(
                message.sender!['name'] as String,
                style: theme.textTheme.labelSmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class _MessageInput extends StatelessWidget {
  const _MessageInput({
    required this.controller,
    required this.onSend,
    required this.isSending,
  });

  final TextEditingController controller;
  final VoidCallback onSend;
  final bool isSending;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Material(
      elevation: 8,
      color: theme.cardColor,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  decoration: InputDecoration(
                    hintText: 'اكتب رسالتك...',
                    filled: true,
                    fillColor: colorScheme.surfaceContainerHighest.withValues(
                      alpha: 0.50,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppTheme.radiusFull),
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingM,
                      vertical: AppTheme.spacingS,
                    ),
                  ),
                  textCapitalization: TextCapitalization.sentences,
                  minLines: 1,
                  maxLines: 5,
                ),
              ),
              const SizedBox(width: AppTheme.spacingS),
              if (isSending)
                const Padding(
                  padding: EdgeInsets.all(12),
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
              if (!isSending)
                IconButton(
                  icon: const Icon(Icons.send),
                  onPressed: onSend,
                  style: IconButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                    padding: const EdgeInsets.all(12),
                  ),
                  tooltip: 'إرسال',
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ClosedTicketMessage extends StatelessWidget {
  const _ClosedTicketMessage({this.ticket});
  final SupportTicketModel? ticket;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.50),
      child: Text(
        'تم ${ticket?.status == TicketStatus.resolved ? "حل" : "إغلاق"} هذه التذكرة ولا يمكن إضافة المزيد من الرسائل.',
        textAlign: TextAlign.center,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }
}

class _EmptyMessagesView extends StatelessWidget {
  const _EmptyMessagesView({this.onSendFirstMessage});
  final VoidCallback? onSendFirstMessage;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.forum_outlined,
              size: 80,
              color: colorScheme.onSurface.withValues(alpha: 0.30),
            ),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              'لا توجد رسائل بعد',
              style: theme.textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              'ابدأ المحادثة بإرسال رسالتك الأولى.',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingL),
            if (onSendFirstMessage != null)
              ElevatedButton.icon(
                onPressed: onSendFirstMessage,
                icon: const Icon(Icons.send_outlined),
                label: const Text('إرسال رسالة'),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingL,
                    vertical: AppTheme.spacingM,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
