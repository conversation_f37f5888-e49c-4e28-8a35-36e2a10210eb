/// شاشة تذاكر الدعم الفني
///
/// تعرض هذه الشاشة قائمة بجميع تذاكر الدعم التي أنشأها المستخدم.
/// تتيح للمستخدم متابعة حالة تذاكره (مفتوحة، قيد المعالجة، مغلقة)
/// وتوفر نقطة انطلاق لإنشاء تذكرة دعم جديدة.
library;

// ignore_for_file: unused_import
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/theme/app_theme.dart';
import '../models/support_ticket_model.dart';
import '../providers/support_provider.dart';
import '../../../shared/widgets/error_message.dart';

class SupportTicketsScreen extends HookConsumerWidget {
  const SupportTicketsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ticketsAsync = ref.watch(supportTicketsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تذاكر الدعم'),
        elevation: 1,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(supportTicketsProvider);
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(supportTicketsProvider);
        },
        child: ticketsAsync.when(
          data: (tickets) {
            if (tickets.isEmpty) {
              return const _EmptyTicketsView();
            }

            return ListView.builder(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              itemCount: tickets.length,
              itemBuilder: (context, index) {
                final ticket = tickets[index];
                return _TicketTile(
                  ticket: ticket,
                  onTap: () {
                    context.push('/support/tickets/${ticket.id}');
                  },
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) => Center(
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: ErrorMessage(message: error.toString()),
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          context.push('/support/tickets/new');
        },
        icon: const Icon(Icons.add),
        label: const Text('تذكرة جديدة'),
      ),
    );
  }
}

class _TicketTile extends StatelessWidget {
  const _TicketTile({required this.ticket, required this.onTap});
  final SupportTicketModel ticket;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      elevation: 0.5,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        side: BorderSide(
          color: theme.colorScheme.outline.withAlpha((0.12 * 255).toInt()),
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with ticket number and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'تذكرة #${ticket.id}',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _StatusChip(status: ticket.status),
                ],
              ),
              const SizedBox(height: AppTheme.spacingS),

              // Subject
              Text(
                ticket.subject,
                style: theme.textTheme.titleSmall,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppTheme.spacingXS),

              // Category and Priority
              Row(
                children: [
                  _CategoryChip(category: ticket.category),
                  const SizedBox(width: AppTheme.spacingS),
                  _PriorityChip(priority: ticket.priority),
                ],
              ),
              const SizedBox(height: AppTheme.spacingS),

              // Last message and time
              if (ticket.lastMessageText != null) ...[
                Text(
                  ticket.lastMessageText!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: AppTheme.spacingXS),
              ],

              // Footer with time and unread indicator
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: AppTheme.spacingXS),
                  Text(
                    ticket.lastMessageAt != null
                        ? timeago.format(ticket.lastMessageAt!, locale: 'ar')
                        : timeago.format(ticket.createdAt!, locale: 'ar'),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const Spacer(),
                  if (ticket.hasUnreadMessages) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingS,
                        vertical: AppTheme.spacingXS,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${ticket.unreadCount}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _StatusChip extends StatelessWidget {
  const _StatusChip({required this.status});
  final TicketStatus status;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Color backgroundColor;
    Color textColor;

    switch (status) {
      case TicketStatus.pending:
        backgroundColor = Colors.orange.withAlpha((06 * 255).toInt());
        textColor = Colors.orange.shade700;
        break;
      case TicketStatus.open:
        backgroundColor = Colors.blue.withAlpha((06 * 255).toInt());
        textColor = Colors.blue.shade700;
        break;
      case TicketStatus.inProgress:
        backgroundColor = Colors.purple.withAlpha((06 * 255).toInt());
        textColor = Colors.purple.shade700;
        break;
      case TicketStatus.resolved:
        backgroundColor = Colors.green.withAlpha((06 * 255).toInt());
        textColor = Colors.green.shade700;
        break;
      case TicketStatus.closed:
        backgroundColor = Colors.grey.withAlpha((06 * 255).toInt());
        textColor = Colors.grey.shade700;
        break;
      case TicketStatus.urgent:
        backgroundColor = Colors.red.withAlpha((06 * 255).toInt());
        textColor = Colors.red.shade700;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingS,
        vertical: AppTheme.spacingXS,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusS),
      ),
      child: Text(
        _getStatusDisplayName(status),
        style: theme.textTheme.bodySmall?.copyWith(
          color: textColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  String _getStatusDisplayName(TicketStatus status) {
    switch (status) {
      case TicketStatus.pending:
        return 'في الانتظار';
      case TicketStatus.open:
        return 'مفتوح';
      case TicketStatus.inProgress:
        return 'قيد المعالجة';
      case TicketStatus.resolved:
        return 'تم حله';
      case TicketStatus.closed:
        return 'مغلق';
      case TicketStatus.urgent:
        return 'عاجل';
    }
  }
}

class _CategoryChip extends StatelessWidget {
  const _CategoryChip({required this.category});
  final TicketCategory category;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingS,
        vertical: AppTheme.spacingXS,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(AppTheme.radiusS),
      ),
      child: Text(
        _getCategoryDisplayName(category),
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }

  String _getCategoryDisplayName(TicketCategory category) {
    switch (category) {
      case TicketCategory.general:
        return 'عام';
      case TicketCategory.account:
        return 'الحساب';
      case TicketCategory.orders:
        return 'الطلبات';
      case TicketCategory.payments:
        return 'المدفوعات';
      case TicketCategory.billing:
        return 'الفواتير';
      case TicketCategory.technical:
        return 'تقني';
      case TicketCategory.seller:
        return 'البائع';
      case TicketCategory.returns:
        return 'المرتجعات';
      case TicketCategory.featureRequest:
        return 'طلب ميزة';
      case TicketCategory.bugReport:
        return 'تقرير خطأ';
    }
  }
}

class _PriorityChip extends StatelessWidget {
  const _PriorityChip({required this.priority});
  final TicketPriority priority;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Color backgroundColor;
    Color textColor;

    switch (priority) {
      case TicketPriority.low:
        backgroundColor = Colors.blue.withAlpha((06 * 255).toInt());
        textColor = Colors.blue.shade600;
        break;
      case TicketPriority.medium:
        backgroundColor = Colors.orange.withAlpha((06 * 255).toInt());
        textColor = Colors.orange.shade600;
        break;
      case TicketPriority.high:
        backgroundColor = Colors.red.withAlpha((06 * 255).toInt());
        textColor = Colors.red.shade600;
        break;
      case TicketPriority.urgent:
        backgroundColor = Colors.purple.withAlpha((06 * 255).toInt());
        textColor = Colors.purple.shade600;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingS,
        vertical: AppTheme.spacingXS,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusS),
      ),
      child: Text(
        _getPriorityDisplayName(priority),
        style: theme.textTheme.bodySmall?.copyWith(
          color: textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _getPriorityDisplayName(TicketPriority priority) {
    switch (priority) {
      case TicketPriority.low:
        return 'منخفض';
      case TicketPriority.medium:
        return 'متوسط';
      case TicketPriority.high:
        return 'عالي';
      case TicketPriority.urgent:
        return 'عاجل';
    }
  }
}

class _EmptyTicketsView extends StatelessWidget {
  const _EmptyTicketsView();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.confirmation_number_outlined,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              'لا توجد تذاكر دعم',
              style: theme.textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              'يمكنك إنشاء تذكرة جديدة للحصول على المساعدة.',
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
