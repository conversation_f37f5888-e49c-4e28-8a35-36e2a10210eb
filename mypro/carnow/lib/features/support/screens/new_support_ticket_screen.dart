/// شاشة إنشاء تذكرة دعم جديدة
///
/// تتيح للمستخدم فتح طلب دعم جديد عن طريق ملء نموذج.
/// يحتوي النموذج على حقول لإدخال عنوان المشكلة، وصف تفصيلي،
/// وتحديد فئة المشكلة لمساعدة فريق الدعم على توجيهها بشكل صحيح.
library;

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/theme/app_theme.dart';
import '../models/ticket_model.dart';
import '../providers/support_ticket_provider.dart';

class NewSupportTicketScreen extends HookConsumerWidget {
  const NewSupportTicketScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // Form controllers
    final subjectController = useTextEditingController();
    final descriptionController = useTextEditingController();

    // Form state
    final selectedCategory = useState<TicketCategory>(TicketCategory.general);
    final selectedPriority = useState<TicketPriority>(TicketPriority.medium);
    final isSubmitting = useState(false);

    // Form key for validation
    final formKey = useMemoized(GlobalKey<FormState>.new);

    return Scaffold(
      appBar: AppBar(title: const Text('تذكرة دعم جديدة'), elevation: 1),
      body: Form(
        key: formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppTheme.spacingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                'أخبرنا كيف يمكننا مساعدتك',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppTheme.spacingS),
              Text(
                'سنعمل على الرد عليك في أقرب وقت ممكن',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: AppTheme.spacingXL),

              // Subject field
              Text(
                'الموضوع *',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppTheme.spacingS),
              TextFormField(
                controller: subjectController,
                decoration: InputDecoration(
                  hintText: 'اكتب موضوع تذكرة الدعم',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  ),
                  filled: true,
                  fillColor: theme.colorScheme.surfaceContainerHighest,
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'الموضوع مطلوب';
                  }
                  if (value.trim().length < 3) {
                    return 'الموضوع يجب أن يكون 3 أحرف على الأقل';
                  }
                  return null;
                },
                textCapitalization: TextCapitalization.sentences,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: AppTheme.spacingL),

              // Category field
              Text(
                'الفئة *',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppTheme.spacingS),
              DropdownButtonFormField<TicketCategory>(
                value: selectedCategory.value,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  ),
                  filled: true,
                  fillColor: theme.colorScheme.surfaceContainerHighest,
                ),
                items: TicketCategory.values
                    .map(
                      (category) => DropdownMenuItem(
                        value: category,
                        child: Text(_getCategoryDisplayName(category)),
                      ),
                    )
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    selectedCategory.value = value;
                  }
                },
              ),
              const SizedBox(height: AppTheme.spacingL),

              // Priority field
              Text(
                'الأولوية',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppTheme.spacingS),
              DropdownButtonFormField<TicketPriority>(
                value: selectedPriority.value,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  ),
                  filled: true,
                  fillColor: theme.colorScheme.surfaceContainerHighest,
                ),
                items: TicketPriority.values
                    .map(
                      (priority) => DropdownMenuItem(
                        value: priority,
                        child: Text(_getPriorityDisplayName(priority)),
                      ),
                    )
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    selectedPriority.value = value;
                  }
                },
              ),
              const SizedBox(height: AppTheme.spacingL),

              // Description field
              Text(
                'الوصف *',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppTheme.spacingS),
              TextFormField(
                controller: descriptionController,
                decoration: InputDecoration(
                  hintText: 'اشرح مشكلتك أو استفسارك بالتفصيل...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  ),
                  filled: true,
                  fillColor: theme.colorScheme.surfaceContainerHighest,
                  alignLabelWithHint: true,
                ),
                maxLines: 6,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'الوصف مطلوب';
                  }
                  if (value.trim().length < 10) {
                    return 'الوصف يجب أن يكون 10 أحرف على الأقل';
                  }
                  return null;
                },
                textCapitalization: TextCapitalization.sentences,
                textInputAction: TextInputAction.newline,
              ),
              const SizedBox(height: AppTheme.spacingXL),

              // Submit button
              SizedBox(
                width: double.infinity,
                child: FilledButton(
                  onPressed: isSubmitting.value
                      ? null
                      : () => _submitTicket(
                          context,
                          ref,
                          formKey,
                          subjectController.text,
                          descriptionController.text,
                          selectedCategory.value,
                          selectedPriority.value,
                          isSubmitting,
                        ),
                  style: FilledButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(
                      vertical: AppTheme.spacingL,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppTheme.radiusM),
                    ),
                    elevation: 2,
                  ),
                  child: isSubmitting.value
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  theme.colorScheme.onPrimary,
                                ),
                              ),
                            ),
                            const SizedBox(width: AppTheme.spacingM),
                            const Text('جاري الإرسال...'),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.send),
                            const SizedBox(width: AppTheme.spacingS),
                            Text(
                              'إرسال التذكرة',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                ),
              ),
              const SizedBox(height: AppTheme.spacingM),
              Center(
                child: TextButton(
                  onPressed: () => context.pop(),
                  child: const Text('إلغاء'),
                ),
              ),

              // Help text
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingM),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: AppTheme.spacingS),
                    Expanded(
                      child: Text(
                        'ستتلقى رداً من فريق الدعم عبر التذكرة خلال 24 ساعة',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _submitTicket(
    BuildContext context,
    WidgetRef ref,
    GlobalKey<FormState> formKey,
    String subject,
    String description,
    TicketCategory category,
    TicketPriority priority,
    ValueNotifier<bool> isSubmitting,
  ) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    isSubmitting.value = true;

    try {
      final ticket = await ref
          .read(supportTicketActionsProvider.notifier)
          .createTicket(
            subject: subject.trim(),
            description: description.trim(),
            category: category.name,
            priority: priority.name,
          );

      if (context.mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إنشاء التذكرة بنجاح - رقم التذكرة: #${ticket.id}',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );

        // Navigate to the ticket details
        context.go('/support/tickets/${ticket.id}');
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إنشاء التذكرة: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      isSubmitting.value = false;
    }
  }

  String _getCategoryDisplayName(TicketCategory category) {
    switch (category) {
      case TicketCategory.general:
        return 'عام';
      case TicketCategory.account:
        return 'الحساب';
      case TicketCategory.orders:
        return 'الطلبات';
      case TicketCategory.payments:
        return 'المدفوعات';
      case TicketCategory.technical:
        return 'تقني';
      case TicketCategory.seller:
        return 'البائع';
      case TicketCategory.returns:
        return 'المرتجعات';
      case TicketCategory.billing:
        return 'الفواتير';
      case TicketCategory.featureRequest:
        return 'طلب ميزة';
      case TicketCategory.bugReport:
        return 'تقرير خطأ';
    }
  }

  String _getPriorityDisplayName(TicketPriority priority) {
    switch (priority) {
      case TicketPriority.low:
        return 'منخفض';
      case TicketPriority.medium:
        return 'متوسط';
      case TicketPriority.high:
        return 'عالي';
      case TicketPriority.urgent:
        return 'عاجل';
    }
  }
}
