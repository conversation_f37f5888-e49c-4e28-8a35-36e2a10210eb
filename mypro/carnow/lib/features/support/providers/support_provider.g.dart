// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'support_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$supportTicketsHash() => r'35587c9e2ffb54e6976d4a93c6c51124f8ddc634';

/// Support tickets provider - Clean implementation using Go backend
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [supportTickets].
@ProviderFor(supportTickets)
final supportTicketsProvider =
    AutoDisposeFutureProvider<List<SupportTicketModel>>.internal(
      supportTickets,
      name: r'supportTicketsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$supportTicketsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SupportTicketsRef =
    AutoDisposeFutureProviderRef<List<SupportTicketModel>>;
String _$supportTicketByIdHash() => r'edc0184c58bebad059cb6edb638214f12f13f436';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Support ticket by ID provider - Clean implementation using Go backend
///
/// Copied from [supportTicketById].
@ProviderFor(supportTicketById)
const supportTicketByIdProvider = SupportTicketByIdFamily();

/// Support ticket by ID provider - Clean implementation using Go backend
///
/// Copied from [supportTicketById].
class SupportTicketByIdFamily extends Family<AsyncValue<SupportTicketModel?>> {
  /// Support ticket by ID provider - Clean implementation using Go backend
  ///
  /// Copied from [supportTicketById].
  const SupportTicketByIdFamily();

  /// Support ticket by ID provider - Clean implementation using Go backend
  ///
  /// Copied from [supportTicketById].
  SupportTicketByIdProvider call(String ticketId) {
    return SupportTicketByIdProvider(ticketId);
  }

  @override
  SupportTicketByIdProvider getProviderOverride(
    covariant SupportTicketByIdProvider provider,
  ) {
    return call(provider.ticketId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'supportTicketByIdProvider';
}

/// Support ticket by ID provider - Clean implementation using Go backend
///
/// Copied from [supportTicketById].
class SupportTicketByIdProvider
    extends AutoDisposeFutureProvider<SupportTicketModel?> {
  /// Support ticket by ID provider - Clean implementation using Go backend
  ///
  /// Copied from [supportTicketById].
  SupportTicketByIdProvider(String ticketId)
    : this._internal(
        (ref) => supportTicketById(ref as SupportTicketByIdRef, ticketId),
        from: supportTicketByIdProvider,
        name: r'supportTicketByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$supportTicketByIdHash,
        dependencies: SupportTicketByIdFamily._dependencies,
        allTransitiveDependencies:
            SupportTicketByIdFamily._allTransitiveDependencies,
        ticketId: ticketId,
      );

  SupportTicketByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.ticketId,
  }) : super.internal();

  final String ticketId;

  @override
  Override overrideWith(
    FutureOr<SupportTicketModel?> Function(SupportTicketByIdRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SupportTicketByIdProvider._internal(
        (ref) => create(ref as SupportTicketByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        ticketId: ticketId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<SupportTicketModel?> createElement() {
    return _SupportTicketByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SupportTicketByIdProvider && other.ticketId == ticketId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, ticketId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SupportTicketByIdRef
    on AutoDisposeFutureProviderRef<SupportTicketModel?> {
  /// The parameter `ticketId` of this provider.
  String get ticketId;
}

class _SupportTicketByIdProviderElement
    extends AutoDisposeFutureProviderElement<SupportTicketModel?>
    with SupportTicketByIdRef {
  _SupportTicketByIdProviderElement(super.provider);

  @override
  String get ticketId => (origin as SupportTicketByIdProvider).ticketId;
}

String _$supportTicketMessagesHash() =>
    r'3ccec2be82c9e50d6b37142df2e8063ea7a9dd68';

/// Support ticket messages provider - Clean implementation using Go backend
///
/// Copied from [supportTicketMessages].
@ProviderFor(supportTicketMessages)
const supportTicketMessagesProvider = SupportTicketMessagesFamily();

/// Support ticket messages provider - Clean implementation using Go backend
///
/// Copied from [supportTicketMessages].
class SupportTicketMessagesFamily
    extends Family<AsyncValue<List<SupportTicketMessageModel>>> {
  /// Support ticket messages provider - Clean implementation using Go backend
  ///
  /// Copied from [supportTicketMessages].
  const SupportTicketMessagesFamily();

  /// Support ticket messages provider - Clean implementation using Go backend
  ///
  /// Copied from [supportTicketMessages].
  SupportTicketMessagesProvider call(String ticketId) {
    return SupportTicketMessagesProvider(ticketId);
  }

  @override
  SupportTicketMessagesProvider getProviderOverride(
    covariant SupportTicketMessagesProvider provider,
  ) {
    return call(provider.ticketId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'supportTicketMessagesProvider';
}

/// Support ticket messages provider - Clean implementation using Go backend
///
/// Copied from [supportTicketMessages].
class SupportTicketMessagesProvider
    extends AutoDisposeFutureProvider<List<SupportTicketMessageModel>> {
  /// Support ticket messages provider - Clean implementation using Go backend
  ///
  /// Copied from [supportTicketMessages].
  SupportTicketMessagesProvider(String ticketId)
    : this._internal(
        (ref) =>
            supportTicketMessages(ref as SupportTicketMessagesRef, ticketId),
        from: supportTicketMessagesProvider,
        name: r'supportTicketMessagesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$supportTicketMessagesHash,
        dependencies: SupportTicketMessagesFamily._dependencies,
        allTransitiveDependencies:
            SupportTicketMessagesFamily._allTransitiveDependencies,
        ticketId: ticketId,
      );

  SupportTicketMessagesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.ticketId,
  }) : super.internal();

  final String ticketId;

  @override
  Override overrideWith(
    FutureOr<List<SupportTicketMessageModel>> Function(
      SupportTicketMessagesRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SupportTicketMessagesProvider._internal(
        (ref) => create(ref as SupportTicketMessagesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        ticketId: ticketId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<SupportTicketMessageModel>>
  createElement() {
    return _SupportTicketMessagesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SupportTicketMessagesProvider && other.ticketId == ticketId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, ticketId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SupportTicketMessagesRef
    on AutoDisposeFutureProviderRef<List<SupportTicketMessageModel>> {
  /// The parameter `ticketId` of this provider.
  String get ticketId;
}

class _SupportTicketMessagesProviderElement
    extends AutoDisposeFutureProviderElement<List<SupportTicketMessageModel>>
    with SupportTicketMessagesRef {
  _SupportTicketMessagesProviderElement(super.provider);

  @override
  String get ticketId => (origin as SupportTicketMessagesProvider).ticketId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
