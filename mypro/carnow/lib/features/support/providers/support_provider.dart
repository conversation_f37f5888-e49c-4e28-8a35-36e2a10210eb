import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/unified_auth_provider.dart';
import '../models/support_ticket_model.dart';

part 'support_provider.g.dart';

/// Support tickets provider - Clean implementation using Go backend
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
@riverpod
Future<List<SupportTicketModel>> supportTickets(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    final currentUser = ref.watch(currentUserProvider);
    
    if (currentUser?.id == null) {
      throw Exception('المستخدم غير مسجل الدخول');
    }

    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/support/tickets',
    );

    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to fetch support tickets: ${response.message}');
    }

    final data = response.data!;
    final ticketsJson = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : throw Exception('Invalid support tickets response format');

    final tickets = ticketsJson.map((item) {
      final ticketData = item as Map<String, dynamic>;

      return SupportTicketModel(
        id: ticketData['id'].toString(),
        userId: ticketData['user_id'] as String,
        subject: ticketData['subject'] as String,
        description: ticketData['description'] as String,
        status: _parseTicketStatus(ticketData['status'] as String?),
        priority: _parseTicketPriority(ticketData['priority'] as String?),
        category: _parseTicketCategory(ticketData['category'] as String?),
        unreadCount: ticketData['unread_count'] as int? ?? 0,
        createdAt: DateTime.parse(ticketData['created_at'] as String),
        lastMessageAt: ticketData['last_message_at'] != null
            ? DateTime.parse(ticketData['last_message_at'] as String)
            : null,
        lastMessageText: ticketData['last_message_text'] as String?,
        resolvedAt: ticketData['resolved_at'] != null
            ? DateTime.parse(ticketData['resolved_at'] as String)
            : null,
      );
    }).toList();

    return tickets;
  } catch (e) {
    throw Exception('خطأ في تحميل تذاكر الدعم: $e');
  }
}

/// Support ticket by ID provider - Clean implementation using Go backend
@riverpod
Future<SupportTicketModel?> supportTicketById(
  Ref ref,
  String ticketId,
) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);

    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/support/tickets/$ticketId',
    );

    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to fetch support ticket: ${response.message}');
    }

    final ticketData = response.data!;

    return SupportTicketModel(
      id: ticketData['id'].toString(),
      userId: ticketData['user_id'] as String,
      subject: ticketData['subject'] as String,
      description: ticketData['description'] as String,
      status: _parseTicketStatus(ticketData['status'] as String?),
      priority: _parseTicketPriority(ticketData['priority'] as String?),
      category: _parseTicketCategory(ticketData['category'] as String?),
      unreadCount: ticketData['unread_count'] as int? ?? 0,
      createdAt: DateTime.parse(ticketData['created_at'] as String),
      lastMessageAt: ticketData['last_message_at'] != null
          ? DateTime.parse(ticketData['last_message_at'] as String)
          : null,
      lastMessageText: ticketData['last_message_text'] as String?,
      resolvedAt: ticketData['resolved_at'] != null
          ? DateTime.parse(ticketData['resolved_at'] as String)
          : null,
    );
  } catch (e) {
    throw Exception('خطأ في تحميل تذكرة الدعم: $e');
  }
}

/// Support ticket messages provider - Clean implementation using Go backend
@riverpod
Future<List<SupportTicketMessageModel>> supportTicketMessages(
  Ref ref,
  String ticketId,
) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);

    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/support/tickets/$ticketId/messages',
    );

    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to fetch ticket messages: ${response.message}');
    }

    final data = response.data!;
    final messagesJson = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : throw Exception('Invalid ticket messages response format');

    final messages = messagesJson.map((item) {
      final messageData = item as Map<String, dynamic>;

      return SupportTicketMessageModel(
        id: messageData['id'].toString(),
        ticketId: messageData['ticket_id'] as String,
        senderId: messageData['sender_id'] as String,
        message: messageData['message'] as String,
        senderType: messageData['sender_type'] as String,
        sentAt: DateTime.parse(messageData['sent_at'] as String),
        createdAt: DateTime.parse(messageData['created_at'] as String),
      );
    }).toList();

    return messages;
  } catch (e) {
    throw Exception('خطأ في تحميل رسائل التذكرة: $e');
  }
}

/// Helper functions to parse enums
TicketStatus _parseTicketStatus(String? status) {
  switch (status) {
    case 'open':
      return TicketStatus.open;
    case 'in_progress':
      return TicketStatus.inProgress;
    case 'closed':
      return TicketStatus.closed;
    case 'urgent':
      return TicketStatus.urgent;
    case 'pending':
      return TicketStatus.pending;
    case 'resolved':
      return TicketStatus.resolved;
    default:
      return TicketStatus.open;
  }
}

TicketPriority _parseTicketPriority(String? priority) {
  switch (priority) {
    case 'high':
      return TicketPriority.high;
    case 'medium':
      return TicketPriority.medium;
    case 'low':
      return TicketPriority.low;
    case 'urgent':
      return TicketPriority.urgent;
    default:
      return TicketPriority.medium;
  }
}

TicketCategory _parseTicketCategory(String? category) {
  switch (category) {
    case 'technical':
      return TicketCategory.technical;
    case 'billing':
      return TicketCategory.billing;
    case 'feature_request':
      return TicketCategory.featureRequest;
    case 'general':
      return TicketCategory.general;
    case 'bug_report':
      return TicketCategory.bugReport;
    case 'account':
      return TicketCategory.account;
    case 'orders':
      return TicketCategory.orders;
    case 'payments':
      return TicketCategory.payments;
    case 'seller':
      return TicketCategory.seller;
    case 'returns':
      return TicketCategory.returns;
    default:
      return TicketCategory.general;
  }
}
