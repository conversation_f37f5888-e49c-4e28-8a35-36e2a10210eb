import 'package:freezed_annotation/freezed_annotation.dart';

part 'faq_model.freezed.dart';
part 'faq_model.g.dart';

/// نموذج الأسئلة الشائعة (FAQ)
@freezed
abstract class FaqModel with _$FaqModel {
  const factory FaqModel({
    required String id,
    required String question,
    required String answer,
    @JsonKey(name: 'category') @Default('general') String category,
    @JsonKey(name: 'is_active') @Default(true) bool isActive,
    @JsonKey(name: 'order_index') @Default(0) int orderIndex,
    @JsonKey(name: 'created_at') required DateTime createdAt,
    @JsonKey(name: 'updated_at') DateTime? updatedAt,
  }) = _FaqModel;

  factory FaqModel.fromJson(Map<String, dynamic> json) =>
      _$FaqModelFromJson(json);
}

/// فئات الأسئلة الشائعة
enum FaqCategory {
  @JsonValue('general')
  general,
  @JsonValue('account')
  account,
  @JsonValue('orders')
  orders,
  @JsonValue('payments')
  payments,
  @JsonValue('technical')
  technical,
  @JsonValue('seller')
  seller,
  @JsonValue('returns')
  returns,
} 