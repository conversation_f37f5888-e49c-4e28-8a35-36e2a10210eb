import 'package:freezed_annotation/freezed_annotation.dart';

part 'support_ticket_model.freezed.dart';
part 'support_ticket_model.g.dart';

/// Support ticket status enumeration
enum TicketStatus {
  @JsonValue('open')
  open,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('closed')
  closed,
  @JsonValue('urgent')
  urgent,
  @JsonValue('pending')
  pending,
  @JsonValue('resolved')
  resolved,
}

/// Support ticket priority enumeration
enum TicketPriority {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('urgent')
  urgent,
}

/// Support ticket category enumeration
enum TicketCategory {
  @JsonValue('technical')
  technical,
  @JsonValue('billing')
  billing,
  @JsonValue('general')
  general,
  @JsonValue('feature_request')
  featureRequest,
  @JsonValue('bug_report')
  bugReport,
  @JsonValue('account')
  account,
  @JsonValue('orders')
  orders,
  @JsonValue('payments')
  payments,
  @JsonValue('seller')
  seller,
  @JsonValue('returns')
  returns,
}

/// Support ticket model
@freezed
abstract class SupportTicketModel with _$SupportTicketModel {
  const factory SupportTicketModel({
    required String id,
    required String userId,
    required String subject,
    required String description,
    required TicketStatus status,
    required TicketPriority priority,
    required TicketCategory category,
    String? assignedTo,
    DateTime? resolvedAt,
    DateTime? lastMessageAt,
    String? lastMessageText,
    @Default(0) int unreadCount,
    String? userType,
    DateTime? createdAt,
    DateTime? updatedAt,
    @Default(false) bool isDeleted,
    @Default(true) bool isActive,
  }) = _SupportTicketModel;

  factory SupportTicketModel.fromJson(Map<String, dynamic> json) =>
      _$SupportTicketModelFromJson(json);
}

/// Support ticket message model
@freezed
abstract class SupportTicketMessageModel with _$SupportTicketMessageModel {
  const factory SupportTicketMessageModel({
    required String id,
    required String ticketId,
    required String senderId,
    required String message,
    required String senderType,
    @Default(false) bool isInternal,
    DateTime? readAt,
    DateTime? sentAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    @Default(false) bool isDeleted,
  }) = _SupportTicketMessageModel;

  factory SupportTicketMessageModel.fromJson(Map<String, dynamic> json) =>
      _$SupportTicketMessageModelFromJson(json);
}

/// Extension methods for SupportTicketModel
extension SupportTicketModelExtension on SupportTicketModel {
  /// Check if ticket has unread messages
  bool get hasUnreadMessages => unreadCount > 0;
}

/// Extension methods for SupportTicketMessageModel
extension SupportTicketMessageModelExtension on SupportTicketMessageModel {
  /// Check if message is from user
  bool get isFromUser => senderType == 'user';

  /// Check if message has been read
  bool get isRead => readAt != null;

  /// Get sender information
  Map<String, dynamic>? get sender => {
        'id': senderId,
        'type': senderType,
        'name': senderType == 'user' ? 'أنت' : 'الدعم الفني',
      };
}
