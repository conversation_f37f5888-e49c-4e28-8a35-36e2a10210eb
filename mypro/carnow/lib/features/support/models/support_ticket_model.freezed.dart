// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'support_ticket_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SupportTicketModel {

 String get id; String get userId; String get subject; String get description; TicketStatus get status; TicketPriority get priority; TicketCategory get category; String? get assignedTo; DateTime? get resolvedAt; DateTime? get lastMessageAt; String? get lastMessageText; int get unreadCount; String? get userType; DateTime? get createdAt; DateTime? get updatedAt; bool get isDeleted; bool get isActive;
/// Create a copy of SupportTicketModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SupportTicketModelCopyWith<SupportTicketModel> get copyWith => _$SupportTicketModelCopyWithImpl<SupportTicketModel>(this as SupportTicketModel, _$identity);

  /// Serializes this SupportTicketModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SupportTicketModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.subject, subject) || other.subject == subject)&&(identical(other.description, description) || other.description == description)&&(identical(other.status, status) || other.status == status)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.category, category) || other.category == category)&&(identical(other.assignedTo, assignedTo) || other.assignedTo == assignedTo)&&(identical(other.resolvedAt, resolvedAt) || other.resolvedAt == resolvedAt)&&(identical(other.lastMessageAt, lastMessageAt) || other.lastMessageAt == lastMessageAt)&&(identical(other.lastMessageText, lastMessageText) || other.lastMessageText == lastMessageText)&&(identical(other.unreadCount, unreadCount) || other.unreadCount == unreadCount)&&(identical(other.userType, userType) || other.userType == userType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,subject,description,status,priority,category,assignedTo,resolvedAt,lastMessageAt,lastMessageText,unreadCount,userType,createdAt,updatedAt,isDeleted,isActive);

@override
String toString() {
  return 'SupportTicketModel(id: $id, userId: $userId, subject: $subject, description: $description, status: $status, priority: $priority, category: $category, assignedTo: $assignedTo, resolvedAt: $resolvedAt, lastMessageAt: $lastMessageAt, lastMessageText: $lastMessageText, unreadCount: $unreadCount, userType: $userType, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class $SupportTicketModelCopyWith<$Res>  {
  factory $SupportTicketModelCopyWith(SupportTicketModel value, $Res Function(SupportTicketModel) _then) = _$SupportTicketModelCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String subject, String description, TicketStatus status, TicketPriority priority, TicketCategory category, String? assignedTo, DateTime? resolvedAt, DateTime? lastMessageAt, String? lastMessageText, int unreadCount, String? userType, DateTime? createdAt, DateTime? updatedAt, bool isDeleted, bool isActive
});




}
/// @nodoc
class _$SupportTicketModelCopyWithImpl<$Res>
    implements $SupportTicketModelCopyWith<$Res> {
  _$SupportTicketModelCopyWithImpl(this._self, this._then);

  final SupportTicketModel _self;
  final $Res Function(SupportTicketModel) _then;

/// Create a copy of SupportTicketModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? subject = null,Object? description = null,Object? status = null,Object? priority = null,Object? category = null,Object? assignedTo = freezed,Object? resolvedAt = freezed,Object? lastMessageAt = freezed,Object? lastMessageText = freezed,Object? unreadCount = null,Object? userType = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? isActive = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,subject: null == subject ? _self.subject : subject // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as TicketStatus,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as TicketPriority,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as TicketCategory,assignedTo: freezed == assignedTo ? _self.assignedTo : assignedTo // ignore: cast_nullable_to_non_nullable
as String?,resolvedAt: freezed == resolvedAt ? _self.resolvedAt : resolvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,lastMessageAt: freezed == lastMessageAt ? _self.lastMessageAt : lastMessageAt // ignore: cast_nullable_to_non_nullable
as DateTime?,lastMessageText: freezed == lastMessageText ? _self.lastMessageText : lastMessageText // ignore: cast_nullable_to_non_nullable
as String?,unreadCount: null == unreadCount ? _self.unreadCount : unreadCount // ignore: cast_nullable_to_non_nullable
as int,userType: freezed == userType ? _self.userType : userType // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [SupportTicketModel].
extension SupportTicketModelPatterns on SupportTicketModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SupportTicketModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SupportTicketModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SupportTicketModel value)  $default,){
final _that = this;
switch (_that) {
case _SupportTicketModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SupportTicketModel value)?  $default,){
final _that = this;
switch (_that) {
case _SupportTicketModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String subject,  String description,  TicketStatus status,  TicketPriority priority,  TicketCategory category,  String? assignedTo,  DateTime? resolvedAt,  DateTime? lastMessageAt,  String? lastMessageText,  int unreadCount,  String? userType,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted,  bool isActive)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SupportTicketModel() when $default != null:
return $default(_that.id,_that.userId,_that.subject,_that.description,_that.status,_that.priority,_that.category,_that.assignedTo,_that.resolvedAt,_that.lastMessageAt,_that.lastMessageText,_that.unreadCount,_that.userType,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.isActive);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String subject,  String description,  TicketStatus status,  TicketPriority priority,  TicketCategory category,  String? assignedTo,  DateTime? resolvedAt,  DateTime? lastMessageAt,  String? lastMessageText,  int unreadCount,  String? userType,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted,  bool isActive)  $default,) {final _that = this;
switch (_that) {
case _SupportTicketModel():
return $default(_that.id,_that.userId,_that.subject,_that.description,_that.status,_that.priority,_that.category,_that.assignedTo,_that.resolvedAt,_that.lastMessageAt,_that.lastMessageText,_that.unreadCount,_that.userType,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.isActive);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String subject,  String description,  TicketStatus status,  TicketPriority priority,  TicketCategory category,  String? assignedTo,  DateTime? resolvedAt,  DateTime? lastMessageAt,  String? lastMessageText,  int unreadCount,  String? userType,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted,  bool isActive)?  $default,) {final _that = this;
switch (_that) {
case _SupportTicketModel() when $default != null:
return $default(_that.id,_that.userId,_that.subject,_that.description,_that.status,_that.priority,_that.category,_that.assignedTo,_that.resolvedAt,_that.lastMessageAt,_that.lastMessageText,_that.unreadCount,_that.userType,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.isActive);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SupportTicketModel implements SupportTicketModel {
  const _SupportTicketModel({required this.id, required this.userId, required this.subject, required this.description, required this.status, required this.priority, required this.category, this.assignedTo, this.resolvedAt, this.lastMessageAt, this.lastMessageText, this.unreadCount = 0, this.userType, this.createdAt, this.updatedAt, this.isDeleted = false, this.isActive = true});
  factory _SupportTicketModel.fromJson(Map<String, dynamic> json) => _$SupportTicketModelFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String subject;
@override final  String description;
@override final  TicketStatus status;
@override final  TicketPriority priority;
@override final  TicketCategory category;
@override final  String? assignedTo;
@override final  DateTime? resolvedAt;
@override final  DateTime? lastMessageAt;
@override final  String? lastMessageText;
@override@JsonKey() final  int unreadCount;
@override final  String? userType;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
@override@JsonKey() final  bool isDeleted;
@override@JsonKey() final  bool isActive;

/// Create a copy of SupportTicketModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SupportTicketModelCopyWith<_SupportTicketModel> get copyWith => __$SupportTicketModelCopyWithImpl<_SupportTicketModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SupportTicketModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SupportTicketModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.subject, subject) || other.subject == subject)&&(identical(other.description, description) || other.description == description)&&(identical(other.status, status) || other.status == status)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.category, category) || other.category == category)&&(identical(other.assignedTo, assignedTo) || other.assignedTo == assignedTo)&&(identical(other.resolvedAt, resolvedAt) || other.resolvedAt == resolvedAt)&&(identical(other.lastMessageAt, lastMessageAt) || other.lastMessageAt == lastMessageAt)&&(identical(other.lastMessageText, lastMessageText) || other.lastMessageText == lastMessageText)&&(identical(other.unreadCount, unreadCount) || other.unreadCount == unreadCount)&&(identical(other.userType, userType) || other.userType == userType)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,subject,description,status,priority,category,assignedTo,resolvedAt,lastMessageAt,lastMessageText,unreadCount,userType,createdAt,updatedAt,isDeleted,isActive);

@override
String toString() {
  return 'SupportTicketModel(id: $id, userId: $userId, subject: $subject, description: $description, status: $status, priority: $priority, category: $category, assignedTo: $assignedTo, resolvedAt: $resolvedAt, lastMessageAt: $lastMessageAt, lastMessageText: $lastMessageText, unreadCount: $unreadCount, userType: $userType, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class _$SupportTicketModelCopyWith<$Res> implements $SupportTicketModelCopyWith<$Res> {
  factory _$SupportTicketModelCopyWith(_SupportTicketModel value, $Res Function(_SupportTicketModel) _then) = __$SupportTicketModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String subject, String description, TicketStatus status, TicketPriority priority, TicketCategory category, String? assignedTo, DateTime? resolvedAt, DateTime? lastMessageAt, String? lastMessageText, int unreadCount, String? userType, DateTime? createdAt, DateTime? updatedAt, bool isDeleted, bool isActive
});




}
/// @nodoc
class __$SupportTicketModelCopyWithImpl<$Res>
    implements _$SupportTicketModelCopyWith<$Res> {
  __$SupportTicketModelCopyWithImpl(this._self, this._then);

  final _SupportTicketModel _self;
  final $Res Function(_SupportTicketModel) _then;

/// Create a copy of SupportTicketModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? subject = null,Object? description = null,Object? status = null,Object? priority = null,Object? category = null,Object? assignedTo = freezed,Object? resolvedAt = freezed,Object? lastMessageAt = freezed,Object? lastMessageText = freezed,Object? unreadCount = null,Object? userType = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? isActive = null,}) {
  return _then(_SupportTicketModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,subject: null == subject ? _self.subject : subject // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as TicketStatus,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as TicketPriority,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as TicketCategory,assignedTo: freezed == assignedTo ? _self.assignedTo : assignedTo // ignore: cast_nullable_to_non_nullable
as String?,resolvedAt: freezed == resolvedAt ? _self.resolvedAt : resolvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,lastMessageAt: freezed == lastMessageAt ? _self.lastMessageAt : lastMessageAt // ignore: cast_nullable_to_non_nullable
as DateTime?,lastMessageText: freezed == lastMessageText ? _self.lastMessageText : lastMessageText // ignore: cast_nullable_to_non_nullable
as String?,unreadCount: null == unreadCount ? _self.unreadCount : unreadCount // ignore: cast_nullable_to_non_nullable
as int,userType: freezed == userType ? _self.userType : userType // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$SupportTicketMessageModel {

 String get id; String get ticketId; String get senderId; String get message; String get senderType; bool get isInternal; DateTime? get readAt; DateTime? get sentAt; DateTime? get createdAt; DateTime? get updatedAt; bool get isDeleted;
/// Create a copy of SupportTicketMessageModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SupportTicketMessageModelCopyWith<SupportTicketMessageModel> get copyWith => _$SupportTicketMessageModelCopyWithImpl<SupportTicketMessageModel>(this as SupportTicketMessageModel, _$identity);

  /// Serializes this SupportTicketMessageModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SupportTicketMessageModel&&(identical(other.id, id) || other.id == id)&&(identical(other.ticketId, ticketId) || other.ticketId == ticketId)&&(identical(other.senderId, senderId) || other.senderId == senderId)&&(identical(other.message, message) || other.message == message)&&(identical(other.senderType, senderType) || other.senderType == senderType)&&(identical(other.isInternal, isInternal) || other.isInternal == isInternal)&&(identical(other.readAt, readAt) || other.readAt == readAt)&&(identical(other.sentAt, sentAt) || other.sentAt == sentAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,ticketId,senderId,message,senderType,isInternal,readAt,sentAt,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'SupportTicketMessageModel(id: $id, ticketId: $ticketId, senderId: $senderId, message: $message, senderType: $senderType, isInternal: $isInternal, readAt: $readAt, sentAt: $sentAt, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class $SupportTicketMessageModelCopyWith<$Res>  {
  factory $SupportTicketMessageModelCopyWith(SupportTicketMessageModel value, $Res Function(SupportTicketMessageModel) _then) = _$SupportTicketMessageModelCopyWithImpl;
@useResult
$Res call({
 String id, String ticketId, String senderId, String message, String senderType, bool isInternal, DateTime? readAt, DateTime? sentAt, DateTime? createdAt, DateTime? updatedAt, bool isDeleted
});




}
/// @nodoc
class _$SupportTicketMessageModelCopyWithImpl<$Res>
    implements $SupportTicketMessageModelCopyWith<$Res> {
  _$SupportTicketMessageModelCopyWithImpl(this._self, this._then);

  final SupportTicketMessageModel _self;
  final $Res Function(SupportTicketMessageModel) _then;

/// Create a copy of SupportTicketMessageModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? ticketId = null,Object? senderId = null,Object? message = null,Object? senderType = null,Object? isInternal = null,Object? readAt = freezed,Object? sentAt = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,ticketId: null == ticketId ? _self.ticketId : ticketId // ignore: cast_nullable_to_non_nullable
as String,senderId: null == senderId ? _self.senderId : senderId // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,senderType: null == senderType ? _self.senderType : senderType // ignore: cast_nullable_to_non_nullable
as String,isInternal: null == isInternal ? _self.isInternal : isInternal // ignore: cast_nullable_to_non_nullable
as bool,readAt: freezed == readAt ? _self.readAt : readAt // ignore: cast_nullable_to_non_nullable
as DateTime?,sentAt: freezed == sentAt ? _self.sentAt : sentAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [SupportTicketMessageModel].
extension SupportTicketMessageModelPatterns on SupportTicketMessageModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SupportTicketMessageModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SupportTicketMessageModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SupportTicketMessageModel value)  $default,){
final _that = this;
switch (_that) {
case _SupportTicketMessageModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SupportTicketMessageModel value)?  $default,){
final _that = this;
switch (_that) {
case _SupportTicketMessageModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String ticketId,  String senderId,  String message,  String senderType,  bool isInternal,  DateTime? readAt,  DateTime? sentAt,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SupportTicketMessageModel() when $default != null:
return $default(_that.id,_that.ticketId,_that.senderId,_that.message,_that.senderType,_that.isInternal,_that.readAt,_that.sentAt,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String ticketId,  String senderId,  String message,  String senderType,  bool isInternal,  DateTime? readAt,  DateTime? sentAt,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted)  $default,) {final _that = this;
switch (_that) {
case _SupportTicketMessageModel():
return $default(_that.id,_that.ticketId,_that.senderId,_that.message,_that.senderType,_that.isInternal,_that.readAt,_that.sentAt,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String ticketId,  String senderId,  String message,  String senderType,  bool isInternal,  DateTime? readAt,  DateTime? sentAt,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted)?  $default,) {final _that = this;
switch (_that) {
case _SupportTicketMessageModel() when $default != null:
return $default(_that.id,_that.ticketId,_that.senderId,_that.message,_that.senderType,_that.isInternal,_that.readAt,_that.sentAt,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SupportTicketMessageModel implements SupportTicketMessageModel {
  const _SupportTicketMessageModel({required this.id, required this.ticketId, required this.senderId, required this.message, required this.senderType, this.isInternal = false, this.readAt, this.sentAt, this.createdAt, this.updatedAt, this.isDeleted = false});
  factory _SupportTicketMessageModel.fromJson(Map<String, dynamic> json) => _$SupportTicketMessageModelFromJson(json);

@override final  String id;
@override final  String ticketId;
@override final  String senderId;
@override final  String message;
@override final  String senderType;
@override@JsonKey() final  bool isInternal;
@override final  DateTime? readAt;
@override final  DateTime? sentAt;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
@override@JsonKey() final  bool isDeleted;

/// Create a copy of SupportTicketMessageModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SupportTicketMessageModelCopyWith<_SupportTicketMessageModel> get copyWith => __$SupportTicketMessageModelCopyWithImpl<_SupportTicketMessageModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SupportTicketMessageModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SupportTicketMessageModel&&(identical(other.id, id) || other.id == id)&&(identical(other.ticketId, ticketId) || other.ticketId == ticketId)&&(identical(other.senderId, senderId) || other.senderId == senderId)&&(identical(other.message, message) || other.message == message)&&(identical(other.senderType, senderType) || other.senderType == senderType)&&(identical(other.isInternal, isInternal) || other.isInternal == isInternal)&&(identical(other.readAt, readAt) || other.readAt == readAt)&&(identical(other.sentAt, sentAt) || other.sentAt == sentAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,ticketId,senderId,message,senderType,isInternal,readAt,sentAt,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'SupportTicketMessageModel(id: $id, ticketId: $ticketId, senderId: $senderId, message: $message, senderType: $senderType, isInternal: $isInternal, readAt: $readAt, sentAt: $sentAt, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class _$SupportTicketMessageModelCopyWith<$Res> implements $SupportTicketMessageModelCopyWith<$Res> {
  factory _$SupportTicketMessageModelCopyWith(_SupportTicketMessageModel value, $Res Function(_SupportTicketMessageModel) _then) = __$SupportTicketMessageModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String ticketId, String senderId, String message, String senderType, bool isInternal, DateTime? readAt, DateTime? sentAt, DateTime? createdAt, DateTime? updatedAt, bool isDeleted
});




}
/// @nodoc
class __$SupportTicketMessageModelCopyWithImpl<$Res>
    implements _$SupportTicketMessageModelCopyWith<$Res> {
  __$SupportTicketMessageModelCopyWithImpl(this._self, this._then);

  final _SupportTicketMessageModel _self;
  final $Res Function(_SupportTicketMessageModel) _then;

/// Create a copy of SupportTicketMessageModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? ticketId = null,Object? senderId = null,Object? message = null,Object? senderType = null,Object? isInternal = null,Object? readAt = freezed,Object? sentAt = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_SupportTicketMessageModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,ticketId: null == ticketId ? _self.ticketId : ticketId // ignore: cast_nullable_to_non_nullable
as String,senderId: null == senderId ? _self.senderId : senderId // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,senderType: null == senderType ? _self.senderType : senderType // ignore: cast_nullable_to_non_nullable
as String,isInternal: null == isInternal ? _self.isInternal : isInternal // ignore: cast_nullable_to_non_nullable
as bool,readAt: freezed == readAt ? _self.readAt : readAt // ignore: cast_nullable_to_non_nullable
as DateTime?,sentAt: freezed == sentAt ? _self.sentAt : sentAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
