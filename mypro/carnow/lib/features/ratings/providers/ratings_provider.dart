import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/unified_auth_provider.dart';
import '../models/rating_model.dart';

part 'ratings_provider.g.dart';

@riverpod
class RatingsNotifier extends _$RatingsNotifier {
  @override
  Future<List<RatingModel>> build(int productId) async =>
      _fetchRatings(productId);

  Future<List<RatingModel>> _fetchRatings(int productId) async {
    final client = ref.read(simpleApiClientProvider);

    try {
      final response = await client.getApi(
        '/ratings',
        queryParameters: {'product_id': productId},
      );

      if (response.isSuccess && response.data != null) {
        return (response.data as List)
            .map((json) => RatingModel.fromJson(json))
            .toList();
      }
      
      return [];
    } catch (e) {
      throw Exception('فشل في جلب التقييمات: $e');
    }
  }

  Future<void> addRating({
    required int productId,
    required int rating,
    String? comment,
  }) async {
    final client = ref.read(simpleApiClientProvider);
    final isAuthenticated = ref.read(isAuthenticatedProvider);

    if (!isAuthenticated) {
      throw Exception('يجب تسجيل الدخول لإضافة تقييم');
    }

    if (rating < 1 || rating > 5) {
      throw Exception('التقييم يجب أن يكون بين 1 و 5');
    }

    try {
      await client.postApi(
        '/ratings',
        data: {'product_id': productId, 'rating': rating, 'comment': comment},
      );

      ref.invalidateSelf();
    } catch (e) {
      throw Exception('فشل في إضافة التقييم: $e');
    }
  }

  Future<void> updateRating({
    required int ratingId,
    required int rating,
    String? comment,
  }) async {
    final client = ref.read(simpleApiClientProvider);
    final isAuthenticated = ref.read(isAuthenticatedProvider);

    if (!isAuthenticated) {
      throw Exception('يجب تسجيل الدخول لتحديث التقييم');
    }

    if (rating < 1 || rating > 5) {
      throw Exception('التقييم يجب أن يكون بين 1 و 5');
    }

    try {
      await client.putApi(
        '/ratings/$ratingId',
        data: {'rating': rating, 'comment': comment},
      );

      ref.invalidateSelf();
    } catch (e) {
      throw Exception('فشل في تحديث التقييم: $e');
    }
  }

  Future<void> deleteRating(int ratingId) async {
    final client = ref.read(simpleApiClientProvider);
    final isAuthenticated = ref.read(isAuthenticatedProvider);

    if (!isAuthenticated) {
      throw Exception('يجب تسجيل الدخول لحذف التقييم');
    }

    try {
      await client.deleteApi('/ratings/$ratingId');
      ref.invalidateSelf();
    } catch (e) {
      throw Exception('فشل في حذف التقييم: $e');
    }
  }
}

// مزود لحساب متوسط التقييم لمنتج معين
@riverpod
Future<Map<String, dynamic>> productRatingStats(Ref ref, int productId) async {
  final client = ref.read(simpleApiClientProvider);

  try {
    final response = await client.getApi<Map<String, dynamic>>(
      '/ratings/stats',
      queryParameters: {'product_id': productId},
    );

    return response.data ??
        {
          'average': 0,
          'count': 0,
          'distribution': <int, int>{1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
        };
  } catch (e) {
    // Return default stats if API call fails
    return {
      'average': 0,
      'count': 0,
      'distribution': <int, int>{1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
    };
  }
}

// مزود للحصول على تقييم المستخدم الحالي لمنتج معين
@riverpod
Future<RatingModel?> userRating(Ref ref, int productId) async {
  final client = ref.read(simpleApiClientProvider);
  final isAuthenticated = ref.read(isAuthenticatedProvider);

  if (!isAuthenticated) {
    return null;
  }

  try {
    final response = await client.getApi(
      '/ratings/user',
      queryParameters: {'product_id': productId},
    );

    if (response.isSuccess && response.data != null) {
      return RatingModel.fromJson(response.data);
    }
    
    return null;
  } catch (e) {
    // Return null if no rating found or API call fails
    return null;
  }
}

// مزود للحصول على التقييمات مع الصفحات
@riverpod
Future<List<RatingModel>> paginatedRatings(
  Ref ref,
  int productId, {
  int page = 0,
  int limit = 10,
}) async {
  final client = ref.read(simpleApiClientProvider);

  try {
    final response = await client.getApi(
      '/ratings/paginated',
      queryParameters: {'product_id': productId, 'page': page, 'limit': limit},
    );

    if (response.isSuccess && response.data != null) {
      return (response.data as List)
          .map((json) => RatingModel.fromJson(json))
          .toList();
    }
    
    return [];
  } catch (e) {
    throw Exception('فشل في جلب التقييمات: $e');
  }
}
