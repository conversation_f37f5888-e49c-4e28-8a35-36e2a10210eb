import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/networking/simple_api_client.dart';
import '../models/inventory_models.dart';

part 'inventory_provider.g.dart';

@riverpod
Future<InventorySummary> inventorySummary(InventorySummaryRef ref) async {
  final apiClient = ref.read(simpleApiClientProvider);
  
  try {
    final response = await apiClient.getApi('/api/v1/inventory/summary');
    if (response.isSuccess && response.data != null) {
      return InventorySummary.fromJson(response.data!);
    } else {
      throw Exception(response.message ?? 'Failed to fetch inventory summary');
    }
  } catch (e) {
    throw Exception('Failed to fetch inventory summary: $e');
  }
}

@riverpod
class InventoryNotifier extends _$InventoryNotifier {
  @override
  Future<List<InventoryItemModel>> build() async {
    return _fetchInventoryItems();
  }

  Future<List<InventoryItemModel>> _fetchInventoryItems() async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    try {
      final response = await apiClient.getApi('/api/v1/inventory/items');
      if (response.isSuccess && response.data != null) {
        final List<dynamic> itemsJson = response.data!['items'] ?? [];
        return itemsJson
            .map((json) => InventoryItemModel.fromJson(json))
            .toList();
      } else {
        throw Exception(response.message ?? 'Failed to fetch inventory items');
      }
    } catch (e) {
      throw Exception('Failed to fetch inventory items: $e');
    }
  }

  Future<void> addItem(InventoryItemModel item) async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    try {
      final response = await apiClient.postApi('/api/v1/inventory/items', data: item.toJson());
      if (response.isSuccess) {
        ref.invalidateSelf();
      } else {
        throw Exception(response.message ?? 'Failed to add inventory item');
      }
    } catch (e) {
      throw Exception('Failed to add inventory item: $e');
    }
  }

  Future<void> updateItem(InventoryItemModel item) async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    try {
      final response = await apiClient.putApi('/api/v1/inventory/items/${item.id}', data: item.toJson());
      if (response.isSuccess) {
        ref.invalidateSelf();
      } else {
        throw Exception(response.message ?? 'Failed to update inventory item');
      }
    } catch (e) {
      throw Exception('Failed to update inventory item: $e');
    }
  }

  Future<void> deleteItem(String itemId) async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    try {
      final response = await apiClient.deleteApi('/api/v1/inventory/items/$itemId');
      if (response.isSuccess) {
        ref.invalidateSelf();
      } else {
        throw Exception(response.message ?? 'Failed to delete inventory item');
      }
    } catch (e) {
      throw Exception('Failed to delete inventory item: $e');
    }
  }

  Future<void> updateStock(String itemId, int newStock, String reason) async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    try {
      await apiClient.putApi('/api/v1/inventory/items/$itemId/stock', data: {
        'new_stock': newStock,
        'reason': reason,
      });
      ref.invalidateSelf();
    } catch (e) {
      throw Exception('Failed to update stock: $e');
    }
  }
}