// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'inventory_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$inventorySummaryHash() => r'db044d2805d3c1f57ea0c05cca247448c6d45517';

/// See also [inventorySummary].
@ProviderFor(inventorySummary)
final inventorySummaryProvider =
    AutoDisposeFutureProvider<InventorySummary>.internal(
      inventorySummary,
      name: r'inventorySummaryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$inventorySummaryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef InventorySummaryRef = AutoDisposeFutureProviderRef<InventorySummary>;
String _$inventoryNotifierHash() => r'4ff36f1f2460e9f4649cd754773b100bd36629d1';

/// See also [InventoryNotifier].
@ProviderFor(InventoryNotifier)
final inventoryNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      InventoryNotifier,
      List<InventoryItemModel>
    >.internal(
      InventoryNotifier.new,
      name: r'inventoryNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$inventoryNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$InventoryNotifier =
    AutoDisposeAsyncNotifier<List<InventoryItemModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
