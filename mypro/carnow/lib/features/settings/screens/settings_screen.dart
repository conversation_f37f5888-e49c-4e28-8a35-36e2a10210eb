/// شاشة الإعدادات الرئيسية
///
/// تعمل هذه الشاشة كمركز تحكم للمستخدم.
/// توفر وصولاً إلى الأقسام المختلفة مثل "إدارة الحساب"، "الإعدادات المتقدمة"،
/// "الدعم والمساعدة"، و"المعلومات القانونية". كما تتيح للمستخدم تسجيل الخروج
/// من حسابه وتتضمن رابطًا لأدوات المطور في وضع التصحيح.
library;

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/widgets/unified_app_bar.dart';
import '../../../core/widgets/unified_card.dart';
import '../../../core/utils/unified_theme_extension.dart';
import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/providers/admin_provider.dart';
import '../../../core/providers/theme_provider.dart';
import '../../account/providers/auth_providers.dart' as account_auth;
import '../../account/models/user_model.dart';
import '../../admin_tools/users/widgets/admin_settings_widget.dart';

class SettingsScreen extends HookConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(account_auth.currentUserProvider);
    final isAuthenticated = ref.watch(account_auth.isAuthenticatedProvider);
    final themeMode = ref.watch(appThemeModeProvider);
    final isAdmin = ref.watch(isAdminProvider);

    return Scaffold(
      backgroundColor: context.colorScheme.surface,
      appBar: const SettingsUnifiedAppBar(title: 'الإعدادات'),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(context.paddingLarge),
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // User Profile Section (if authenticated)
              if (isAuthenticated && currentUser != null) ...[
                _buildUserProfileSection(context, currentUser),
              ],

              // App Settings Section
              _buildSectionHeader(context, 'إعدادات التطبيق'),
              _buildSettingsGroup(context, [
                _buildSettingItem(
                  context,
                  'اللغة',
                  'العربية',
                  Icons.language_outlined,
                  () {
                    // TODO: Implement language settings
                    context.showInfoSnackBar('ميزة تغيير اللغة قريباً');
                  },
                ),
                _buildSettingItem(
                  context,
                  'المظهر',
                  themeMode.when(
                    data: (mode) => _themeModeToString(context, mode),
                    loading: () => 'جار التحميل...',
                    error: (e, s) => 'خطأ',
                  ),
                  Icons.palette_outlined,
                  () => _showThemeSelectionDialog(context, ref),
                ),
                _buildSettingItem(
                  context,
                  'الإشعارات',
                  'مفعلة',
                  Icons.notifications_outlined,
                  () {
                    // TODO: Implement notification settings
                    context.showInfoSnackBar('إعدادات الإشعارات قريباً');
                  },
                ),
              ]),

              SizedBox(height: context.paddingLarge),

              // Account Settings Section (if authenticated)
              if (isAuthenticated) ...[
                _buildSectionHeader(context, 'إعدادات الحساب'),
                _buildSettingsGroup(context, [
                  _buildSettingItem(
                    context,
                    'تعديل الملف الشخصي',
                    'تحديث معلوماتك الشخصية',
                    Icons.person_outline,
                    () => context.push('/account/profile/edit'),
                  ),
                  _buildSettingItem(
                    context,
                    'تغيير كلمة المرور',
                    'تحديث كلمة المرور',
                    Icons.lock_outline,
                    () => context.push('/change-password'),
                  ),
                  _buildSettingItem(
                    context,
                    'الخصوصية والأمان',
                    'إدارة إعدادات الخصوصية',
                    Icons.security_outlined,
                    () {
                      // TODO: Implement privacy settings
                      context.showInfoSnackBar('إعدادات الخصوصية قريباً');
                    },
                  ),
                ]),
                SizedBox(height: context.paddingLarge),
              ],

              // Support & Help Section
              _buildSectionHeader(context, 'المساعدة والدعم'),
              _buildSettingsGroup(context, [
                _buildSettingItem(
                  context,
                  'مركز المساعدة',
                  'الأسئلة الشائعة والدعم',
                  Icons.help_outline,
                  () => context.push('/support'),
                ),
                _buildSettingItem(
                  context,
                  'تواصل معنا',
                  'راسلنا للحصول على المساعدة',
                  Icons.contact_support_outlined,
                  () {
                    // TODO: Implement contact us
                    context.showInfoSnackBar('صفحة التواصل قريباً');
                  },
                ),
                _buildSettingItem(
                  context,
                  'تقييم التطبيق',
                  'ساعدنا في تحسين التطبيق',
                  Icons.star_outline,
                  () {
                    // TODO: Implement app rating
                    context.showSuccessSnackBar(
                      'شكراً لك! ميزة التقييم قريباً',
                    );
                  },
                ),
              ]),

              SizedBox(height: context.paddingLarge),

              // Legal & About Section
              _buildSectionHeader(context, 'معلومات قانونية'),
              _buildSettingsGroup(context, [
                _buildSettingItem(
                  context,
                  'حول التطبيق',
                  'معلومات عن CarNow',
                  Icons.info_outline,
                  () => context.push('/about'),
                ),
                _buildSettingItem(
                  context,
                  'الشروط والأحكام',
                  'اطلع على شروط الاستخدام',
                  Icons.description_outlined,
                  () => context.push('/terms-conditions'),
                ),
                _buildSettingItem(
                  context,
                  'سياسة الخصوصية',
                  'كيف نحمي بياناتك',
                  Icons.privacy_tip_outlined,
                  () => context.push('/privacy-policy'),
                ),
              ]),

              SizedBox(height: context.paddingLarge),

              // Admin Tools Section (only for admins)
              if (isAdmin) ...[
                // Admin Quick Stats Widget
                const AdminQuickStatsWidget(),

                SizedBox(height: context.paddingMedium),

                // Admin Settings Widget (contains all admin tools)
                const AdminSettingsWidget(),

                SizedBox(height: context.paddingLarge),

                // Developer Tools Section (only advanced tools)
                _buildSectionHeader(context, 'أدوات المطور المتقدمة'),
                _buildSettingsGroup(context, [
                  _buildSettingItem(
                    context,
                    'أدوات مطور متقدمة',
                    'أدوات خاصة بالمطورين والتطوير',
                    Icons.developer_board,
                    () => context.push('/developer-tools'),
                  ),
                  _buildSettingItem(
                    context,
                    'سجل النظام',
                    'عرض سجل النظام والتشخيص',
                    Icons.bug_report,
                    () => context.push('/developer/system-logs'),
                  ),
                  _buildSettingItem(
                    context,
                    'أدوات الاختبار',
                    'أدوات اختبار المطورين',
                    Icons.science,
                    () => context.push('/developer/testing-tools'),
                  ),
                ]),
              ],

              SizedBox(height: context.paddingLarge),

              // Account Actions Section (if authenticated)
              if (isAuthenticated) ...[
                _buildSectionHeader(context, 'إجراءات الحساب'),
                _buildDangerousAction(
                  context,
                  'تسجيل الخروج',
                  'الخروج من حسابك',
                  Icons.logout_outlined,
                  () => _showLogoutDialog(context, ref),
                ),
                SizedBox(height: context.paddingSmall),
                _buildDangerousAction(
                  context,
                  'حذف الحساب',
                  'حذف حسابك نهائياً',
                  Icons.delete_outline,
                  () => _showDeleteAccountDialog(context),
                  isDestructive: true,
                ),
              ],

              SizedBox(height: context.paddingXLarge),

              // إضافة مساحة إضافية في الأسفل لتجنب مشاكل الـ overflow
              SizedBox(height: context.paddingLarge),
            ],
          ),
        ),
      ),
    );
  }

  String _themeModeToString(BuildContext context, ThemeMode themeMode) {
    // Assuming you have localization set up
    // final l10n = AppLocalizations.of(context)!;
    switch (themeMode) {
      case ThemeMode.light:
        return 'فاتح'; // l10n.lightTheme
      case ThemeMode.dark:
        return 'داكن'; // l10n.darkTheme
      case ThemeMode.system:
        return 'تلقائي'; // l10n.systemTheme
    }
  }

  void _showThemeSelectionDialog(BuildContext context, WidgetRef ref) {
    final currentThemeMode = ref.read(appThemeModeProvider).value;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('اختر المظهر'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: ThemeMode.values.map((themeMode) {
              return RadioListTile<ThemeMode>(
                title: Text(_themeModeToString(context, themeMode)),
                value: themeMode,
                groupValue: currentThemeMode,
                onChanged: (newThemeMode) {
                  if (newThemeMode != null) {
                    ref
                        .read(appThemeModeProvider.notifier)
                        .setThemeMode(newThemeMode);
                    Navigator.of(context).pop();
                  }
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildUserProfileSection(BuildContext context, UserModel user) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: context.paddingLarge),
      child: UnifiedCard(
        backgroundColor: context.colorScheme.primaryContainer,
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: context.colorScheme.primary,
              child: Text(
                user.name?.substring(0, 1).toUpperCase() ??
                    (user.email?.substring(0, 1).toUpperCase() ?? 'U'),
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: context.colorScheme.onPrimary,
                ),
              ),
            ),
            SizedBox(width: context.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.name ?? 'عضو جديد',
                    style: context.titleLarge?.copyWith(
                      color: context.colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user.email ?? '',
                    style: context.bodyMedium?.copyWith(
                      color: context.colorScheme.onPrimaryContainer.withAlpha(
                        (0.8 * 255).toInt(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () => context.push('/account/profile/edit'),
              icon: Icon(
                Icons.edit_outlined,
                color: context.colorScheme.onPrimaryContainer,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: context.paddingMedium),
      child: Text(
        title,
        style: context.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: context.colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildSettingsGroup(BuildContext context, List<Widget> items) {
    return UnifiedCard(
      child: Column(
        children: items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;

          return Column(
            children: [
              item,
              if (index < items.length - 1)
                Divider(
                  height: 1,
                  color: context.colorScheme.outline.withAlpha(
                    (0.1 * 255).toInt(),
                  ),
                ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSettingItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return SettingsCard(
      title: title,
      subtitle: subtitle,
      icon: icon,
      onTap: onTap,
    );
  }

  Widget _buildDangerousAction(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    final actionColor = isDestructive ? Colors.red : context.colorScheme.error;

    return UnifiedCard(
      borderColor: actionColor.withAlpha((0.2 * 255).toInt()),
      child: SettingsCard(
        title: title,
        subtitle: subtitle,
        icon: icon,
        onTap: onTap,
        isDestructive: isDestructive,
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(unifiedAuthProviderProvider.notifier).signOut();
                // Navigation is now handled globally by the auth system
                // No need for manual navigation here
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('خطأ في تسجيل الخروج: $e')),
                  );
                }
              }
            },
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الحساب'),
        content: const Text(
          'هل أنت متأكد من رغبتك في حذف حسابك نهائياً؟ هذا الإجراء لا يمكن التراجع عنه.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement account deletion
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('ميزة حذف الحساب قريباً')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف الحساب'),
          ),
        ],
      ),
    );
  }
}
