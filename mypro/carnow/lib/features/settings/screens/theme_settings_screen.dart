import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';

import '../../../core/theme/carnow_color_system.dart';
import '../../../core/theme/carnow_theme_provider.dart';

/// Material 3 Theme Settings Screen with Dynamic Color Support
class ThemeSettingsScreen extends ConsumerWidget {
  const ThemeSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themePreferences = ref.watch(themePreferencesNotifierProvider);
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات التصميم'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Theme Mode Section
            _buildThemeModeSection(context, ref, themePreferences),
            const Gap(24),
            
            // Dynamic Color Section
            _buildDynamicColorSection(context, ref, themePreferences),
            const Gap(24),
            
            // Custom Color Section
            _buildCustomColorSection(context, ref, themePreferences),
            const Gap(24),
            
            // Accessibility Section
            _buildAccessibilitySection(context, ref, themePreferences),
            const Gap(24),
            
            // Text Scale Section
            _buildTextScaleSection(context, ref, themePreferences),
            const Gap(24),
            
            // Automotive Color Demo
            _buildAutomotiveColorDemo(context),
            const Gap(24),
            
            // Preview Cards
            _buildPreviewSection(context),
            const Gap(32),
            
            // Reset Button
            _buildResetSection(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeModeSection(
    BuildContext context,
    WidgetRef ref,
    ThemePreferences preferences,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'وضع التصميم',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(16),
            Column(
              children: ThemeMode.values.map((mode) {
                return RadioListTile<ThemeMode>(
                  title: Text(_getThemeModeTitle(mode)),
                  subtitle: Text(_getThemeModeSubtitle(mode)),
                  value: mode,
                  groupValue: preferences.themeMode,
                  onChanged: (value) {
                    if (value != null) {
                      ref.read(themePreferencesNotifierProvider.notifier)
                          .updateThemeMode(value);
                    }
                  },
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDynamicColorSection(
    BuildContext context,
    WidgetRef ref,
    ThemePreferences preferences,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الألوان الديناميكية (Material 3)',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(8),
            Text(
              'يتكيف مع خلفية النظام وتفضيلات المستخدم',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const Gap(16),
            SwitchListTile(
              title: const Text('تفعيل الألوان الديناميكية'),
              subtitle: preferences.useDynamicColor
                  ? const Text('سيتم استخدام ألوان النظام')
                  : const Text('سيتم استخدام ألوان العلامة التجارية'),
              value: preferences.useDynamicColor,
              onChanged: (value) {
                ref.read(themePreferencesNotifierProvider.notifier)
                    .updateDynamicColor(value);
              },
            ),
            if (!preferences.useDynamicColor) ...[
              const Gap(16),
              Text(
                'ألوان العلامة التجارية CarNow',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const Gap(12),
              _buildBrandColorChips(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBrandColorChips(BuildContext context) {
    final brandColors = [
      ('أخضر الغابة', CarNowColorSystem.primaryBrand),
      ('أزرق السماء', CarNowColorSystem.secondaryBrand),
      ('برتقالي السيارات', CarNowColorSystem.accentBrand),
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: brandColors.map((colorData) {
        return Chip(
          avatar: CircleAvatar(
            backgroundColor: colorData.$2,
            radius: 12,
          ),
          label: Text(colorData.$1),
        );
      }).toList(),
    );
  }

  Widget _buildCustomColorSection(
    BuildContext context,
    WidgetRef ref,
    ThemePreferences preferences,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'لون مخصص',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(8),
            Text(
              'اختر لون أساسي مخصص لتوليد نظام الألوان',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const Gap(16),
            if (preferences.customSeedColor != null) ...[
              Container(
                width: double.infinity,
                height: 60,
                decoration: BoxDecoration(
                  color: preferences.customSeedColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline,
                  ),
                ),
                child: Center(
                  child: Text(
                    'اللون المخصص الحالي',
                    style: TextStyle(
                      color: AccessibilityColorSystem.ensureContrast(
                        Colors.white,
                        preferences.customSeedColor!,
                      ),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              const Gap(16),
            ],
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showColorPicker(context, ref),
                    icon: const Icon(Icons.color_lens),
                    label: const Text('اختيار لون'),
                  ),
                ),
                if (preferences.customSeedColor != null) ...[
                  const Gap(8),
                  IconButton(
                    onPressed: () {
                      ref.read(themePreferencesNotifierProvider.notifier)
                          .updateCustomSeedColor(null);
                    },
                    icon: const Icon(Icons.clear),
                    tooltip: 'إزالة اللون المخصص',
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccessibilitySection(
    BuildContext context,
    WidgetRef ref,
    ThemePreferences preferences,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إمكانية الوصول',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(16),
            SwitchListTile(
              title: const Text('التباين العالي'),
              subtitle: const Text('لتحسين إمكانية القراءة'),
              value: preferences.useHighContrast,
              onChanged: (value) {
                ref.read(themePreferencesNotifierProvider.notifier)
                    .updateHighContrast(value);
              },
            ),
            SwitchListTile(
              title: const Text('شريط تنقل النظام الشفاف'),
              subtitle: const Text('لتجربة أكثر انسيابية'),
              value: preferences.useSystemNavigationBar,
              onChanged: (value) {
                ref.read(themePreferencesNotifierProvider.notifier)
                    .updateSystemNavigationBar(value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextScaleSection(
    BuildContext context,
    WidgetRef ref,
    ThemePreferences preferences,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حجم النص',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(8),
            Text(
              'تكبير: ${(preferences.textScaleFactor * 100).toInt()}%',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const Gap(16),
            Slider(
              value: preferences.textScaleFactor,
              min: 0.8,
              max: 2.0,
              divisions: 12,
              label: '${(preferences.textScaleFactor * 100).toInt()}%',
              onChanged: (value) {
                ref.read(themePreferencesNotifierProvider.notifier)
                    .updateTextScaleFactor(value);
              },
            ),
            const Gap(8),
            Text(
              'مثال على النص مع التكبير الحالي',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAutomotiveColorDemo(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ألوان صناعة السيارات',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(16),
            
            // Category Colors
            Text(
              'فئات قطع الغيار',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: CarNowExpressiveColors.categories.entries.map((entry) {
                return Chip(
                  backgroundColor: entry.value,
                  label: Text(
                    _getArabicCategoryName(entry.key),
                    style: TextStyle(
                      color: AccessibilityColorSystem.ensureContrast(
                        Colors.white,
                        entry.value,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            
            const Gap(16),
            
            // Status Colors
            Text(
              'حالة المنتجات',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: CarNowExpressiveColors.status.entries.map((entry) {
                return Chip(
                  backgroundColor: entry.value,
                  label: Text(
                    _getArabicStatusName(entry.key),
                    style: TextStyle(
                      color: AccessibilityColorSystem.ensureContrast(
                        Colors.white,
                        entry.value,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معاينة التصميم',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(16),
            
            // Button Styles Preview
            Text(
              'أنماط الأزرار',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('زر مرفوع'),
                ),
                FilledButton(
                  onPressed: () {},
                  child: const Text('زر ممتلئ'),
                ),
                OutlinedButton(
                  onPressed: () {},
                  child: const Text('زر محدود'),
                ),
                TextButton(
                  onPressed: () {},
                  child: const Text('زر نصي'),
                ),
              ],
            ),
            
            const Gap(16),
            
            // Form Elements Preview
            Text(
              'عناصر النموذج',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            const TextField(
              decoration: InputDecoration(
                labelText: 'حقل نصي',
                hintText: 'اكتب هنا...',
                prefixIcon: Icon(Icons.search),
              ),
            ),
            
            const Gap(16),
            
            // List Tile Preview
            Text(
              'عناصر القائمة',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            ListTile(
              leading: const Icon(Icons.car_rental),
              title: const Text('قطعة غيار محرك'),
              subtitle: const Text('متوفر - 250 ريال'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {},
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResetSection(BuildContext context, WidgetRef ref) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: () => _showResetDialog(context, ref),
        icon: const Icon(Icons.restore),
        label: const Text('إعادة تعيين إلى الافتراضي'),
        style: OutlinedButton.styleFrom(
          foregroundColor: Theme.of(context).colorScheme.error,
          side: BorderSide(color: Theme.of(context).colorScheme.error),
        ),
      ),
    );
  }

  String _getThemeModeTitle(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'الوضع الفاتح';
      case ThemeMode.dark:
        return 'الوضع الداكن';
      case ThemeMode.system:
        return 'تلقائي (حسب النظام)';
    }
  }

  String _getThemeModeSubtitle(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'خلفية فاتحة دائماً';
      case ThemeMode.dark:
        return 'خلفية داكنة دائماً';
      case ThemeMode.system:
        return 'يتبع إعدادات النظام';
    }
  }

  String _getArabicCategoryName(String category) {
    final categoryNames = {
      'engine': 'محرك',
      'exterior': 'خارجي',
      'interior': 'داخلي',
      'electrical': 'كهربائي',
      'brakes': 'فرامل',
      'transmission': 'ناقل الحركة',
      'suspension': 'تعليق',
      'tires': 'إطارات',
      'accessories': 'إكسسوارات',
      'tools': 'أدوات',
    };
    return categoryNames[category] ?? category;
  }

  String _getArabicStatusName(String status) {
    final statusNames = {
      'available': 'متوفر',
      'pending': 'معلق',
      'sold': 'مباع',
      'reserved': 'محجوز',
      'maintenance': 'صيانة',
      'shipping': 'شحن',
      'delivered': 'تم التسليم',
      'cancelled': 'ملغي',
    };
    return statusNames[status] ?? status;
  }

  void _showColorPicker(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار لون مخصص'),
        content: SizedBox(
          width: 300,
          height: 200,
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 6,
              mainAxisSpacing: 8,
              crossAxisSpacing: 8,
            ),
            itemCount: Colors.primaries.length,
            itemBuilder: (context, index) {
              final color = Colors.primaries[index];
              return GestureDetector(
                onTap: () {
                  ref.read(themePreferencesNotifierProvider.notifier)
                      .updateCustomSeedColor(color);
                  Navigator.of(context).pop();
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showResetDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text(
          'هل أنت متأكد من رغبتك في إعادة تعيين جميع إعدادات التصميم إلى القيم الافتراضية؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          FilledButton(
            onPressed: () {
              ref.read(themePreferencesNotifierProvider.notifier)
                  .resetToDefaults();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إعادة تعيين الإعدادات بنجاح'),
                ),
              );
            },
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }
} 