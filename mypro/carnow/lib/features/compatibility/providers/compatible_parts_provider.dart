import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../cars/models/car_model.dart';
import '../../../core/networking/simple_api_client.dart';
import '../../products/models/product_model.dart';

part 'compatible_parts_provider.g.dart';

/// Compatible Parts Provider
/// Following Forever Plan: Flutter → Go API → Supabase
@riverpod
Future<List<ProductModel>> compatibleParts(
  Ref ref,
  String vehicleId,
  String category,
) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    // Get vehicle details first
    final vehicleResponse = await apiClient.get('/api/v1/vehicles/$vehicleId');
    if (!vehicleResponse.isSuccess) {
      return [];
    }

    final vehicle = CarModel.fromJson(vehicleResponse.data);

    // Get compatible parts
    final compatibilityResponse = await apiClient.get(
      '/api/v1/compatibility/parts',
      queryParameters: {
        'vehicle_id': vehicleId,
        'category': category,
        'make': vehicle.make,
        'model': vehicle.model,
        'year': vehicle.year,
      },
    );

    if (compatibilityResponse.isSuccess && 
        compatibilityResponse.data != null) {
      final List<dynamic> partsData = compatibilityResponse.data['parts'] ?? [];
      return partsData
          .map((json) => ProductModel.fromJson(json))
          .toList();
    }

    return [];
  } catch (e) {
    // Handle errors gracefully
    return [];
  }
}

@riverpod
Future<CarModel?> vehicle(Ref ref, String vehicleId) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.get('/api/v1/vehicles/$vehicleId');

    if (response.isSuccess && response.data != null) {
      return CarModel.fromJson(response.data);
    }

    return null;
  } catch (e) {
    // Handle errors gracefully
    return null;
  }
}

/* // MODIFIED - Commented out problematic provider
@riverpod
Future<List<String>> availablePartCategories(
  Ref ref, {
  required String carModel,
}) async {
  // ... existing code ...
}
*/
