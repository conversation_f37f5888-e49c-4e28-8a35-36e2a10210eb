import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';

import '../repositories/checkout_repository.dart';
import '../services/checkout_service.dart';

part 'checkout_provider.g.dart';

final _logger = Logger('CheckoutProvider');

/// Checkout Repository Provider
@riverpod
CheckoutRepository checkoutRepository(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return CheckoutRepository(apiClient);
}

/// Checkout Service Provider for enhanced checkout flow
@riverpod
CheckoutService checkoutService(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return CheckoutService(apiClient: apiClient);
}

/// Enhanced Checkout Provider following Forever Plan architecture
/// Integrates with existing wallet system for payment processing
@riverpod
class Checkout extends _$Checkout {
  @override
  Future<Map<String, dynamic>?> build() async {
    // Initial state is null (no active checkout)
    return null;
  }

  /// Place order using existing wallet system
  Future<Map<String, dynamic>> placeOrder(Map<String, dynamic> orderRequest) async {
    final repository = ref.read(checkoutRepositoryProvider);
    
    try {
      _logger.info('Placing order with wallet payment');
      
      // Show loading state
      state = const AsyncValue.loading();
      
      // Place order via repository
      final orderResponse = await repository.createOrder(orderRequest);
      
      // Update state with order response
      state = AsyncValue.data(orderResponse);
      
      _logger.info('Order placed successfully', orderResponse);
      return orderResponse;
      
    } catch (e, stackTrace) {
      _logger.severe('Failed to place order', e, stackTrace);
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Get order status
  Future<Map<String, dynamic>?> getOrderStatus(String orderId) async {
    final repository = ref.read(checkoutRepositoryProvider);
    
    try {
      _logger.info('Getting order status for order: $orderId');
      
      final orderStatus = await repository.getOrderStatus(orderId);
      
      _logger.info('Order status retrieved successfully');
      return orderStatus;
      
    } catch (e, stackTrace) {
      _logger.severe('Failed to get order status', e, stackTrace);
      throw Exception('فشل في الحصول على حالة الطلب: $e');
    }
  }

  /// Reset checkout state
  void reset() {
    state = const AsyncValue.data(null);
  }
}

/// Checkout Loading State Provider
@riverpod
bool isCheckoutLoading(Ref ref) {
  final checkoutAsync = ref.watch(checkoutProvider);
  return checkoutAsync.isLoading;
}

/// Checkout Error Provider
@riverpod
String? checkoutError(Ref ref) {
  final checkoutAsync = ref.watch(checkoutProvider);
  return checkoutAsync.when(
    data: (_) => null,
    loading: () => null,
    error: (error, _) => error.toString(),
  );
}
