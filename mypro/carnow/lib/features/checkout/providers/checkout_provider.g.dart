// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'checkout_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$checkoutRepositoryHash() =>
    r'b4b71431cb912e8f221d15a3e7aac928de2089b2';

/// Checkout Repository Provider
///
/// Copied from [checkoutRepository].
@ProviderFor(checkoutRepository)
final checkoutRepositoryProvider =
    AutoDisposeProvider<CheckoutRepository>.internal(
      checkoutRepository,
      name: r'checkoutRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$checkoutRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CheckoutRepositoryRef = AutoDisposeProviderRef<CheckoutRepository>;
String _$checkoutServiceHash() => r'043fb453f86f85896e702672f78acd60730a03cf';

/// Checkout Service Provider for enhanced checkout flow
///
/// Copied from [checkoutService].
@ProviderFor(checkoutService)
final checkoutServiceProvider = AutoDisposeProvider<CheckoutService>.internal(
  checkoutService,
  name: r'checkoutServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$checkoutServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CheckoutServiceRef = AutoDisposeProviderRef<CheckoutService>;
String _$isCheckoutLoadingHash() => r'a94a5ba9f45d8bdb63986727f2740669b38c07f6';

/// Checkout Loading State Provider
///
/// Copied from [isCheckoutLoading].
@ProviderFor(isCheckoutLoading)
final isCheckoutLoadingProvider = AutoDisposeProvider<bool>.internal(
  isCheckoutLoading,
  name: r'isCheckoutLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isCheckoutLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsCheckoutLoadingRef = AutoDisposeProviderRef<bool>;
String _$checkoutErrorHash() => r'08439f90efa698addd3d9c738497c79e95b685d2';

/// Checkout Error Provider
///
/// Copied from [checkoutError].
@ProviderFor(checkoutError)
final checkoutErrorProvider = AutoDisposeProvider<String?>.internal(
  checkoutError,
  name: r'checkoutErrorProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$checkoutErrorHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CheckoutErrorRef = AutoDisposeProviderRef<String?>;
String _$checkoutHash() => r'aa72760db5c26a4b531b733a71e175ff43e8864e';

/// Enhanced Checkout Provider following Forever Plan architecture
/// Integrates with existing wallet system for payment processing
///
/// Copied from [Checkout].
@ProviderFor(Checkout)
final checkoutProvider =
    AutoDisposeAsyncNotifierProvider<Checkout, Map<String, dynamic>?>.internal(
      Checkout.new,
      name: r'checkoutProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$checkoutHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$Checkout = AutoDisposeAsyncNotifier<Map<String, dynamic>?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
