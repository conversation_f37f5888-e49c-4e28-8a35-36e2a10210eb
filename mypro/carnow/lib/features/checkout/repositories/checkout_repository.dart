import '../../../core/networking/simple_api_client.dart';
import '../../../core/utils/unified_logger.dart';

/// Enhanced Checkout Repository following Forever Plan architecture
/// Handles order creation and payment processing with existing wallet system
class CheckoutRepository {
  final SimpleApiClient _apiClient;

  CheckoutRepository(this._apiClient);

  /// Create order using existing wallet system
  Future<Map<String, dynamic>> createOrder(Map<String, dynamic> orderRequest) async {
    try {
      UnifiedLogger.info('Creating order with request: $orderRequest');

      final response = await _apiClient.postApi('/orders', data: orderRequest);

      if (response.isSuccess && response.data != null) {
        final orderData = response.data as Map<String, dynamic>;
        
        UnifiedLogger.info('Order created successfully: ${orderData['order_id']}');
        return orderData;
      } else {
        throw CheckoutException('فشل في إنشاء الطلب: ${response.error ?? 'خطأ غير معروف'}');
      }
    } catch (e) {
      UnifiedLogger.error('Error creating order', error: e);
      if (e is CheckoutException) rethrow;
      throw CheckoutException('فشل في إنشاء الطلب: $e');
    }
  }

  /// Get order status
  Future<Map<String, dynamic>?> getOrderStatus(String orderId) async {
    try {
      UnifiedLogger.info('Getting order status for: $orderId');

      final response = await _apiClient.getApi('/orders/$orderId');

      if (response.isSuccess && response.data != null) {
        final orderData = response.data as Map<String, dynamic>;
        
        UnifiedLogger.info('Order status retrieved successfully');
        return orderData;
      } else {
        throw CheckoutException('فشل في الحصول على حالة الطلب: ${response.error ?? 'خطأ غير معروف'}');
      }
    } catch (e) {
      UnifiedLogger.error('Error getting order status', error: e);
      if (e is CheckoutException) rethrow;
      throw CheckoutException('فشل في الحصول على حالة الطلب: $e');
    }
  }

  /// Get user orders
  Future<List<Map<String, dynamic>>> getUserOrders() async {
    try {
      UnifiedLogger.info('Getting user orders');

      final response = await _apiClient.getApi('/orders');

      if (response.isSuccess && response.data != null) {
        final ordersData = response.data as List<dynamic>;
        
        final orders = ordersData
            .map((order) => order as Map<String, dynamic>)
            .toList();
        
        UnifiedLogger.info('User orders retrieved successfully: ${orders.length} orders');
        return orders;
      } else {
        throw CheckoutException('فشل في الحصول على الطلبات: ${response.error ?? 'خطأ غير معروف'}');
      }
    } catch (e) {
      UnifiedLogger.error('Error getting user orders', error: e);
      if (e is CheckoutException) rethrow;
      throw CheckoutException('فشل في الحصول على الطلبات: $e');
    }
  }

  /// Cancel order
  Future<void> cancelOrder(String orderId, String reason) async {
    try {
      UnifiedLogger.info('Cancelling order: $orderId, reason: $reason');

      final response = await _apiClient.deleteApi('/orders/$orderId');

      if (response.isSuccess) {
        UnifiedLogger.info('Order cancelled successfully');
      } else {
        throw CheckoutException('فشل في إلغاء الطلب: ${response.error ?? 'خطأ غير معروف'}');
      }
    } catch (e) {
      UnifiedLogger.error('Error cancelling order', error: e);
      if (e is CheckoutException) rethrow;
      throw CheckoutException('فشل في إلغاء الطلب: $e');
    }
  }
}

/// Exception class for checkout repository errors
class CheckoutException implements Exception {
  final String message;
  
  const CheckoutException(this.message);
  
  @override
  String toString() => 'CheckoutException: $message';
}
