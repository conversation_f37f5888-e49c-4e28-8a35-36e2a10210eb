// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'checkout_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CheckoutRequestModel {

 String get cartId; ShippingAddressModel get shippingAddress; String get paymentMethodId; String get currency; Map<String, String> get metadata;
/// Create a copy of CheckoutRequestModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CheckoutRequestModelCopyWith<CheckoutRequestModel> get copyWith => _$CheckoutRequestModelCopyWithImpl<CheckoutRequestModel>(this as CheckoutRequestModel, _$identity);

  /// Serializes this CheckoutRequestModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CheckoutRequestModel&&(identical(other.cartId, cartId) || other.cartId == cartId)&&(identical(other.shippingAddress, shippingAddress) || other.shippingAddress == shippingAddress)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&(identical(other.currency, currency) || other.currency == currency)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,cartId,shippingAddress,paymentMethodId,currency,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'CheckoutRequestModel(cartId: $cartId, shippingAddress: $shippingAddress, paymentMethodId: $paymentMethodId, currency: $currency, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $CheckoutRequestModelCopyWith<$Res>  {
  factory $CheckoutRequestModelCopyWith(CheckoutRequestModel value, $Res Function(CheckoutRequestModel) _then) = _$CheckoutRequestModelCopyWithImpl;
@useResult
$Res call({
 String cartId, ShippingAddressModel shippingAddress, String paymentMethodId, String currency, Map<String, String> metadata
});


$ShippingAddressModelCopyWith<$Res> get shippingAddress;

}
/// @nodoc
class _$CheckoutRequestModelCopyWithImpl<$Res>
    implements $CheckoutRequestModelCopyWith<$Res> {
  _$CheckoutRequestModelCopyWithImpl(this._self, this._then);

  final CheckoutRequestModel _self;
  final $Res Function(CheckoutRequestModel) _then;

/// Create a copy of CheckoutRequestModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? cartId = null,Object? shippingAddress = null,Object? paymentMethodId = null,Object? currency = null,Object? metadata = null,}) {
  return _then(_self.copyWith(
cartId: null == cartId ? _self.cartId : cartId // ignore: cast_nullable_to_non_nullable
as String,shippingAddress: null == shippingAddress ? _self.shippingAddress : shippingAddress // ignore: cast_nullable_to_non_nullable
as ShippingAddressModel,paymentMethodId: null == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, String>,
  ));
}
/// Create a copy of CheckoutRequestModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ShippingAddressModelCopyWith<$Res> get shippingAddress {
  
  return $ShippingAddressModelCopyWith<$Res>(_self.shippingAddress, (value) {
    return _then(_self.copyWith(shippingAddress: value));
  });
}
}


/// Adds pattern-matching-related methods to [CheckoutRequestModel].
extension CheckoutRequestModelPatterns on CheckoutRequestModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CheckoutRequestModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CheckoutRequestModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CheckoutRequestModel value)  $default,){
final _that = this;
switch (_that) {
case _CheckoutRequestModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CheckoutRequestModel value)?  $default,){
final _that = this;
switch (_that) {
case _CheckoutRequestModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String cartId,  ShippingAddressModel shippingAddress,  String paymentMethodId,  String currency,  Map<String, String> metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CheckoutRequestModel() when $default != null:
return $default(_that.cartId,_that.shippingAddress,_that.paymentMethodId,_that.currency,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String cartId,  ShippingAddressModel shippingAddress,  String paymentMethodId,  String currency,  Map<String, String> metadata)  $default,) {final _that = this;
switch (_that) {
case _CheckoutRequestModel():
return $default(_that.cartId,_that.shippingAddress,_that.paymentMethodId,_that.currency,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String cartId,  ShippingAddressModel shippingAddress,  String paymentMethodId,  String currency,  Map<String, String> metadata)?  $default,) {final _that = this;
switch (_that) {
case _CheckoutRequestModel() when $default != null:
return $default(_that.cartId,_that.shippingAddress,_that.paymentMethodId,_that.currency,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CheckoutRequestModel implements CheckoutRequestModel {
  const _CheckoutRequestModel({required this.cartId, required this.shippingAddress, required this.paymentMethodId, this.currency = 'USD', final  Map<String, String> metadata = const {}}): _metadata = metadata;
  factory _CheckoutRequestModel.fromJson(Map<String, dynamic> json) => _$CheckoutRequestModelFromJson(json);

@override final  String cartId;
@override final  ShippingAddressModel shippingAddress;
@override final  String paymentMethodId;
@override@JsonKey() final  String currency;
 final  Map<String, String> _metadata;
@override@JsonKey() Map<String, String> get metadata {
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_metadata);
}


/// Create a copy of CheckoutRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CheckoutRequestModelCopyWith<_CheckoutRequestModel> get copyWith => __$CheckoutRequestModelCopyWithImpl<_CheckoutRequestModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CheckoutRequestModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CheckoutRequestModel&&(identical(other.cartId, cartId) || other.cartId == cartId)&&(identical(other.shippingAddress, shippingAddress) || other.shippingAddress == shippingAddress)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&(identical(other.currency, currency) || other.currency == currency)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,cartId,shippingAddress,paymentMethodId,currency,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'CheckoutRequestModel(cartId: $cartId, shippingAddress: $shippingAddress, paymentMethodId: $paymentMethodId, currency: $currency, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$CheckoutRequestModelCopyWith<$Res> implements $CheckoutRequestModelCopyWith<$Res> {
  factory _$CheckoutRequestModelCopyWith(_CheckoutRequestModel value, $Res Function(_CheckoutRequestModel) _then) = __$CheckoutRequestModelCopyWithImpl;
@override @useResult
$Res call({
 String cartId, ShippingAddressModel shippingAddress, String paymentMethodId, String currency, Map<String, String> metadata
});


@override $ShippingAddressModelCopyWith<$Res> get shippingAddress;

}
/// @nodoc
class __$CheckoutRequestModelCopyWithImpl<$Res>
    implements _$CheckoutRequestModelCopyWith<$Res> {
  __$CheckoutRequestModelCopyWithImpl(this._self, this._then);

  final _CheckoutRequestModel _self;
  final $Res Function(_CheckoutRequestModel) _then;

/// Create a copy of CheckoutRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? cartId = null,Object? shippingAddress = null,Object? paymentMethodId = null,Object? currency = null,Object? metadata = null,}) {
  return _then(_CheckoutRequestModel(
cartId: null == cartId ? _self.cartId : cartId // ignore: cast_nullable_to_non_nullable
as String,shippingAddress: null == shippingAddress ? _self.shippingAddress : shippingAddress // ignore: cast_nullable_to_non_nullable
as ShippingAddressModel,paymentMethodId: null == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,metadata: null == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, String>,
  ));
}

/// Create a copy of CheckoutRequestModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ShippingAddressModelCopyWith<$Res> get shippingAddress {
  
  return $ShippingAddressModelCopyWith<$Res>(_self.shippingAddress, (value) {
    return _then(_self.copyWith(shippingAddress: value));
  });
}
}


/// @nodoc
mixin _$ShippingAddressModel {

 String get name; String get phone; String get addressLine; String get city; String get state; String get postalCode; String get country;
/// Create a copy of ShippingAddressModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ShippingAddressModelCopyWith<ShippingAddressModel> get copyWith => _$ShippingAddressModelCopyWithImpl<ShippingAddressModel>(this as ShippingAddressModel, _$identity);

  /// Serializes this ShippingAddressModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ShippingAddressModel&&(identical(other.name, name) || other.name == name)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.addressLine, addressLine) || other.addressLine == addressLine)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.postalCode, postalCode) || other.postalCode == postalCode)&&(identical(other.country, country) || other.country == country));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,phone,addressLine,city,state,postalCode,country);

@override
String toString() {
  return 'ShippingAddressModel(name: $name, phone: $phone, addressLine: $addressLine, city: $city, state: $state, postalCode: $postalCode, country: $country)';
}


}

/// @nodoc
abstract mixin class $ShippingAddressModelCopyWith<$Res>  {
  factory $ShippingAddressModelCopyWith(ShippingAddressModel value, $Res Function(ShippingAddressModel) _then) = _$ShippingAddressModelCopyWithImpl;
@useResult
$Res call({
 String name, String phone, String addressLine, String city, String state, String postalCode, String country
});




}
/// @nodoc
class _$ShippingAddressModelCopyWithImpl<$Res>
    implements $ShippingAddressModelCopyWith<$Res> {
  _$ShippingAddressModelCopyWithImpl(this._self, this._then);

  final ShippingAddressModel _self;
  final $Res Function(ShippingAddressModel) _then;

/// Create a copy of ShippingAddressModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? phone = null,Object? addressLine = null,Object? city = null,Object? state = null,Object? postalCode = null,Object? country = null,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,addressLine: null == addressLine ? _self.addressLine : addressLine // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String,postalCode: null == postalCode ? _self.postalCode : postalCode // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [ShippingAddressModel].
extension ShippingAddressModelPatterns on ShippingAddressModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ShippingAddressModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ShippingAddressModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ShippingAddressModel value)  $default,){
final _that = this;
switch (_that) {
case _ShippingAddressModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ShippingAddressModel value)?  $default,){
final _that = this;
switch (_that) {
case _ShippingAddressModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  String phone,  String addressLine,  String city,  String state,  String postalCode,  String country)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ShippingAddressModel() when $default != null:
return $default(_that.name,_that.phone,_that.addressLine,_that.city,_that.state,_that.postalCode,_that.country);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  String phone,  String addressLine,  String city,  String state,  String postalCode,  String country)  $default,) {final _that = this;
switch (_that) {
case _ShippingAddressModel():
return $default(_that.name,_that.phone,_that.addressLine,_that.city,_that.state,_that.postalCode,_that.country);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  String phone,  String addressLine,  String city,  String state,  String postalCode,  String country)?  $default,) {final _that = this;
switch (_that) {
case _ShippingAddressModel() when $default != null:
return $default(_that.name,_that.phone,_that.addressLine,_that.city,_that.state,_that.postalCode,_that.country);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ShippingAddressModel implements ShippingAddressModel {
  const _ShippingAddressModel({required this.name, required this.phone, required this.addressLine, required this.city, required this.state, required this.postalCode, this.country = 'SA'});
  factory _ShippingAddressModel.fromJson(Map<String, dynamic> json) => _$ShippingAddressModelFromJson(json);

@override final  String name;
@override final  String phone;
@override final  String addressLine;
@override final  String city;
@override final  String state;
@override final  String postalCode;
@override@JsonKey() final  String country;

/// Create a copy of ShippingAddressModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ShippingAddressModelCopyWith<_ShippingAddressModel> get copyWith => __$ShippingAddressModelCopyWithImpl<_ShippingAddressModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ShippingAddressModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ShippingAddressModel&&(identical(other.name, name) || other.name == name)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.addressLine, addressLine) || other.addressLine == addressLine)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.postalCode, postalCode) || other.postalCode == postalCode)&&(identical(other.country, country) || other.country == country));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,phone,addressLine,city,state,postalCode,country);

@override
String toString() {
  return 'ShippingAddressModel(name: $name, phone: $phone, addressLine: $addressLine, city: $city, state: $state, postalCode: $postalCode, country: $country)';
}


}

/// @nodoc
abstract mixin class _$ShippingAddressModelCopyWith<$Res> implements $ShippingAddressModelCopyWith<$Res> {
  factory _$ShippingAddressModelCopyWith(_ShippingAddressModel value, $Res Function(_ShippingAddressModel) _then) = __$ShippingAddressModelCopyWithImpl;
@override @useResult
$Res call({
 String name, String phone, String addressLine, String city, String state, String postalCode, String country
});




}
/// @nodoc
class __$ShippingAddressModelCopyWithImpl<$Res>
    implements _$ShippingAddressModelCopyWith<$Res> {
  __$ShippingAddressModelCopyWithImpl(this._self, this._then);

  final _ShippingAddressModel _self;
  final $Res Function(_ShippingAddressModel) _then;

/// Create a copy of ShippingAddressModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? phone = null,Object? addressLine = null,Object? city = null,Object? state = null,Object? postalCode = null,Object? country = null,}) {
  return _then(_ShippingAddressModel(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,addressLine: null == addressLine ? _self.addressLine : addressLine // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String,postalCode: null == postalCode ? _self.postalCode : postalCode // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$CheckoutResultModel {

 bool get success; String? get orderId; String? get paymentIntentId; String? get clientSecret; String get status; String get message; String? get error;
/// Create a copy of CheckoutResultModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CheckoutResultModelCopyWith<CheckoutResultModel> get copyWith => _$CheckoutResultModelCopyWithImpl<CheckoutResultModel>(this as CheckoutResultModel, _$identity);

  /// Serializes this CheckoutResultModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CheckoutResultModel&&(identical(other.success, success) || other.success == success)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.paymentIntentId, paymentIntentId) || other.paymentIntentId == paymentIntentId)&&(identical(other.clientSecret, clientSecret) || other.clientSecret == clientSecret)&&(identical(other.status, status) || other.status == status)&&(identical(other.message, message) || other.message == message)&&(identical(other.error, error) || other.error == error));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,orderId,paymentIntentId,clientSecret,status,message,error);

@override
String toString() {
  return 'CheckoutResultModel(success: $success, orderId: $orderId, paymentIntentId: $paymentIntentId, clientSecret: $clientSecret, status: $status, message: $message, error: $error)';
}


}

/// @nodoc
abstract mixin class $CheckoutResultModelCopyWith<$Res>  {
  factory $CheckoutResultModelCopyWith(CheckoutResultModel value, $Res Function(CheckoutResultModel) _then) = _$CheckoutResultModelCopyWithImpl;
@useResult
$Res call({
 bool success, String? orderId, String? paymentIntentId, String? clientSecret, String status, String message, String? error
});




}
/// @nodoc
class _$CheckoutResultModelCopyWithImpl<$Res>
    implements $CheckoutResultModelCopyWith<$Res> {
  _$CheckoutResultModelCopyWithImpl(this._self, this._then);

  final CheckoutResultModel _self;
  final $Res Function(CheckoutResultModel) _then;

/// Create a copy of CheckoutResultModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = null,Object? orderId = freezed,Object? paymentIntentId = freezed,Object? clientSecret = freezed,Object? status = null,Object? message = null,Object? error = freezed,}) {
  return _then(_self.copyWith(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,paymentIntentId: freezed == paymentIntentId ? _self.paymentIntentId : paymentIntentId // ignore: cast_nullable_to_non_nullable
as String?,clientSecret: freezed == clientSecret ? _self.clientSecret : clientSecret // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [CheckoutResultModel].
extension CheckoutResultModelPatterns on CheckoutResultModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CheckoutResultModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CheckoutResultModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CheckoutResultModel value)  $default,){
final _that = this;
switch (_that) {
case _CheckoutResultModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CheckoutResultModel value)?  $default,){
final _that = this;
switch (_that) {
case _CheckoutResultModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool success,  String? orderId,  String? paymentIntentId,  String? clientSecret,  String status,  String message,  String? error)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CheckoutResultModel() when $default != null:
return $default(_that.success,_that.orderId,_that.paymentIntentId,_that.clientSecret,_that.status,_that.message,_that.error);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool success,  String? orderId,  String? paymentIntentId,  String? clientSecret,  String status,  String message,  String? error)  $default,) {final _that = this;
switch (_that) {
case _CheckoutResultModel():
return $default(_that.success,_that.orderId,_that.paymentIntentId,_that.clientSecret,_that.status,_that.message,_that.error);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool success,  String? orderId,  String? paymentIntentId,  String? clientSecret,  String status,  String message,  String? error)?  $default,) {final _that = this;
switch (_that) {
case _CheckoutResultModel() when $default != null:
return $default(_that.success,_that.orderId,_that.paymentIntentId,_that.clientSecret,_that.status,_that.message,_that.error);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CheckoutResultModel implements CheckoutResultModel {
  const _CheckoutResultModel({required this.success, this.orderId, this.paymentIntentId, this.clientSecret, required this.status, required this.message, this.error});
  factory _CheckoutResultModel.fromJson(Map<String, dynamic> json) => _$CheckoutResultModelFromJson(json);

@override final  bool success;
@override final  String? orderId;
@override final  String? paymentIntentId;
@override final  String? clientSecret;
@override final  String status;
@override final  String message;
@override final  String? error;

/// Create a copy of CheckoutResultModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CheckoutResultModelCopyWith<_CheckoutResultModel> get copyWith => __$CheckoutResultModelCopyWithImpl<_CheckoutResultModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CheckoutResultModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CheckoutResultModel&&(identical(other.success, success) || other.success == success)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.paymentIntentId, paymentIntentId) || other.paymentIntentId == paymentIntentId)&&(identical(other.clientSecret, clientSecret) || other.clientSecret == clientSecret)&&(identical(other.status, status) || other.status == status)&&(identical(other.message, message) || other.message == message)&&(identical(other.error, error) || other.error == error));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,orderId,paymentIntentId,clientSecret,status,message,error);

@override
String toString() {
  return 'CheckoutResultModel(success: $success, orderId: $orderId, paymentIntentId: $paymentIntentId, clientSecret: $clientSecret, status: $status, message: $message, error: $error)';
}


}

/// @nodoc
abstract mixin class _$CheckoutResultModelCopyWith<$Res> implements $CheckoutResultModelCopyWith<$Res> {
  factory _$CheckoutResultModelCopyWith(_CheckoutResultModel value, $Res Function(_CheckoutResultModel) _then) = __$CheckoutResultModelCopyWithImpl;
@override @useResult
$Res call({
 bool success, String? orderId, String? paymentIntentId, String? clientSecret, String status, String message, String? error
});




}
/// @nodoc
class __$CheckoutResultModelCopyWithImpl<$Res>
    implements _$CheckoutResultModelCopyWith<$Res> {
  __$CheckoutResultModelCopyWithImpl(this._self, this._then);

  final _CheckoutResultModel _self;
  final $Res Function(_CheckoutResultModel) _then;

/// Create a copy of CheckoutResultModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = null,Object? orderId = freezed,Object? paymentIntentId = freezed,Object? clientSecret = freezed,Object? status = null,Object? message = null,Object? error = freezed,}) {
  return _then(_CheckoutResultModel(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,paymentIntentId: freezed == paymentIntentId ? _self.paymentIntentId : paymentIntentId // ignore: cast_nullable_to_non_nullable
as String?,clientSecret: freezed == clientSecret ? _self.clientSecret : clientSecret // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$PaymentMethodModel {

 String get id; String get type;// 'card', 'wallet', 'bank_transfer'
 String get displayName; String? get last4; String? get brand; String? get expiryMonth; String? get expiryYear; bool get isEnabled; bool get isDefault;
/// Create a copy of PaymentMethodModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentMethodModelCopyWith<PaymentMethodModel> get copyWith => _$PaymentMethodModelCopyWithImpl<PaymentMethodModel>(this as PaymentMethodModel, _$identity);

  /// Serializes this PaymentMethodModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentMethodModel&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.last4, last4) || other.last4 == last4)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.expiryMonth, expiryMonth) || other.expiryMonth == expiryMonth)&&(identical(other.expiryYear, expiryYear) || other.expiryYear == expiryYear)&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.isDefault, isDefault) || other.isDefault == isDefault));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,displayName,last4,brand,expiryMonth,expiryYear,isEnabled,isDefault);

@override
String toString() {
  return 'PaymentMethodModel(id: $id, type: $type, displayName: $displayName, last4: $last4, brand: $brand, expiryMonth: $expiryMonth, expiryYear: $expiryYear, isEnabled: $isEnabled, isDefault: $isDefault)';
}


}

/// @nodoc
abstract mixin class $PaymentMethodModelCopyWith<$Res>  {
  factory $PaymentMethodModelCopyWith(PaymentMethodModel value, $Res Function(PaymentMethodModel) _then) = _$PaymentMethodModelCopyWithImpl;
@useResult
$Res call({
 String id, String type, String displayName, String? last4, String? brand, String? expiryMonth, String? expiryYear, bool isEnabled, bool isDefault
});




}
/// @nodoc
class _$PaymentMethodModelCopyWithImpl<$Res>
    implements $PaymentMethodModelCopyWith<$Res> {
  _$PaymentMethodModelCopyWithImpl(this._self, this._then);

  final PaymentMethodModel _self;
  final $Res Function(PaymentMethodModel) _then;

/// Create a copy of PaymentMethodModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? type = null,Object? displayName = null,Object? last4 = freezed,Object? brand = freezed,Object? expiryMonth = freezed,Object? expiryYear = freezed,Object? isEnabled = null,Object? isDefault = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,last4: freezed == last4 ? _self.last4 : last4 // ignore: cast_nullable_to_non_nullable
as String?,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,expiryMonth: freezed == expiryMonth ? _self.expiryMonth : expiryMonth // ignore: cast_nullable_to_non_nullable
as String?,expiryYear: freezed == expiryYear ? _self.expiryYear : expiryYear // ignore: cast_nullable_to_non_nullable
as String?,isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,isDefault: null == isDefault ? _self.isDefault : isDefault // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [PaymentMethodModel].
extension PaymentMethodModelPatterns on PaymentMethodModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PaymentMethodModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PaymentMethodModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PaymentMethodModel value)  $default,){
final _that = this;
switch (_that) {
case _PaymentMethodModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PaymentMethodModel value)?  $default,){
final _that = this;
switch (_that) {
case _PaymentMethodModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String type,  String displayName,  String? last4,  String? brand,  String? expiryMonth,  String? expiryYear,  bool isEnabled,  bool isDefault)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PaymentMethodModel() when $default != null:
return $default(_that.id,_that.type,_that.displayName,_that.last4,_that.brand,_that.expiryMonth,_that.expiryYear,_that.isEnabled,_that.isDefault);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String type,  String displayName,  String? last4,  String? brand,  String? expiryMonth,  String? expiryYear,  bool isEnabled,  bool isDefault)  $default,) {final _that = this;
switch (_that) {
case _PaymentMethodModel():
return $default(_that.id,_that.type,_that.displayName,_that.last4,_that.brand,_that.expiryMonth,_that.expiryYear,_that.isEnabled,_that.isDefault);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String type,  String displayName,  String? last4,  String? brand,  String? expiryMonth,  String? expiryYear,  bool isEnabled,  bool isDefault)?  $default,) {final _that = this;
switch (_that) {
case _PaymentMethodModel() when $default != null:
return $default(_that.id,_that.type,_that.displayName,_that.last4,_that.brand,_that.expiryMonth,_that.expiryYear,_that.isEnabled,_that.isDefault);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PaymentMethodModel implements PaymentMethodModel {
  const _PaymentMethodModel({required this.id, required this.type, required this.displayName, this.last4, this.brand, this.expiryMonth, this.expiryYear, this.isEnabled = true, this.isDefault = false});
  factory _PaymentMethodModel.fromJson(Map<String, dynamic> json) => _$PaymentMethodModelFromJson(json);

@override final  String id;
@override final  String type;
// 'card', 'wallet', 'bank_transfer'
@override final  String displayName;
@override final  String? last4;
@override final  String? brand;
@override final  String? expiryMonth;
@override final  String? expiryYear;
@override@JsonKey() final  bool isEnabled;
@override@JsonKey() final  bool isDefault;

/// Create a copy of PaymentMethodModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentMethodModelCopyWith<_PaymentMethodModel> get copyWith => __$PaymentMethodModelCopyWithImpl<_PaymentMethodModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaymentMethodModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentMethodModel&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.last4, last4) || other.last4 == last4)&&(identical(other.brand, brand) || other.brand == brand)&&(identical(other.expiryMonth, expiryMonth) || other.expiryMonth == expiryMonth)&&(identical(other.expiryYear, expiryYear) || other.expiryYear == expiryYear)&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.isDefault, isDefault) || other.isDefault == isDefault));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,displayName,last4,brand,expiryMonth,expiryYear,isEnabled,isDefault);

@override
String toString() {
  return 'PaymentMethodModel(id: $id, type: $type, displayName: $displayName, last4: $last4, brand: $brand, expiryMonth: $expiryMonth, expiryYear: $expiryYear, isEnabled: $isEnabled, isDefault: $isDefault)';
}


}

/// @nodoc
abstract mixin class _$PaymentMethodModelCopyWith<$Res> implements $PaymentMethodModelCopyWith<$Res> {
  factory _$PaymentMethodModelCopyWith(_PaymentMethodModel value, $Res Function(_PaymentMethodModel) _then) = __$PaymentMethodModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String type, String displayName, String? last4, String? brand, String? expiryMonth, String? expiryYear, bool isEnabled, bool isDefault
});




}
/// @nodoc
class __$PaymentMethodModelCopyWithImpl<$Res>
    implements _$PaymentMethodModelCopyWith<$Res> {
  __$PaymentMethodModelCopyWithImpl(this._self, this._then);

  final _PaymentMethodModel _self;
  final $Res Function(_PaymentMethodModel) _then;

/// Create a copy of PaymentMethodModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? type = null,Object? displayName = null,Object? last4 = freezed,Object? brand = freezed,Object? expiryMonth = freezed,Object? expiryYear = freezed,Object? isEnabled = null,Object? isDefault = null,}) {
  return _then(_PaymentMethodModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,last4: freezed == last4 ? _self.last4 : last4 // ignore: cast_nullable_to_non_nullable
as String?,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,expiryMonth: freezed == expiryMonth ? _self.expiryMonth : expiryMonth // ignore: cast_nullable_to_non_nullable
as String?,expiryYear: freezed == expiryYear ? _self.expiryYear : expiryYear // ignore: cast_nullable_to_non_nullable
as String?,isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,isDefault: null == isDefault ? _self.isDefault : isDefault // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$OrderSummaryModel {

 double get subtotal; double get tax; double get shipping; double get total; String get currency; int get itemCount; List<OrderItemSummaryModel> get items;
/// Create a copy of OrderSummaryModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderSummaryModelCopyWith<OrderSummaryModel> get copyWith => _$OrderSummaryModelCopyWithImpl<OrderSummaryModel>(this as OrderSummaryModel, _$identity);

  /// Serializes this OrderSummaryModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderSummaryModel&&(identical(other.subtotal, subtotal) || other.subtotal == subtotal)&&(identical(other.tax, tax) || other.tax == tax)&&(identical(other.shipping, shipping) || other.shipping == shipping)&&(identical(other.total, total) || other.total == total)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.itemCount, itemCount) || other.itemCount == itemCount)&&const DeepCollectionEquality().equals(other.items, items));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,subtotal,tax,shipping,total,currency,itemCount,const DeepCollectionEquality().hash(items));

@override
String toString() {
  return 'OrderSummaryModel(subtotal: $subtotal, tax: $tax, shipping: $shipping, total: $total, currency: $currency, itemCount: $itemCount, items: $items)';
}


}

/// @nodoc
abstract mixin class $OrderSummaryModelCopyWith<$Res>  {
  factory $OrderSummaryModelCopyWith(OrderSummaryModel value, $Res Function(OrderSummaryModel) _then) = _$OrderSummaryModelCopyWithImpl;
@useResult
$Res call({
 double subtotal, double tax, double shipping, double total, String currency, int itemCount, List<OrderItemSummaryModel> items
});




}
/// @nodoc
class _$OrderSummaryModelCopyWithImpl<$Res>
    implements $OrderSummaryModelCopyWith<$Res> {
  _$OrderSummaryModelCopyWithImpl(this._self, this._then);

  final OrderSummaryModel _self;
  final $Res Function(OrderSummaryModel) _then;

/// Create a copy of OrderSummaryModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? subtotal = null,Object? tax = null,Object? shipping = null,Object? total = null,Object? currency = null,Object? itemCount = null,Object? items = null,}) {
  return _then(_self.copyWith(
subtotal: null == subtotal ? _self.subtotal : subtotal // ignore: cast_nullable_to_non_nullable
as double,tax: null == tax ? _self.tax : tax // ignore: cast_nullable_to_non_nullable
as double,shipping: null == shipping ? _self.shipping : shipping // ignore: cast_nullable_to_non_nullable
as double,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,itemCount: null == itemCount ? _self.itemCount : itemCount // ignore: cast_nullable_to_non_nullable
as int,items: null == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as List<OrderItemSummaryModel>,
  ));
}

}


/// Adds pattern-matching-related methods to [OrderSummaryModel].
extension OrderSummaryModelPatterns on OrderSummaryModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OrderSummaryModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OrderSummaryModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OrderSummaryModel value)  $default,){
final _that = this;
switch (_that) {
case _OrderSummaryModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OrderSummaryModel value)?  $default,){
final _that = this;
switch (_that) {
case _OrderSummaryModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double subtotal,  double tax,  double shipping,  double total,  String currency,  int itemCount,  List<OrderItemSummaryModel> items)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OrderSummaryModel() when $default != null:
return $default(_that.subtotal,_that.tax,_that.shipping,_that.total,_that.currency,_that.itemCount,_that.items);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double subtotal,  double tax,  double shipping,  double total,  String currency,  int itemCount,  List<OrderItemSummaryModel> items)  $default,) {final _that = this;
switch (_that) {
case _OrderSummaryModel():
return $default(_that.subtotal,_that.tax,_that.shipping,_that.total,_that.currency,_that.itemCount,_that.items);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double subtotal,  double tax,  double shipping,  double total,  String currency,  int itemCount,  List<OrderItemSummaryModel> items)?  $default,) {final _that = this;
switch (_that) {
case _OrderSummaryModel() when $default != null:
return $default(_that.subtotal,_that.tax,_that.shipping,_that.total,_that.currency,_that.itemCount,_that.items);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _OrderSummaryModel implements OrderSummaryModel {
  const _OrderSummaryModel({required this.subtotal, required this.tax, required this.shipping, required this.total, required this.currency, required this.itemCount, final  List<OrderItemSummaryModel> items = const []}): _items = items;
  factory _OrderSummaryModel.fromJson(Map<String, dynamic> json) => _$OrderSummaryModelFromJson(json);

@override final  double subtotal;
@override final  double tax;
@override final  double shipping;
@override final  double total;
@override final  String currency;
@override final  int itemCount;
 final  List<OrderItemSummaryModel> _items;
@override@JsonKey() List<OrderItemSummaryModel> get items {
  if (_items is EqualUnmodifiableListView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_items);
}


/// Create a copy of OrderSummaryModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderSummaryModelCopyWith<_OrderSummaryModel> get copyWith => __$OrderSummaryModelCopyWithImpl<_OrderSummaryModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OrderSummaryModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderSummaryModel&&(identical(other.subtotal, subtotal) || other.subtotal == subtotal)&&(identical(other.tax, tax) || other.tax == tax)&&(identical(other.shipping, shipping) || other.shipping == shipping)&&(identical(other.total, total) || other.total == total)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.itemCount, itemCount) || other.itemCount == itemCount)&&const DeepCollectionEquality().equals(other._items, _items));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,subtotal,tax,shipping,total,currency,itemCount,const DeepCollectionEquality().hash(_items));

@override
String toString() {
  return 'OrderSummaryModel(subtotal: $subtotal, tax: $tax, shipping: $shipping, total: $total, currency: $currency, itemCount: $itemCount, items: $items)';
}


}

/// @nodoc
abstract mixin class _$OrderSummaryModelCopyWith<$Res> implements $OrderSummaryModelCopyWith<$Res> {
  factory _$OrderSummaryModelCopyWith(_OrderSummaryModel value, $Res Function(_OrderSummaryModel) _then) = __$OrderSummaryModelCopyWithImpl;
@override @useResult
$Res call({
 double subtotal, double tax, double shipping, double total, String currency, int itemCount, List<OrderItemSummaryModel> items
});




}
/// @nodoc
class __$OrderSummaryModelCopyWithImpl<$Res>
    implements _$OrderSummaryModelCopyWith<$Res> {
  __$OrderSummaryModelCopyWithImpl(this._self, this._then);

  final _OrderSummaryModel _self;
  final $Res Function(_OrderSummaryModel) _then;

/// Create a copy of OrderSummaryModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? subtotal = null,Object? tax = null,Object? shipping = null,Object? total = null,Object? currency = null,Object? itemCount = null,Object? items = null,}) {
  return _then(_OrderSummaryModel(
subtotal: null == subtotal ? _self.subtotal : subtotal // ignore: cast_nullable_to_non_nullable
as double,tax: null == tax ? _self.tax : tax // ignore: cast_nullable_to_non_nullable
as double,shipping: null == shipping ? _self.shipping : shipping // ignore: cast_nullable_to_non_nullable
as double,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,itemCount: null == itemCount ? _self.itemCount : itemCount // ignore: cast_nullable_to_non_nullable
as int,items: null == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as List<OrderItemSummaryModel>,
  ));
}


}


/// @nodoc
mixin _$OrderItemSummaryModel {

 String get productId; String get productName; String get productImage; int get quantity; double get price; double get total; String? get category; String? get brand;
/// Create a copy of OrderItemSummaryModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderItemSummaryModelCopyWith<OrderItemSummaryModel> get copyWith => _$OrderItemSummaryModelCopyWithImpl<OrderItemSummaryModel>(this as OrderItemSummaryModel, _$identity);

  /// Serializes this OrderItemSummaryModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderItemSummaryModel&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productImage, productImage) || other.productImage == productImage)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.price, price) || other.price == price)&&(identical(other.total, total) || other.total == total)&&(identical(other.category, category) || other.category == category)&&(identical(other.brand, brand) || other.brand == brand));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,productName,productImage,quantity,price,total,category,brand);

@override
String toString() {
  return 'OrderItemSummaryModel(productId: $productId, productName: $productName, productImage: $productImage, quantity: $quantity, price: $price, total: $total, category: $category, brand: $brand)';
}


}

/// @nodoc
abstract mixin class $OrderItemSummaryModelCopyWith<$Res>  {
  factory $OrderItemSummaryModelCopyWith(OrderItemSummaryModel value, $Res Function(OrderItemSummaryModel) _then) = _$OrderItemSummaryModelCopyWithImpl;
@useResult
$Res call({
 String productId, String productName, String productImage, int quantity, double price, double total, String? category, String? brand
});




}
/// @nodoc
class _$OrderItemSummaryModelCopyWithImpl<$Res>
    implements $OrderItemSummaryModelCopyWith<$Res> {
  _$OrderItemSummaryModelCopyWithImpl(this._self, this._then);

  final OrderItemSummaryModel _self;
  final $Res Function(OrderItemSummaryModel) _then;

/// Create a copy of OrderItemSummaryModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? productId = null,Object? productName = null,Object? productImage = null,Object? quantity = null,Object? price = null,Object? total = null,Object? category = freezed,Object? brand = freezed,}) {
  return _then(_self.copyWith(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,productImage: null == productImage ? _self.productImage : productImage // ignore: cast_nullable_to_non_nullable
as String,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as double,category: freezed == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String?,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [OrderItemSummaryModel].
extension OrderItemSummaryModelPatterns on OrderItemSummaryModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OrderItemSummaryModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OrderItemSummaryModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OrderItemSummaryModel value)  $default,){
final _that = this;
switch (_that) {
case _OrderItemSummaryModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OrderItemSummaryModel value)?  $default,){
final _that = this;
switch (_that) {
case _OrderItemSummaryModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String productId,  String productName,  String productImage,  int quantity,  double price,  double total,  String? category,  String? brand)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OrderItemSummaryModel() when $default != null:
return $default(_that.productId,_that.productName,_that.productImage,_that.quantity,_that.price,_that.total,_that.category,_that.brand);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String productId,  String productName,  String productImage,  int quantity,  double price,  double total,  String? category,  String? brand)  $default,) {final _that = this;
switch (_that) {
case _OrderItemSummaryModel():
return $default(_that.productId,_that.productName,_that.productImage,_that.quantity,_that.price,_that.total,_that.category,_that.brand);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String productId,  String productName,  String productImage,  int quantity,  double price,  double total,  String? category,  String? brand)?  $default,) {final _that = this;
switch (_that) {
case _OrderItemSummaryModel() when $default != null:
return $default(_that.productId,_that.productName,_that.productImage,_that.quantity,_that.price,_that.total,_that.category,_that.brand);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _OrderItemSummaryModel implements OrderItemSummaryModel {
  const _OrderItemSummaryModel({required this.productId, required this.productName, required this.productImage, required this.quantity, required this.price, required this.total, this.category, this.brand});
  factory _OrderItemSummaryModel.fromJson(Map<String, dynamic> json) => _$OrderItemSummaryModelFromJson(json);

@override final  String productId;
@override final  String productName;
@override final  String productImage;
@override final  int quantity;
@override final  double price;
@override final  double total;
@override final  String? category;
@override final  String? brand;

/// Create a copy of OrderItemSummaryModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderItemSummaryModelCopyWith<_OrderItemSummaryModel> get copyWith => __$OrderItemSummaryModelCopyWithImpl<_OrderItemSummaryModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OrderItemSummaryModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderItemSummaryModel&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productImage, productImage) || other.productImage == productImage)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.price, price) || other.price == price)&&(identical(other.total, total) || other.total == total)&&(identical(other.category, category) || other.category == category)&&(identical(other.brand, brand) || other.brand == brand));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,productName,productImage,quantity,price,total,category,brand);

@override
String toString() {
  return 'OrderItemSummaryModel(productId: $productId, productName: $productName, productImage: $productImage, quantity: $quantity, price: $price, total: $total, category: $category, brand: $brand)';
}


}

/// @nodoc
abstract mixin class _$OrderItemSummaryModelCopyWith<$Res> implements $OrderItemSummaryModelCopyWith<$Res> {
  factory _$OrderItemSummaryModelCopyWith(_OrderItemSummaryModel value, $Res Function(_OrderItemSummaryModel) _then) = __$OrderItemSummaryModelCopyWithImpl;
@override @useResult
$Res call({
 String productId, String productName, String productImage, int quantity, double price, double total, String? category, String? brand
});




}
/// @nodoc
class __$OrderItemSummaryModelCopyWithImpl<$Res>
    implements _$OrderItemSummaryModelCopyWith<$Res> {
  __$OrderItemSummaryModelCopyWithImpl(this._self, this._then);

  final _OrderItemSummaryModel _self;
  final $Res Function(_OrderItemSummaryModel) _then;

/// Create a copy of OrderItemSummaryModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? productId = null,Object? productName = null,Object? productImage = null,Object? quantity = null,Object? price = null,Object? total = null,Object? category = freezed,Object? brand = freezed,}) {
  return _then(_OrderItemSummaryModel(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,productImage: null == productImage ? _self.productImage : productImage // ignore: cast_nullable_to_non_nullable
as String,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as double,category: freezed == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String?,brand: freezed == brand ? _self.brand : brand // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$CheckoutStateModel {

 CheckoutStep get currentStep; ShippingAddressModel? get shippingAddress; PaymentMethodModel? get paymentMethod; OrderSummaryModel? get orderSummary; CheckoutResultModel? get result; bool get isLoading; String? get error;
/// Create a copy of CheckoutStateModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CheckoutStateModelCopyWith<CheckoutStateModel> get copyWith => _$CheckoutStateModelCopyWithImpl<CheckoutStateModel>(this as CheckoutStateModel, _$identity);

  /// Serializes this CheckoutStateModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CheckoutStateModel&&(identical(other.currentStep, currentStep) || other.currentStep == currentStep)&&(identical(other.shippingAddress, shippingAddress) || other.shippingAddress == shippingAddress)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.orderSummary, orderSummary) || other.orderSummary == orderSummary)&&(identical(other.result, result) || other.result == result)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,currentStep,shippingAddress,paymentMethod,orderSummary,result,isLoading,error);

@override
String toString() {
  return 'CheckoutStateModel(currentStep: $currentStep, shippingAddress: $shippingAddress, paymentMethod: $paymentMethod, orderSummary: $orderSummary, result: $result, isLoading: $isLoading, error: $error)';
}


}

/// @nodoc
abstract mixin class $CheckoutStateModelCopyWith<$Res>  {
  factory $CheckoutStateModelCopyWith(CheckoutStateModel value, $Res Function(CheckoutStateModel) _then) = _$CheckoutStateModelCopyWithImpl;
@useResult
$Res call({
 CheckoutStep currentStep, ShippingAddressModel? shippingAddress, PaymentMethodModel? paymentMethod, OrderSummaryModel? orderSummary, CheckoutResultModel? result, bool isLoading, String? error
});


$ShippingAddressModelCopyWith<$Res>? get shippingAddress;$PaymentMethodModelCopyWith<$Res>? get paymentMethod;$OrderSummaryModelCopyWith<$Res>? get orderSummary;$CheckoutResultModelCopyWith<$Res>? get result;

}
/// @nodoc
class _$CheckoutStateModelCopyWithImpl<$Res>
    implements $CheckoutStateModelCopyWith<$Res> {
  _$CheckoutStateModelCopyWithImpl(this._self, this._then);

  final CheckoutStateModel _self;
  final $Res Function(CheckoutStateModel) _then;

/// Create a copy of CheckoutStateModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? currentStep = null,Object? shippingAddress = freezed,Object? paymentMethod = freezed,Object? orderSummary = freezed,Object? result = freezed,Object? isLoading = null,Object? error = freezed,}) {
  return _then(_self.copyWith(
currentStep: null == currentStep ? _self.currentStep : currentStep // ignore: cast_nullable_to_non_nullable
as CheckoutStep,shippingAddress: freezed == shippingAddress ? _self.shippingAddress : shippingAddress // ignore: cast_nullable_to_non_nullable
as ShippingAddressModel?,paymentMethod: freezed == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as PaymentMethodModel?,orderSummary: freezed == orderSummary ? _self.orderSummary : orderSummary // ignore: cast_nullable_to_non_nullable
as OrderSummaryModel?,result: freezed == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as CheckoutResultModel?,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of CheckoutStateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ShippingAddressModelCopyWith<$Res>? get shippingAddress {
    if (_self.shippingAddress == null) {
    return null;
  }

  return $ShippingAddressModelCopyWith<$Res>(_self.shippingAddress!, (value) {
    return _then(_self.copyWith(shippingAddress: value));
  });
}/// Create a copy of CheckoutStateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentMethodModelCopyWith<$Res>? get paymentMethod {
    if (_self.paymentMethod == null) {
    return null;
  }

  return $PaymentMethodModelCopyWith<$Res>(_self.paymentMethod!, (value) {
    return _then(_self.copyWith(paymentMethod: value));
  });
}/// Create a copy of CheckoutStateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OrderSummaryModelCopyWith<$Res>? get orderSummary {
    if (_self.orderSummary == null) {
    return null;
  }

  return $OrderSummaryModelCopyWith<$Res>(_self.orderSummary!, (value) {
    return _then(_self.copyWith(orderSummary: value));
  });
}/// Create a copy of CheckoutStateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CheckoutResultModelCopyWith<$Res>? get result {
    if (_self.result == null) {
    return null;
  }

  return $CheckoutResultModelCopyWith<$Res>(_self.result!, (value) {
    return _then(_self.copyWith(result: value));
  });
}
}


/// Adds pattern-matching-related methods to [CheckoutStateModel].
extension CheckoutStateModelPatterns on CheckoutStateModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CheckoutStateModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CheckoutStateModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CheckoutStateModel value)  $default,){
final _that = this;
switch (_that) {
case _CheckoutStateModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CheckoutStateModel value)?  $default,){
final _that = this;
switch (_that) {
case _CheckoutStateModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( CheckoutStep currentStep,  ShippingAddressModel? shippingAddress,  PaymentMethodModel? paymentMethod,  OrderSummaryModel? orderSummary,  CheckoutResultModel? result,  bool isLoading,  String? error)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CheckoutStateModel() when $default != null:
return $default(_that.currentStep,_that.shippingAddress,_that.paymentMethod,_that.orderSummary,_that.result,_that.isLoading,_that.error);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( CheckoutStep currentStep,  ShippingAddressModel? shippingAddress,  PaymentMethodModel? paymentMethod,  OrderSummaryModel? orderSummary,  CheckoutResultModel? result,  bool isLoading,  String? error)  $default,) {final _that = this;
switch (_that) {
case _CheckoutStateModel():
return $default(_that.currentStep,_that.shippingAddress,_that.paymentMethod,_that.orderSummary,_that.result,_that.isLoading,_that.error);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( CheckoutStep currentStep,  ShippingAddressModel? shippingAddress,  PaymentMethodModel? paymentMethod,  OrderSummaryModel? orderSummary,  CheckoutResultModel? result,  bool isLoading,  String? error)?  $default,) {final _that = this;
switch (_that) {
case _CheckoutStateModel() when $default != null:
return $default(_that.currentStep,_that.shippingAddress,_that.paymentMethod,_that.orderSummary,_that.result,_that.isLoading,_that.error);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CheckoutStateModel implements CheckoutStateModel {
  const _CheckoutStateModel({this.currentStep = CheckoutStep.shipping, this.shippingAddress, this.paymentMethod, this.orderSummary, this.result, this.isLoading = false, this.error});
  factory _CheckoutStateModel.fromJson(Map<String, dynamic> json) => _$CheckoutStateModelFromJson(json);

@override@JsonKey() final  CheckoutStep currentStep;
@override final  ShippingAddressModel? shippingAddress;
@override final  PaymentMethodModel? paymentMethod;
@override final  OrderSummaryModel? orderSummary;
@override final  CheckoutResultModel? result;
@override@JsonKey() final  bool isLoading;
@override final  String? error;

/// Create a copy of CheckoutStateModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CheckoutStateModelCopyWith<_CheckoutStateModel> get copyWith => __$CheckoutStateModelCopyWithImpl<_CheckoutStateModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CheckoutStateModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CheckoutStateModel&&(identical(other.currentStep, currentStep) || other.currentStep == currentStep)&&(identical(other.shippingAddress, shippingAddress) || other.shippingAddress == shippingAddress)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.orderSummary, orderSummary) || other.orderSummary == orderSummary)&&(identical(other.result, result) || other.result == result)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,currentStep,shippingAddress,paymentMethod,orderSummary,result,isLoading,error);

@override
String toString() {
  return 'CheckoutStateModel(currentStep: $currentStep, shippingAddress: $shippingAddress, paymentMethod: $paymentMethod, orderSummary: $orderSummary, result: $result, isLoading: $isLoading, error: $error)';
}


}

/// @nodoc
abstract mixin class _$CheckoutStateModelCopyWith<$Res> implements $CheckoutStateModelCopyWith<$Res> {
  factory _$CheckoutStateModelCopyWith(_CheckoutStateModel value, $Res Function(_CheckoutStateModel) _then) = __$CheckoutStateModelCopyWithImpl;
@override @useResult
$Res call({
 CheckoutStep currentStep, ShippingAddressModel? shippingAddress, PaymentMethodModel? paymentMethod, OrderSummaryModel? orderSummary, CheckoutResultModel? result, bool isLoading, String? error
});


@override $ShippingAddressModelCopyWith<$Res>? get shippingAddress;@override $PaymentMethodModelCopyWith<$Res>? get paymentMethod;@override $OrderSummaryModelCopyWith<$Res>? get orderSummary;@override $CheckoutResultModelCopyWith<$Res>? get result;

}
/// @nodoc
class __$CheckoutStateModelCopyWithImpl<$Res>
    implements _$CheckoutStateModelCopyWith<$Res> {
  __$CheckoutStateModelCopyWithImpl(this._self, this._then);

  final _CheckoutStateModel _self;
  final $Res Function(_CheckoutStateModel) _then;

/// Create a copy of CheckoutStateModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? currentStep = null,Object? shippingAddress = freezed,Object? paymentMethod = freezed,Object? orderSummary = freezed,Object? result = freezed,Object? isLoading = null,Object? error = freezed,}) {
  return _then(_CheckoutStateModel(
currentStep: null == currentStep ? _self.currentStep : currentStep // ignore: cast_nullable_to_non_nullable
as CheckoutStep,shippingAddress: freezed == shippingAddress ? _self.shippingAddress : shippingAddress // ignore: cast_nullable_to_non_nullable
as ShippingAddressModel?,paymentMethod: freezed == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as PaymentMethodModel?,orderSummary: freezed == orderSummary ? _self.orderSummary : orderSummary // ignore: cast_nullable_to_non_nullable
as OrderSummaryModel?,result: freezed == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as CheckoutResultModel?,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of CheckoutStateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ShippingAddressModelCopyWith<$Res>? get shippingAddress {
    if (_self.shippingAddress == null) {
    return null;
  }

  return $ShippingAddressModelCopyWith<$Res>(_self.shippingAddress!, (value) {
    return _then(_self.copyWith(shippingAddress: value));
  });
}/// Create a copy of CheckoutStateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentMethodModelCopyWith<$Res>? get paymentMethod {
    if (_self.paymentMethod == null) {
    return null;
  }

  return $PaymentMethodModelCopyWith<$Res>(_self.paymentMethod!, (value) {
    return _then(_self.copyWith(paymentMethod: value));
  });
}/// Create a copy of CheckoutStateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OrderSummaryModelCopyWith<$Res>? get orderSummary {
    if (_self.orderSummary == null) {
    return null;
  }

  return $OrderSummaryModelCopyWith<$Res>(_self.orderSummary!, (value) {
    return _then(_self.copyWith(orderSummary: value));
  });
}/// Create a copy of CheckoutStateModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CheckoutResultModelCopyWith<$Res>? get result {
    if (_self.result == null) {
    return null;
  }

  return $CheckoutResultModelCopyWith<$Res>(_self.result!, (value) {
    return _then(_self.copyWith(result: value));
  });
}
}

// dart format on
