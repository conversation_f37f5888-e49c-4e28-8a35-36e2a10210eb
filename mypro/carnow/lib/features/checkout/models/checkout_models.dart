import 'package:freezed_annotation/freezed_annotation.dart';

part 'checkout_models.freezed.dart';
part 'checkout_models.g.dart';

/// Checkout Request Model for CarNow multi-product platform
/// Supports all product types: cars, clothes, electronics, home, sports, etc.
@freezed
abstract class CheckoutRequestModel with _$CheckoutRequestModel {
  const factory CheckoutRequestModel({
    required String cartId,
    required ShippingAddressModel shippingAddress,
    required String paymentMethodId,
    @Default('USD') String currency,
    @Default({}) Map<String, String> metadata,
  }) = _CheckoutRequestModel;

  factory CheckoutRequestModel.fromJson(Map<String, dynamic> json) =>
      _$CheckoutRequestModelFromJson(json);
}

/// Shipping Address Model
@freezed
abstract class ShippingAddressModel with _$ShippingAddressModel {
  const factory ShippingAddressModel({
    required String name,
    required String phone,
    required String addressLine,
    required String city,
    required String state,
    required String postalCode,
    @Default('SA') String country, // Default to Saudi Arabia
  }) = _ShippingAddressModel;

  factory ShippingAddressModel.fromJson(Map<String, dynamic> json) =>
      _$ShippingAddressModelFromJson(json);
}

/// Checkout Result Model
@freezed
abstract class CheckoutResultModel with _$CheckoutResultModel {
  const factory CheckoutResultModel({
    required bool success,
    String? orderId,
    String? paymentIntentId,
    String? clientSecret,
    required String status,
    required String message,
    String? error,
  }) = _CheckoutResultModel;

  factory CheckoutResultModel.fromJson(Map<String, dynamic> json) =>
      _$CheckoutResultModelFromJson(json);
}

/// Payment Method Model
@freezed
abstract class PaymentMethodModel with _$PaymentMethodModel {
  const factory PaymentMethodModel({
    required String id,
    required String type, // 'card', 'wallet', 'bank_transfer'
    required String displayName,
    String? last4,
    String? brand,
    String? expiryMonth,
    String? expiryYear,
    @Default(true) bool isEnabled,
    @Default(false) bool isDefault,
  }) = _PaymentMethodModel;

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentMethodModelFromJson(json);
}

/// Order Summary Model for checkout display
@freezed
abstract class OrderSummaryModel with _$OrderSummaryModel {
  const factory OrderSummaryModel({
    required double subtotal,
    required double tax,
    required double shipping,
    required double total,
    required String currency,
    required int itemCount,
    @Default([]) List<OrderItemSummaryModel> items,
  }) = _OrderSummaryModel;

  factory OrderSummaryModel.fromJson(Map<String, dynamic> json) =>
      _$OrderSummaryModelFromJson(json);
}

/// Order Item Summary for checkout display
@freezed
abstract class OrderItemSummaryModel with _$OrderItemSummaryModel {
  const factory OrderItemSummaryModel({
    required String productId,
    required String productName,
    required String productImage,
    required int quantity,
    required double price,
    required double total,
    String? category,
    String? brand,
  }) = _OrderItemSummaryModel;

  factory OrderItemSummaryModel.fromJson(Map<String, dynamic> json) =>
      _$OrderItemSummaryModelFromJson(json);
}

/// Checkout Step Enum
enum CheckoutStep {
  shipping,
  payment,
  review,
  processing,
  completed,
  failed,
}

/// Checkout State Model
@freezed
abstract class CheckoutStateModel with _$CheckoutStateModel {
  const factory CheckoutStateModel({
    @Default(CheckoutStep.shipping) CheckoutStep currentStep,
    ShippingAddressModel? shippingAddress,
    PaymentMethodModel? paymentMethod,
    OrderSummaryModel? orderSummary,
    CheckoutResultModel? result,
    @Default(false) bool isLoading,
    String? error,
  }) = _CheckoutStateModel;

  factory CheckoutStateModel.fromJson(Map<String, dynamic> json) =>
      _$CheckoutStateModelFromJson(json);
}

/// Extensions for CheckoutStateModel
extension CheckoutStateModelExtensions on CheckoutStateModel {
  /// Check if current step is completed
  bool isStepCompleted(CheckoutStep step) {
    switch (step) {
      case CheckoutStep.shipping:
        return shippingAddress != null;
      case CheckoutStep.payment:
        return paymentMethod != null;
      case CheckoutStep.review:
        return orderSummary != null;
      case CheckoutStep.processing:
        return result != null;
      case CheckoutStep.completed:
        return result?.success == true;
      case CheckoutStep.failed:
        return result?.success == false;
    }
  }

  /// Check if can proceed to next step
  bool canProceedToNextStep() {
    switch (currentStep) {
      case CheckoutStep.shipping:
        return shippingAddress != null;
      case CheckoutStep.payment:
        return paymentMethod != null;
      case CheckoutStep.review:
        return orderSummary != null;
      case CheckoutStep.processing:
        return false; // Wait for processing to complete
      case CheckoutStep.completed:
      case CheckoutStep.failed:
        return false; // Final states
    }
  }

  /// Get next step
  CheckoutStep? getNextStep() {
    if (!canProceedToNextStep()) return null;

    switch (currentStep) {
      case CheckoutStep.shipping:
        return CheckoutStep.payment;
      case CheckoutStep.payment:
        return CheckoutStep.review;
      case CheckoutStep.review:
        return CheckoutStep.processing;
      case CheckoutStep.processing:
      case CheckoutStep.completed:
      case CheckoutStep.failed:
        return null;
    }
  }

  /// Get step title in Arabic
  String getStepTitle(CheckoutStep step) {
    switch (step) {
      case CheckoutStep.shipping:
        return 'عنوان التسليم';
      case CheckoutStep.payment:
        return 'طريقة الدفع';
      case CheckoutStep.review:
        return 'مراجعة الطلب';
      case CheckoutStep.processing:
        return 'معالجة الطلب';
      case CheckoutStep.completed:
        return 'تم الطلب';
      case CheckoutStep.failed:
        return 'فشل الطلب';
    }
  }

  /// Get step description in Arabic
  String getStepDescription(CheckoutStep step) {
    switch (step) {
      case CheckoutStep.shipping:
        return 'أدخل عنوان التسليم';
      case CheckoutStep.payment:
        return 'اختر طريقة الدفع';
      case CheckoutStep.review:
        return 'راجع تفاصيل طلبك';
      case CheckoutStep.processing:
        return 'جاري معالجة طلبك...';
      case CheckoutStep.completed:
        return 'تم إنشاء طلبك بنجاح';
      case CheckoutStep.failed:
        return 'حدث خطأ في معالجة الطلب';
    }
  }
}
