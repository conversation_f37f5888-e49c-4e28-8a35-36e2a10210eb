import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/theme/carnow_colors.dart';

/// Order Success Screen following Forever Plan architecture
/// 
/// Displays order confirmation and success message
/// with proper Material 3 design and accessibility
class OrderSuccessScreen extends ConsumerWidget {
  const OrderSuccessScreen({
    super.key,
    this.orderData,
  });

  final Map<String, dynamic>? orderData;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Success Animation/Icon
              _buildSuccessIcon(context),
              
              const SizedBox(height: 32),
              
              // Success Title
              Text(
                'تم إنشاء الطلب بنجاح!',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green[700],
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              // Success Message
              Text(
                'شكراً لك على طلبك من CarNow.\nسيتم معالجة طلبك وشحنه في أقرب وقت ممكن.',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
              
              // Order Details Card
              if (orderData != null) _buildOrderDetailsCard(context),
              
              const SizedBox(height: 32),
              
              // Action Buttons
              _buildActionButtons(context),
              
              const SizedBox(height: 24),
              
              // Additional Info
              _buildAdditionalInfo(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSuccessIcon(BuildContext context) {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: Colors.green[50],
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.green[200]!,
          width: 2,
        ),
      ),
      child: Icon(
        Icons.check_circle_outline,
        size: 64,
        color: Colors.green[700],
      ),
    );
  }

  Widget _buildOrderDetailsCard(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  color: CarnowColors.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'تفاصيل الطلب',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Order ID
            if (orderData!['order_id'] != null)
              _buildDetailRow(
                context,
                'رقم الطلب',
                '#${orderData!['order_id']}',
                isHighlighted: true,
              ),
            
            const SizedBox(height: 8),
            
            // Total Amount
            if (orderData!['total_amount'] != null)
              _buildDetailRow(
                context,
                'المبلغ الإجمالي',
                '${orderData!['total_amount'].toStringAsFixed(3)} د.ل',
                isHighlighted: true,
              ),
            
            const SizedBox(height: 8),
            
            // Status
            if (orderData!['status'] != null)
              _buildDetailRow(
                context,
                'حالة الطلب',
                _getStatusText(orderData!['status']),
              ),
            
            const SizedBox(height: 8),
            
            // Payment Status
            if (orderData!['payment_status'] != null)
              _buildDetailRow(
                context,
                'حالة الدفع',
                _getPaymentStatusText(orderData!['payment_status']),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value, {
    bool isHighlighted = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: isHighlighted ? FontWeight.bold : FontWeight.w500,
            color: isHighlighted ? CarnowColors.primary : null,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // Primary Action - View Orders
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => _navigateToOrders(context),
            icon: const Icon(Icons.list_alt),
            label: const Text('عرض طلباتي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: CarnowColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Secondary Action - Continue Shopping
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _navigateToHome(context),
            icon: const Icon(Icons.shopping_bag_outlined),
            label: const Text('متابعة التسوق'),
            style: OutlinedButton.styleFrom(
              foregroundColor: CarnowColors.primary,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAdditionalInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.blue[700],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'معلومات مهمة',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '• سيتم إرسال تأكيد الطلب إلى بريدك الإلكتروني\n'
            '• يمكنك متابعة حالة الطلب من قسم "طلباتي"\n'
            '• سيتم التواصل معك قبل التسليم',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.blue[700],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'تم التأكيد';
      case 'processing':
        return 'جاري المعالجة';
      case 'shipped':
        return 'تم الشحن';
      case 'delivered':
        return 'تم التسليم';
      case 'cancelled':
        return 'تم الإلغاء';
      default:
        return status;
    }
  }

  String _getPaymentStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'تم الدفع';
      case 'pending':
        return 'في الانتظار';
      case 'failed':
        return 'فشل الدفع';
      case 'refunded':
        return 'تم الاسترداد';
      default:
        return status;
    }
  }

  void _navigateToOrders(BuildContext context) {
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/orders',
      (route) => route.isFirst,
    );
  }

  void _navigateToHome(BuildContext context) {
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/',
      (route) => false,
    );
  }
}
