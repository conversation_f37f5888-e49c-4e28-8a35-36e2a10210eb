import '../../../core/networking/simple_api_client.dart';
import '../../../core/utils/unified_logger.dart';
import '../models/checkout_models.dart';

/// Checkout Service for CarNow multi-product platform
/// Handles checkout process for all product types: cars, clothes, electronics, etc.
/// Following Forever Plan Architecture - real data only from Supabase via Go API
class CheckoutService {
  final SimpleApiClient _apiClient;

  CheckoutService({required SimpleApiClient apiClient}) : _apiClient = apiClient;

  /// Process checkout for multi-product cart
  Future<CheckoutResultModel> processCheckout({
    required String cartId,
    required ShippingAddressModel shippingAddress,
    required String paymentMethodId,
    String currency = 'USD',
    Map<String, String>? metadata,
  }) async {
    try {
      UnifiedLogger.info('Processing checkout for CarNow platform - cart_id: $cartId, currency: $currency');

      final request = CheckoutRequestModel(
        cartId: cartId,
        shippingAddress: shippingAddress,
        paymentMethodId: paymentMethodId,
        currency: currency,
        metadata: metadata ?? {},
      );

      final response = await _apiClient.post(
        '/checkout',
        data: request.toJson(),
      );

      if (response.isSuccess) {
        final result = CheckoutResultModel.fromJson(response.data);

        UnifiedLogger.info('Checkout processed successfully - order_id: ${result.orderId}');

        return result;
      } else {
        final errorMessage = response.error ?? 'Checkout failed';
        UnifiedLogger.error('Checkout processing failed', error: errorMessage);
        
        return CheckoutResultModel(
          success: false,
          status: 'failed',
          message: 'Checkout failed',
          error: errorMessage,
        );
      }
    } catch (e) {
      UnifiedLogger.error('Checkout service error', error: e);
      return CheckoutResultModel(
        success: false,
        status: 'error',
        message: 'An error occurred during checkout',
        error: e.toString(),
      );
    }
  }

  /// Confirm checkout after payment
  Future<CheckoutResultModel> confirmCheckout({
    required String paymentIntentId,
  }) async {
    try {
      UnifiedLogger.info('Confirming checkout - payment_intent_id: $paymentIntentId');

      final response = await _apiClient.post(
        '/checkout/confirm',
        data: {
          'payment_intent_id': paymentIntentId,
        },
      );

      if (response.isSuccess) {
        final result = CheckoutResultModel.fromJson(response.data);

        UnifiedLogger.info('Checkout confirmed successfully - payment_intent_id: $paymentIntentId');

        return result;
      } else {
        final errorMessage = response.error ?? 'Checkout confirmation failed';
        UnifiedLogger.error('Checkout confirmation failed', error: errorMessage);
        
        return CheckoutResultModel(
          success: false,
          status: 'failed',
          message: 'Checkout confirmation failed',
          error: errorMessage,
        );
      }
    } catch (e) {
      UnifiedLogger.error('Checkout confirmation error', error: e);
      return CheckoutResultModel(
        success: false,
        status: 'error',
        message: 'An error occurred during checkout confirmation',
        error: e.toString(),
      );
    }
  }

  /// Get checkout status
  Future<Map<String, dynamic>> getCheckoutStatus({
    required String paymentIntentId,
  }) async {
    try {
      UnifiedLogger.info('Getting checkout status - payment_intent_id: $paymentIntentId');

      final response = await _apiClient.get(
        '/checkout/status/$paymentIntentId',
      );

      if (response.isSuccess) {
        UnifiedLogger.info('Checkout status retrieved successfully');
        return response.data;
      } else {
        final errorMessage = response.error ?? 'Failed to get checkout status';
        UnifiedLogger.error('Failed to get checkout status', error: errorMessage);
        throw Exception(errorMessage);
      }
    } catch (e) {
      UnifiedLogger.error('Get checkout status error', error: e);
      rethrow;
    }
  }

  /// Calculate order summary from cart
  Future<OrderSummaryModel> calculateOrderSummary({
    required String cartId,
  }) async {
    try {
      UnifiedLogger.info('Calculating order summary - cart_id: $cartId');

      // TODO: Get cart details from cart service
      // For now, return a placeholder
      return const OrderSummaryModel(
        subtotal: 0.0,
        tax: 0.0,
        shipping: 0.0,
        total: 0.0,
        currency: 'USD',
        itemCount: 0,
        items: [],
      );
    } catch (e) {
      UnifiedLogger.error('Calculate order summary error', error: e);
      rethrow;
    }
  }

  /// Get available payment methods
  Future<List<PaymentMethodModel>> getPaymentMethods() async {
    try {
      UnifiedLogger.info('Getting available payment methods');

      // TODO: Get from API
      // For now, return default payment methods
      return [
        const PaymentMethodModel(
          id: 'card',
          type: 'card',
          displayName: 'بطاقة ائتمان',
          isEnabled: true,
          isDefault: true,
        ),
        const PaymentMethodModel(
          id: 'wallet',
          type: 'wallet',
          displayName: 'محفظة إلكترونية',
          isEnabled: true,
          isDefault: false,
        ),
        const PaymentMethodModel(
          id: 'bank_transfer',
          type: 'bank_transfer',
          displayName: 'تحويل بنكي',
          isEnabled: true,
          isDefault: false,
        ),
      ];
    } catch (e) {
      UnifiedLogger.error('Get payment methods error', error: e);
      rethrow;
    }
  }

  /// Validate shipping address
  bool validateShippingAddress(ShippingAddressModel address) {
    return address.name.isNotEmpty &&
           address.phone.isNotEmpty &&
           address.addressLine.isNotEmpty &&
           address.city.isNotEmpty &&
           address.state.isNotEmpty &&
           address.postalCode.isNotEmpty;
  }

  /// Format currency amount
  String formatCurrency(double amount, String currency) {
    switch (currency) {
      case 'SAR':
        return '${amount.toStringAsFixed(2)} ر.س';
      case 'USD':
        return '\$${amount.toStringAsFixed(2)}';
      case 'EUR':
        return '€${amount.toStringAsFixed(2)}';
      default:
        return '${amount.toStringAsFixed(2)} $currency';
    }
  }

  /// Get supported currencies for CarNow platform
  List<String> getSupportedCurrencies() {
    return ['USD', 'SAR', 'EUR', 'AED'];
  }

  /// Get currency symbol
  String getCurrencySymbol(String currency) {
    switch (currency) {
      case 'SAR':
        return 'ر.س';
      case 'USD':
        return '\$';
      case 'EUR':
        return '€';
      case 'AED':
        return 'د.إ';
      default:
        return currency;
    }
  }
}
