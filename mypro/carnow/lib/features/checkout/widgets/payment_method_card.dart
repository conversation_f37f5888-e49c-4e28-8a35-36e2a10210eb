import 'package:flutter/material.dart';

import '../../../core/theme/carnow_colors.dart';

/// Payment Method Card following Forever Plan architecture
/// 
/// Integrates with existing wallet system
/// Shows wallet balance and payment options
class PaymentMethodCard extends StatelessWidget {
  const PaymentMethodCard({
    super.key,
    required this.selectedMethod,
    required this.walletBalance,
    required this.orderTotal,
    required this.onMethodChanged,
  });

  final String selectedMethod;
  final double walletBalance;
  final double orderTotal;
  final Function(String method) onMethodChanged;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.payment,
                  color: CarnowColors.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'طريقة الدفع',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Wallet Payment Option
            _buildPaymentOption(
              context,
              method: 'wallet',
              title: 'الدفع من المحفظة',
              subtitle: 'الرصيد المتاح: ${walletBalance.toStringAsFixed(3)} د.ل',
              icon: Icons.account_balance_wallet,
              isSelected: selectedMethod == 'wallet',
              isEnabled: walletBalance >= orderTotal,
            ),
            
            const SizedBox(height: 12),
            
            // Balance Status
            _buildBalanceStatus(context),
            
            // Future payment methods can be added here
            // const SizedBox(height: 12),
            // _buildPaymentOption(
            //   context,
            //   method: 'card',
            //   title: 'بطاقة ائتمان',
            //   subtitle: 'Visa, Mastercard',
            //   icon: Icons.credit_card,
            //   isSelected: selectedMethod == 'card',
            //   isEnabled: false, // Not implemented yet
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentOption(
    BuildContext context, {
    required String method,
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isSelected,
    required bool isEnabled,
  }) {
    return InkWell(
      onTap: isEnabled ? () => onMethodChanged(method) : null,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected
                ? CarnowColors.primary
                : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isEnabled 
              ? (isSelected ? CarnowColors.primary.withValues(alpha: 0.05) : null)
              : Colors.grey[100],
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isEnabled 
                  ? (isSelected ? CarnowColors.primary : Colors.grey[600])
                  : Colors.grey[400],
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: isEnabled ? null : Colors.grey[500],
                      fontWeight: isSelected ? FontWeight.bold : null,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isEnabled ? Colors.grey[600] : Colors.grey[400],
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: CarnowColors.primary,
              )
            else if (!isEnabled)
              Icon(
                Icons.lock_outline,
                color: Colors.grey[400],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceStatus(BuildContext context) {
    final hasEnoughBalance = walletBalance >= orderTotal;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: hasEnoughBalance 
            ? Colors.green[50] 
            : Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: hasEnoughBalance 
              ? Colors.green[200]! 
              : Colors.red[200]!,
        ),
      ),
      child: Row(
        children: [
          Icon(
            hasEnoughBalance 
                ? Icons.check_circle_outline 
                : Icons.warning_outlined,
            color: hasEnoughBalance 
                ? Colors.green[700] 
                : Colors.red[700],
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              hasEnoughBalance
                  ? 'الرصيد كافي لإتمام الطلب'
                  : 'الرصيد غير كافي. يرجى شحن المحفظة أولاً',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: hasEnoughBalance 
                    ? Colors.green[700] 
                    : Colors.red[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (!hasEnoughBalance)
            TextButton(
              onPressed: () {
                // TODO: Navigate to wallet recharge screen
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('سيتم إضافة شحن المحفظة قريباً'),
                  ),
                );
              },
              child: const Text('شحن المحفظة'),
            ),
        ],
      ),
    );
  }
}
