import 'package:flutter/material.dart';

import '../../../core/theme/carnow_colors.dart';

/// Shipping Address Card following Forever Plan architecture
/// 
/// Collects shipping address information for order delivery
class ShippingAddressCard extends StatefulWidget {
  const ShippingAddressCard({
    super.key,
    required this.onAddressChanged,
  });

  final Function(Map<String, dynamic> address) onAddressChanged;

  @override
  State<ShippingAddressCard> createState() => _ShippingAddressCardState();
}

class _ShippingAddressCardState extends State<ShippingAddressCard> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _cityController = TextEditingController();
  final _areaController = TextEditingController();
  final _streetController = TextEditingController();
  final _buildingController = TextEditingController();
  final _notesController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _cityController.dispose();
    _areaController.dispose();
    _streetController.dispose();
    _buildingController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.location_on_outlined,
                    color: CarnowColors.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'عنوان الشحن',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Name and Phone Row
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'الاسم الكامل *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.person_outline),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'الاسم مطلوب';
                        }
                        return null;
                      },
                      onChanged: _updateAddress,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextFormField(
                      controller: _phoneController,
                      decoration: const InputDecoration(
                        labelText: 'رقم الهاتف *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.phone_outlined),
                      ),
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'رقم الهاتف مطلوب';
                        }
                        if (value.length < 10) {
                          return 'رقم الهاتف غير صحيح';
                        }
                        return null;
                      },
                      onChanged: _updateAddress,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // City and Area Row
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _cityController,
                      decoration: const InputDecoration(
                        labelText: 'المدينة *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.location_city_outlined),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'المدينة مطلوبة';
                        }
                        return null;
                      },
                      onChanged: _updateAddress,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextFormField(
                      controller: _areaController,
                      decoration: const InputDecoration(
                        labelText: 'المنطقة *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.map_outlined),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'المنطقة مطلوبة';
                        }
                        return null;
                      },
                      onChanged: _updateAddress,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Street Address
              TextFormField(
                controller: _streetController,
                decoration: const InputDecoration(
                  labelText: 'اسم الشارع *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.location_on_outlined),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'اسم الشارع مطلوب';
                  }
                  return null;
                },
                onChanged: _updateAddress,
              ),
              
              const SizedBox(height: 16),
              
              // Building/House Number
              TextFormField(
                controller: _buildingController,
                decoration: const InputDecoration(
                  labelText: 'رقم المبنى/المنزل',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.home_outlined),
                ),
                onChanged: _updateAddress,
              ),
              
              const SizedBox(height: 16),
              
              // Additional Notes
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات إضافية',
                  hintText: 'معالم مميزة، تعليمات خاصة للتوصيل...',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.note_outlined),
                ),
                maxLines: 2,
                onChanged: _updateAddress,
              ),
              
              const SizedBox(height: 12),
              
              // Required fields note
              Text(
                '* الحقول المطلوبة',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _updateAddress([String? value]) {
    if (_formKey.currentState?.validate() ?? false) {
      final address = {
        'name': _nameController.text.trim(),
        'phone': _phoneController.text.trim(),
        'city': _cityController.text.trim(),
        'area': _areaController.text.trim(),
        'street': _streetController.text.trim(),
        'building': _buildingController.text.trim(),
        'notes': _notesController.text.trim(),
      };
      
      // Only call callback if all required fields are filled
      if (address['name']!.isNotEmpty &&
          address['phone']!.isNotEmpty &&
          address['city']!.isNotEmpty &&
          address['area']!.isNotEmpty &&
          address['street']!.isNotEmpty) {
        widget.onAddressChanged(address);
      }
    }
  }
}
