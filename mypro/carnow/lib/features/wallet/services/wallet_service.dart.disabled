import 'package:logging/logging.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/simple_auth_system.dart';
import '../../../core/errors/app_error.dart';
import '../models/wallet_model.dart';
import '../models/wallet_transaction_model.dart';

final _logger = Logger('WalletService');

/// Clean Wallet Service following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// ✅ Uses SimpleApiClient for ALL operations
/// ✅ Uses SimpleAuthSystem for authentication
/// ✅ NO direct Supabase calls
/// ✅ Clean error handling with user-friendly messages
class WalletService {
  WalletService(this._ref);
  
  final Ref _ref;

  /// Get authenticated API client
  SimpleApiClient get _apiClient => _ref.read(simpleApiClientProvider);

  /// Check if user is authenticated
  bool get _isAuthenticated => _ref.read(isAuthenticatedProvider);

  /// Get user wallet - Uses Go backend API
  Future<WalletModel?> getUserWallet() async {
    try {
      if (!_isAuthenticated) {
        _logger.warning('No authenticated user for wallet request');
        return null;
      }

      _logger.info('🔍 WalletService: Getting user wallet from backend...');

      final response = await _apiClient.getApi<Map<String, dynamic>>(
        '/wallets/user',
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final walletData = response.data!;
        _logger.info('✅ WalletService: Wallet retrieved successfully');
        return WalletModel.fromJson(walletData);
      } else {
        final errorMessage = response.message.isNotEmpty ? response.message : 'Failed to get wallet';
        _logger.warning('❌ WalletService: Failed to get wallet: $errorMessage');
        return null;
      }
    } catch (e) {
      _logger.severe('❌ WalletService: Unexpected error in getUserWallet: $e');
      // Return null instead of throwing to prevent app crashes
      return null;
    }
  }

  /// Get wallet transactions - Uses Go backend API with pagination
  Future<List<WalletTransactionModel>> getTransactions({
    int page = 1,
    int limit = 20,
    String? status,
  }) async {
    try {
      if (!_isAuthenticated) {
        _logger.warning('No authenticated user for transactions request');
        return [];
      }

      _logger.info('🔍 WalletService: Getting transactions from backend (page: $page, limit: $limit)...');

      final queryParams = <String, dynamic>{
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      if (status != null && status.isNotEmpty) {
        queryParams['status'] = status;
      }

      final response = await _apiClient.getApi<Map<String, dynamic>>(
        '/wallets/transactions',
        queryParameters: queryParams,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final transactionsData = data['transactions'] as List<dynamic>? ?? [];
        
        final transactions = transactionsData
            .map((item) => WalletTransactionModel.fromJson(item as Map<String, dynamic>))
            .toList();

        _logger.info('✅ WalletService: Retrieved ${transactions.length} transactions');
        return transactions;
      } else {
        final errorMessage = response.message.isNotEmpty ? response.message : 'Failed to get transactions';
        _logger.warning('❌ WalletService: Failed to get transactions: $errorMessage');
        return [];
      }
    } catch (e) {
      _logger.severe('❌ WalletService: Unexpected error in getTransactions: $e');
      // Return empty list instead of throwing to prevent app crashes
      return [];
    }
  }

  /// Deposit to wallet - Uses Go backend API
  Future<WalletTransactionModel?> deposit({
    required double amount,
    required String paymentMethod,
    required String paymentReference,
    String? description,
  }) async {
    try {
      if (!_isAuthenticated) {
        throw AppError.authentication(message: 'المستخدم غير مسجل الدخول');
      }

      _logger.info('🔍 WalletService: Creating deposit transaction (amount: $amount)...');

      final response = await _apiClient.postApi<Map<String, dynamic>>(
        '/wallets/transactions',
        data: {
          'amount': amount,
          'type': 'deposit',
          'description': description ?? 'إيداع - $paymentMethod',
        },
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final transactionData = data['transaction'] as Map<String, dynamic>? ?? data;

        final transaction = WalletTransactionModel.fromJson(transactionData);
        _logger.info('✅ WalletService: Deposit transaction created successfully');
        return transaction;
      } else {
        final errorMessage = response.message.isNotEmpty ? response.message : 'فشل في إيداع المبلغ';
        _logger.severe('❌ WalletService: Deposit failed: $errorMessage');
        throw AppError.network(message: errorMessage);
      }
    } catch (e) {
      _logger.severe('❌ WalletService: Deposit error: $e');
      if (e is AppError) {
        rethrow;
      } else {
        throw AppError.network(
          message: 'فشل في إيداع المبلغ',
          originalError: e,
        );
      }
    }
  }

  /// Withdraw from wallet - Uses Go backend API
  Future<WalletTransactionModel?> withdraw({
    required double amount,
    required String bankName,
    required String accountNumber,
    required String accountHolderName,
    required String iban,
    String? swiftCode,
  }) async {
    try {
      if (!_isAuthenticated) {
        throw AppError.authentication(message: 'المستخدم غير مسجل الدخول');
      }

      _logger.info('🔍 WalletService: Creating withdrawal transaction (amount: $amount)...');

      final response = await _apiClient.postApi<Map<String, dynamic>>(
        '/wallets/transactions',
        data: {
          'amount': amount,
          'type': 'withdraw',
          'description': 'سحب إلى $bankName - $accountNumber',
        },
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final transactionData = data['transaction'] as Map<String, dynamic>? ?? data;

        final transaction = WalletTransactionModel.fromJson(transactionData);
        _logger.info('✅ WalletService: Withdrawal transaction created successfully');
        return transaction;
      } else {
        final errorMessage = response.message.isNotEmpty ? response.message : 'فشل في سحب المبلغ';
        _logger.severe('❌ WalletService: Withdrawal failed: $errorMessage');
        throw AppError.network(message: errorMessage);
      }
    } catch (e) {
      _logger.severe('❌ WalletService: Withdrawal error: $e');
      if (e is AppError) {
        rethrow;
      } else {
        throw AppError.network(
          message: 'فشل في سحب المبلغ',
          originalError: e,
        );
      }
    }
  }

  /// Transfer to another user - Not implemented in backend yet
  Future<void> transferToUser({
    required String toUserId,
    required double amount,
    String? description,
  }) async {
    try {
      if (!_isAuthenticated) {
        throw AppError.authentication(message: 'المستخدم غير مسجل الدخول');
      }

      // This endpoint is not yet implemented in the Go backend.
      // Throwing an unimplemented error is better than a 404.
      throw UnimplementedError('User-to-user transfer is not supported yet.');
    } catch (e) {
      _logger.severe('❌ WalletService: Transfer error: $e');
      if (e is AppError) {
        rethrow;
      } else {
        throw AppError.network(
          message: 'فشل في التحويل',
          originalError: e,
        );
      }
    }
  }

  /// Create wallet - Not needed as backend auto-creates wallets
  @Deprecated('Wallet creation is handled automatically by the backend')
  Future<WalletModel> createWallet({String currency = 'LYD'}) async {
    throw UnimplementedError(
        'Wallet creation is handled automatically by the backend.');
  }
}

/// Provider for WalletService following Forever Plan architecture
final walletServiceProvider = Provider<WalletService>((ref) {
  return WalletService(ref);
});
