import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/utils/unified_logger.dart';
import '../models/wallet_model.dart';

/// Wallet repository provider
final walletRepositoryProvider = Provider<WalletRepository>((ref) {
  return WalletRepository(ref.read(simpleApiClientProvider));
});

/// Wallet repository
class WalletRepository {
  final SimpleApiClient _apiClient;

  WalletRepository(this._apiClient);

  /// Get user wallet
  Future<WalletModel> getWallet() async {
    try {
      UnifiedLogger.info('Getting user wallet');

      final response = await _apiClient.getApi('/wallet');

      if (response.isSuccess && response.data != null) {
        final wallet = WalletModel.fromJson(response.data!);
        UnifiedLogger.info('Wallet retrieved successfully: ${wallet.balance}');
        return wallet;
      } else {
        throw Exception('Failed to get wallet: ${response.message}');
      }
    } catch (e) {
      UnifiedLogger.error('Error getting wallet', error: e);
      rethrow;
    }
  }

  /// Add funds to wallet
  Future<WalletModel> addFunds(double amount) async {
    try {
      UnifiedLogger.info('Adding funds to wallet: $amount');

      final response = await _apiClient.postApi('/wallet/add-funds', data: {
        'amount': amount,
      });

      if (response.isSuccess && response.data != null) {
        final wallet = WalletModel.fromJson(response.data!);
        UnifiedLogger.info('Funds added successfully: ${wallet.balance}');
        return wallet;
      } else {
        throw Exception('Failed to add funds: ${response.message}');
      }
    } catch (e) {
      UnifiedLogger.error('Error adding funds', error: e);
      rethrow;
    }
  }

  /// Deduct funds from wallet
  Future<WalletModel> deductFunds(double amount) async {
    try {
      UnifiedLogger.info('Deducting funds from wallet: $amount');

      final response = await _apiClient.postApi('/wallet/deduct-funds', data: {
        'amount': amount,
      });

      if (response.isSuccess && response.data != null) {
        final wallet = WalletModel.fromJson(response.data!);
        UnifiedLogger.info('Funds deducted successfully: ${wallet.balance}');
        return wallet;
      } else {
        throw Exception('Failed to deduct funds: ${response.message}');
      }
    } catch (e) {
      UnifiedLogger.error('Error deducting funds', error: e);
      rethrow;
    }
  }

  /// Get wallet transactions
  Future<List<Map<String, dynamic>>> getTransactions({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      UnifiedLogger.info('Getting wallet transactions: page $page, limit $limit');

      final response = await _apiClient.getApi('/wallet/transactions', queryParameters: {
        'page': page.toString(),
        'limit': limit.toString(),
      });

      if (response.isSuccess && response.data != null) {
        final List<dynamic> transactionsJson = response.data!['transactions'] ?? [];
        final transactions = transactionsJson
            .map((json) => json as Map<String, dynamic>)
            .toList();

        UnifiedLogger.info('Wallet transactions retrieved: ${transactions.length}');
        return transactions;
      } else {
        throw Exception('Failed to get transactions: ${response.message}');
      }
    } catch (e) {
      UnifiedLogger.error('Error getting wallet transactions', error: e);
      rethrow;
    }
  }

  /// Transfer funds to another wallet
  Future<WalletModel> transferFunds({
    required String recipientId,
    required double amount,
    String? description,
  }) async {
    try {
      UnifiedLogger.info('Transferring funds: $amount to $recipientId');

      final response = await _apiClient.postApi('/wallet/transfer', data: {
        'recipient_id': recipientId,
        'amount': amount,
        'description': description,
      });

      if (response.isSuccess && response.data != null) {
        final wallet = WalletModel.fromJson(response.data!);
        UnifiedLogger.info('Funds transferred successfully: ${wallet.balance}');
        return wallet;
      } else {
        throw Exception('Failed to transfer funds: ${response.message}');
      }
    } catch (e) {
      UnifiedLogger.error('Error transferring funds', error: e);
      rethrow;
    }
  }
}
