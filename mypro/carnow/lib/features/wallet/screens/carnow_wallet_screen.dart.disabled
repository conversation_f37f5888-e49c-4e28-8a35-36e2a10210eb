import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import '../providers/wallet_providers.dart';
import '../../../shared/widgets/loading_indicator.dart';

class CarnowWalletScreen extends HookConsumerWidget {
  const CarnowWalletScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    
    // استخدام المزودين البسطاء المتاحين
    final walletAsync = ref.watch(currentWalletProvider);
    final transactionsAsync = ref.watch(currentWalletTransactionsProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('محفظة CarNow'),
        backgroundColor: theme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // تحديث البيانات
          ref.invalidate(currentWalletProvider);
          ref.invalidate(currentWalletTransactionsProvider);
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رصيد المحفظة
              walletAsync.when(
                data: (wallet) => _buildWalletCard(context, wallet),
                loading: () => const LoadingIndicator(),
                error: (error, stack) => Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        const Icon(Icons.error, color: Colors.red),
                        const SizedBox(height: 8),
                        const Text('خطأ في تحميل المحفظة'),
                        const SizedBox(height: 8),
                        Text(error.toString(), style: const TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // المعاملات
              Text(
                'المعاملات الأخيرة',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 12),
              
              transactionsAsync.when(
                data: (transactions) => _buildTransactionsList(context, transactions),
                loading: () => const LoadingIndicator(),
                error: (error, stack) => Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        const Icon(Icons.error, color: Colors.red),
                        const SizedBox(height: 8),
                        const Text('خطأ في تحميل المعاملات'),
                        const SizedBox(height: 8),
                        Text(error.toString(), style: const TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton.extended(
            onPressed: () {
              // إيداع
              _showDepositDialog(context, ref);
            },
            icon: const Icon(Icons.add),
            label: const Text('إيداع'),
          ),
          const SizedBox(height: 10),
          FloatingActionButton.extended(
            onPressed: () {
              // سحب
              _showWithdrawDialog(context, ref);
            },
            icon: const Icon(Icons.remove),
            label: const Text('سحب'),
          ),
        ],
      ),
    );
  }

  Widget _buildWalletCard(BuildContext context, wallet) {
    if (wallet == null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Icon(Icons.account_balance_wallet, size: 48, color: Colors.grey[400]),
              const SizedBox(height: 12),
              Text(
                'لا توجد محفظة',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'سيتم إنشاء محفظة تلقائياً عند أول معاملة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.account_balance_wallet, color: Theme.of(context).primaryColor),
                const SizedBox(width: 12),
                Text(
                  'رصيد المحفظة',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              '${wallet.balance?.toStringAsFixed(2) ?? '0.00'} ${wallet.currency ?? 'LYD'}',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsList(BuildContext context, List transactions) {
    if (transactions.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Icon(Icons.receipt_long, size: 48, color: Colors.grey[400]),
              const SizedBox(height: 12),
              Text(
                'لا توجد معاملات',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: transactions.length,
      itemBuilder: (context, index) {
        final transaction = transactions[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: transaction.type == 'deposit' 
                  ? Colors.green[100] 
                  : Colors.red[100],
              child: Icon(
                transaction.type == 'deposit' 
                    ? Icons.add 
                    : Icons.remove,
                color: transaction.type == 'deposit' 
                    ? Colors.green[700] 
                    : Colors.red[700],
              ),
            ),
            title: Text(transaction.description ?? 'معاملة'),
            subtitle: Text(
              DateFormat('yyyy-MM-dd HH:mm').format(
                transaction.createdAt ?? DateTime.now(),
              ),
            ),
            trailing: Text(
              '${transaction.amount.toStringAsFixed(2)} ${transaction.currency}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: transaction.type == 'deposit' 
                    ? Colors.green[700] 
                    : Colors.red[700],
              ),
            ),
          ),
        );
      },
    );
  }

  void _showDepositDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إيداع في المحفظة'),
        content: const Text('سيتم تطوير ميزة الإيداع قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showWithdrawDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سحب من المحفظة'),
        content: const Text('سيتم تطوير ميزة السحب قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
} 