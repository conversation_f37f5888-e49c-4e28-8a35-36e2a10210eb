import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/wallet_providers.dart';
import '../models/wallet_model.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/widgets/app_error_widget.dart';
import '../../../shared/widgets/loading_widget.dart';

/// شاشة إيداع الأموال في المحفظة
class WalletDepositScreen extends HookConsumerWidget {
  const WalletDepositScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final walletAsync = ref.watch(walletNotifierProvider);
    final formKey = GlobalKey<FormState>();
    final amountController = useTextEditingController();
    final descriptionController = useTextEditingController();

    final selectedPaymentMethod = useState<String?>(null);
    final isProcessing = useState(false);

    // قائمة طرق الدفع المتاحة
    final paymentMethods = [
      {
        'id': 'bank_transfer',
        'name': 'حوالة بنكية',
        'icon': Icons.account_balance,
      },
      {'id': 'credit_card', 'name': 'بطاقة ائتمان', 'icon': Icons.credit_card},
      {'id': 'mada', 'name': 'مدى', 'icon': Icons.payment},
      {'id': 'stc_pay', 'name': 'STC Pay', 'icon': Icons.phone_android},
      {'id': 'apple_pay', 'name': 'Apple Pay', 'icon': Icons.apple},
    ];

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'إيداع الأموال',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: walletAsync.when(
        data: (wallet) {
          if (wallet == null) {
            return const Center(child: Text('لم يتم العثور على المحفظة'));
          }

          if (!wallet.canPerformDeposit) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.block, size: 64, color: Colors.red[400]),
                  const SizedBox(height: 16),
                  const Text(
                    'الإيداع غير متاح',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    wallet.statusMessage,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey[600], fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => context.push('/wallet/verification'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                    ),
                    child: const Text('التحقق من الهوية'),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المحفظة
                  _WalletInfoCard(wallet: wallet),

                  const SizedBox(height: 24),

                  // نموذج الإيداع
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تفاصيل الإيداع',
                            style: Theme.of(context).textTheme.headlineSmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 20),

                          // مبلغ الإيداع
                          TextFormField(
                            controller: amountController,
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*\.?\d*'),
                              ),
                            ],
                            decoration: InputDecoration(
                              labelText: 'المبلغ',
                              hintText: 'أدخل المبلغ المراد إيداعه',
                              prefixIcon: const Icon(Icons.attach_money),
                              suffixText: wallet.currency,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                  color: AppColors.primary,
                                ),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال المبلغ';
                              }
                              final amount = double.tryParse(value);
                              if (amount == null || amount <= 0) {
                                return 'يرجى إدخال مبلغ صحيح';
                              }
                              if (amount > wallet.dailyLimit) {
                                return 'المبلغ يتجاوز الحد اليومي (${wallet.dailyLimit})';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 16),

                          // طريقة الدفع
                          Text(
                            'طريقة الدفع',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 12),

                          ...paymentMethods.map(
                            (method) => _PaymentMethodTile(
                              method: method,
                              isSelected:
                                  selectedPaymentMethod.value == method['id'],
                              onTap: () => selectedPaymentMethod.value =
                                  method['id'] as String,
                            ),
                          ),

                          const SizedBox(height: 16),

                          // وصف الإيداع (اختياري)
                          TextFormField(
                            controller: descriptionController,
                            decoration: InputDecoration(
                              labelText: 'وصف الإيداع (اختياري)',
                              hintText: 'اكتب وصف للإيداع',
                              prefixIcon: const Icon(Icons.description),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                  color: AppColors.primary,
                                ),
                              ),
                            ),
                            maxLines: 3,
                            maxLength: 200,
                          ),

                          const SizedBox(height: 24),

                          // زر الإيداع
                          SizedBox(
                            width: double.infinity,
                            height: 50,
                            child: ElevatedButton(
                              onPressed: isProcessing.value
                                  ? null
                                  : () async {
                                      if (formKey.currentState?.validate() ??
                                          false) {
                                        if (selectedPaymentMethod.value ==
                                            null) {
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            const SnackBar(
                                              content: Text(
                                                'يرجى اختيار طريقة الدفع',
                                              ),
                                              backgroundColor: Colors.red,
                                            ),
                                          );
                                          return;
                                        }

                                        isProcessing.value = true;

                                        try {
                                          final amount = double.parse(
                                            amountController.text,
                                          );
                                          final paymentMethod =
                                              selectedPaymentMethod.value!;
                                          final description =
                                              descriptionController.text.trim();

                                          await ref
                                              .read(
                                                walletOperationsNotifierProvider
                                                    .notifier,
                                              )
                                              .deposit(
                                                amount: amount,
                                                paymentMethod: paymentMethod,
                                                paymentReference:
                                                    'REF-${DateTime.now().millisecondsSinceEpoch}',
                                                description: description.isEmpty
                                                    ? null
                                                    : description,
                                              );

                                          if (context.mounted) {
                                            ScaffoldMessenger.of(
                                              context,
                                            ).showSnackBar(
                                              const SnackBar(
                                                content: Text(
                                                  'تم إرسال طلب الإيداع بنجاح',
                                                ),
                                                backgroundColor: Colors.green,
                                              ),
                                            );
                                            context.pop();
                                          }
                                        } catch (e) {
                                          if (context.mounted) {
                                            ScaffoldMessenger.of(
                                              context,
                                            ).showSnackBar(
                                              SnackBar(
                                                content: Text(
                                                  'خطأ في الإيداع: ${e.toString()}',
                                                ),
                                                backgroundColor: Colors.red,
                                              ),
                                            );
                                          }
                                        } finally {
                                          isProcessing.value = false;
                                        }
                                      }
                                    },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primary,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: isProcessing.value
                                  ? const SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : const Text(
                                      'إيداع الأموال',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // ملاحظات وتعليمات
                  _InstructionsCard(),
                ],
              ),
            ),
          );
        },
        loading: () => const LoadingWidget(),
        error: (error, stackTrace) => AppErrorWidget(
          message: error.toString(),
          onRetry: () => ref.invalidate(walletNotifierProvider),
        ),
      ),
    );
  }
}

/// بطاقة معلومات المحفظة
class _WalletInfoCard extends StatelessWidget {
  const _WalletInfoCard({required this.wallet});
  final WalletModel wallet;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المحفظة',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('الرصيد الحالي:'),
                Text(
                  '${wallet.balance} ${wallet.currency}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('الحد اليومي:'),
                Text(
                  '${wallet.dailyLimit} ${wallet.currency}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('حالة التحقق:'),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: wallet.verificationStatus == WalletVerificationStatus.fullyVerified
                        ? Colors.green
                        : wallet.verificationStatus == WalletVerificationStatus.basicVerified
                            ? Colors.orange
                            : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    wallet.verificationStatus.displayName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// عنصر طريقة الدفع
class _PaymentMethodTile extends StatelessWidget {
  const _PaymentMethodTile({
    required this.method,
    required this.isSelected,
    required this.onTap,
  });
  final Map<String, dynamic> method;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
          color: isSelected
              ? AppColors.primary.withValues(alpha: 0.1)
              : Colors.transparent,
        ),
        child: Row(
          children: [
            Icon(
              method['icon'] as IconData,
              color: isSelected ? AppColors.primary : Colors.grey.shade600,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                method['name'] as String,
                style: TextStyle(
                  color: isSelected ? AppColors.primary : Colors.black,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: AppColors.primary,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}

/// بطاقة التعليمات
class _InstructionsCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'تعليمات مهمة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '• سيتم مراجعة طلب الإيداع في غضون 24 ساعة',
              style: TextStyle(height: 1.5),
            ),
            const Text(
              '• تأكد من صحة المبلغ قبل التأكيد',
              style: TextStyle(height: 1.5),
            ),
            const Text(
              '• احتفظ بإيصال الدفع للمراجعة',
              style: TextStyle(height: 1.5),
            ),
            const Text(
              '• يمكنك تتبع حالة الإيداع في قائمة المعاملات',
              style: TextStyle(height: 1.5),
            ),
          ],
        ),
      ),
    );
  }
}
