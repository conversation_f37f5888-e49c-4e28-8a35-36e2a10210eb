import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/wallet_providers.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/widgets/app_error_widget.dart';
import '../../../core/utils/date_formatter.dart';
import '../../../shared/widgets/loading_widget.dart';

/// شاشة سحب الأموال من المحفظة
class WalletWithdrawScreen extends HookConsumerWidget {
  const WalletWithdrawScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final walletAsync = ref.watch(walletNotifierProvider);
    final withdrawalRequestsAsync = ref.watch(
      withdrawalRequestsNotifierProvider,
    );

    final formKey = GlobalKey<FormState>();
    final amountController = useTextEditingController();
    final bankNameController = useTextEditingController();
    final accountNumberController = useTextEditingController();
    final accountHolderController = useTextEditingController();
    final ibanController = useTextEditingController();
    final swiftCodeController = useTextEditingController();
    final descriptionController = useTextEditingController();

    final isProcessing = useState(false);
    final showBankForm = useState(false);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'سحب الأموال',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: walletAsync.when(
        data: (wallet) {
          if (wallet == null) {
            return const Center(child: Text('لم يتم العثور على المحفظة'));
          }

          if (!wallet.canPerformWithdraw) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.block, size: 64, color: Colors.red[400]),
                  const SizedBox(height: 16),
                  const Text(
                    'السحب غير متاح',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    wallet.statusMessage,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey[600], fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => context.push('/wallet/verification'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                    ),
                    child: const Text('التحقق من الهوية'),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المحفظة
                  _WalletInfoCard(wallet: wallet),

                  const SizedBox(height: 24),

                  // نموذج السحب
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تفاصيل السحب',
                            style: Theme.of(context).textTheme.headlineSmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 20),

                          // مبلغ السحب
                          TextFormField(
                            controller: amountController,
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*\.?\d*'),
                              ),
                            ],
                            decoration: InputDecoration(
                              labelText: 'المبلغ المراد سحبه',
                              hintText: 'أدخل المبلغ',
                              prefixIcon: const Icon(Icons.attach_money),
                              suffixText: wallet.currency,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                  color: AppColors.primary,
                                ),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال المبلغ';
                              }
                              final amount = double.tryParse(value);
                              if (amount == null || amount <= 0) {
                                return 'يرجى إدخال مبلغ صحيح';
                              }
                              if (amount < wallet.minWithdrawal) {
                                return 'الحد الأدنى للسحب هو ${wallet.minWithdrawal} ${wallet.currency}';
                              }
                              if (amount > wallet.balance) {
                                return 'المبلغ يتجاوز الرصيد المتاح';
                              }
                              if (amount > wallet.dailyLimit) {
                                return 'المبلغ يتجاوز الحد اليومي';
                              }
                              return null;
                            },
                            onChanged: (value) {
                              final amount = double.tryParse(value);
                              if (amount != null &&
                                  amount >= wallet.minWithdrawal) {
                                showBankForm.value = true;
                              } else {
                                showBankForm.value = false;
                              }
                            },
                          ),

                          const SizedBox(height: 16),

                          // معلومات الحساب البنكي
                          if (showBankForm.value) ...[
                            const Divider(),
                            const SizedBox(height: 16),
                            Text(
                              'معلومات الحساب البنكي',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 16),

                            // اسم البنك
                            TextFormField(
                              controller: bankNameController,
                              decoration: InputDecoration(
                                labelText: 'اسم البنك',
                                hintText: 'أدخل اسم البنك',
                                prefixIcon: const Icon(Icons.account_balance),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(
                                    color: AppColors.primary,
                                  ),
                                ),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال اسم البنك';
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 16),

                            // رقم الحساب
                            TextFormField(
                              controller: accountNumberController,
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              decoration: InputDecoration(
                                labelText: 'رقم الحساب',
                                hintText: 'أدخل رقم الحساب',
                                prefixIcon: const Icon(Icons.credit_card),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(
                                    color: AppColors.primary,
                                  ),
                                ),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال رقم الحساب';
                                }
                                if (value.length < 8) {
                                  return 'رقم الحساب يجب أن يكون على الأقل 8 أرقام';
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 16),

                            // اسم صاحب الحساب
                            TextFormField(
                              controller: accountHolderController,
                              decoration: InputDecoration(
                                labelText: 'اسم صاحب الحساب',
                                hintText: 'أدخل اسم صاحب الحساب',
                                prefixIcon: const Icon(Icons.person),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(
                                    color: AppColors.primary,
                                  ),
                                ),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال اسم صاحب الحساب';
                                }
                                if (value.length < 2) {
                                  return 'اسم صاحب الحساب قصير جداً';
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 16),

                            // رقم الآيبان
                            TextFormField(
                              controller: ibanController,
                              textCapitalization: TextCapitalization.characters,
                              decoration: InputDecoration(
                                labelText: 'رقم الآيبان (IBAN)',
                                hintText: '************************',
                                prefixIcon: const Icon(
                                  Icons.credit_card_outlined,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(
                                    color: AppColors.primary,
                                  ),
                                ),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال رقم الآيبان';
                                }
                                if (!value.startsWith('SA')) {
                                  return 'رقم الآيبان يجب أن يبدأ بـ SA';
                                }
                                if (value.length != 24) {
                                  return 'رقم الآيبان يجب أن يكون 24 حرف';
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 16),

                            // رمز السويفت (اختياري)
                            TextFormField(
                              controller: swiftCodeController,
                              textCapitalization: TextCapitalization.characters,
                              decoration: InputDecoration(
                                labelText: 'رمز السويفت (اختياري)',
                                hintText: 'RJHISARI',
                                prefixIcon: const Icon(Icons.location_on),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(
                                    color: AppColors.primary,
                                  ),
                                ),
                              ),
                              validator: (value) {
                                if (value != null && value.isNotEmpty) {
                                  if (value.length < 8 || value.length > 11) {
                                    return 'رمز السويفت يجب أن يكون بين 8-11 حرف';
                                  }
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 16),
                          ],

                          // وصف السحب (اختياري)
                          TextFormField(
                            controller: descriptionController,
                            decoration: InputDecoration(
                              labelText: 'وصف السحب (اختياري)',
                              hintText: 'اكتب وصف للسحب',
                              prefixIcon: const Icon(Icons.description),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                  color: AppColors.primary,
                                ),
                              ),
                            ),
                            maxLines: 3,
                            maxLength: 200,
                          ),

                          const SizedBox(height: 24),

                          // زر السحب
                          SizedBox(
                            width: double.infinity,
                            height: 50,
                            child: ElevatedButton(
                              onPressed: isProcessing.value
                                  ? null
                                  : () async {
                                      if (formKey.currentState?.validate() ??
                                          false) {
                                        isProcessing.value = true;

                                        try {
                                          final amount = double.parse(
                                            amountController.text,
                                          );
                                          final bankName = bankNameController
                                              .text
                                              .trim();
                                          final accountNumber =
                                              accountNumberController.text
                                                  .trim();
                                          final accountHolder =
                                              accountHolderController.text
                                                  .trim();
                                          final iban = ibanController.text
                                              .trim();
                                          final swiftCode = swiftCodeController
                                              .text
                                              .trim();

                                          await ref
                                              .read(
                                                withdrawalRequestsNotifierProvider
                                                    .notifier,
                                              )
                                              .createWithdrawalRequest(
                                                amount: amount,
                                                bankName: bankName,
                                                accountNumber: accountNumber,
                                                accountHolderName:
                                                    accountHolder,
                                                iban: iban,
                                                swiftCode: swiftCode.isEmpty
                                                    ? null
                                                    : swiftCode,
                                              );

                                          if (context.mounted) {
                                            ScaffoldMessenger.of(
                                              context,
                                            ).showSnackBar(
                                              const SnackBar(
                                                content: Text(
                                                  'تم إرسال طلب السحب بنجاح',
                                                ),
                                                backgroundColor: Colors.green,
                                              ),
                                            );
                                            context.pop();
                                          }
                                        } catch (e) {
                                          if (context.mounted) {
                                            ScaffoldMessenger.of(
                                              context,
                                            ).showSnackBar(
                                              SnackBar(
                                                content: Text(
                                                  'خطأ في السحب: ${e.toString()}',
                                                ),
                                                backgroundColor: Colors.red,
                                              ),
                                            );
                                          }
                                        } finally {
                                          isProcessing.value = false;
                                        }
                                      }
                                    },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primary,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: isProcessing.value
                                  ? const SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : const Text(
                                      'طلب السحب',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // طلبات السحب السابقة
                  withdrawalRequestsAsync.when(
                    data: (requests) {
                      if (requests.isEmpty) {
                        return const SizedBox.shrink();
                      }

                      return Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'طلبات السحب السابقة',
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 12),
                              ...requests
                                  .take(3)
                                  .map(
                                    (request) => _WithdrawalRequestItem(
                                      request: request,
                                    ),
                                  ),
                              if (requests.length > 3)
                                TextButton(
                                  onPressed: () {
                                    // انتقال لقائمة طلبات السحب الكاملة
                                  },
                                  child: const Text('عرض جميع الطلبات'),
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                    loading: () => const SizedBox.shrink(),
                    error: (error, stackTrace) => const SizedBox.shrink(),
                  ),

                  const SizedBox(height: 24),

                  // ملاحظات وتعليمات
                  _InstructionsCard(),
                ],
              ),
            ),
          );
        },
        loading: () => const LoadingWidget(),
        error: (error, stackTrace) => AppErrorWidget(
          message: error.toString(),
          onRetry: () => ref.invalidate(walletNotifierProvider),
        ),
      ),
    );
  }
}

/// بطاقة معلومات المحفظة
class _WalletInfoCard extends StatelessWidget {
  const _WalletInfoCard({required this.wallet});
  final dynamic wallet;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المحفظة',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('الرصيد المتاح:'),
                Text(
                  '${wallet.balance} ${wallet.currency}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('الحد الأدنى للسحب:'),
                Text(
                  '${wallet.minWithdrawal} ${wallet.currency}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('الحد اليومي:'),
                Text(
                  '${wallet.dailyLimit} ${wallet.currency}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// عنصر طلب السحب
class _WithdrawalRequestItem extends StatelessWidget {
  const _WithdrawalRequestItem({required this.request});
  final dynamic request;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${request.amount} ${request.currency ?? 'LYD'}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              Text(
                DateFormatter.formatDate(request.createdAt),
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
            ],
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(request.status),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              _getStatusText(request.status),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(dynamic status) {
    switch (status.toString().toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      case 'completed':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(dynamic status) {
    switch (status.toString().toLowerCase()) {
      case 'pending':
        return 'في انتظار';
      case 'approved':
        return 'موافق عليه';
      case 'rejected':
        return 'مرفوض';
      case 'completed':
        return 'مكتمل';
      default:
        return 'غير معروف';
    }
  }
}

/// بطاقة التعليمات
class _InstructionsCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'تعليمات مهمة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '• سيتم مراجعة طلب السحب في غضون 48 ساعة عمل',
              style: TextStyle(height: 1.5),
            ),
            const Text(
              '• تأكد من صحة معلومات الحساب البنكي',
              style: TextStyle(height: 1.5),
            ),
            const Text(
              '• الحد الأدنى للسحب يختلف حسب حالة التحقق',
              style: TextStyle(height: 1.5),
            ),
            const Text(
              '• لا يمكن إلغاء طلب السحب بعد الإرسال',
              style: TextStyle(height: 1.5),
            ),
            const Text(
              '• ستصل الأموال خلال 3-5 أيام عمل بعد الموافقة',
              style: TextStyle(height: 1.5),
            ),
          ],
        ),
      ),
    );
  }
}
