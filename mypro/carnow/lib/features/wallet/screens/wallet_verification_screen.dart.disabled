import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../providers/wallet_providers.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/widgets/app_error_widget.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../core/providers/users_provider.dart';
import '../../../core/utils/date_formatter.dart';
import '../../../features/account/models/user_model.dart';
import '../services/identity_verification_service.dart' show VerificationLevel;

/// شاشة التحقق من الهوية للمحفظة
class WalletVerificationScreen extends HookConsumerWidget {
  const WalletVerificationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLevelAsync = ref.watch(currentVerificationLevelProvider);
    final verificationRequestsAsync = ref.watch(
      identityVerificationNotifierProvider,
    );
    final userProfileAsync = ref.watch(currentUserStreamProvider);
    final userProfile = userProfileAsync.value;

    final currentStep = useState(0);
    final uploadedDocuments = useState<Map<String, File>>({});
    final isSubmitting = useState(false);

    // تحديد نوع الحساب (مشتري أم بائع)
    final isSellerAccount = userProfile?.sellerStatus == SellerStatus.approved;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'التحقق من الهوية',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: currentLevelAsync.when(
        data: (currentLevel) => SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // مؤشر التقدم
              _ProgressIndicator(
                currentLevel: currentLevel,
                isSellerAccount: isSellerAccount,
              ),

              const SizedBox(height: 24),

              // حالة التحقق الحالية
              _CurrentStatusCard(
                currentLevel: currentLevel,
                isSellerAccount: isSellerAccount,
              ),

              const SizedBox(height: 24),

              // خطوات التحقق
              if (currentLevel.index < (isSellerAccount ? 2 : 1)) ...[
                _VerificationSteps(
                  currentStep: currentStep.value,
                  isSellerAccount: isSellerAccount,
                  uploadedDocuments: uploadedDocuments.value,
                  onStepChanged: (step) => currentStep.value = step,
                  onDocumentUploaded: (documentType, file) {
                    uploadedDocuments.value = {
                      ...uploadedDocuments.value,
                      documentType: file,
                    };
                  },
                  onSubmit: () async {
                    isSubmitting.value = true;

                    try {
                      // إرسال طلب التحقق
                      await ref
                          .read(identityVerificationNotifierProvider.notifier)
                          .submitVerificationRequest(
                            documents: uploadedDocuments.value,
                            isSellerAccount: isSellerAccount,
                          );

                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تم إرسال طلب التحقق بنجاح'),
                            backgroundColor: Colors.green,
                          ),
                        );

                        // إعادة تحميل البيانات
                        ref.invalidate(currentVerificationLevelProvider);
                        ref.invalidate(identityVerificationNotifierProvider);
                      }
                    } catch (e) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'خطأ في إرسال الطلب: ${e.toString()}',
                            ),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    } finally {
                      isSubmitting.value = false;
                    }
                  },
                  isSubmitting: isSubmitting.value,
                ),
              ],

              const SizedBox(height: 24),

              // طلبات التحقق السابقة
              verificationRequestsAsync.when(
                data: (requests) {
                  if (requests.isEmpty) {
                    return const SizedBox.shrink();
                  }

                  return _PreviousRequestsCard(requests: requests);
                },
                loading: () => const LoadingWidget(),
                error: (error, stackTrace) => const SizedBox.shrink(),
              ),

              const SizedBox(height: 24),

              // معلومات وتعليمات
              _InformationCard(isSellerAccount: isSellerAccount),
            ],
          ),
        ),
        loading: () => const LoadingWidget(),
        error: (error, stackTrace) => AppErrorWidget(
          message: error.toString(),
          onRetry: () => ref.invalidate(currentVerificationLevelProvider),
        ),
      ),
    );
  }
}

/// مؤشر التقدم في التحقق
class _ProgressIndicator extends StatelessWidget {
  const _ProgressIndicator({
    required this.currentLevel,
    required this.isSellerAccount,
  });
  final VerificationLevel currentLevel;
  final bool isSellerAccount;

  @override
  Widget build(BuildContext context) {
    final maxLevel = isSellerAccount ? 2 : 1;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مستوى التحقق',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            LinearProgressIndicator(
              value: currentLevel.index / maxLevel,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(
                currentLevel == VerificationLevel.fullyVerified
                    ? Colors.green
                    : AppColors.primary,
              ),
            ),

            const SizedBox(height: 12),

            Text(
              '${currentLevel.index}/$maxLevel مكتمل',
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// بطاقة الحالة الحالية
class _CurrentStatusCard extends StatelessWidget {
  const _CurrentStatusCard({
    required this.currentLevel,
    required this.isSellerAccount,
  });
  final VerificationLevel currentLevel;
  final bool isSellerAccount;

  @override
  Widget build(BuildContext context) {
    String statusText;
    Color statusColor;
    IconData statusIcon;

    if (currentLevel == VerificationLevel.unverified) {
      statusText = 'غير محقق';
      statusColor = Colors.red;
      statusIcon = Icons.error;
    } else if (currentLevel == VerificationLevel.basicVerified) {
      statusText = isSellerAccount ? 'محقق جزئياً' : 'محقق كاملاً';
      statusColor = isSellerAccount ? Colors.orange : Colors.green;
      statusIcon = isSellerAccount ? Icons.warning : Icons.check_circle;
    } else {
      statusText = 'محقق كاملاً';
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(statusIcon, color: statusColor, size: 32),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    statusText,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _getStatusDescription(currentLevel, isSellerAccount),
                    style: TextStyle(color: Colors.grey[600], height: 1.4),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getStatusDescription(VerificationLevel level, bool isSellerAccount) {
    if (level == VerificationLevel.unverified) {
      return 'يجب التحقق من الهوية لاستخدام المحفظة';
    } else if (level == VerificationLevel.basicVerified) {
      if (isSellerAccount) {
        return 'يمكن الشراء والتحويل. يتطلب تحقق إضافي لفتح المتجر';
      } else {
        return 'يمكن استخدام جميع ميزات المحفظة';
      }
    } else {
      return 'تم التحقق الكامل. جميع الميزات متاحة';
    }
  }
}

/// خطوات التحقق
class _VerificationSteps extends StatelessWidget {
  const _VerificationSteps({
    required this.currentStep,
    required this.isSellerAccount,
    required this.uploadedDocuments,
    required this.onStepChanged,
    required this.onDocumentUploaded,
    required this.onSubmit,
    required this.isSubmitting,
  });
  final int currentStep;
  final bool isSellerAccount;
  final Map<String, File> uploadedDocuments;
  final Function(int) onStepChanged;
  final Function(String, File) onDocumentUploaded;
  final VoidCallback onSubmit;
  final bool isSubmitting;

  @override
  Widget build(BuildContext context) {
    final basicDocuments = [
      {
        'type': 'national_id',
        'name': 'الهوية الوطنية أو الإقامة',
        'required': true,
      },
      {'type': 'selfie', 'name': 'صورة شخصية', 'required': true},
    ];

    final sellerDocuments = [
      {
        'type': 'commercial_register',
        'name': 'السجل التجاري',
        'required': true,
      },
      {'type': 'tax_certificate', 'name': 'الشهادة الضريبية', 'required': true},
      {'type': 'bank_statement', 'name': 'كشف حساب بنكي', 'required': false},
    ];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'خطوات التحقق',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),

            // التحقق الأساسي
            _StepCard(
              stepNumber: 1,
              title: 'التحقق الأساسي',
              description: 'رفع الهوية الوطنية والصورة الشخصية',
              isActive: currentStep == 0,
              isCompleted: false,
              documents: basicDocuments,
              uploadedDocuments: uploadedDocuments,
              onDocumentUploaded: onDocumentUploaded,
              onToggle: () => onStepChanged(currentStep == 0 ? -1 : 0),
            ),

            // التحقق الإضافي للبائعين
            if (isSellerAccount) ...[
              const SizedBox(height: 16),
              _StepCard(
                stepNumber: 2,
                title: 'التحقق التجاري',
                description: 'رفع السجل التجاري والشهادة الضريبية',
                isActive: currentStep == 1,
                isCompleted: false,
                documents: sellerDocuments,
                uploadedDocuments: uploadedDocuments,
                onDocumentUploaded: onDocumentUploaded,
                onToggle: () => onStepChanged(currentStep == 1 ? -1 : 1),
              ),
            ],

            const SizedBox(height: 24),

            // زر الإرسال
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _canSubmit() && !isSubmitting ? onSubmit : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: isSubmitting
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text(
                        'إرسال طلب التحقق',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _canSubmit() {
    // التحقق من رفع الوثائق الأساسية
    if (!uploadedDocuments.containsKey('national_id') ||
        !uploadedDocuments.containsKey('selfie')) {
      return false;
    }

    // التحقق من الوثائق التجارية للبائعين
    if (isSellerAccount) {
      if (!uploadedDocuments.containsKey('commercial_register') ||
          !uploadedDocuments.containsKey('tax_certificate')) {
        return false;
      }
    }

    return true;
  }
}

/// بطاقة خطوة واحدة
class _StepCard extends StatelessWidget {
  const _StepCard({
    required this.stepNumber,
    required this.title,
    required this.description,
    required this.isActive,
    required this.isCompleted,
    required this.documents,
    required this.uploadedDocuments,
    required this.onDocumentUploaded,
    required this.onToggle,
  });
  final int stepNumber;
  final String title;
  final String description;
  final bool isActive;
  final bool isCompleted;
  final List<Map<String, dynamic>> documents;
  final Map<String, File> uploadedDocuments;
  final Function(String, File) onDocumentUploaded;
  final VoidCallback onToggle;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: isActive ? AppColors.primary : Colors.grey.shade300,
          width: isActive ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: onToggle,
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: isCompleted
                          ? Colors.green
                          : (isActive
                                ? AppColors.primary
                                : Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: isCompleted
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 20,
                            )
                          : Text(
                              stepNumber.toString(),
                              style: TextStyle(
                                color: isActive ? Colors.white : Colors.grey,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: isActive ? AppColors.primary : Colors.black,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          description,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    isActive ? Icons.expand_less : Icons.expand_more,
                    color: isActive ? AppColors.primary : Colors.grey,
                  ),
                ],
              ),
            ),
          ),

          if (isActive) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: documents
                    .map(
                      (doc) => _DocumentUploadItem(
                        documentType: doc['type'],
                        documentName: doc['name'],
                        isRequired: doc['required'],
                        isUploaded: uploadedDocuments.containsKey(doc['type']),
                        onUpload: (file) =>
                            onDocumentUploaded(doc['type'], file),
                      ),
                    )
                    .toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// عنصر رفع الوثيقة
class _DocumentUploadItem extends StatelessWidget {
  const _DocumentUploadItem({
    required this.documentType,
    required this.documentName,
    required this.isRequired,
    required this.isUploaded,
    required this.onUpload,
  });
  final String documentType;
  final String documentName;
  final bool isRequired;
  final bool isUploaded;
  final Function(File) onUpload;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isUploaded
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isUploaded ? Colors.green : Colors.grey.shade300,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isUploaded ? Icons.check_circle : Icons.upload_file,
            color: isUploaded ? Colors.green : Colors.grey,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Flexible(
                      child: Text(
                        documentName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (isRequired) ...[
                      const SizedBox(width: 4),
                      const Text(
                        '*',
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  isUploaded ? 'تم الرفع' : 'اضغط لرفع الملف',
                  style: TextStyle(
                    color: isUploaded ? Colors.green : Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () => _pickImage(context),
            child: Text(
              isUploaded ? 'تغيير' : 'رفع',
              style: const TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage(BuildContext context) async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 1920,
      maxHeight: 1080,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      onUpload(File(pickedFile.path));
    }
  }
}

/// بطاقة الطلبات السابقة
class _PreviousRequestsCard extends StatelessWidget {
  const _PreviousRequestsCard({required this.requests});
  final List<dynamic> requests;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'طلبات التحقق السابقة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            ...requests
                .take(3)
                .map((request) => _RequestItem(request: request)),

            if (requests.length > 3)
              TextButton(
                onPressed: () {
                  // انتقال لقائمة الطلبات الكاملة
                },
                child: const Text('عرض جميع الطلبات'),
              ),
          ],
        ),
      ),
    );
  }
}

/// عنصر الطلب
class _RequestItem extends StatelessWidget {
  const _RequestItem({required this.request});
  final dynamic request;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                request.documentType?.displayName ?? 'طلب تحقق',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                DateFormatter.formatDate(request.createdAt),
                style: TextStyle(color: Colors.grey[600], fontSize: 14),
              ),
            ],
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getStatusColor(request.status),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              request.status?.displayName ?? 'غير معروف',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(dynamic status) {
    if (status == null) return Colors.grey;

    switch (status.toString().toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'verified':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

/// بطاقة المعلومات
class _InformationCard extends StatelessWidget {
  const _InformationCard({required this.isSellerAccount});
  final bool isSellerAccount;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'معلومات مهمة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            const Text(
              '• سيتم مراجعة الطلب في غضون 24-48 ساعة',
              style: TextStyle(height: 1.5),
            ),
            const Text(
              '• تأكد من وضوح الصور المرفوعة',
              style: TextStyle(height: 1.5),
            ),
            const Text(
              '• يجب أن تكون الوثائق سارية المفعول',
              style: TextStyle(height: 1.5),
            ),
            if (isSellerAccount) ...[
              const Text(
                '• السجل التجاري يجب أن يكون نشطاً',
                style: TextStyle(height: 1.5),
              ),
              const Text(
                '• الشهادة الضريبية يجب أن تكون حديثة',
                style: TextStyle(height: 1.5),
              ),
            ],
            const Text(
              '• في حالة الرفض، يمكنك إعادة المحاولة',
              style: TextStyle(height: 1.5),
            ),
          ],
        ),
      ),
    );
  }
}
