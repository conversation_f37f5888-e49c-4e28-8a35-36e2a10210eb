import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/wallet_providers.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/widgets/app_error_widget.dart';
import '../../../shared/widgets/loading_widget.dart';

/// شاشة تحويل الأموال بين المحافظ
class WalletTransferScreen extends HookConsumerWidget {
  const WalletTransferScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final walletAsync = ref.watch(walletNotifierProvider);

    final formKey = GlobalKey<FormState>();
    final amountController = useTextEditingController();
    final recipientController = useTextEditingController();
    final descriptionController = useTextEditingController();

    final isProcessing = useState(false);
    final selectedRecipient = useState<Map<String, dynamic>?>(null);
    final showConfirmation = useState(false);
    final searchResults = useState<List<Map<String, dynamic>>>([]);
    final isSearching = useState(false);

    // البحث عن المستخدمين
    Future<void> searchUsers(String query) async {
      if (query.length < 3) {
        searchResults.value = [];
        return;
      }

      isSearching.value = true;
      try {
        // محاكاة البحث - يجب استبدالها بالـ API الحقيقي
        await Future.delayed(const Duration(milliseconds: 500));

        // نتائج وهمية للعرض
        searchResults.value =
            [
                  {
                    'id': '1',
                    'name': 'أحمد محمد',
                    'email': '<EMAIL>',
                    'phone': '+966501234567',
                    'avatar': null,
                  },
                  {
                    'id': '2',
                    'name': 'فاطمة علي',
                    'email': '<EMAIL>',
                    'phone': '+966502345678',
                    'avatar': null,
                  },
                  {
                    'id': '3',
                    'name': 'محمد السعيد',
                    'email': '<EMAIL>',
                    'phone': '+966503456789',
                    'avatar': null,
                  },
                ]
                .where(
                  (user) =>
                      user['name'].toString().toLowerCase().contains(
                        query.toLowerCase(),
                      ) ||
                      user['email'].toString().toLowerCase().contains(
                        query.toLowerCase(),
                      ) ||
                      user['phone'].toString().contains(query),
                )
                .toList();
      } catch (e) {
        searchResults.value = [];
      } finally {
        isSearching.value = false;
      }
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'تحويل الأموال',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: walletAsync.when(
        data: (wallet) {
          if (wallet == null) {
            return const Center(child: Text('لم يتم العثور على المحفظة'));
          }

          if (!wallet.canPerformTransfer) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.block, size: 64, color: Colors.red[400]),
                  const SizedBox(height: 16),
                  const Text(
                    'التحويل غير متاح',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    wallet.statusMessage,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey[600], fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => context.push('/wallet/verification'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                    ),
                    child: const Text('التحقق من الهوية'),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المحفظة
                  _WalletInfoCard(wallet: wallet),

                  const SizedBox(height: 24),

                  if (!showConfirmation.value) ...[
                    // نموذج التحويل
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'تفاصيل التحويل',
                              style: Theme.of(context).textTheme.headlineSmall
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 20),

                            // البحث عن المستقبل
                            TextFormField(
                              controller: recipientController,
                              decoration: InputDecoration(
                                labelText: 'المستقبل',
                                hintText:
                                    'ابحث بالاسم أو البريد الإلكتروني أو رقم الهاتف',
                                prefixIcon: const Icon(Icons.search),
                                suffixIcon: isSearching.value
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : null,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(
                                    color: AppColors.primary,
                                  ),
                                ),
                              ),
                              onChanged: (value) {
                                if (selectedRecipient.value != null) {
                                  selectedRecipient.value = null;
                                }
                                searchUsers(value);
                              },
                              validator: (value) {
                                if (selectedRecipient.value == null) {
                                  return 'يرجى اختيار المستقبل';
                                }
                                return null;
                              },
                            ),

                            // نتائج البحث
                            if (searchResults.value.isNotEmpty &&
                                selectedRecipient.value == null) ...[
                              const SizedBox(height: 12),
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Column(
                                  children: searchResults.value
                                      .map(
                                        (user) => _UserSearchResult(
                                          user: user,
                                          onTap: () {
                                            selectedRecipient.value = user;
                                            recipientController.text =
                                                user['name'];
                                            searchResults.value = [];
                                          },
                                        ),
                                      )
                                      .toList(),
                                ),
                              ),
                            ],

                            // عرض المستقبل المحدد
                            if (selectedRecipient.value != null) ...[
                              const SizedBox(height: 16),
                              _SelectedRecipientCard(
                                recipient: selectedRecipient.value!,
                                onRemove: () {
                                  selectedRecipient.value = null;
                                  recipientController.clear();
                                },
                              ),
                            ],

                            const SizedBox(height: 16),

                            // مبلغ التحويل
                            TextFormField(
                              controller: amountController,
                              keyboardType:
                                  const TextInputType.numberWithOptions(
                                    decimal: true,
                                  ),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                  RegExp(r'^\d*\.?\d*'),
                                ),
                              ],
                              decoration: InputDecoration(
                                labelText: 'المبلغ',
                                hintText: 'أدخل المبلغ المراد تحويله',
                                prefixIcon: const Icon(Icons.attach_money),
                                suffixText: wallet.currency,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(
                                    color: AppColors.primary,
                                  ),
                                ),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال المبلغ';
                                }
                                final amount = double.tryParse(value);
                                if (amount == null || amount <= 0) {
                                  return 'يرجى إدخال مبلغ صحيح';
                                }
                                if (amount > wallet.balance) {
                                  return 'المبلغ يتجاوز الرصيد المتاح';
                                }
                                if (amount > wallet.dailyLimit) {
                                  return 'المبلغ يتجاوز الحد اليومي';
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 16),

                            // وصف التحويل (اختياري)
                            TextFormField(
                              controller: descriptionController,
                              decoration: InputDecoration(
                                labelText: 'وصف التحويل (اختياري)',
                                hintText: 'اكتب وصف للتحويل',
                                prefixIcon: const Icon(Icons.description),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(
                                    color: AppColors.primary,
                                  ),
                                ),
                              ),
                              maxLines: 3,
                              maxLength: 200,
                            ),

                            const SizedBox(height: 24),

                            // زر المراجعة
                            SizedBox(
                              width: double.infinity,
                              height: 50,
                              child: ElevatedButton(
                                onPressed: () {
                                  if (formKey.currentState?.validate() ??
                                      false) {
                                    showConfirmation.value = true;
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.primary,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: const Text(
                                  'مراجعة التحويل',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ] else ...[
                    // شاشة التأكيد
                    _TransferConfirmationCard(
                      wallet: wallet,
                      recipient: selectedRecipient.value!,
                      amount: double.parse(amountController.text),
                      description: descriptionController.text.trim(),
                      onEdit: () => showConfirmation.value = false,
                      onConfirm: () async {
                        isProcessing.value = true;

                        try {
                          await ref
                              .read(walletOperationsNotifierProvider.notifier)
                              .transfer(
                                toUserId: selectedRecipient.value!['id'],
                                amount: double.parse(amountController.text),
                                description:
                                    descriptionController.text.trim().isEmpty
                                    ? null
                                    : descriptionController.text.trim(),
                              );

                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('تم التحويل بنجاح'),
                                backgroundColor: Colors.green,
                              ),
                            );
                            context.pop();
                          }
                        } catch (e) {
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'خطأ في التحويل: ${e.toString()}',
                                ),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        } finally {
                          isProcessing.value = false;
                        }
                      },
                      isProcessing: isProcessing.value,
                    ),
                  ],

                  const SizedBox(height: 24),

                  // ملاحظات وتعليمات
                  if (!showConfirmation.value) _InstructionsCard(),
                ],
              ),
            ),
          );
        },
        loading: () => const LoadingWidget(),
        error: (error, stackTrace) => AppErrorWidget(
          message: error.toString(),
          onRetry: () => ref.invalidate(walletNotifierProvider),
        ),
      ),
    );
  }
}

/// بطاقة معلومات المحفظة
class _WalletInfoCard extends StatelessWidget {
  const _WalletInfoCard({required this.wallet});
  final dynamic wallet;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'محفظتي',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('الرصيد المتاح:'),
                Text(
                  '${wallet.balance} ${wallet.currency}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('الحد اليومي:'),
                Text(
                  '${wallet.dailyLimit} ${wallet.currency}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// نتيجة البحث عن المستخدم
class _UserSearchResult extends StatelessWidget {
  const _UserSearchResult({required this.user, required this.onTap});
  final Map<String, dynamic> user;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
        ),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: AppColors.primary,
              child: Text(
                user['name'][0].toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user['name'],
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user['email'],
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                  Text(
                    user['phone'],
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
          ],
        ),
      ),
    );
  }
}

/// بطاقة المستقبل المحدد
class _SelectedRecipientCard extends StatelessWidget {
  const _SelectedRecipientCard({
    required this.recipient,
    required this.onRemove,
  });
  final Map<String, dynamic> recipient;
  final VoidCallback onRemove;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary),
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: AppColors.primary,
            child: Text(
              recipient['name'][0].toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  recipient['name'],
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  recipient['email'],
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: onRemove,
            icon: const Icon(Icons.close),
            iconSize: 20,
          ),
        ],
      ),
    );
  }
}

/// بطاقة تأكيد التحويل
class _TransferConfirmationCard extends StatelessWidget {
  const _TransferConfirmationCard({
    required this.wallet,
    required this.recipient,
    required this.amount,
    required this.description,
    required this.onEdit,
    required this.onConfirm,
    required this.isProcessing,
  });
  final dynamic wallet;
  final Map<String, dynamic> recipient;
  final double amount;
  final String description;
  final VoidCallback onEdit;
  final VoidCallback onConfirm;
  final bool isProcessing;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تأكيد التحويل',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),

            // تفاصيل التحويل
            _ConfirmationRow(
              label: 'المبلغ:',
              value: '$amount ${wallet.currency}',
              isAmount: true,
            ),
            _ConfirmationRow(label: 'المستقبل:', value: recipient['name']),
            _ConfirmationRow(
              label: 'البريد الإلكتروني:',
              value: recipient['email'],
            ),
            if (description.isNotEmpty)
              _ConfirmationRow(label: 'الوصف:', value: description),

            const SizedBox(height: 20),
            const Divider(),
            const SizedBox(height: 20),

            // رسوم التحويل
            _ConfirmationRow(
              label: 'رسوم التحويل:',
              value: '0.00 ${wallet.currency}',
            ),
            _ConfirmationRow(
              label: 'إجمالي المبلغ:',
              value: '$amount ${wallet.currency}',
              isAmount: true,
            ),

            const SizedBox(height: 24),

            // أزرار التحكم
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: isProcessing ? null : onEdit,
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: AppColors.primary),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('تعديل'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: isProcessing ? null : onConfirm,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: isProcessing
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Text(
                            'تأكيد التحويل',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// صف التأكيد
class _ConfirmationRow extends StatelessWidget {
  const _ConfirmationRow({
    required this.label,
    required this.value,
    this.isAmount = false,
  });
  final String label;
  final String value;
  final bool isAmount;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(color: Colors.grey[600], fontSize: 16)),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: isAmount ? AppColors.primary : Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}

/// بطاقة التعليمات
class _InstructionsCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'تعليمات مهمة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '• تأكد من صحة معلومات المستقبل قبل التحويل',
              style: TextStyle(height: 1.5),
            ),
            const Text(
              '• لا يمكن إلغاء التحويل بعد التأكيد',
              style: TextStyle(height: 1.5),
            ),
            const Text(
              '• سيتم إشعار المستقبل بالتحويل فوراً',
              style: TextStyle(height: 1.5),
            ),
            const Text(
              '• رسوم التحويل مجانية حالياً',
              style: TextStyle(height: 1.5),
            ),
          ],
        ),
      ),
    );
  }
}
