import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import '../models/wallet_model.dart';
import '../models/wallet_transaction_model.dart';
import '../models/identity_verification_model.dart';
// import '../services/wallet_service.dart'; // Temporarily disabled
import '../services/identity_verification_service.dart';
import '../../account/providers/profile_completion_provider.dart' as profile;

part 'wallet_providers.g.dart';

final _logger = Logger('WalletProviders');

/// Provider لخدمة المحفظة - Temporarily disabled
/// 🏗️ ARCHITECTURE: Flutter (UI Only) → Go API → Supabase (Data Only)
// @riverpod
// WalletService walletService(Ref ref) {
//   return WalletService(ref);
// }

/// Provider لخدمة التحقق من الهوية
/// TODO: Migrate to Go backend API - currently disabled per Forever Plan
@riverpod
IdentityVerificationService identityVerificationService(Ref ref) {
  throw UnimplementedError(
    'Identity verification service needs migration to Go backend API. '
    'Direct Supabase calls are not allowed per Forever Plan Architecture.'
  );
}

/// Provider للمحفظة الحالية
/// 🏗️ ARCHITECTURE: Wallet data managed through Go Backend API
@riverpod
class WalletNotifier extends _$WalletNotifier {
  @override
  Future<WalletModel?> build() async {
    final service = ref.watch(walletServiceProvider);

    try {
      final wallet = await service.getUserWallet();
      
      // Check profile completion status (existing logic)
      ref.watch(profile.profileCompletionProvider);

      if (wallet == null) {
        _logger.info('No wallet found for user - this is normal for new users');
      } else {
        _logger.info('Successfully loaded wallet with balance: ${wallet.balance} ${wallet.currency}');
      }

      return wallet;
    } catch (e) {
      _logger.severe('WalletNotifier error: $e');

      if (e.toString().contains('permission denied')) {
        _logger.warning(
          'Permission denied error in wallet provider - RLS policy issue?',
        );
      }
      
      // Return null instead of re-throwing to prevent app crashes
      _logger.warning('Returning null wallet due to error to prevent app crash');
      return null;
    }
  }

  /// Refreshes the wallet data
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final service = ref.read(walletServiceProvider);
      final wallet = await service.getUserWallet();
      if (wallet == null) {
        _logger.info('Wallet refresh returned null - user may not have wallet yet');
      }
      return wallet;
    });
  }
}

/// Provider لعمليات المحفظة - يستخدم Go Backend لجميع العمليات المالية
@riverpod
class WalletOperationsNotifier extends _$WalletOperationsNotifier {
  @override
  FutureOr<void> build() {
    // لا يحتاج إلى state أولي
  }

  /// إيداع أموال
  Future<WalletTransactionModel> deposit({
    required double amount,
    required String paymentMethod,
    required String paymentReference,
    String? description,
  }) async {
    state = const AsyncValue.loading();

    final result = await AsyncValue.guard(() async {
      final service = ref.read(walletServiceProvider);
      final transaction = await service.deposit(
        amount: amount,
        paymentMethod: paymentMethod,
        paymentReference: paymentReference,
        description: description,
      );
      
      if (transaction == null) {
        throw Exception('فشل في إيداع المبلغ');
      }
      
      return transaction;
    });

    // تحديث المحفظة والمعاملات
    ref.invalidate(walletNotifierProvider);
    ref.invalidate(walletTransactionsNotifierProvider);

    state = result;
    return result.value!;
  }

  /// سحب أموال
  Future<WalletTransactionModel> withdraw({
    required double amount,
    required String bankName,
    required String accountNumber,
    required String accountHolderName,
    required String iban,
    String? swiftCode,
  }) async {
    state = const AsyncValue.loading();

    final result = await AsyncValue.guard(() async {
      final service = ref.read(walletServiceProvider);
      final transaction = await service.withdraw(
        amount: amount,
        bankName: bankName,
        accountNumber: accountNumber,
        accountHolderName: accountHolderName,
        iban: iban,
        swiftCode: swiftCode,
      );
      
      if (transaction == null) {
        throw Exception('فشل في سحب المبلغ');
      }
      
      return transaction;
    });

    // تحديث المحفظة والمعاملات
    ref.invalidate(walletNotifierProvider);
    ref.invalidate(walletTransactionsNotifierProvider);

    state = result;
    return result.value!;
  }

  /// تحويل أموال
  Future<void> transfer({
    required String toUserId,
    required double amount,
    String? description,
  }) async {
    state = const AsyncValue.loading();

    final result = await AsyncValue.guard(() async {
      final service = ref.read(walletServiceProvider);
      return service.transferToUser(
        toUserId: toUserId,
        amount: amount,
        description: description,
      );
    });

    // تحديث المحفظة والمعاملات
    ref.invalidate(walletNotifierProvider);
    ref.invalidate(walletTransactionsNotifierProvider);

    state = result;
  }
}

/// Provider لتاريخ المعاملات
@riverpod
class WalletTransactionsNotifier extends _$WalletTransactionsNotifier {
  @override
  Future<List<WalletTransactionModel>> build() async {
    final service = ref.watch(walletServiceProvider);

    try {
      return await service.getTransactions();
    } catch (e) {
      if (e.toString().contains('String is not a subtype of type int')) {
        _logger.severe('Error loading wallet transactions - Type conversion error (likely response parsing issue): $e');
      } else if (e.toString().contains('404')) {
        _logger.info('No wallet found for user - this is expected for new users');
        return []; // Empty list for new users without wallets
      } else if (e.toString().contains('network') || e.toString().contains('timeout')) {
        _logger.warning('Network error loading wallet transactions: $e');
      } else {
        _logger.severe('Error loading wallet transactions: $e');
      }
      
      // إرجاع قائمة فارغة بدلاً من إعادة إلقاء الخطأ
      return [];
    }
  }

  /// جلب المعاملات مع pagination
  Future<List<WalletTransactionModel>> getTransactionsByPage({
    int page = 1,
    int limit = 20,
    String? status,
  }) async {
    final service = ref.read(walletServiceProvider);
    try {
      return await service.getTransactions(
        page: page,
        limit: limit,
        status: status,
      );
    } catch (e) {
      _logger.severe('Error loading paginated wallet transactions: $e');
      return [];
    }
  }

  /// جلب المزيد من المعاملات (للتمرير اللانهائي)
  Future<List<WalletTransactionModel>> loadMoreTransactions({
    required int currentCount,
    int limit = 20,
  }) async {
    final service = ref.read(walletServiceProvider);
    try {
      // Calculate page number from current count
      final page = (currentCount / limit).ceil() + 1;
      return await service.getTransactions(
        page: page,
        limit: limit,
      );
    } catch (e) {
      _logger.severe('Error loading more wallet transactions: $e');
      return [];
    }
  }

  /// تحديث المعاملات
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final service = ref.read(walletServiceProvider);
      try {
        return await service.getTransactions();
      } catch (e) {
        _logger.severe('Error refreshing wallet transactions: $e');
        // في حالة الفشل، إرجاع قائمة فارغة بدلاً من خطأ
        return <WalletTransactionModel>[];
      }
    });
  }

  /// تحميل المزيد من المعاملات
  Future<void> loadMore() async {
    final currentState = state.value;
    if (currentState == null) return;

    try {
      final service = ref.read(walletServiceProvider);
      final page = (currentState.length / 20).ceil() + 1;
      final newTransactions = await service.getTransactions(
        page: page,
        limit: 20,
      );

      if (newTransactions.isNotEmpty) {
        state = AsyncValue.data([...currentState, ...newTransactions]);
      }
    } catch (e) {
      _logger.severe('Error loading more wallet transactions: $e');
      // في حالة الفشل، الاحتفاظ بالحالة الحالية
    }
  }
}

/// Provider لطلبات السحب (simplified for now)
@riverpod
class WithdrawalRequestsNotifier extends _$WithdrawalRequestsNotifier {
  @override
  Future<List<WithdrawalRequestModel>> build() async {
    // Note: Backend doesn't have separate withdrawal requests endpoint yet
    return [];
  }

  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async => <WithdrawalRequestModel>[]);
  }

  /// إنشاء طلب سحب جديد
  Future<WalletTransactionModel> createWithdrawalRequest({
    required double amount,
    required String bankName,
    required String accountNumber,
    required String accountHolderName,
    required String iban,
    String? swiftCode,
  }) async {
    final service = ref.read(walletServiceProvider);
    final transaction = await service.withdraw(
      amount: amount,
      bankName: bankName,
      accountNumber: accountNumber,
      accountHolderName: accountHolderName,
      iban: iban,
      swiftCode: swiftCode,
    );

    if (transaction == null) {
      throw Exception('فشل في إنشاء طلب السحب');
    }

    // تحديث القائمة
    await refresh();
    // تحديث المحفظة
    ref.invalidate(walletNotifierProvider);

    return transaction;
  }
}

/// Provider لمستوى التحقق الحالي
@riverpod
Future<VerificationLevel> currentVerificationLevel(Ref ref) async {
  try {
    final wallet = await ref.watch(walletNotifierProvider.future);
    
    if (wallet == null) {
      return VerificationLevel.unverified;
    }
    
    // تحويل مستوى التحقق من string إلى enum
    switch (wallet.verificationLevel) {
      case 'basic_verified':
        return VerificationLevel.basicVerified;
      case 'fully_verified':
        return VerificationLevel.fullyVerified;
      case 'unverified':
      default:
        return VerificationLevel.unverified;
    }
  } catch (e) {
    _logger.severe('Error getting verification level: $e');
    return VerificationLevel.unverified;
  }
}

/// Provider لإحصائيات المحفظة
@riverpod
Future<WalletStatistics> walletStatistics(Ref ref) async {
  try {
    final wallet = await ref.watch(walletNotifierProvider.future);
    final transactions = await ref.watch(walletTransactionsNotifierProvider.future);

    if (wallet == null) {
      return const WalletStatistics(
        totalTransactions: 0,
        totalDeposits: 0,
        totalWithdrawals: 0,
        totalTransfers: 0,
        pendingTransactions: 0,
      );
    }

    final int totalTransactions = transactions.length;
    double totalDeposits = 0;
    double totalWithdrawals = 0;
    double totalTransfers = 0;
    int pendingTransactions = 0;

    for (final transaction in transactions) {
      switch (transaction.type) {
        case WalletTransactionType.deposit:
          totalDeposits += transaction.amount;
          break;
        case WalletTransactionType.withdraw:
          totalWithdrawals += transaction.amount;
          break;
        case WalletTransactionType.transferIn:
          totalDeposits += transaction.amount;
          totalTransfers += transaction.amount;
          break;
        case WalletTransactionType.transferOut:
          totalWithdrawals += transaction.amount;
          totalTransfers += transaction.amount;
          break;
        case WalletTransactionType.fee:
        case WalletTransactionType.refund:
          break;
      }

      if (transaction.status == WalletTransactionStatus.pending) {
        pendingTransactions++;
      }
    }

    return WalletStatistics(
      totalTransactions: totalTransactions,
      totalDeposits: totalDeposits,
      totalWithdrawals: totalWithdrawals,
      totalTransfers: totalTransfers,
      pendingTransactions: pendingTransactions,
    );
  } catch (e) {
    _logger.severe('Error calculating wallet statistics: $e');
    return const WalletStatistics(
      totalTransactions: 0,
      totalDeposits: 0,
      totalWithdrawals: 0,
      totalTransfers: 0,
      pendingTransactions: 0,
    );
  }
}

/// Provider للمحفظة الحالية (alias for compatibility)
@riverpod
Future<WalletModel?> currentWallet(Ref ref) async {
  return ref.watch(walletNotifierProvider.future);
}

/// Provider لمعاملات المحفظة (alias for compatibility)
@riverpod
Future<List<WalletTransactionModel>> currentWalletTransactions(Ref ref) async {
  return ref.watch(walletTransactionsNotifierProvider.future);
}

/// Provider لأحداث المعاملات (Real-time) - simplified for now
@riverpod
Stream<Map<String, dynamic>> walletTransactionsEvents(Ref ref) {
  // TODO: Implement real-time updates when backend supports it
  return const Stream.empty();
}

/// نموذج إحصائيات المحفظة
class WalletStatistics {
  const WalletStatistics({
    required this.totalTransactions,
    required this.totalDeposits,
    required this.totalWithdrawals,
    required this.totalTransfers,
    required this.pendingTransactions,
    this.lastTransactionDate,
  });

  final int totalTransactions;
  final double totalDeposits;
  final double totalWithdrawals;
  final double totalTransfers;
  final int pendingTransactions;
  final DateTime? lastTransactionDate;
}

/// Provider لحالة التحقق من الهوية
@riverpod
class IdentityVerificationNotifier extends _$IdentityVerificationNotifier {
  @override
  Future<List<IdentityVerificationModel>> build() async {
    final service = ref.watch(identityVerificationServiceProvider);
    return service.getMyVerifications();
  }

  /// إرسال طلب التحقق
  Future<IdentityVerificationModel> submitVerification({
    required String documentTypeId,
    required String documentNumber,
    required String documentImageUrl,
    DateTime? documentExpiry,
    String? notes,
  }) async {
    final service = ref.read(identityVerificationServiceProvider);
    final verification = await service.submitVerification(
      documentTypeId: documentTypeId,
      documentNumber: documentNumber,
      documentImageUrl: documentImageUrl,
      documentExpiry: documentExpiry,
      notes: notes,
    );

    // تحديث القائمة
    refresh();
    // تحديث المحفظة
    ref.invalidate(walletNotifierProvider);

    return verification;
  }

  /// إرسال طلب تحقق متعدد المستندات (متوافق مع واجهة المستخدم)
  Future<void> submitVerificationRequest({
    required Map<String, dynamic> documents,
    required bool isSellerAccount,
  }) async {
    // Loop through each uploaded document and submit it individually.
    for (final entry in documents.entries) {
      if (entry.value is String) {
        // Assume it's already an uploaded image URL
        await submitVerification(
          documentTypeId: entry.key,
          documentNumber: '',
          documentImageUrl: entry.value as String,
        );
      }
    }

    // Refresh state after uploading all documents
    await refresh();
    ref.invalidate(walletNotifierProvider);
  }

  /// تحديث حالة التحقق
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final service = ref.read(identityVerificationServiceProvider);
      return service.getMyVerifications();
    });
  }
}

/// Provider لأنواع المستندات
@riverpod
Future<List<DocumentTypeModel>> documentTypes(Ref ref) async {
  final service = ref.watch(identityVerificationServiceProvider);
  return service.getDocumentTypes();
}

/// Provider لحالات التحقق
@riverpod
Future<List<VerificationStatusModel>> verificationStatuses(Ref ref) async {
  final service = ref.watch(identityVerificationServiceProvider);
  return service.getVerificationStatuses();
}

/// Provider للمعاملات مع pagination (Family Provider)
@riverpod
Future<List<WalletTransactionModel>> paginatedWalletTransactions(
  Ref ref,
  Map<String, dynamic> params,
) async {
  final service = ref.watch(walletServiceProvider);
  final page = params['page'] as int? ?? 1;
  final limit = params['limit'] as int? ?? 20;
  final status = params['status'] as String?;
  
  try {
    return await service.getTransactions(
      page: page,
      limit: limit,
      status: status,
    );
  } catch (e) {
    _logger.severe('Error loading paginated wallet transactions: $e');
    return [];
  }
}

/// Provider لأحداث المحفظة (Real-time) - simplified for now
@riverpod
Stream<Map<String, dynamic>> walletEvents(Ref ref) {
  // TODO: Implement real-time updates when backend supports it
  return const Stream.empty();
}

// =============================================================================
// PROVIDER ALIASES - For backward compatibility with screens
// =============================================================================

/// Alias for WalletNotifier - for backward compatibility
final carnowWalletNotifierProvider = walletNotifierProvider;

/// Alias for WalletTransactions - for backward compatibility  
final carnowTransactionNotifierProvider = walletTransactionsNotifierProvider;

/// Alias for UserNotifier - for backward compatibility
final carnowUserNotifierProvider = walletNotifierProvider;

/// User Wallet Provider (simple alias)
final userWalletProvider = walletNotifierProvider;

/// Carnow Health Check Provider
@riverpod
Future<Map<String, dynamic>> carnowHealthCheck(CarnowHealthCheckRef ref) async {
  try {
    final service = ref.watch(walletServiceProvider);
    // Mock implementation for now
    // TODO: Replace with actual API call when backend supports it
    return {
      'status': 'healthy',
      'version': '1.0.0',
      'timestamp': DateTime.now().toIso8601String(),
    };
  } catch (e) {
    _logger.severe('Error checking Carnow health: $e');
    return {
      'status': 'error',
      'error': e.toString(),
    };
  }
}
