import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/utils/unified_logger.dart';
import '../models/wallet_model.dart';
import '../repositories/wallet_repository.dart';

/// Wallet state
class WalletState {
  final WalletModel? wallet;
  final bool isLoading;
  final bool isUpdating;
  final String? error;

  const WalletState({
    this.wallet,
    this.isLoading = false,
    this.isUpdating = false,
    this.error,
  });

  WalletState copyWith({
    WalletModel? wallet,
    bool? isLoading,
    bool? isUpdating,
    String? error,
  }) {
    return WalletState(
      wallet: wallet ?? this.wallet,
      isLoading: isLoading ?? this.isLoading,
      isUpdating: isUpdating ?? this.isUpdating,
      error: error ?? this.error,
    );
  }
}

/// Wallet provider
final walletProvider = StateNotifierProvider<WalletNotifier, WalletState>((ref) {
  return WalletNotifier(ref.read(walletRepositoryProvider));
});

/// Wallet notifier
class WalletNotifier extends StateNotifier<WalletState> {
  final WalletRepository _repository;

  WalletNotifier(this._repository) : super(const WalletState());

  /// Get wallet balance
  Future<void> getWallet() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final wallet = await _repository.getWallet();
      
      state = state.copyWith(
        wallet: wallet,
        isLoading: false,
      );
      
      UnifiedLogger.info('Wallet loaded successfully: ${wallet.balance}');
    } catch (e) {
      UnifiedLogger.error('Failed to load wallet', error: e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Add funds to wallet
  Future<bool> addFunds(double amount) async {
    try {
      state = state.copyWith(isUpdating: true, error: null);
      
      final updatedWallet = await _repository.addFunds(amount);
      
      state = state.copyWith(
        wallet: updatedWallet,
        isUpdating: false,
      );
      
      UnifiedLogger.info('Funds added successfully: $amount');
      return true;
    } catch (e) {
      UnifiedLogger.error('Failed to add funds', error: e);
      state = state.copyWith(
        isUpdating: false,
        error: e.toString(),
      );
      return false;
    }
  }

  /// Deduct funds from wallet
  Future<bool> deductFunds(double amount) async {
    try {
      state = state.copyWith(isUpdating: true, error: null);
      
      final updatedWallet = await _repository.deductFunds(amount);
      
      state = state.copyWith(
        wallet: updatedWallet,
        isUpdating: false,
      );
      
      UnifiedLogger.info('Funds deducted successfully: $amount');
      return true;
    } catch (e) {
      UnifiedLogger.error('Failed to deduct funds', error: e);
      state = state.copyWith(
        isUpdating: false,
        error: e.toString(),
      );
      return false;
    }
  }

  /// Check if wallet has sufficient balance
  bool hasSufficientBalance(double amount) {
    final wallet = state.wallet;
    if (wallet == null) return false;
    return wallet.balance >= amount;
  }

  /// Get formatted balance
  String get formattedBalance {
    final wallet = state.wallet;
    if (wallet == null) return '0.00 د.ل';
    return '${wallet.balance.toStringAsFixed(2)} د.ل';
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Refresh wallet
  Future<void> refresh() async {
    await getWallet();
  }
}
