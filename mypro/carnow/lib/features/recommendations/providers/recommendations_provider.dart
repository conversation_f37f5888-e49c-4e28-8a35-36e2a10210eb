import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';
import 'package:carnow/features/products/models/product_model.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/services/recommendation_api_service.dart';
import '../models/recommendation_model.dart';
import '../services/recommendation_service.dart';

part 'recommendations_provider.g.dart';

final _logger = Logger('RecommendationProvider');

final recommendationServiceProvider = Provider(
  (ref) => RecommendationService(ref.read(simpleApiClientProvider)),
);

/// Personalized recommendations - Clean implementation using Go backend
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
@riverpod
Future<List<RecommendationGroup>> personalizedRecommendations(PersonalizedRecommendationsRef ref) async {
  try {
    _logger.info('Fetching personalized recommendations via Go backend API...');
    final apiService = ref.watch(recommendationApiServiceProvider);
    final data = await apiService.getPersonalizedRecommendations();
    
    // Convert API response to RecommendationGroup format
    final recommendations = _convertApiToRecommendationGroups(data);
    
    _logger.info('Fetched ${recommendations.length} recommendation groups');
    return recommendations;
  } catch (e, stack) {
    _logger.severe('Error fetching personalized recommendations: $e', e, stack);
    rethrow;
  }
}

/// Provider for product-specific recommendations - Clean implementation
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<RecommendationModel>> productRecommendations(
  ProductRecommendationsRef ref,
  String productId,
) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    final currentUser = ref.watch(currentUserProvider);

    if (currentUser?.id == null) {
      return [];
    }

    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/recommendations/products/$productId',
      queryParameters: {'limit': 5},
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch product recommendations: ${response.message}');
      return [];
    }

    final data = response.data!;
    final recommendationsJson = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data.containsKey('recommendations') && data['recommendations'] is List
            ? data['recommendations'] as List<dynamic>
            : <dynamic>[];

    final recommendations = <RecommendationModel>[];

    for (final entry in recommendationsJson.asMap().entries) {
      final index = entry.key;
      final recommendationData = entry.value as Map<String, dynamic>;

      final productData = recommendationData['product'] as Map<String, dynamic>? ?? recommendationData;

      final recommendation = RecommendationModel(
        id: '${currentUser!.id}_${productId}_similar_${productData['id']}',
        userId: currentUser.id,
        product: ProductModel(
          id: productData['id'].toString(),
          name: productData['name'] as String,
          price: (productData['price'] as num).toDouble(),
          isAvailable: productData['is_available'] as bool? ?? true,
          isFeatured: productData['is_featured'] as bool? ?? false,
          categoryId: productData['category_id']?.toString() ?? '',
          sellerId: productData['seller_id']?.toString() ?? '',
        ),
        type: RecommendationType.similarProducts,
        score: 0.8 - (index * 0.1),
        reason: 'Similar product',
        createdAt: DateTime.now(),
      );
      recommendations.add(recommendation);
    }

    return recommendations;
  } catch (e, stack) {
    _logger.severe('Error fetching product recommendations: $e', e, stack);
    return [];
  }
}

/// Provider for trending recommendations - Clean implementation
/// Forever Plan: Uses HTTP calls to Go backend only
@riverpod
Future<List<RecommendationModel>> trendingRecommendations(TrendingRecommendationsRef ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);

    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/recommendations/trending',
      queryParameters: {'limit': 10},
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch trending recommendations: ${response.message}');
      return [];
    }

    final data = response.data!;
    final trendingJson = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data.containsKey('products') && data['products'] is List
            ? data['products'] as List<dynamic>
            : <dynamic>[];

    final recommendations = <RecommendationModel>[];

    for (final entry in trendingJson.asMap().entries) {
      final index = entry.key;
      final productData = entry.value as Map<String, dynamic>;

      final recommendation = RecommendationModel(
        id: 'trending_${productData['id']}',
        userId: '', // Will be set by the caller
        product: ProductModel(
          id: productData['id'].toString(),
          name: productData['name'] as String,
          price: (productData['price'] as num).toDouble(),
          isAvailable: productData['is_available'] as bool? ?? true,
          isFeatured: productData['is_featured'] as bool? ?? false,
          categoryId: productData['category_id']?.toString() ?? '',
          sellerId: productData['seller_id']?.toString() ?? '',
        ),
        type: RecommendationType.trending,
        score: 0.9 - (index * 0.05),
        reason: 'Trending this week',
        createdAt: DateTime.now(),
      );
      recommendations.add(recommendation);
    }

    return recommendations;
  } catch (e, stack) {
    _logger.severe('Error fetching trending recommendations: $e', e, stack);
    return [];
  }
}

/// Provider for tracking user interactions - Clean implementation
/// Forever Plan: Uses HTTP calls to Go backend only
class UserInteractionNotifier extends StateNotifier<AsyncValue<void>> {
  UserInteractionNotifier(this.ref) : super(const AsyncValue.data(null));

  final Ref ref;

  /// Track product view
  Future<void> trackProductView(
    String productId, {
    Map<String, dynamic>? context,
  }) async {
    await _trackInteraction(productId, InteractionType.view, context);
  }

  /// Track product click
  Future<void> trackProductClick(
    String productId, {
    Map<String, dynamic>? context,
  }) async {
    await _trackInteraction(productId, InteractionType.click, context);
  }

  /// Track add to cart
  Future<void> trackAddToCart(
    String productId, {
    Map<String, dynamic>? context,
  }) async {
    await _trackInteraction(productId, InteractionType.addToCart, context);
  }

  /// Track purchase
  Future<void> trackPurchase(
    String productId, {
    Map<String, dynamic>? context,
  }) async {
    await _trackInteraction(productId, InteractionType.purchase, context);
  }

  /// Track add to favorites
  Future<void> trackAddToFavorites(
    String productId, {
    Map<String, dynamic>? context,
  }) async {
    await _trackInteraction(productId, InteractionType.favorite, context);
  }

  /// Track share
  Future<void> trackShare(
    String productId, {
    Map<String, dynamic>? context,
  }) async {
    await _trackInteraction(productId, InteractionType.share, context);
  }

  /// Track search
  Future<void> trackSearch(
    String query, {
    Map<String, dynamic>? context,
  }) async {
    final searchContext = {'query': query, ...?context};
    await _trackInteraction('search', InteractionType.search, searchContext);
  }

  /// Track general interaction - Clean implementation using Go backend
  Future<void> _trackInteraction(
    String productId,
    InteractionType type,
    Map<String, dynamic>? context,
  ) async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);
      final currentUser = ref.watch(currentUserProvider);

      if (currentUser?.id == null) {
        _logger.warning('Cannot track interaction: user not authenticated');
        return;
      }

      final response = await apiClient.postApi<Map<String, dynamic>>(
        '/analytics/interactions',
        data: {
          'user_id': currentUser!.id,
          'product_id': productId,
          'interaction_type': type.toString().split('.').last,
          'context': context ?? {},
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (!response.isSuccess) {
        _logger.warning('Failed to track interaction: ${response.message}');
      }

      // Invalidate recommendations to recalculate them
      ref.invalidate(personalizedRecommendationsProvider);
    } catch (e, stack) {
      _logger.severe('Error tracking interaction: $e', e, stack);
    }
  }
}

final userInteractionProvider =
    StateNotifierProvider<UserInteractionNotifier, AsyncValue<void>>(
      UserInteractionNotifier.new,
    );

// Helper functions for API data conversion
List<RecommendationGroup> _convertApiToRecommendationGroups(List<Map<String, dynamic>> data) {
  // Simple mock conversion for now - will be updated with actual backend data structure
  return [
    RecommendationGroup(
      type: RecommendationType.personalized,
      title: 'المنتجات المقترحة لك',
      subtitle: 'منتجات مختارة بناءً على تفضيلاتك',
      recommendations: data.map((item) => RecommendationModel(
        id: item['id'] ?? '',
        userId: '', // Will be populated from user context
        product: ProductModel(
          id: item['id'] ?? '',
          name: item['title'] ?? '',
          description: item['description'] ?? '',
          price: (item['price'] as num?)?.toDouble() ?? 0.0,
          images: item['image_url'] != null ? [item['image_url']] : [],
          categoryId: item['category_id'] ?? '',
          sellerId: item['seller_id'] ?? '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        type: RecommendationType.personalized,
        score: 0.8,
        reason: 'مقترح بناءً على تفضيلاتك',
      )).toList(),
    ),
  ];
}
