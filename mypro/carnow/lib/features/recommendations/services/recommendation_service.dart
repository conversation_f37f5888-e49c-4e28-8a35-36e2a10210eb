import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../models/recommendation_model.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// خدمة التوصيات الذكية – الآن تعتمد على Go Backend فقط
class RecommendationService {
  RecommendationService(this._apiClient);

  final SimpleApiClient _apiClient;

  static final _logger = Logger('RecommendationService');

  Future<List<RecommendationGroup>> generatePersonalizedRecommendations(
    String userId, {
    int maxGroups = 5,
    int maxItemsPerGroup = 10,
  }) async {
    try {
      _logger.info(
        'Requesting personalized recommendations for user: $userId',
      );

      final response = await _apiClient.getApi<Map<String, dynamic>>(
        '/recommendations/personalized',
        queryParameters: {
          'user_id': userId,
          'max_groups': maxGroups,
          'max_items': maxItemsPerGroup,
        },
      );

      if (!response.isSuccess || response.data == null) {
        _logger.warning('Recommendation API failed: ${response.message}');
        return [];
      }

      final data = response.data!;
      final groupsJson = data['groups'] as List<dynamic>? ?? [];

      return groupsJson
          .map((json) => RecommendationGroup.fromJson(
              json as Map<String, dynamic>))
          .toList();
    } catch (e, stack) {
      _logger.severe('Error fetching recommendations: $e', e, stack);
      return [];
    }
  }

  // All helper methods that relied on Supabase have been removed because the
  // backend now delivers ready-made recommendation groups.

  // Dummy conversion helpers are not required anymore. Front-end receives
  // fully-formed JSON structures from backend.
}

/// Provider for RecommendationService following Forever Plan Architecture
final recommendationServiceProvider = Provider<RecommendationService>((ref) {
  final apiClient = ref.read(simpleApiClientProvider);
  return RecommendationService(apiClient);
});
