import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/auth/unified_auth_provider.dart';

import '../models/models.dart';
import '../repositories/unified_orders_repository.dart';

part 'unified_orders_providers.g.dart';

/// موفر جميع طلبات المستخدم
@riverpod
class UserOrders extends _$UserOrders {
  @override
  Future<List<UnifiedOrder>> build({
    OrderType? orderType,
    OrderStatus? status,
    int? limit,
  }) async {
    final user = ref.watch(currentUserProvider);
    final userId = user?.id ?? '';
    if (userId.isEmpty) return [];

    final repository = ref.watch(unifiedOrdersRepositoryProvider);
    return repository.getUserOrders(
      userId: userId,
      orderType: orderType,
      status: status,
      limit: limit,
    );
  }

  /// تحديث قائمة الطلبات
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => future);
  }

  /// إضافة طلب جديد إلى القائمة
  void addOrder(UnifiedOrder newOrder) {
    state.whenData((orders) {
      state = AsyncValue.data([newOrder, ...orders]);
    });
  }

  /// تحديث طلب موجود
  void updateOrder(UnifiedOrder updatedOrder) {
    state.whenData((orders) {
      final index = orders.indexWhere((order) => order.id == updatedOrder.id);
      if (index != -1) {
        final newOrders = [...orders];
        newOrders[index] = updatedOrder;
        state = AsyncValue.data(newOrders);
      }
    });
  }

  /// إزالة طلب من القائمة
  void removeOrder(String orderId) {
    state.whenData((orders) {
      final newOrders = orders.where((order) => order.id != orderId).toList();
      state = AsyncValue.data(newOrders);
    });
  }
}

/// موفر طلبات حسب النوع
@riverpod
Future<List<UnifiedOrder>> ordersByType(Ref ref, OrderType orderType) async {
  final user = ref.watch(currentUserProvider);
  final userId = user?.id ?? '';
  if (userId.isEmpty) return [];

  final repository = ref.watch(unifiedOrdersRepositoryProvider);
  return repository.getUserOrders(userId: userId, orderType: orderType);
}

/// موفر طلبات حسب الحالة
@riverpod
Future<List<UnifiedOrder>> ordersByStatus(Ref ref, OrderStatus status) async {
  final user = ref.watch(currentUserProvider);
  final userId = user?.id ?? '';
  if (userId.isEmpty) return [];

  final repository = ref.watch(unifiedOrdersRepositoryProvider);
  return repository.getUserOrders(userId: userId, status: status);
}

/// موفر طلب محدد
@riverpod
Future<UnifiedOrder?> orderById(Ref ref, String orderId) async {
  final repository = ref.watch(unifiedOrdersRepositoryProvider);
  return repository.getOrderById(orderId);
}

/// موفر الطلبات النشطة
@riverpod
Future<List<UnifiedOrder>> activeOrders(Ref ref) async {
  final user = ref.watch(currentUserProvider);
  final userId = user?.id ?? '';
  if (userId.isEmpty) return [];

  final repository = ref.watch(unifiedOrdersRepositoryProvider);
  final allOrders = await repository.getUserOrders(userId: userId);

  return allOrders
      .where(
        (order) =>
            order.status == OrderStatus.pending ||
            order.status == OrderStatus.confirmed ||
            order.status == OrderStatus.processing ||
            order.status == OrderStatus.shipped,
      )
      .toList();
}

/// موفر إحصائيات الطلبات
@riverpod
Future<Map<String, dynamic>> orderStatistics(
  Ref ref, {
  OrderType? orderType,
  DateTime? startDate,
  DateTime? endDate,
}) async {
  final user = ref.watch(currentUserProvider);
  final userId = user?.id ?? '';
  if (userId.isEmpty) return {};

  final repository = ref.watch(unifiedOrdersRepositoryProvider);
  return repository.getOrderStatistics(
    userId: userId,
    orderType: orderType,
    startDate: startDate,
    endDate: endDate,
  );
}

/// موفر البحث في الطلبات
@riverpod
class OrderSearch extends _$OrderSearch {
  @override
  Future<List<UnifiedOrder>> build() async {
    return [];
  }

  /// البحث في الطلبات
  Future<void> searchOrders({
    String? searchQuery,
    OrderType? orderType,
    OrderStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    final user = ref.watch(currentUserProvider);
    final userId = user?.id ?? '';
    if (userId.isEmpty) {
      state = const AsyncValue.data([]);
      return;
    }

    state = const AsyncValue.loading();

    try {
      final repository = ref.watch(unifiedOrdersRepositoryProvider);
      final results = await repository.searchOrders(
        userId: userId,
        searchQuery: searchQuery,
        orderType: orderType,
        status: status,
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );

      state = AsyncValue.data(results);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// مسح نتائج البحث
  void clearSearch() {
    state = const AsyncValue.data([]);
  }
}

/// موفر إنشاء طلب جديد
@riverpod
class CreateOrder extends _$CreateOrder {
  @override
  FutureOr<UnifiedOrder?> build() {
    return null;
  }

  /// إنشاء طلب جديد
  Future<UnifiedOrder> createOrder({
    required OrderType orderType,
    required List<OrderItem> items,
    required ShippingAddress shippingAddress,
    String? sellerId,
    String? buyerName,
    String? buyerEmail,
    String? sellerName,
    String? sellerEmail,
    PaymentInfo? paymentInfo,
    OrderPriority priority = OrderPriority.normal,
    String? notes,
    DateTime? estimatedDeliveryDate,
    Map<String, dynamic>? metadata,
  }) async {
    final user = ref.watch(currentUserProvider);
    final userId = user?.id ?? '';
    if (userId.isEmpty) {
      throw Exception('المستخدم غير مسجل الدخول');
    }

    state = const AsyncValue.loading();

    try {
      final repository = ref.watch(unifiedOrdersRepositoryProvider);
      final newOrder = await repository.createOrder(
        buyerId: userId,
        orderType: orderType,
        items: items,
        shippingAddress: shippingAddress,
        sellerId: sellerId,
        buyerName: buyerName ?? user?.email,
        buyerEmail: buyerEmail ?? user?.email,
        sellerName: sellerName,
        sellerEmail: sellerEmail,
        paymentInfo: paymentInfo,
        priority: priority,
        notes: notes,
        estimatedDeliveryDate: estimatedDeliveryDate,
        metadata: metadata,
      );

      state = AsyncValue.data(newOrder);

      // تحديث قائمة الطلبات
      ref.invalidate(userOrdersProvider);
      ref.invalidate(activeOrdersProvider);

      return newOrder;
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  /// إلغاء إنشاء الطلب
  void cancelOrder() {
    state = const AsyncValue.data(null);
  }
}

/// موفر تحديث طلب
@riverpod
class UpdateOrder extends _$UpdateOrder {
  @override
  FutureOr<UnifiedOrder?> build() {
    return null;
  }

  /// تحديث طلب موجود
  Future<UnifiedOrder> updateOrder({
    required String orderId,
    OrderStatus? status,
    OrderPriority? priority,
    String? notes,
    DateTime? estimatedDeliveryDate,
    Map<String, dynamic>? metadata,
  }) async {
    final user = ref.watch(currentUserProvider);
    final userId = user?.id ?? '';
    if (userId.isEmpty) {
      throw Exception('المستخدم غير مسجل الدخول');
    }

    state = const AsyncValue.loading();

    try {
      final repository = ref.watch(unifiedOrdersRepositoryProvider);
      final updatedOrder = await repository.updateOrder(
        orderId: orderId,
        userId: userId,
        status: status,
        priority: priority,
        notes: notes,
        estimatedDeliveryDate: estimatedDeliveryDate,
        metadata: metadata,
      );

      state = AsyncValue.data(updatedOrder);

      // تحديث الموفرات ذات الصلة
      ref.invalidate(userOrdersProvider);
      ref.invalidate(activeOrdersProvider);
      ref.invalidate(orderByIdProvider);

      return updatedOrder;
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }
}

/// موفر تفاصيل طلب محدد
@riverpod
Future<UnifiedOrder?> orderDetails(Ref ref, String orderId) async {
  final repository = ref.watch(unifiedOrdersRepositoryProvider);
  try {
    return await repository.getOrderById(orderId);
  } catch (e) {
    throw Exception('فشل في تحميل تفاصيل الطلب: $e');
  }
}

/// موفر حالة طلب محدد (للقراءة فقط)
@riverpod
Future<String> orderStatus(Ref ref, String orderId) async {
  final repository = ref.watch(unifiedOrdersRepositoryProvider);
  try {
    final order = await repository.getOrderById(orderId);
    return order?.status.name ?? 'unknown';
  } catch (e) {
    return 'error';
  }
}
