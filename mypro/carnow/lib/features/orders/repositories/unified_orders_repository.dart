import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import 'package:uuid/uuid.dart';
import 'dart:developer' as dev;

import '../../../core/networking/simple_api_client.dart';
import '../models/models.dart';

part 'unified_orders_repository.g.dart';

/// مستودع موحد لإدارة جميع عمليات الطلبات
class UnifiedOrdersRepository {
  UnifiedOrdersRepository(this._apiClient);
  final SimpleApiClient _apiClient;
  final _uuid = const Uuid();

  /// Logger function for consistent logging
  void log(String message) {
    dev.log(message, name: 'UnifiedOrdersRepository');
  }

  /// الحصول على جميع الطلبات للمستخدم
  Future<List<UnifiedOrder>> getUserOrders({
    required String userId,
    OrderType? orderType,
    OrderStatus? status,
    int? limit,
    int? offset,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'user_id': userId,
      };

      if (orderType != null) {
        queryParams['order_type'] = orderType.name;
      }

      if (status != null) {
        queryParams['status'] = status.name;
      }

      if (limit != null) {
        queryParams['limit'] = limit;
      }

      if (offset != null) {
        queryParams['offset'] = offset;
      }

      final response = await _apiClient.get('/orders', queryParameters: queryParams);

      // Handle null or non-list responses gracefully
      if (response.data == null) {
        log('تحذير: API returned null data for orders');
        return [];
      }
      
      if (response.data is! List) {
        log('تحذير: API returned non-list data for orders: ${response.data.runtimeType}');
        return [];
      }
      
      return (response.data as List).map<UnifiedOrder>(_mapToUnifiedOrder).toList();
    } catch (e) {
      log('خطأ في جلب طلبات المستخدم: $e');
      throw Exception('فشل في جلب الطلبات: $e');
    }
  }

  /// الحصول على طلب محدد
  Future<UnifiedOrder?> getOrderById(String orderId) async {
    try {
      final response = await _apiClient.get('/orders/$orderId');

      if (response.data == null) return null;

      return _mapToUnifiedOrder(response.data);
    } catch (e) {
      if (e is DioException && e.response?.statusCode == 404) {
        return null;
      }
      log('خطأ في جلب الطلب: $e');
      throw Exception('فشل في جلب الطلب: $e');
    }
  }

  /// إنشاء طلب جديد
  Future<UnifiedOrder> createOrder({
    required String buyerId,
    required OrderType orderType,
    required List<OrderItem> items,
    required ShippingAddress shippingAddress,
    String? sellerId,
    String? buyerName,
    String? buyerEmail,
    String? sellerName,
    String? sellerEmail,
    PaymentInfo? paymentInfo,
    OrderPriority priority = OrderPriority.normal,
    String? notes,
    DateTime? estimatedDeliveryDate,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // حساب المبالغ
      final subtotal = items.fold<double>(
        0,
        (sum, item) => sum + item.totalPrice,
      );
      final taxAmount = subtotal * 0.15; // 15% ضريبة
      final shippingCost = _calculateShippingCost(orderType, subtotal);
      final totalAmount = subtotal + taxAmount + shippingCost;

      // إنشاء رقم الطلب
      final orderNumber = _generateOrderNumber(orderType);

      // إنشاء الاسم الوصفي
      final displayName = UnifiedOrder.generateDisplayName(
        orderType: orderType,
        orderNumber: orderNumber,
        customerName: buyerName,
      );

      // إنشاء ID للطلب
      final orderId = _uuid.v4();

      // إنشاء بيانات الطلب
      final orderData = {
        'id': orderId,
        'order_number': orderNumber,
        'display_name': displayName,
        'order_type': orderType.name,
        'status': OrderStatus.pending.name,
        'priority': priority.name,
        'buyer_id': buyerId,
        'seller_id': sellerId,
        'buyer_name': buyerName,
        'buyer_email': buyerEmail,
        'seller_name': sellerName,
        'seller_email': sellerEmail,
        'subtotal': subtotal,
        'tax_amount': taxAmount,
        'shipping_cost': shippingCost,
        'total_amount': totalAmount,
        'currency': 'SAR',
        'notes': notes,
        'estimated_delivery_date': estimatedDeliveryDate?.toIso8601String(),
        'metadata': metadata,
      };

      // إنشاء بيانات الطلب الكاملة
      final fullOrderData = {
        ...orderData,
        'order_items': items.map((item) => {
          'id': _uuid.v4(),
          'order_id': orderId,
          'product_id': item.productId,
          'product_name': item.productName,
          'product_sku': item.productSku,
          'product_image_url': item.productImageUrl,
          'quantity': item.quantity,
          'unit_price': item.unitPrice,
          'total_price': item.totalPrice,
          'notes': item.notes,
          'specifications': item.specifications,
        }).toList(),
        'shipping_address': {
          'id': _uuid.v4(),
          'order_id': orderId,
          'full_name': shippingAddress.fullName,
          'phone_number': shippingAddress.phoneNumber,
          'address_line_1': shippingAddress.addressLine1,
          'address_line_2': shippingAddress.addressLine2,
          'city': shippingAddress.city,
          'state': shippingAddress.state,
          'postal_code': shippingAddress.postalCode,
          'country': shippingAddress.country,
          'special_instructions': shippingAddress.specialInstructions,
          'is_default': shippingAddress.isDefault,
        },
        if (paymentInfo != null) 'payment_info': {
          'id': _uuid.v4(),
          'order_id': orderId,
          'method': paymentInfo.method,
          'transaction_id': paymentInfo.transactionId,
          'payment_gateway': paymentInfo.paymentGateway,
          'paid_amount': paymentInfo.paidAmount,
          'currency': paymentInfo.currency,
          'paid_at': paymentInfo.paidAt?.toIso8601String(),
          'metadata': paymentInfo.metadata,
        },
      };

      // إرسال الطلب إلى API
      final response = await _apiClient.post('/orders', data: fullOrderData);

      // إرجاع الطلب المُنشأ من الاستجابة
      return _mapToUnifiedOrder(response.data);
    } catch (e) {
      log('خطأ في إنشاء الطلب: $e');
      throw Exception('فشل في إنشاء الطلب: $e');
    }
  }

  /// تحديث حالة الطلب
  Future<UnifiedOrder> updateOrderStatus({
    required String orderId,
    required OrderStatus newStatus,
    String? notes,
    required String updatedBy,
    String? trackingNumber,
  }) async {
    try {
      // تحديث الطلب
      final updateData = <String, dynamic>{
        'status': newStatus.name,
        'notes': notes,
        'updated_by': updatedBy,
      };

      if (trackingNumber != null) {
        updateData['tracking_number'] = trackingNumber;
      }

      final response = await _apiClient.put('/orders/$orderId/status', data: updateData);

      return _mapToUnifiedOrder(response.data);
    } catch (e) {
      log('خطأ في تحديث حالة الطلب: $e');
      throw Exception('فشل في تحديث حالة الطلب: $e');
    }
  }

  /// إلغاء الطلب
  Future<UnifiedOrder> cancelOrder({
    required String orderId,
    required String cancellationReason,
    required String cancelledBy,
  }) async {
    return updateOrderStatus(
      orderId: orderId,
      newStatus: OrderStatus.cancelled,
      notes: 'سبب الإلغاء: $cancellationReason',
      updatedBy: cancelledBy,
    );
  }

  /// البحث في الطلبات
  Future<List<UnifiedOrder>> searchOrders({
    required String userId,
    String? searchQuery,
    OrderType? orderType,
    OrderStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'user_id': userId,
      };

      if (searchQuery != null && searchQuery.isNotEmpty) {
        queryParams['search'] = searchQuery;
      }

      if (orderType != null) {
        queryParams['order_type'] = orderType.name;
      }

      if (status != null) {
        queryParams['status'] = status.name;
      }

      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }

      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }

      if (limit != null) {
        queryParams['limit'] = limit;
      }

      final response = await _apiClient.get('/orders/search', queryParameters: queryParams);

      // Handle null or non-list responses gracefully
      if (response.data == null) {
        log('تحذير: API returned null data for orders search');
        return [];
      }
      
      if (response.data is! List) {
        log('تحذير: API returned non-list data for orders search: ${response.data.runtimeType}');
        return [];
      }
      
      return (response.data as List).map<UnifiedOrder>(_mapToUnifiedOrder).toList();
    } catch (e) {
      log('خطأ في البحث في الطلبات: $e');
      throw Exception('فشل في البحث في الطلبات: $e');
    }
  }

  /// الحصول على إحصائيات الطلبات
  Future<Map<String, dynamic>> getOrderStatistics({
    required String userId,
    OrderType? orderType,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'user_id': userId,
      };

      if (orderType != null) {
        queryParams['order_type'] = orderType.name;
      }

      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }

      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }

      final response = await _apiClient.get('/orders/statistics', queryParameters: queryParams);
      
      // Handle null or non-list responses gracefully
      if (response.data == null) {
        log('تحذير: API returned null data for orders statistics');
        return {
          'total_orders': 0,
          'total_amount': 0,
          'pending_orders': 0,
          'processing_orders': 0,
          'shipped_orders': 0,
          'delivered_orders': 0,
          'cancelled_orders': 0,
          'average_order_value': 0,
          'orders_by_type': <String, int>{},
        };
      }
      
      if (response.data is! List) {
        log('تحذير: API returned non-list data for orders statistics: ${response.data.runtimeType}');
        return {
          'total_orders': 0,
          'total_amount': 0,
          'pending_orders': 0,
          'processing_orders': 0,
          'shipped_orders': 0,
          'delivered_orders': 0,
          'cancelled_orders': 0,
          'average_order_value': 0,
          'orders_by_type': <String, int>{},
        };
      }
      
      final orders = response.data as List;

      final stats = <String, dynamic>{
        'total_orders': orders.length,
        'total_amount': 0,
        'pending_orders': 0,
        'processing_orders': 0,
        'shipped_orders': 0,
        'delivered_orders': 0,
        'cancelled_orders': 0,
        'average_order_value': 0,
        'orders_by_type': <String, int>{},
        'orders_by_month': <String, int>{},
      };

      double totalAmount = 0;
      final ordersByType = <String, int>{};
      final ordersByMonth = <String, int>{};

      for (final order in orders) {
        final orderData = order as Map<String, dynamic>;

        // حساب المجموع
        final amount = (orderData['total_amount'] as num?)?.toDouble() ?? 0;
        totalAmount += amount;

        // عد الطلبات حسب الحالة
        final status = orderData['status'] as String?;
        switch (status) {
          case 'pending':
            stats['pending_orders'] = (stats['pending_orders'] as int) + 1;
            break;
          case 'processing':
            stats['processing_orders'] =
                (stats['processing_orders'] as int) + 1;
            break;
          case 'shipped':
            stats['shipped_orders'] = (stats['shipped_orders'] as int) + 1;
            break;
          case 'delivered':
            stats['delivered_orders'] = (stats['delivered_orders'] as int) + 1;
            break;
          case 'cancelled':
            stats['cancelled_orders'] = (stats['cancelled_orders'] as int) + 1;
            break;
        }

        // عد الطلبات حسب النوع
        final type = orderData['order_type'] as String?;
        if (type != null) {
          ordersByType[type] = (ordersByType[type] ?? 0) + 1;
        }

        // عد الطلبات حسب الشهر
        final createdAt = orderData['created_at'] as String?;
        if (createdAt != null) {
          final date = DateTime.parse(createdAt);
          final month = '${date.year}-${date.month.toString().padLeft(2, '0')}';
          ordersByMonth[month] = (ordersByMonth[month] ?? 0) + 1;
        }
      }

      stats['total_amount'] = totalAmount;
      stats['average_order_value'] = orders.isNotEmpty
          ? totalAmount / orders.length
          : 0;
      stats['orders_by_type'] = ordersByType;
      stats['orders_by_month'] = ordersByMonth;

      return stats;
    } catch (e) {
      log('خطأ في جلب إحصائيات الطلبات: $e');
      throw Exception('فشل في جلب إحصائيات الطلبات: $e');
    }
  }

  /// تحديث طلب
  Future<UnifiedOrder> updateOrder({
    required String orderId,
    required String userId,
    OrderStatus? status,
    OrderPriority? priority,
    String? notes,
    DateTime? estimatedDeliveryDate,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (status != null) {
        updateData['status'] = status.name;
      }

      if (priority != null) {
        updateData['priority'] = priority.name;
      }

      if (notes != null) {
        updateData['notes'] = notes;
      }

      if (estimatedDeliveryDate != null) {
        updateData['estimated_delivery_date'] = estimatedDeliveryDate
            .toIso8601String();
      }

      if (metadata != null) {
        updateData['metadata'] = metadata;
      }

      await _apiClient.put('/orders/$orderId', data: updateData);

      // إضافة سجل في تاريخ الحالات إذا تم تحديث الحالة
      if (status != null) {
        await _apiClient.post('/orders/$orderId/status-history', data: {
          'status': status.name,
          'changed_by': userId,
          'notes': notes,
        });
      }

      final updatedOrder = await getOrderById(orderId);
      if (updatedOrder == null) {
        throw Exception('فشل في استرجاع الطلب المُحدث');
      }

      return updatedOrder;
    } catch (e) {
      log('خطأ في تحديث الطلب: $e');
      throw Exception('فشل في تحديث الطلب: $e');
    }
  }

  /// حذف طلب
  Future<bool> deleteOrder({
    required String orderId,
    required String userId,
  }) async {
    try {
      // حذف الطلب (سيتم التحقق من الصلاحيات والحذف المتتالي في الخادم)
      await _apiClient.delete('/orders/$orderId');

      return true;
    } catch (e) {
      if (e is DioException && e.response?.statusCode == 404) {
        throw Exception('الطلب غير موجود أو ليس لديك صلاحية لحذفه');
      }
      log('خطأ في حذف الطلب: $e');
      throw Exception('فشل في حذف الطلب: $e');
    }
  }

  /// تصدير الطلبات إلى JSON
  Future<String> exportOrdersToJson({
    required String userId,
    OrderType? orderType,
    OrderStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final orders = await getUserOrders(
        userId: userId,
        orderType: orderType,
        status: status,
      );

      final exportData = {
        'export_date': DateTime.now().toIso8601String(),
        'user_id': userId,
        'filters': {
          'order_type': orderType?.name,
          'status': status?.name,
          'start_date': startDate?.toIso8601String(),
          'end_date': endDate?.toIso8601String(),
        },
        'total_orders': orders.length,
        'orders': orders.map((order) => order.toJson()).toList(),
      };

      return exportData.toString();
    } catch (e) {
      log('خطأ في تصدير الطلبات: $e');
      throw Exception('فشل في تصدير الطلبات: $e');
    }
  }

  /// حساب تكلفة الشحن
  double _calculateShippingCost(OrderType orderType, double subtotal) {
    switch (orderType) {
      case OrderType.autoParts:
        return subtotal > 500 ? 0 : 25;
      case OrderType.electronics:
        return subtotal > 300 ? 0 : 20;
      case OrderType.clothing:
        return subtotal > 200 ? 0 : 15;
      case OrderType.books:
        return subtotal > 100 ? 0 : 10;
      case OrderType.homeGarden:
      case OrderType.sports:
      case OrderType.general:
        return subtotal > 400 ? 0 : 20;
    }
  }

  /// توليد رقم الطلب
  String _generateOrderNumber(OrderType orderType) {
    final prefix = orderType.name.toUpperCase().substring(0, 2);
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '$prefix$timestamp';
  }

  /// تحويل البيانات الخام إلى UnifiedOrder
  UnifiedOrder _mapToUnifiedOrder(dynamic json) {
    // Ensure the incoming json is a Map<String, dynamic> before parsing
    final Map<String, dynamic> map = json as Map<String, dynamic>;
    try {
      return UnifiedOrder.fromJson(map);
    } catch (e) {
      log('خطأ في تحويل بيانات الطلب: $e');
      rethrow;
    }
  }
}

/// موفر مستودع الطلبات الموحد
@riverpod
UnifiedOrdersRepository unifiedOrdersRepository(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return UnifiedOrdersRepository(apiClient);
}
