import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../models/category_model.dart';

final _logger = Logger('CategoryRepository');

// FIXED: Simple provider using SimpleApiClient instead of direct Supabase
final categoryRepositoryProvider = Provider<CategoryRepository>((ref) {
  return CategoryRepository(ref.read(simpleApiClientProvider));
});

class CategoryRepository {
  CategoryRepository(this._client);
  final SimpleApiClient _client;

  /// جلب جميع الفئات المفعلة - FIXED: Now uses Go API backend
  Future<List<CategoryModel>> getActiveCategories() async {
    try {
      _logger.info('Fetching active categories from Go backend');

      final response = await _client.get('/categories');
      
      if (response.data != null) {
        final categories = (response.data as List<dynamic>).map((item) {
          final itemMap = item as Map<String, dynamic>;
          return CategoryModel.fromJson({
            'id': itemMap['id'],
            'name': itemMap['name'],
            'nameAr': itemMap['name_ar'] ?? itemMap['name'], // Use Arabic name if available
            'description': itemMap['description'],
            'slug': itemMap['slug'] ?? itemMap['name'].toString().toLowerCase().replaceAll(' ', '-'),
            'isActive': itemMap['is_active'] ?? true,
            'sortOrder': itemMap['sort_order'] ?? itemMap['id'],
            'type': itemMap['type'] ?? 'part',
          });
        }).toList();

        _logger.info('Found ${categories.length} active categories');
        return categories;
      }
      
      return [];
    } catch (e, stack) {
      _logger.severe('Error fetching active categories: $e', e, stack);
      // Forever Plan Architecture: NO MOCK DATA - throw proper error
      throw Exception('Failed to fetch categories from backend: $e');
    }
  }

  /// جلب جميع الفئات (نفس getActiveCategories لأن كل الفئات نشطة)
  Future<List<CategoryModel>> getAllCategories() => getActiveCategories();

  /// الحصول على معرف الفئة من الاسم العربي - FIXED: Now uses Go API backend
  Future<int?> getCategoryIdByName(String arabicName) async {
    try {
      _logger.info('Searching for category ID with name: $arabicName');

      final response = await _client.get('/categories/search', 
        queryParameters: {'name': arabicName});

      if (response.data != null && response.data.isNotEmpty) {
        final id = response.data[0]['id'] as int;
        _logger.info('Found category ID: $id for name: $arabicName');
        return id;
      }

      _logger.warning('No category found with name: $arabicName');
      return null;
    } catch (e, stack) {
      _logger.severe('Error searching category by name: $arabicName', e, stack);
      // Forever Plan Architecture: NO MOCK DATA - return null for proper error handling
      return null;
    }
  }

  /// الحصول على فئة بالـ slug - FIXED: Now uses Go API backend
  Future<CategoryModel> getCategoryBySlug(String slug) async {
    try {
      _logger.info('Fetching category by slug: $slug');

      final response = await _client.get('/categories/slug/$slug');

      if (response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final category = CategoryModel.fromJson({
          'id': data['id'],
          'name': data['name'],
          'nameAr': data['name_ar'] ?? data['name'],
          'description': data['description'],
          'slug': slug,
          'isActive': data['is_active'] ?? true,
          'sortOrder': data['sort_order'] ?? data['id'],
          'type': data['type'] ?? 'part',
        });

        _logger.info('Found category: ${category.name}');
        return category;
      }
      
      throw Exception('Category not found with slug: $slug');
    } catch (e, stack) {
      _logger.severe('Error fetching category by slug: $slug', e, stack);
      // Forever Plan Architecture: NO MOCK DATA - throw proper error
      throw Exception('Failed to fetch category with slug "$slug" from backend: $e');
    }
  }

  /// جلب الفئات الفرعية - FIXED: Now uses Go API backend
  Future<List<CategoryModel>> getChildCategories(String parentId) async {
    try {
      _logger.info('Fetching child categories for parent: $parentId');
      
      final response = await _client.get('/categories/$parentId/children');
      
      if (response.data != null) {
        return (response.data as List<dynamic>)
            .map((item) => CategoryModel.fromJson(item as Map<String, dynamic>))
            .toList();
      }
      
      return [];
    } catch (e) {
      _logger.info('No hierarchical structure in current schema, returning empty list');
      return <CategoryModel>[];
    }
  }

  /// جلب الفئات الرئيسية (كل الفئات رئيسية حالياً)
  Future<List<CategoryModel>> getTopLevelCategories() => getAllCategories();

  // Forever Plan Architecture Compliance:
  // ✅ NO MOCK DATA - All data must come from Supabase via Go backend
  // ✅ REAL DATA ONLY - Proper error handling instead of fallbacks
  // ✅ Flutter UI Only → Go API → Supabase Data architecture maintained
}
