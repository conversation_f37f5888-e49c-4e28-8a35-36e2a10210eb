import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/providers/simple_product_provider.dart';
import '../providers/simple_category_provider.dart';
import '../../../core/theme/app_theme.dart';
import '../../products/listings/widgets/product_card.dart';
import '../../../l10n/app_localizations.dart';

/// Clean Category Products Screen - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// Uses simple AsyncValue patterns with clean error handling
/// Displays products filtered by category using SimpleApiClient only
class CleanCategoryProductsScreen extends ConsumerWidget {
  const CleanCategoryProductsScreen({
    required this.categorySlug,
    super.key,
  });

  final String categorySlug;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    
    // Get category info by slug
    final categoryAsync = ref.watch(categoryBySlugProvider(categorySlug));
    
    // Get products for this category (temporarily using all products)
    final productsAsync = ref.watch(allProductsProvider);

    return Scaffold(
      appBar: AppBar(
        title: categoryAsync.when(
          data: (category) => Text(category.name ?? 'Unknown Category'),
          loading: () => const Text('Loading...'),
          error: (_, __) => Text(l10n.categories),
        ),
        elevation: 0,
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Simple refresh - invalidate providers
          ref.invalidate(categoryBySlugProvider(categorySlug));
          ref.invalidate(allProductsProvider);
        },
        child: CustomScrollView(
          slivers: [
            // Category Info Section
            SliverToBoxAdapter(
              child: categoryAsync.when(
                data: (category) => category != null
                    ? _CategoryInfoSection(category: category)
                    : const SizedBox.shrink(),
                loading: () => const _CategoryInfoLoadingSection(),
                error: (error, stack) => _CategoryInfoErrorSection(
                  error: error,
                  onRetry: () => ref.invalidate(categoryBySlugProvider(categorySlug)),
                ),
              ),
            ),
            
            // Products Section
            SliverPadding(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              sliver: productsAsync.when(
                data: (products) {
                  if (products.isEmpty) {
                    return SliverFillRemaining(
                      hasScrollBody: false,
                      child: _EmptyProductsSection(categoryName: categorySlug),
                    );
                  }

                  return SliverGrid(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: AppTheme.spacingM,
                      mainAxisSpacing: AppTheme.spacingM,
                      childAspectRatio: 0.75,
                    ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final product = products[index];
                        return ProductCard(
                          product: product,
                          onTap: () => context.push('/product/${product.id}'),
                        );
                      },
                      childCount: products.length,
                    ),
                  );
                },
                loading: () => const SliverFillRemaining(
                  hasScrollBody: false,
                  child: _ProductsLoadingSection(),
                ),
                error: (error, stack) => SliverFillRemaining(
                  hasScrollBody: false,
                  child: _ProductsErrorSection(
                    error: error,
                    onRetry: () => ref.invalidate(allProductsProvider),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Category information section
class _CategoryInfoSection extends StatelessWidget {
  const _CategoryInfoSection({required this.category});

  final dynamic category; // Using dynamic to avoid import issues

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            category.name ?? 'Unknown Category',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          if (category.description != null && category.description!.isNotEmpty) ...[
            const SizedBox(height: AppTheme.spacingS),
            Text(
              category.description!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Loading state for category info
class _CategoryInfoLoadingSection extends StatelessWidget {
  const _CategoryInfoLoadingSection();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 200,
            height: 24,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.outlineVariant.withOpacity(0.7),
              borderRadius: BorderRadius.circular(AppTheme.radiusS),
            ),
          ),
          const SizedBox(height: AppTheme.spacingS),
          Container(
            width: double.infinity,
            height: 16,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.outlineVariant.withOpacity(0.5),
              borderRadius: BorderRadius.circular(AppTheme.radiusS),
            ),
          ),
        ],
      ),
    );
  }
}

/// Error state for category info
class _CategoryInfoErrorSection extends StatelessWidget {
  const _CategoryInfoErrorSection({
    required this.error,
    required this.onRetry,
  });

  final Object error;
  final VoidCallback onRetry;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.error_outline,
                color: Theme.of(context).colorScheme.onErrorContainer,
              ),
              const SizedBox(width: AppTheme.spacingS),
              Text(
                'خطأ في تحميل معلومات التصنيف',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onErrorContainer,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingS),
          SelectableText.rich(
            TextSpan(
              text: error.toString(),
              style: TextStyle(
                color: Theme.of(context).colorScheme.onErrorContainer,
                fontSize: 12,
              ),
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),
          ElevatedButton(
            onPressed: onRetry,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }
}

/// Empty products section
class _EmptyProductsSection extends StatelessWidget {
  const _EmptyProductsSection({required this.categoryName});

  final String categoryName;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppTheme.spacingL),
          Text(
            'لا توجد منتجات في هذا التصنيف',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppTheme.spacingS),
          Text(
            'سنضيف منتجات جديدة قريباً',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}

/// Loading state for products
class _ProductsLoadingSection extends StatelessWidget {
  const _ProductsLoadingSection();

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppTheme.spacingM,
        mainAxisSpacing: AppTheme.spacingM,
        childAspectRatio: 0.75,
      ),
      itemCount: 6, // Show 6 loading placeholders
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface.withOpacity(0.5),
            borderRadius: BorderRadius.circular(AppTheme.radiusM),
          ),
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        );
      },
    );
  }
}

/// Error state for products
class _ProductsErrorSection extends StatelessWidget {
  const _ProductsErrorSection({
    required this.error,
    required this.onRetry,
  });

  final Object error;
  final VoidCallback onRetry;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: Theme.of(context).colorScheme.error,
            size: 48,
          ),
          const SizedBox(height: AppTheme.spacingM),
          Text(
            'خطأ في تحميل المنتجات',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
          ),
          const SizedBox(height: AppTheme.spacingS),
          SelectableText.rich(
            TextSpan(
              text: error.toString(),
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontSize: 12,
              ),
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),
          ElevatedButton(
            onPressed: onRetry,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }
} 