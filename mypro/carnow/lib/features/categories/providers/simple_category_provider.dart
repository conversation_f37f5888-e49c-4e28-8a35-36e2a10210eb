import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../models/category_model.dart';

part 'simple_category_provider.g.dart';

final _logger = Logger('SimpleCategoryProvider');

/// Simple categories provider using Go backend API instead of direct Supabase calls
/// Follows Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
final categoriesProvider = FutureProvider<List<CategoryModel>>((ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching all categories from Go backend');
    
    final response = await apiClient.getApi('/api/v1/categories');
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to fetch categories: ${response.message}');
    }

    final categories = (response.data as List<dynamic>)
        .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
        .toList();
        
    _logger.info('Fetched ${categories.length} categories from Go backend');
    return categories;
  } catch (e) {
    _logger.severe('Error fetching categories: $e');
    rethrow;
  }
});

/// Category by slug provider - FIXED: Converted to @riverpod pattern
@riverpod
Future<CategoryModel> categoryBySlug(CategoryBySlugRef ref, String slug) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching category with slug: $slug');
    
    final response = await apiClient.getApi('/categories/slug/$slug');
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Category not found: $slug');
    }

    return CategoryModel.fromJson(response.data! as Map<String, dynamic>);
  } catch (e) {
    _logger.severe('Error fetching category with slug: $slug', e);
    rethrow;
  }
}

/// Child categories provider - FIXED: Converted to @riverpod pattern
@riverpod
Future<List<CategoryModel>> childCategories(ChildCategoriesRef ref, String parentId) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching child categories for parent: $parentId');
    
    final response = await apiClient.getApi('/categories/$parentId/children');
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    return (response.data! as List<dynamic>)
        .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    _logger.warning('Error fetching child categories: $e');
    return [];
  }
}

/// Top-level categories provider
final topLevelCategoriesProvider = FutureProvider<List<CategoryModel>>((ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching top-level categories');
    
    final response = await apiClient.getApi('/categories/top-level');
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    return (response.data! as List<dynamic>)
        .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    _logger.warning('Error fetching top-level categories: $e');
    return [];
  }
});

/// Selected category state provider
final selectedCategoryProvider = StateProvider<String?>((ref) => null);

/// Category actions provider
final categoryActionsProvider = Provider<CategoryActions>((ref) {
  return CategoryActions(ref);
});

/// Category actions class for managing category operations
class CategoryActions {
  CategoryActions(this._ref);
  
  final Ref _ref;
  
  /// Select a category
  void selectCategory(String slug) {
    final currentSlug = _ref.read(selectedCategoryProvider);
    if (currentSlug == slug) {
      // Toggle selection: if the same category is tapped again, clear it
      _ref.read(selectedCategoryProvider.notifier).state = null;
    } else {
      _ref.read(selectedCategoryProvider.notifier).state = slug;
    }
    
    _logger.info('Selected category: $slug');
  }
  
  /// Clear category selection
  void clearSelection() {
    _ref.read(selectedCategoryProvider.notifier).state = null;
    _logger.info('Cleared category selection');
  }
  
  /// Create a new category (admin operation)
  Future<CategoryModel> createCategory(Map<String, dynamic> categoryData) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      final response = await apiClient.postApi(
        '/categories',
        data: categoryData,
      );
      
      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to create category: ${response.message}');
      }

      // Refresh categories list
      _ref.invalidate(categoriesProvider);
      _ref.invalidate(topLevelCategoriesProvider);
      
      return CategoryModel.fromJson(response.data!);
    } catch (e) {
      _logger.severe('Error creating category: $e');
      rethrow;
    }
  }
  
  /// Update an existing category (admin operation)
  Future<CategoryModel> updateCategory(String id, Map<String, dynamic> updateData) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      final response = await apiClient.putApi(
        '/categories/$id',
        data: updateData,
      );
      
      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to update category: ${response.message}');
      }

      // Refresh categories list
      _ref.invalidate(categoriesProvider);
      _ref.invalidate(topLevelCategoriesProvider);
      
      return CategoryModel.fromJson(response.data!);
    } catch (e) {
      _logger.severe('Error updating category: $e');
      rethrow;
    }
  }
  
  /// Delete a category (admin operation)
  Future<void> deleteCategory(String id) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      final response = await apiClient.deleteApi('/categories/$id');
      
      if (!response.isSuccess) {
        throw Exception('Failed to delete category: ${response.message}');
      }

      // Refresh categories list
      _ref.invalidate(categoriesProvider);
      _ref.invalidate(topLevelCategoriesProvider);
      
      _logger.info('Successfully deleted category: $id');
    } catch (e) {
      _logger.severe('Error deleting category: $e');
      rethrow;
    }
  }
} 