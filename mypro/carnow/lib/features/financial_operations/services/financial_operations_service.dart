import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../models/transaction_model.dart';
import '../models/wallet_model.dart';
import '../models/refund_model.dart';
import '../models/refund_request.dart';
import '../models/refund_response.dart';
import '../models/refund_statistics.dart';
import '../models/refund_decision.dart';
import '../../wallet/models/wallet_statistics_model.dart';
import '../../../core/config/production_config.dart';

/// خدمة العمليات المالية الموحدة - FIXED: Removed direct Supabase dependency
class FinancialOperationsService {
  FinancialOperationsService({
    required Dio dio,
  }) : _dio = dio;

  final Dio _dio;

  /// الحصول على معلومات المحفظة
  Future<WalletModel> getWallet() async {
    try {
      final response = await _dio.get('${ProductionConfig.apiBaseUrl}/wallet');
      return WalletModel.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// الحصول على ملخص المحفظة
  Future<WalletSummary> getWalletSummary() async {
    try {
      final response = await _dio.get('${ProductionConfig.apiBaseUrl}/wallet/summary');
      return WalletSummary.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// الحصول على معاملات المحفظة
  Future<List<WalletTransaction>> getWalletTransactions({
    int page = 1,
    int limit = 20,
    WalletTransactionType? type,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
        if (type != null) 'type': type.name,
      };

      final response = await _dio.get(
        '${ProductionConfig.apiBaseUrl}/wallet/transactions',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['transactions'];
      return data.map((json) => WalletTransaction.fromJson(json)).toList();
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// تنفيذ معاملة موحدة
  Future<TransactionResult> executeTransaction(
    TransactionRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '${ProductionConfig.apiBaseUrl}/transactions/execute',
        data: request.toJson(),
      );
      return TransactionResult.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// تنفيذ معاملة شراء
  Future<TransactionResult> purchaseTransaction(
    TransactionRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '${ProductionConfig.apiBaseUrl}/transactions/purchase',
        data: request.toJson(),
      );
      return TransactionResult.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// الحصول على معاملة بالمعرف
  Future<TransactionModel> getTransaction(String transactionId) async {
    try {
      final response = await _dio.get('${ProductionConfig.apiBaseUrl}/transactions/$transactionId');
      return TransactionModel.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// الحصول على قائمة المعاملات
  Future<List<TransactionModel>> getTransactions({
    int page = 1,
    int limit = 20,
    TransactionType? type,
    TransactionStatus? status,
    PaymentMethod? paymentMethod,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
        if (type != null) 'type': type.name,
        if (status != null) 'status': status.name,
        if (paymentMethod != null) 'payment_method': paymentMethod.name,
        if (startDate != null) 'start_date': startDate.toIso8601String(),
        if (endDate != null) 'end_date': endDate.toIso8601String(),
      };

      final response = await _dio.get(
        '${ProductionConfig.apiBaseUrl}/transactions/history',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['transactions'];
      return data.map((json) => TransactionModel.fromJson(json)).toList();
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// إيداع في المحفظة
  Future<TransactionResult> depositToWallet(DepositRequest request) async {
    try {
      final response = await _dio.post(
        '${ProductionConfig.apiBaseUrl}/wallet/deposit',
        data: request.toJson(),
      );
      return TransactionResult.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// سحب من المحفظة
  Future<TransactionResult> withdrawFromWallet(
    WithdrawalRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '${ProductionConfig.apiBaseUrl}/wallet/withdraw',
        data: request.toJson(),
      );
      return TransactionResult.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// تحويل الأموال
  Future<TransactionResult> transferMoney(TransferRequest request) async {
    try {
      final response = await _dio.post(
        '${ProductionConfig.apiBaseUrl}/wallet/transfer',
        data: request.toJson(),
      );
      return TransactionResult.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// طلب استرداد
  Future<RefundResponse> requestRefund(RefundRequest request) async {
    try {
      final response = await _dio.post(
        '${ProductionConfig.apiBaseUrl}/refunds/request',
        data: request.toJson(),
      );
      return RefundResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// الحصول على معلومات الاسترداد
  Future<RefundModel> getRefund(String refundId) async {
    try {
      final response = await _dio.get('${ProductionConfig.apiBaseUrl}/refunds/$refundId');
      return RefundModel.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// الحصول على قائمة الاستردادات
  Future<List<RefundModel>> getRefunds({
    int page = 1,
    int limit = 20,
    RefundStatus? status,
    RefundType? type,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
        if (status != null) 'status': status.name,
        if (type != null) 'type': type.name,
      };

      final response = await _dio.get(
        '${ProductionConfig.apiBaseUrl}/refunds',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['refunds'];
      return data.map((json) => RefundModel.fromJson(json)).toList();
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// إلغاء الاسترداد
  Future<bool> cancelRefund(String refundId) async {
    try {
      final response = await _dio.delete('${ProductionConfig.apiBaseUrl}/refunds/$refundId');
      return response.data['success'] == true;
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// الحصول على معلومات التسليم COD
  Future<CODDeliveryInfo> getCODDeliveryInfo(String transactionId) async {
    try {
      final response = await _dio.get('${ProductionConfig.apiBaseUrl}/cod/delivery/$transactionId');
      return CODDeliveryInfo.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// تحديث معلومات التسليم COD
  Future<bool> updateCODDeliveryInfo(
    String transactionId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final response = await _dio.patch(
        '${ProductionConfig.apiBaseUrl}/cod/delivery/$transactionId',
        data: updates,
      );
      return response.data['success'] == true;
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// الحصول على إحصائيات الاستردادات
  Future<RefundStatistics> getRefundStatistics() async {
    try {
      final response = await _dio.get('${ProductionConfig.apiBaseUrl}/refunds/statistics');
      return RefundStatistics.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// الحصول على ملخص العمليات المالية
  Future<Map<String, dynamic>> getFinancialSummary() async {
    try {
      final response = await _dio.get('${ProductionConfig.apiBaseUrl}/summary');
      return response.data;
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// تقييم المخاطر للمعاملة
  Future<Map<String, dynamic>> assessRisk(
    Map<String, dynamic> transactionData,
  ) async {
    try {
      final response = await _dio.post(
        '${ProductionConfig.apiBaseUrl}/risk-assessment',
        data: transactionData,
      );
      return response.data;
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// الحصول على إحصائيات النظام
  Future<Map<String, dynamic>> getSystemStatistics() async {
    try {
      final response = await _dio.get('${ProductionConfig.apiBaseUrl}/stats/system');
      return response.data;
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// إنشاء معاملة دفع عند الاستلام
  Future<TransactionResult> createCODTransaction({
    required String payeeId,
    required double amount,
    required String orderId,
    required String productId,
    required String description,
    required Map<String, dynamic> deliveryData,
  }) async {
    try {
      final request = TransactionRequest(
        payeeId: payeeId,
        amount: amount,
        transactionType: TransactionType.purchase,
        paymentMethod: PaymentMethod.cashOnDelivery,
        description: description,
        orderId: orderId,
        productId: productId,
        deliveryData: deliveryData,
      );

      return await executeTransaction(request);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// إنشاء معاملة المحفظة
  Future<TransactionResult> createWalletTransaction({
    required String payeeId,
    required double amount,
    required TransactionType type,
    required String description,
    String? orderId,
    String? productId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final request = TransactionRequest(
        payeeId: payeeId,
        amount: amount,
        transactionType: type,
        paymentMethod: PaymentMethod.wallet,
        description: description,
        orderId: orderId,
        productId: productId,
        metadata: metadata,
      );

      return await executeTransaction(request);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// التحقق من صحة معاملة الدفع عند الاستلام
  Future<bool> validateCODTransaction({
    required double amount,
    required String address,
    required String phone,
  }) async {
    try {
      final response = await _dio.post(
        '${ProductionConfig.apiBaseUrl}/cod/validate',
        data: {'amount': amount, 'address': address, 'phone': phone},
      );
      return response.data['valid'] == true;
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// الحصول على رسوم الدفع عند الاستلام
  Future<double> getCODFee(double amount) async {
    try {
      final response = await _dio.get(
        '${ProductionConfig.apiBaseUrl}/cod/fee',
        queryParameters: {'amount': amount},
      );
      return response.data['fee'].toDouble();
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// الحصول على حدود المعاملات
  Future<Map<String, dynamic>> getTransactionLimits() async {
    try {
      final response = await _dio.get('${ProductionConfig.apiBaseUrl}/limits');
      return response.data;
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// الاشتراك في تحديثات الوقت الفعلي - DISABLED: Direct Supabase stream removed
  /// TODO: Replace with WebSocket API from Go backend
  Stream<Map<String, dynamic>> subscribeToTransactionUpdates() {
    // Return empty stream for now - should be replaced with WebSocket from Go backend
    return const Stream.empty();
  }

  /// الاشتراك في تحديثات المحفظة - DISABLED: Direct Supabase stream removed
  /// TODO: Replace with WebSocket API from Go backend
  Stream<Map<String, dynamic>> subscribeToWalletUpdates() {
    // Return empty stream for now - should be replaced with WebSocket from Go backend
    return const Stream.empty();
  }

  /// الاشتراك في تحديثات الاستردادات - DISABLED: Direct Supabase stream removed
  /// TODO: Replace with WebSocket API from Go backend
  Stream<Map<String, dynamic>> subscribeToRefundUpdates() {
    // Return empty stream for now - should be replaced with WebSocket from Go backend
    return const Stream.empty();
  }

  /// الحصول على إحصائيات المحفظة
  Future<WalletStatistics> getWalletStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }

      final response = await _dio.get(
        '${ProductionConfig.apiBaseUrl}/wallet/statistics',
        queryParameters: queryParams,
      );
      return WalletStatistics.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// معالجة الأخطاء
  Exception _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return Exception('انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى');
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data['error'] ?? 'خطأ في الخادم';
          return Exception('خطأ $statusCode: $message');
        case DioExceptionType.cancel:
          return Exception('تم إلغاء العملية');
        case DioExceptionType.unknown:
          return Exception('خطأ في الشبكة، يرجى التحقق من الاتصال');
        default:
          return Exception('خطأ غير محدد');
      }
    }

    if (kDebugMode) {
      print('Financial Operations Service Error: $error');
    }

    return Exception('خطأ في العمليات المالية: ${error.toString()}');
  }
}

/// خدمة التحقق من المحفظة
class WalletVerificationService {
  WalletVerificationService(this._financialService);
  final FinancialOperationsService _financialService;

  /// التحقق من الهاتف
  Future<bool> verifyPhone(String phone, String otp) async {
    try {
      final response = await _financialService._dio.post(
        '${ProductionConfig.apiBaseUrl}/wallet/verify/phone',
        data: {'phone': phone, 'otp': otp},
      );
      return response.data['success'] == true;
    } catch (e) {
      throw _financialService._handleError(e);
    }
  }

  /// التحقق من البريد الإلكتروني
  Future<bool> verifyEmail(String email, String token) async {
    try {
      final response = await _financialService._dio.post(
        '${ProductionConfig.apiBaseUrl}/wallet/verify/email',
        data: {'email': email, 'token': token},
      );
      return response.data['success'] == true;
    } catch (e) {
      throw _financialService._handleError(e);
    }
  }

  /// التحقق من الهوية
  Future<bool> verifyIdentity(Map<String, dynamic> identityData) async {
    try {
      final response = await _financialService._dio.post(
        '${ProductionConfig.apiBaseUrl}/wallet/verify/identity',
        data: identityData,
      );
      return response.data['success'] == true;
    } catch (e) {
      throw _financialService._handleError(e);
    }
  }

  /// طلب رمز التحقق
  Future<bool> requestVerificationCode(String phone) async {
    try {
      final response = await _financialService._dio.post(
        '${ProductionConfig.apiBaseUrl}/wallet/verify/request-code',
        data: {'phone': phone},
      );
      return response.data['success'] == true;
    } catch (e) {
      throw _financialService._handleError(e);
    }
  }
}

/// خدمة إدارة الاستردادات (للمديرين)
class RefundManagementService {
  RefundManagementService(this._financialService);
  final FinancialOperationsService _financialService;

  /// معالجة قرار الاسترداد
  Future<bool> processRefundDecision(RefundDecision decision) async {
    try {
      final response = await _financialService._dio.post(
        '${ProductionConfig.apiBaseUrl}/refunds/${decision.refundId}/process',
        data: decision.toJson(),
      );
      return response.data['success'] == true;
    } catch (e) {
      throw _financialService._handleError(e);
    }
  }

  /// الحصول على الاستردادات المعلقة
  Future<List<RefundModel>> getPendingRefunds() async {
    try {
      final response = await _financialService._dio.get(
        '${ProductionConfig.apiBaseUrl}/admin/refunds/pending',
      );

      final List<dynamic> data = response.data['refunds'];
      return data.map((json) => RefundModel.fromJson(json)).toList();
    } catch (e) {
      throw _financialService._handleError(e);
    }
  }

  /// تحديث ملاحظات الإدارة
  Future<bool> updateAdminNotes(String refundId, String notes) async {
    try {
      final response = await _financialService._dio.patch(
        '${ProductionConfig.apiBaseUrl}/refunds/$refundId/notes',
        data: {'admin_notes': notes},
      );
      return response.data['success'] == true;
    } catch (e) {
      throw _financialService._handleError(e);
    }
  }
}
