import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../../../core/providers/dio_provider.dart';
import '../models/wallet_model.dart';
import '../models/transaction_model.dart';
import '../services/financial_operations_service.dart';
import '../../wallet/models/wallet_statistics_model.dart';

part 'financial_operations_providers.g.dart';

final _logger = Logger('FinancialOperationsProviders');

/// مزود خدمة العمليات المالية
@riverpod
FinancialOperationsService financialOperationsService(Ref ref) {
  final dio = ref.watch(dioProvider);
  return FinancialOperationsService(dio: dio);
}

/// مزود بيانات المحفظة
@riverpod
Future<WalletModel> walletData(Ref ref) async {
  try {
    final service = ref.watch(financialOperationsServiceProvider);
    return await service.getWallet();
  } catch (e, stackTrace) {
    _logger.severe('Failed to fetch wallet data', e, stackTrace);
    rethrow;
  }
}

/// مزود ملخص المحفظة
@riverpod
Future<WalletSummary> walletSummary(Ref ref) async {
  try {
    final service = ref.watch(financialOperationsServiceProvider);
    return await service.getWalletSummary();
  } catch (e, stackTrace) {
    _logger.severe('Failed to fetch wallet summary', e, stackTrace);
    rethrow;
  }
}

/// مزود معاملات المحفظة
@riverpod
Future<List<WalletTransaction>> walletTransactions(
  Ref ref, {
  int page = 1,
  int limit = 20,
  WalletTransactionType? type,
}) async {
  try {
    final service = ref.watch(financialOperationsServiceProvider);
    return await service.getWalletTransactions(
      page: page,
      limit: limit,
      type: type,
    );
  } catch (e, stackTrace) {
    _logger.severe('Failed to fetch wallet transactions', e, stackTrace);
    rethrow;
  }
}

/// مزود معاملة واحدة
@riverpod
Future<TransactionModel> transaction(Ref ref, String transactionId) async {
  try {
    final service = ref.watch(financialOperationsServiceProvider);
    return await service.getTransaction(transactionId);
  } catch (e, stackTrace) {
    _logger.severe('Failed to fetch transaction $transactionId', e, stackTrace);
    rethrow;
  }
}

/// مزود تنفيذ المعاملات
@riverpod
class TransactionExecutor extends _$TransactionExecutor {
  @override
  FutureOr<TransactionResult?> build() {
    return null;
  }

  /// تنفيذ معاملة عامة
  Future<TransactionResult> executeTransaction(TransactionRequest request) async {
    state = const AsyncValue.loading();
    try {
      final service = ref.read(financialOperationsServiceProvider);
      final result = await service.executeTransaction(request);
      state = AsyncValue.data(result);
      
      // إبطال مزودات البيانات لتحديثها
      ref.invalidate(walletDataProvider);
      ref.invalidate(walletSummaryProvider);
      
      return result;
    } catch (e, stackTrace) {
      _logger.severe('Failed to execute transaction', e, stackTrace);
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// تنفيذ معاملة شراء
  Future<TransactionResult> executePurchase(TransactionRequest request) async {
    state = const AsyncValue.loading();
    try {
      final service = ref.read(financialOperationsServiceProvider);
      final result = await service.purchaseTransaction(request);
      state = AsyncValue.data(result);
      
      // إبطال مزودات البيانات لتحديثها
      ref.invalidate(walletDataProvider);
      ref.invalidate(walletSummaryProvider);
      ref.invalidate(walletTransactionsProvider);
      
      return result;
    } catch (e, stackTrace) {
      _logger.severe('Failed to execute purchase transaction', e, stackTrace);
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }
}

/// مزود إحصائيات المحفظة
@riverpod
Future<WalletStatistics> walletStatistics(
  Ref ref, {
  DateTime? startDate,
  DateTime? endDate,
}) async {
  try {
    final service = ref.watch(financialOperationsServiceProvider);
    return await service.getWalletStatistics(
      startDate: startDate,
      endDate: endDate,
    );
  } catch (e, stackTrace) {
    _logger.severe('Failed to fetch wallet statistics', e, stackTrace);
    rethrow;
  }
}

/// مزود رصيد المحفظة (للوصول السريع)
@riverpod
Future<double> walletBalance(Ref ref) async {
  try {
    final wallet = await ref.watch(walletDataProvider.future);
    return wallet.balance;
  } catch (e, stackTrace) {
    _logger.severe('Failed to fetch wallet balance', e, stackTrace);
    rethrow;
  }
}

/// مزود حالة التحقق من المحفظة
@riverpod
Future<VerificationLevel> walletVerificationLevel(Ref ref) async {
  try {
    final wallet = await ref.watch(walletDataProvider.future);
    return wallet.verificationLevel;
  } catch (e, stackTrace) {
    _logger.severe('Failed to fetch wallet verification level', e, stackTrace);
    rethrow;
  }
}