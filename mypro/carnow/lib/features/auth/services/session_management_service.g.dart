// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_management_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sessionManagementServiceHash() =>
    r'c9850c639fb5186900c9b01264d2ec3d9e1587e7';

/// Riverpod provider for SessionManagementService
///
/// Copied from [sessionManagementService].
@ProviderFor(sessionManagementService)
final sessionManagementServiceProvider =
    AutoDisposeProvider<SessionManagementService>.internal(
      sessionManagementService,
      name: r'sessionManagementServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sessionManagementServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SessionManagementServiceRef =
    AutoDisposeProviderRef<SessionManagementService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
