import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/accessibility/accessibility_service.dart';
import '../../../core/theme/carnow_colors.dart';

/// Accessible authentication input field with enhanced accessibility features
class AccessibleAuthInputField extends ConsumerStatefulWidget {
  const AccessibleAuthInputField({
    super.key,
    required this.label,
    required this.onChanged,
    this.hint,
    this.initialValue,
    this.isPassword = false,
    this.isRequired = false,
    this.validator,
    this.keyboardType,
    this.textInputAction,
    this.onSubmitted,
    this.focusNode,
    this.nextFocusNode,
    this.prefixIcon,
    this.suffixIcon,
    this.enabled = true,
    this.maxLength,
    this.semanticLabel,
    this.semanticHint,
  });

  final String label;
  final String? hint;
  final String? initialValue;
  final bool isPassword;
  final bool isRequired;
  final bool enabled;
  final int? maxLength;
  final String? semanticLabel;
  final String? semanticHint;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final ValueChanged<String> onChanged;
  final ValueChanged<String>? onSubmitted;
  final FocusNode? focusNode;
  final FocusNode? nextFocusNode;
  final Widget? prefixIcon;
  final Widget? suffixIcon;

  @override
  ConsumerState<AccessibleAuthInputField> createState() => _AccessibleAuthInputFieldState();
}

class _AccessibleAuthInputFieldState extends ConsumerState<AccessibleAuthInputField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _obscureText = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _focusNode = widget.focusNode ?? FocusNode();
    _obscureText = widget.isPassword;

    // Add focus listener for accessibility announcements
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _controller.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    if (_focusNode.hasFocus) {
      // Announce field focus for screen readers
      String announcement = widget.semanticLabel ?? widget.label;
      if (widget.isRequired) {
        announcement += '، مطلوب'; // Required in Arabic
      }
      if (widget.hint != null) {
        announcement += '، ${widget.hint}';
      }
      AccessibilityService.announce(announcement);
      
      // Provide light haptic feedback
      AccessibilityService.provideHapticFeedback(AuthHapticType.lightImpact);
    }
  }

  void _validateInput(String value) {
    setState(() {
      if (widget.validator != null) {
        _errorMessage = widget.validator!(value);
      } else {
        _errorMessage = null;
      }
    });
  }

  void _handleTextChanged(String value) {
    widget.onChanged(value);
    _validateInput(value);
  }

  void _handleSubmitted(String value) {
    widget.onSubmitted?.call(value);
    
    // Move to next field if available
    if (widget.nextFocusNode != null) {
      widget.nextFocusNode!.requestFocus();
    } else {
      _focusNode.unfocus();
    }
  }

  void _togglePasswordVisibility() {
    setState(() {
      _obscureText = !_obscureText;
    });
    
    // Announce password visibility change
    String announcement = _obscureText ? 'كلمة المرور مخفية' : 'كلمة المرور مرئية';
    AccessibilityService.announce(announcement);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasError = _errorMessage != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with accessibility
        Semantics(
          label: widget.semanticLabel ?? widget.label,
          hint: widget.semanticHint,
          textField: true,
          isRequired: widget.isRequired,
          child: Text(
            widget.label,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: hasError ? theme.colorScheme.error : theme.colorScheme.onSurface,
            ),
          ),
        ),
        const SizedBox(height: 8),

        // Input field with enhanced accessibility
        TextField(
          controller: _controller,
          focusNode: _focusNode,
          enabled: widget.enabled,
          obscureText: widget.isPassword && _obscureText,
          keyboardType: widget.keyboardType ?? (widget.isPassword ? TextInputType.visiblePassword : TextInputType.text),
          textInputAction: widget.textInputAction ?? (widget.nextFocusNode != null ? TextInputAction.next : TextInputAction.done),
          maxLength: widget.maxLength,
          onChanged: _handleTextChanged,
          onSubmitted: _handleSubmitted,
          decoration: InputDecoration(
            hintText: widget.hint,
            prefixIcon: widget.prefixIcon,
            suffixIcon: widget.isPassword
                ? IconButton(
                    onPressed: _togglePasswordVisibility,
                    icon: Icon(
                      _obscureText ? Icons.visibility_off : Icons.visibility,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    tooltip: _obscureText ? 'إظهار كلمة المرور' : 'إخفاء كلمة المرور',
                  )
                : widget.suffixIcon,
                         filled: true,
             fillColor: widget.enabled ? theme.colorScheme.surfaceContainerHighest : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.outline,
                width: 1.0,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.outline,
                width: 1.0,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.primary,
                width: 2.0,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.error,
                width: 2.0,
              ),
            ),
          ),
        ),

        // Error message with accessibility
        if (hasError) ...[
          const SizedBox(height: 4),
          Semantics(
            liveRegion: true,
            label: 'خطأ: $_errorMessage',
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  size: 16,
                  color: theme.colorScheme.error,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}

/// Accessible authentication button with enhanced accessibility features
class AccessibleAuthButton extends ConsumerWidget {
  const AccessibleAuthButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.isLoading = false,
    this.enabled = true,
    this.style,
    this.semanticLabel,
    this.semanticHint,
    this.focusNode,
    this.autofocus = false,
  });

  final VoidCallback? onPressed;
  final Widget child;
  final bool isLoading;
  final bool enabled;
  final ButtonStyle? style;
  final String? semanticLabel;
  final String? semanticHint;
  final FocusNode? focusNode;
  final bool autofocus;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isEnabled = enabled && !isLoading && onPressed != null;

    return AccessibilityService.createAccessibleButton(
      label: semanticLabel ?? _extractTextFromChild(child),
      hint: semanticHint,
      enabled: isEnabled,
      isLoading: isLoading,
      onTap: isEnabled ? onPressed : null,
      child: SizedBox(
        width: double.infinity,
        height: 56,
        child: ElevatedButton(
          onPressed: isEnabled ? _handlePress : null,
          focusNode: focusNode,
          autofocus: autofocus,
          style: style ?? theme.elevatedButtonTheme.style?.copyWith(
            backgroundColor: WidgetStateProperty.resolveWith((states) {
              if (states.contains(WidgetState.disabled)) {
                return theme.colorScheme.onSurface.withValues(alpha: 0.12);
              }
              return theme.colorScheme.primary;
            }),
            foregroundColor: WidgetStateProperty.resolveWith((states) {
              if (states.contains(WidgetState.disabled)) {
                return theme.colorScheme.onSurface.withValues(alpha: 0.38);
              }
              return theme.colorScheme.onPrimary;
            }),
            elevation: WidgetStateProperty.resolveWith((states) {
              if (states.contains(WidgetState.pressed)) {
                return 8.0;
              }
              if (states.contains(WidgetState.focused)) {
                return 4.0;
              }
              return 2.0;
            }),
          ),
          child: isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : child,
        ),
      ),
    );
  }

  void _handlePress() {
    if (onPressed != null) {
      // Provide haptic feedback
      AccessibilityService.provideHapticFeedback(AuthHapticType.selection);
      onPressed!();
    }
  }

  String _extractTextFromChild(Widget child) {
    if (child is Text) {
      return child.data ?? '';
    }
    return 'زر'; // Button in Arabic
  }
}

/// Accessible error display with announcements
class AccessibleErrorDisplay extends ConsumerWidget {
  const AccessibleErrorDisplay({
    super.key,
    required this.error,
    this.onRetry,
    this.showIcon = true,
  });

  final String error;
  final VoidCallback? onRetry;
  final bool showIcon;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // Announce error when displayed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AccessibilityService.announce('خطأ: $error');
      AccessibilityService.provideHapticFeedback(AuthHapticType.error);
    });

    return Semantics(
      liveRegion: true,
      label: 'خطأ: $error',
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.errorContainer.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: theme.colorScheme.error,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (showIcon) ...[
                  Icon(
                    Icons.error_outline,
                    color: theme.colorScheme.error,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                ],
                Expanded(
                  child: Text(
                    error,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onErrorContainer,
                    ),
                  ),
                ),
              ],
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 12),
              AccessibleAuthButton(
                onPressed: onRetry,
                semanticLabel: 'إعادة المحاولة', // Retry
                semanticHint: 'اضغط لإعادة المحاولة', // Press to retry
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.error,
                  foregroundColor: theme.colorScheme.onError,
                ),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Accessible success display with announcements
class AccessibleSuccessDisplay extends ConsumerWidget {
  const AccessibleSuccessDisplay({
    super.key,
    required this.message,
    this.showIcon = true,
    this.onDismiss,
  });

  final String message;
  final bool showIcon;
  final VoidCallback? onDismiss;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // Announce success when displayed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AccessibilityService.announce('نجح: $message');
      AccessibilityService.provideHapticFeedback(AuthHapticType.success);
    });

    return Semantics(
      liveRegion: true,
      label: 'نجح: $message',
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: CarnowColors.success.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: CarnowColors.success,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            if (showIcon) ...[
              Icon(
                Icons.check_circle_outline,
                color: CarnowColors.success,
                size: 20,
              ),
              const SizedBox(width: 8),
            ],
            Expanded(
              child: Text(
                message,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: CarnowColors.success,
                ),
              ),
            ),
            if (onDismiss != null) ...[
              const SizedBox(width: 8),
              IconButton(
                onPressed: onDismiss,
                icon: const Icon(Icons.close),
                color: CarnowColors.success,
                tooltip: 'إغلاق', // Close
              ),
            ],
          ],
        ),
      ),
    );
  }
}
