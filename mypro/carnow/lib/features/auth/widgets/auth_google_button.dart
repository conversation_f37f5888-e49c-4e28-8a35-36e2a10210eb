/// ============================================================================
/// AUTH GOOGLE BUTTON - Material 3 Design System
/// ============================================================================
/// 
/// Enhanced Google OAuth button with Material 3 design and proper branding
/// Task 20: Improve authentication UI components - Enhanced SVG Google icon
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ============================================================================
library;

import 'package:flutter/material.dart';


/// Enhanced Google OAuth button with proper branding guidelines
class AuthGoogleButton extends StatefulWidget {
  const AuthGoogleButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.enabled = true,
    this.width,
    this.height = 56,
    this.borderRadius = 12,
  });

  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;
  final bool enabled;
  final double? width;
  final double height;
  final double borderRadius;

  @override
  State<AuthGoogleButton> createState() => _AuthGoogleButtonState();
}

class _AuthGoogleButtonState extends State<AuthGoogleButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isButtonEnabled = widget.enabled && !widget.isLoading && widget.onPressed != null;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: SizedBox(
            width: widget.width ?? double.infinity,
            height: widget.height,
            child: ElevatedButton(
              onPressed: isButtonEnabled ? _handlePressed : null,
              style: _buildButtonStyle(theme, isButtonEnabled),
              child: _buildButtonContent(theme),
            ),
          ),
        );
      },
    );
  }

  ButtonStyle _buildButtonStyle(ThemeData theme, bool isEnabled) {
    final colorScheme = theme.colorScheme;
    
    return ElevatedButton.styleFrom(
      backgroundColor: isEnabled ? colorScheme.surface : colorScheme.surfaceContainerLow,
      foregroundColor: colorScheme.onSurface,
      disabledBackgroundColor: colorScheme.surfaceContainerLowest,
      disabledForegroundColor: colorScheme.onSurface.withValues(alpha: 0.38),
      
      // Material 3 elevation
      elevation: isEnabled ? 1 : 0,
      shadowColor: colorScheme.shadow.withValues(alpha: 0.15),
      surfaceTintColor: Colors.transparent,
      
      // Shape and border with Material 3 styling
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        side: BorderSide(
          color: isEnabled 
              ? colorScheme.outline.withValues(alpha: 0.12)
              : colorScheme.outline.withValues(alpha: 0.06),
          width: 1,
        ),
      ),
      
      // Padding
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    );
  }

  Widget _buildButtonContent(ThemeData theme) {
    if (widget.isLoading) {
      return _buildLoadingContent(theme);
    }
    
    return _buildNormalContent(theme);
  }

  Widget _buildLoadingContent(ThemeData theme) {
    final colorScheme = theme.colorScheme;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2.5,
            valueColor: AlwaysStoppedAnimation<Color>(
              colorScheme.primary,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          'جاري المعالجة...',
          style: theme.textTheme.labelLarge?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.87),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildNormalContent(ThemeData theme) {
    final colorScheme = theme.colorScheme;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildGoogleIcon(),
        const SizedBox(width: 12),
        Text(
          widget.text,
          style: theme.textTheme.labelLarge?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.87),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildGoogleIcon() {
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2),
      ),
      child: CustomPaint(
        painter: GoogleLogoPainter(),
      ),
    );
  }

  void _handlePressed() {
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
    
    widget.onPressed?.call();
  }
}

/// Enhanced Custom painter for the Google logo with precise SVG-like rendering
class GoogleLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 0
      ..isAntiAlias = true;

    // Scale factor for consistent sizing
    final scale = size.width / 24.0;
    
    // Draw the Google 'G' logo with official brand colors
    // Blue section (main G shape)
    paint.color = const Color(0xFF4285F4);
    canvas.drawPath(_createGPath(size, scale), paint);

    // Red section (top arc)
    paint.color = const Color(0xFFEA4335);
    canvas.drawPath(_createRedPath(size, scale), paint);

    // Yellow section (bottom left)
    paint.color = const Color(0xFFFBBC05);
    canvas.drawPath(_createYellowPath(size, scale), paint);

    // Green section (bottom right)
    paint.color = const Color(0xFF34A853);
    canvas.drawPath(_createGreenPath(size, scale), paint);
  }

  Path _createGPath(Size size, double scale) {
    final path = Path();
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    
    // Create precise Google 'G' shape based on official specifications
    path.moveTo(centerX + 5.4 * scale, centerY - 2.4 * scale);
    path.lineTo(centerX + 5.4 * scale, centerY + 0.6 * scale);
    path.lineTo(centerX + 2.4 * scale, centerY + 0.6 * scale);
    path.lineTo(centerX + 2.4 * scale, centerY + 2.4 * scale);
    path.cubicTo(
      centerX + 2.4 * scale, centerY + 4.8 * scale,
      centerX + 0.6 * scale, centerY + 6.0 * scale,
      centerX - 1.8 * scale, centerY + 6.0 * scale,
    );
    path.cubicTo(
      centerX - 4.8 * scale, centerY + 6.0 * scale,
      centerX - 6.0 * scale, centerY + 3.6 * scale,
      centerX - 6.0 * scale, centerY,
    );
    path.cubicTo(
      centerX - 6.0 * scale, centerY - 3.6 * scale,
      centerX - 4.8 * scale, centerY - 6.0 * scale,
      centerX - 1.8 * scale, centerY - 6.0 * scale,
    );
    path.cubicTo(
      centerX - 0.6 * scale, centerY - 6.0 * scale,
      centerX + 0.6 * scale, centerY - 5.4 * scale,
      centerX + 1.8 * scale, centerY - 4.2 * scale,
    );
    path.lineTo(centerX + 3.6 * scale, centerY - 5.4 * scale);
    path.cubicTo(
      centerX + 2.4 * scale, centerY - 7.2 * scale,
      centerX + 0.0 * scale, centerY - 8.4 * scale,
      centerX - 1.8 * scale, centerY - 8.4 * scale,
    );
    path.cubicTo(
      centerX - 6.0 * scale, centerY - 8.4 * scale,
      centerX - 8.4 * scale, centerY - 4.8 * scale,
      centerX - 8.4 * scale, centerY,
    );
    path.cubicTo(
      centerX - 8.4 * scale, centerY + 4.8 * scale,
      centerX - 6.0 * scale, centerY + 8.4 * scale,
      centerX - 1.8 * scale, centerY + 8.4 * scale,
    );
    path.cubicTo(
      centerX + 1.2 * scale, centerY + 8.4 * scale,
      centerX + 4.8 * scale, centerY + 6.6 * scale,
      centerX + 4.8 * scale, centerY + 2.4 * scale,
    );
    path.lineTo(centerX + 4.8 * scale, centerY - 2.4 * scale);
    path.close();
    
    return path;
  }

  Path _createRedPath(Size size, double scale) {
    final path = Path();
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    
    // Red section - precise arc based on Google brand guidelines
    path.moveTo(centerX - 1.8 * scale, centerY - 8.4 * scale);
    path.cubicTo(
      centerX + 0.0 * scale, centerY - 8.4 * scale,
      centerX + 2.4 * scale, centerY - 7.2 * scale,
      centerX + 3.6 * scale, centerY - 5.4 * scale,
    );
    path.lineTo(centerX + 1.8 * scale, centerY - 4.2 * scale);
    path.cubicTo(
      centerX + 0.6 * scale, centerY - 5.4 * scale,
      centerX - 0.6 * scale, centerY - 6.0 * scale,
      centerX - 1.8 * scale, centerY - 6.0 * scale,
    );
    path.cubicTo(
      centerX - 4.8 * scale, centerY - 6.0 * scale,
      centerX - 6.0 * scale, centerY - 3.6 * scale,
      centerX - 6.0 * scale, centerY,
    );
    path.lineTo(centerX - 8.4 * scale, centerY);
    path.cubicTo(
      centerX - 8.4 * scale, centerY - 4.8 * scale,
      centerX - 6.0 * scale, centerY - 8.4 * scale,
      centerX - 1.8 * scale, centerY - 8.4 * scale,
    );
    path.close();
    
    return path;
  }

  Path _createYellowPath(Size size, double scale) {
    final path = Path();
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    
    // Yellow section - bottom left arc
    path.moveTo(centerX - 8.4 * scale, centerY);
    path.lineTo(centerX - 6.0 * scale, centerY);
    path.cubicTo(
      centerX - 6.0 * scale, centerY + 3.6 * scale,
      centerX - 4.8 * scale, centerY + 6.0 * scale,
      centerX - 1.8 * scale, centerY + 6.0 * scale,
    );
    path.cubicTo(
      centerX - 0.6 * scale, centerY + 6.0 * scale,
      centerX + 0.6 * scale, centerY + 5.4 * scale,
      centerX + 1.8 * scale, centerY + 4.2 * scale,
    );
    path.lineTo(centerX + 3.6 * scale, centerY + 5.4 * scale);
    path.cubicTo(
      centerX + 2.4 * scale, centerY + 7.2 * scale,
      centerX + 0.0 * scale, centerY + 8.4 * scale,
      centerX - 1.8 * scale, centerY + 8.4 * scale,
    );
    path.cubicTo(
      centerX - 6.0 * scale, centerY + 8.4 * scale,
      centerX - 8.4 * scale, centerY + 4.8 * scale,
      centerX - 8.4 * scale, centerY,
    );
    path.close();
    
    return path;
  }

  Path _createGreenPath(Size size, double scale) {
    final path = Path();
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    
    // Green section - bottom right area
    path.moveTo(centerX + 3.6 * scale, centerY + 5.4 * scale);
    path.lineTo(centerX + 1.8 * scale, centerY + 4.2 * scale);
    path.cubicTo(
      centerX + 0.6 * scale, centerY + 5.4 * scale,
      centerX - 0.6 * scale, centerY + 6.0 * scale,
      centerX - 1.8 * scale, centerY + 6.0 * scale,
    );
    path.cubicTo(
      centerX + 0.6 * scale, centerY + 6.0 * scale,
      centerX + 2.4 * scale, centerY + 4.8 * scale,
      centerX + 2.4 * scale, centerY + 2.4 * scale,
    );
    path.lineTo(centerX + 2.4 * scale, centerY + 0.6 * scale);
    path.lineTo(centerX + 4.8 * scale, centerY + 0.6 * scale);
    path.lineTo(centerX + 4.8 * scale, centerY + 2.4 * scale);
    path.cubicTo(
      centerX + 4.8 * scale, centerY + 6.6 * scale,
      centerX + 1.2 * scale, centerY + 8.4 * scale,
      centerX - 1.8 * scale, centerY + 8.4 * scale,
    );
    path.cubicTo(
      centerX + 0.0 * scale, centerY + 8.4 * scale,
      centerX + 2.4 * scale, centerY + 7.2 * scale,
      centerX + 3.6 * scale, centerY + 5.4 * scale,
    );
    path.close();
    
    return path;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Alternative Google button with icon from assets
class AuthGoogleButtonWithAsset extends StatelessWidget {
  const AuthGoogleButtonWithAsset({
    super.key,
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.enabled = true,
    this.width,
    this.height = 56,
    this.borderRadius = 12,
    this.iconAssetPath = 'assets/icons/google_logo.png',
  });

  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;
  final bool enabled;
  final double? width;
  final double height;
  final double borderRadius;
  final String iconAssetPath;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isButtonEnabled = enabled && !isLoading && onPressed != null;

    return SizedBox(
      width: width ?? double.infinity,
      height: height,
      child: ElevatedButton(
        onPressed: isButtonEnabled ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: isButtonEnabled ? Colors.white : Colors.grey[100],
          foregroundColor: Colors.grey[700],
          disabledBackgroundColor: Colors.grey[50],
          disabledForegroundColor: Colors.grey[400],
          elevation: isButtonEnabled ? 1 : 0,
          shadowColor: Colors.black.withValues(alpha: 0.1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            side: BorderSide(
              color: isButtonEnabled 
                  ? Colors.grey[300]! 
                  : Colors.grey[200]!,
              width: 1,
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
        child: isLoading
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.5,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[600]!),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'جاري المعالجة...',
                    style: theme.textTheme.labelLarge?.copyWith(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset(
                    iconAssetPath,
                    width: 20,
                    height: 20,
                    errorBuilder: (context, error, stackTrace) {
                      // Fallback to Material icon if asset not found
                      return Icon(
                        Icons.g_mobiledata,
                        size: 20,
                        color: Colors.red,
                      );
                    },
                  ),
                  const SizedBox(width: 12),
                  Text(
                    text,
                    style: theme.textTheme.labelLarge?.copyWith(
                      color: Colors.grey[700],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

/// Compact Google button for smaller spaces
class AuthCompactGoogleButton extends StatelessWidget {
  const AuthCompactGoogleButton({
    super.key,
    required this.onPressed,
    this.isLoading = false,
    this.enabled = true,
    this.size = 48,
  });

  final VoidCallback? onPressed;
  final bool isLoading;
  final bool enabled;
  final double size;

  @override
  Widget build(BuildContext context) {
    final isButtonEnabled = enabled && !isLoading && onPressed != null;

    return SizedBox(
      width: size,
      height: size,
      child: ElevatedButton(
        onPressed: isButtonEnabled ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: isButtonEnabled ? Colors.white : Colors.grey[100],
          disabledBackgroundColor: Colors.grey[50],
          elevation: isButtonEnabled ? 1 : 0,
          shadowColor: Colors.black.withValues(alpha: 0.1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: isButtonEnabled 
                  ? Colors.grey[300]! 
                  : Colors.grey[200]!,
              width: 1,
            ),
          ),
          padding: EdgeInsets.zero,
        ),
        child: isLoading
            ? SizedBox(
                width: size * 0.4,
                height: size * 0.4,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[600]!),
                ),
              )
            : Container(
                width: size * 0.4,
                height: size * 0.4,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2),
                ),
                child: CustomPaint(
                  painter: GoogleLogoPainter(),
                ),
              ),
      ),
    );
  }
}
