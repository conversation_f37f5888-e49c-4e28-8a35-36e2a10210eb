// ============================================================================
// CarNow Unified Authentication System - Session Management Widget
// ============================================================================
// File: session_management_widget.dart
// Description: UI component for managing user sessions and logout functionality
// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
// Created: 2024-01-20
// ============================================================================

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/carnow_colors.dart';
import '../../../core/error/app_error.dart';
import '../services/session_management_service.dart';
import 'auth_loading_button.dart';
import 'auth_error_display.dart';

/// Widget for managing active sessions
class SessionManagementWidget extends ConsumerStatefulWidget {
  const SessionManagementWidget({super.key});

  @override
  ConsumerState<SessionManagementWidget> createState() => 
      _SessionManagementWidgetState();
}

class _SessionManagementWidgetState 
    extends ConsumerState<SessionManagementWidget> {
  bool _isLoading = false;
  String? _errorMessage;
  List<SessionInfo>? _sessions;

  @override
  void initState() {
    super.initState();
    _loadSessions();
  }

  Future<void> _loadSessions() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    final sessionService = ref.read(sessionManagementServiceProvider);
    final result = await sessionService.getActiveSessions();

    result.when(
      success: (sessions) {
        setState(() {
          _sessions = sessions;
          _isLoading = false;
        });
      },
      failure: (error) {
        setState(() {
          _errorMessage = error.getLocalizedMessage('en');
          _isLoading = false;
        });
      },
      loading: () {
        setState(() => _isLoading = true);
      },
      cancelled: () {
        setState(() => _isLoading = false);
      },
    );
  }

  Future<void> _terminateSession(String sessionId) async {
    final sessionService = ref.read(sessionManagementServiceProvider);
    final result = await sessionService.terminateSession(sessionId);

    result.when(
      success: (_) {
        _loadSessions(); // Refresh the list
        _showSuccessMessage('Session terminated successfully');
      },
      failure: (error) {
        setState(() {
          _errorMessage = error.getLocalizedMessage('en');
        });
      },
      loading: () {},
      cancelled: () {},
    );
  }

  Future<void> _terminateAllOtherSessions() async {
    final confirmed = await _showConfirmationDialog(
      title: 'Terminate All Other Sessions',
      titleAr: 'إنهاء جميع الجلسات الأخرى',
      message: 'This will log you out from all other devices. Continue?',
      messageAr: 'سيتم تسجيل خروجك من جميع الأجهزة الأخرى. هل تريد المتابعة؟',
    );

    if (!confirmed) return;

    final sessionService = ref.read(sessionManagementServiceProvider);
    final result = await sessionService.terminateAllOtherSessions();

    result.when(
      success: (_) {
        _loadSessions(); // Refresh the list
        _showSuccessMessage('All other sessions terminated successfully');
      },
      failure: (error) {
        setState(() {
          _errorMessage = error.getLocalizedMessage('en');
        });
      },
      loading: () {},
      cancelled: () {},
    );
  }

  Future<void> _logout() async {
    final confirmed = await _showConfirmationDialog(
      title: 'Logout',
      titleAr: 'تسجيل الخروج',
      message: 'Are you sure you want to logout?',
      messageAr: 'هل أنت متأكد من تسجيل الخروج؟',
    );

    if (!confirmed) return;

    final sessionService = ref.read(sessionManagementServiceProvider);
    final result = await sessionService.logout(type: LogoutType.manual);

    result.when(
      success: (_) {
        // Navigate to login screen
        if (mounted) {
          context.go('/auth/login');
        }
      },
      failure: (error) {
        setState(() {
          _errorMessage = error.getLocalizedMessage('en');
        });
      },
      loading: () {},
      cancelled: () {},
    );
  }

  Future<void> _logoutFromAllDevices() async {
    final confirmed = await _showConfirmationDialog(
      title: 'Logout from All Devices',
      titleAr: 'تسجيل الخروج من جميع الأجهزة',
      message: 'This will log you out from all devices including this one. Continue?',
      messageAr: 'سيتم تسجيل خروجك من جميع الأجهزة بما في ذلك هذا الجهاز. هل تريد المتابعة؟',
    );

    if (!confirmed) return;

    final sessionService = ref.read(sessionManagementServiceProvider);
    final result = await sessionService.logout(
      type: LogoutType.allDevices,
      invalidateAllSessions: true,
    );

    result.when(
      success: (_) {
        // Navigate to login screen
        if (mounted) {
          context.go('/auth/login');
        }
      },
      failure: (error) {
        setState(() {
          _errorMessage = error.getLocalizedMessage('en');
        });
      },
      loading: () {},
      cancelled: () {},
    );
  }

  Future<bool> _showConfirmationDialog({
    required String title,
    required String titleAr,
    required String message,
    required String messageAr,
  }) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: CarnowColors.error,
            ),
            child: const Text('Confirm'),
          ),
        ],
      ),
    ) ?? false;
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: CarnowColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Session Management'),
        backgroundColor: CarnowColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: RefreshIndicator(
        onRefresh: _loadSessions,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current session info
              _buildCurrentSessionCard(),
              
              const SizedBox(height: 24),
              
              // Error display
              if (_errorMessage != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: AuthErrorDisplay(
                    error: _errorMessage!,
                    onRetry: _loadSessions,
                  ),
                ),
              
              // Active sessions section
              _buildActiveSessionsSection(),
              
              const SizedBox(height: 24),
              
              // Logout options
              _buildLogoutOptionsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentSessionCard() {
    final sessionService = ref.watch(sessionManagementServiceProvider);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.phone_android,
                  color: CarnowColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'Current Session',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            _buildSessionInfoRow(
              'Status',
              _getStatusText(sessionService.currentState),
              _getStatusColor(sessionService.currentState),
            ),
            
            if (sessionService.timeSinceLastActivity != null)
              _buildSessionInfoRow(
                'Last Activity',
                _formatDuration(sessionService.timeSinceLastActivity!),
                Colors.grey[600],
              ),
            
            if (sessionService.timeUntilExpiry != null)
              _buildSessionInfoRow(
                'Expires In',
                _formatDuration(sessionService.timeUntilExpiry!),
                Colors.orange[600],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveSessionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Active Sessions',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton.icon(
              onPressed: _terminateAllOtherSessions,
              icon: const Icon(Icons.logout, size: 16),
              label: const Text('End All Others'),
              style: TextButton.styleFrom(
                foregroundColor: CarnowColors.error,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        if (_isLoading)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: CircularProgressIndicator(),
            ),
          )
        else if (_sessions == null || _sessions!.isEmpty)
          const Card(
            child: Padding(
              padding: EdgeInsets.all(24),
              child: Center(
                child: Text(
                  'No active sessions found',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _sessions!.length,
            itemBuilder: (context, index) {
              final session = _sessions![index];
              return _buildSessionCard(session);
            },
          ),
      ],
    );
  }

  Widget _buildSessionCard(SessionInfo session) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          _getDeviceIcon(session.deviceType),
          color: session.isCurrent ? CarnowColors.primary : Colors.grey,
        ),
        title: Text(
          session.deviceName,
          style: TextStyle(
            fontWeight: session.isCurrent ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${session.deviceType} • ${session.ipAddress ?? 'Unknown IP'}'),
            Text(
              'Last active: ${_formatDateTime(session.lastAccessedAt)}',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        trailing: session.isCurrent
            ? Chip(
                label: const Text(
                  'Current',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
                backgroundColor: CarnowColors.primary,
              )
            : IconButton(
                icon: const Icon(Icons.logout, color: Colors.red),
                onPressed: () => _terminateSession(session.sessionId),
                tooltip: 'Terminate Session',
              ),
        isThreeLine: true,
      ),
    );
  }

  Widget _buildLogoutOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Logout Options',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Regular logout
        SizedBox(
          width: double.infinity,
          child: AuthLoadingButton(
            onPressed: _logout,
            text: 'Logout',
            icon: Icons.logout,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Logout from all devices
        SizedBox(
          width: double.infinity,
          child: AuthLoadingButton(
            onPressed: _logoutFromAllDevices,
            text: 'Logout from All Devices',
            icon: Icons.logout,
            backgroundColor: CarnowColors.error,
          ),
        ),
      ],
    );
  }

  Widget _buildSessionInfoRow(String label, String value, Color? valueColor) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: valueColor ?? Colors.black87,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getDeviceIcon(String deviceType) {
    switch (deviceType.toLowerCase()) {
      case 'mobile':
        return Icons.phone_android;
      case 'web':
        return Icons.web;
      case 'desktop':
        return Icons.computer;
      case 'tablet':
        return Icons.tablet;
      default:
        return Icons.device_unknown;
    }
  }

  String _getStatusText(SessionState state) {
    switch (state) {
      case SessionState.active:
        return 'Active';
      case SessionState.inactive:
        return 'Inactive';
      case SessionState.expired:
        return 'Expired';
      case SessionState.terminated:
        return 'Terminated';
      case SessionState.suspended:
        return 'Suspended';
    }
  }

  Color? _getStatusColor(SessionState state) {
    switch (state) {
      case SessionState.active:
        return CarnowColors.success;
      case SessionState.inactive:
        return Colors.orange;
      case SessionState.expired:
      case SessionState.terminated:
        return CarnowColors.error;
      case SessionState.suspended:
        return Colors.grey;
    }
  }

  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m';
    } else {
      return 'Just now';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }
}

/// Simple logout button widget for use in other screens
class LogoutButton extends ConsumerWidget {
  final String? text;
  final IconData? icon;
  final VoidCallback? onLogoutSuccess;

  const LogoutButton({
    super.key,
    this.text,
    this.icon,
    this.onLogoutSuccess,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AuthLoadingButton(
      onPressed: () => _performLogout(context, ref),
      text: text ?? 'Logout',
      icon: icon ?? Icons.logout,
    );
  }

  Future<void> _performLogout(BuildContext context, WidgetRef ref) async {
    final sessionService = ref.read(sessionManagementServiceProvider);
    final result = await sessionService.logout(type: LogoutType.manual);

    result.when(
      success: (_) {
        onLogoutSuccess?.call();
        if (context.mounted) {
          context.go('/auth/login');
        }
      },
      failure: (error) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(error.getLocalizedMessage('en')),
              backgroundColor: CarnowColors.error,
            ),
          );
        }
      },
      loading: () {},
      cancelled: () {},
    );
  }
}
