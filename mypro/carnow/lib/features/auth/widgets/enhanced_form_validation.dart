/// ============================================================================
/// ENHANCED FORM VALIDATION - Real-time Form Validation System
/// ============================================================================
/// 
/// Enhanced form validation with real-time feedback and localized messages
/// Task 20: Improve authentication UI components - Better form validation
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ============================================================================
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/error/centralized_error_handler.dart';
import 'enhanced_error_display.dart';

/// Enhanced form field with real-time validation
class EnhancedFormField extends ConsumerStatefulWidget {
  const EnhancedFormField({
    super.key,
    required this.controller,
    required this.labelText,
    required this.validator,
    this.hintText,
    this.helperText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.keyboardType,
    this.textInputAction,
    this.onChanged,
    this.onSubmitted,
    this.enabled = true,
    this.autofocus = false,
    this.maxLines = 1,
    this.maxLength,
    this.showValidationIcon = true,
    this.validateOnChange = true,
    this.showStrengthIndicator = false,
    this.semanticLabel,
    this.margin,
    this.padding,
  });

  final TextEditingController controller;
  final String labelText;
  final String? Function(String?) validator;
  final String? hintText;
  final String? helperText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final void Function(String)? onChanged;
  final void Function(String)? onSubmitted;
  final bool enabled;
  final bool autofocus;
  final int maxLines;
  final int? maxLength;
  final bool showValidationIcon;
  final bool validateOnChange;
  final bool showStrengthIndicator;
  final String? semanticLabel;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;

  @override
  ConsumerState<EnhancedFormField> createState() => _EnhancedFormFieldState();
}

class _EnhancedFormFieldState extends ConsumerState<EnhancedFormField>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  String? _validationError;
  bool _isValid = true;
  bool _hasBeenTouched = false;
  double _passwordStrength = 0.0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    _animationController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    if (!widget.validateOnChange && !_hasBeenTouched) return;

    setState(() {
      _hasBeenTouched = true;
      _validateField();
      
      if (widget.showStrengthIndicator) {
        _passwordStrength = _calculatePasswordStrength(widget.controller.text);
      }
    });

    widget.onChanged?.call(widget.controller.text);
  }

  void _validateField() {
    final error = widget.validator(widget.controller.text);
    _validationError = error;
    _isValid = error == null;

    if (error != null) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  double _calculatePasswordStrength(String password) {
    if (password.isEmpty) return 0.0;

    double strength = 0.0;
    
    // Length check
    if (password.length >= 8) strength += 0.2;
    if (password.length >= 12) strength += 0.1;
    
    // Character variety checks
    if (password.contains(RegExp(r'[a-z]'))) strength += 0.2;
    if (password.contains(RegExp(r'[A-Z]'))) strength += 0.2;
    if (password.contains(RegExp(r'[0-9]'))) strength += 0.2;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) strength += 0.2;
    
    // Bonus for very long passwords
    if (password.length >= 16) strength += 0.1;

    return strength.clamp(0.0, 1.0);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final locale = Localizations.localeOf(context);

    return Container(
      margin: widget.margin ?? const EdgeInsets.symmetric(vertical: 8),
      padding: widget.padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main text field
          Semantics(
            label: widget.semanticLabel ?? widget.labelText,
            child: TextFormField(
              controller: widget.controller,
              obscureText: widget.obscureText,
              keyboardType: widget.keyboardType,
              textInputAction: widget.textInputAction,
              enabled: widget.enabled,
              autofocus: widget.autofocus,
              maxLines: widget.maxLines,
              maxLength: widget.maxLength,
              onFieldSubmitted: widget.onSubmitted,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: widget.enabled 
                    ? colorScheme.onSurface 
                    : colorScheme.onSurface.withValues(alpha: 0.38),
              ),
              decoration: InputDecoration(
                labelText: widget.labelText,
                hintText: widget.hintText,
                helperText: widget.helperText,
                prefixIcon: widget.prefixIcon,
                suffixIcon: _buildSuffixIcon(colorScheme),
                
                // Material 3 styling
                filled: true,
                fillColor: widget.enabled 
                    ? colorScheme.surfaceContainerHighest.withValues(alpha: 0.3)
                    : colorScheme.surfaceContainerHighest.withValues(alpha: 0.1),
                
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: colorScheme.outline.withValues(alpha: 0.12),
                    width: 1,
                  ),
                ),
                
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: colorScheme.outline.withValues(alpha: 0.12),
                    width: 1,
                  ),
                ),
                
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: colorScheme.primary,
                    width: 2,
                  ),
                ),
                
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: colorScheme.error,
                    width: 2,
                  ),
                ),
                
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: colorScheme.error,
                    width: 2,
                  ),
                ),
                
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: colorScheme.outline.withValues(alpha: 0.06),
                    width: 1,
                  ),
                ),
                
                // Label styling
                labelStyle: TextStyle(
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                
                floatingLabelStyle: TextStyle(
                  color: _isValid ? colorScheme.primary : colorScheme.error,
                ),
                
                // Hint styling
                hintStyle: TextStyle(
                  color: colorScheme.onSurface.withValues(alpha: 0.4),
                ),
                
                // Helper text styling
                helperStyle: TextStyle(
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                
                // Error styling
                errorStyle: const TextStyle(height: 0), // Hide default error text
              ),
            ),
          ),
          
          // Password strength indicator
          if (widget.showStrengthIndicator && widget.controller.text.isNotEmpty) ...[
            const SizedBox(height: 8),
            _buildPasswordStrengthIndicator(theme, colorScheme, locale),
          ],
          
          // Validation error display
          if (_validationError != null && _hasBeenTouched)
            FadeTransition(
              opacity: _fadeAnimation,
              child: EnhancedErrorDisplay(
                error: CarNowError(
                  code: CarNowErrorCode.invalidFormat,
                  message: _validationError!,
                  severity: ErrorSeverity.error,
                ),
                variant: ErrorDisplayVariant.inline,
                showRetryButton: false,
                showDismissButton: false,
              ),
            ),
        ],
      ),
    );
  }

  Widget? _buildSuffixIcon(ColorScheme colorScheme) {
    if (!widget.showValidationIcon) return widget.suffixIcon;

    Widget? validationIcon;
    if (_hasBeenTouched && widget.controller.text.isNotEmpty) {
      validationIcon = Icon(
        _isValid ? Icons.check_circle : Icons.error,
        color: _isValid ? Colors.green : colorScheme.error,
        size: 20,
      );
    }

    if (widget.suffixIcon != null && validationIcon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          validationIcon,
          const SizedBox(width: 8),
          widget.suffixIcon!,
        ],
      );
    }

    return validationIcon ?? widget.suffixIcon;
  }

  Widget _buildPasswordStrengthIndicator(
    ThemeData theme,
    ColorScheme colorScheme,
    Locale locale,
  ) {
    final isArabic = locale.languageCode == 'ar';
    
    Color strengthColor;
    String strengthLabel;
    
    if (_passwordStrength < 0.3) {
      strengthColor = Colors.red;
      strengthLabel = isArabic ? 'ضعيفة' : 'Weak';
    } else if (_passwordStrength < 0.6) {
      strengthColor = Colors.orange;
      strengthLabel = isArabic ? 'متوسطة' : 'Medium';
    } else if (_passwordStrength < 0.8) {
      strengthColor = Colors.blue;
      strengthLabel = isArabic ? 'قوية' : 'Strong';
    } else {
      strengthColor = Colors.green;
      strengthLabel = isArabic ? 'قوية جداً' : 'Very Strong';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: _passwordStrength,
                backgroundColor: colorScheme.surfaceContainerHighest,
                valueColor: AlwaysStoppedAnimation<Color>(strengthColor),
                minHeight: 4,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              strengthLabel,
              style: theme.textTheme.bodySmall?.copyWith(
                color: strengthColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 4),
        
        Text(
          isArabic 
              ? 'قوة كلمة المرور: ${(_passwordStrength * 100).round()}%'
              : 'Password strength: ${(_passwordStrength * 100).round()}%',
          style: theme.textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
      ],
    );
  }
}

/// Enhanced form validation state manager
class FormValidationState {
  const FormValidationState({
    this.isValid = false,
    this.errors = const {},
    this.isSubmitting = false,
    this.hasBeenSubmitted = false,
  });

  final bool isValid;
  final Map<String, String> errors;
  final bool isSubmitting;
  final bool hasBeenSubmitted;

  FormValidationState copyWith({
    bool? isValid,
    Map<String, String>? errors,
    bool? isSubmitting,
    bool? hasBeenSubmitted,
  }) {
    return FormValidationState(
      isValid: isValid ?? this.isValid,
      errors: errors ?? this.errors,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      hasBeenSubmitted: hasBeenSubmitted ?? this.hasBeenSubmitted,
    );
  }
}

/// Form validation state notifier
class FormValidationNotifier extends StateNotifier<FormValidationState> {
  FormValidationNotifier() : super(const FormValidationState());

  final Map<String, String? Function(String?)> _validators = {};
  final Map<String, TextEditingController> _controllers = {};

  /// Register field validator
  void registerField(String fieldName, String? Function(String?) validator, TextEditingController controller) {
    _validators[fieldName] = validator;
    _controllers[fieldName] = controller;
    controller.addListener(() => _validateField(fieldName));
  }

  /// Unregister field validator
  void unregisterField(String fieldName) {
    _validators.remove(fieldName);
    final controller = _controllers.remove(fieldName);
    controller?.removeListener(() => _validateField(fieldName));
  }

  /// Validate specific field
  void _validateField(String fieldName) {
    final validator = _validators[fieldName];
    final controller = _controllers[fieldName];
    
    if (validator == null || controller == null) return;

    final error = validator(controller.text);
    final newErrors = Map<String, String>.from(state.errors);
    
    if (error != null) {
      newErrors[fieldName] = error;
    } else {
      newErrors.remove(fieldName);
    }

    state = state.copyWith(
      errors: newErrors,
      isValid: newErrors.isEmpty,
    );
  }

  /// Validate all fields
  bool validateAll() {
    final errors = <String, String>{};
    
    for (final entry in _validators.entries) {
      final fieldName = entry.key;
      final validator = entry.value;
      final controller = _controllers[fieldName];
      
      if (controller != null) {
        final error = validator(controller.text);
        if (error != null) {
          errors[fieldName] = error;
        }
      }
    }

    state = state.copyWith(
      errors: errors,
      isValid: errors.isEmpty,
      hasBeenSubmitted: true,
    );

    return errors.isEmpty;
  }

  /// Set submitting state
  void setSubmitting(bool isSubmitting) {
    state = state.copyWith(isSubmitting: isSubmitting);
  }

  /// Clear all validation errors
  void clearErrors() {
    state = state.copyWith(
      errors: {},
      isValid: true,
      hasBeenSubmitted: false,
    );
  }

  /// Get error for specific field
  String? getFieldError(String fieldName) {
    return state.errors[fieldName];
  }

  /// Check if field has error
  bool hasFieldError(String fieldName) {
    return state.errors.containsKey(fieldName);
  }
}

/// Provider for form validation
final formValidationProvider = StateNotifierProvider.autoDispose<FormValidationNotifier, FormValidationState>((ref) {
  return FormValidationNotifier();
});

/// Enhanced submit button with validation integration
class EnhancedSubmitButton extends ConsumerWidget {
  const EnhancedSubmitButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.icon,
    this.width,
    this.height = 56,
    this.enabled = true,
    this.style,
  });

  final VoidCallback? onPressed;
  final String text;
  final Widget? icon;
  final double? width;
  final double height;
  final bool enabled;
  final ButtonStyle? style;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final validationState = ref.watch(formValidationProvider);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final isEnabled = enabled && 
                     validationState.isValid && 
                     !validationState.isSubmitting &&
                     onPressed != null;

    return SizedBox(
      width: width ?? double.infinity,
      height: height,
      child: ElevatedButton(
        onPressed: isEnabled ? () {
          if (ref.read(formValidationProvider.notifier).validateAll()) {
            onPressed?.call();
          }
        } : null,
        style: style ?? ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          disabledBackgroundColor: colorScheme.surfaceContainerHighest,
          disabledForegroundColor: colorScheme.onSurface.withValues(alpha: 0.38),
          elevation: isEnabled ? 2 : 0,
          shadowColor: colorScheme.shadow.withValues(alpha: 0.15),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: validationState.isSubmitting
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    colorScheme.onPrimary,
                  ),
                ),
              )
            : icon != null
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      icon!,
                      const SizedBox(width: 8),
                      Text(
                        text,
                        style: theme.textTheme.labelLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  )
                : Text(
                    text,
                    style: theme.textTheme.labelLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
      ),
    );
  }
}
