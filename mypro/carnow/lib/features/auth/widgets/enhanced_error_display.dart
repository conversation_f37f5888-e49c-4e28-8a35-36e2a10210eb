/// ============================================================================
/// ENHANCED ERROR DISPLAY - Advanced Error UI Components
/// ============================================================================
/// 
/// Enhanced error display with localized messages and better UX
/// Task 20 & 21: Improve UI components and enhance error handling
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ============================================================================
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/error/centralized_error_handler.dart';

/// Enhanced error display widget with comprehensive error handling
class EnhancedErrorDisplay extends ConsumerWidget {
  const EnhancedErrorDisplay({
    super.key,
    this.error,
    this.onRetry,
    this.onDismiss,
    this.showRetryButton = true,
    this.showDismissButton = true,
    this.autoHide = false,
    this.autoHideDuration = const Duration(seconds: 5),
    this.variant = ErrorDisplayVariant.full,
    this.margin,
    this.padding,
  });

  final CarNowError? error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showRetryButton;
  final bool showDismissButton;
  final bool autoHide;
  final Duration autoHideDuration;
  final ErrorDisplayVariant variant;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentError = error ?? ref.watch(errorStateProvider);
    
    if (currentError == null) {
      return const SizedBox.shrink();
    }

    return _buildErrorDisplay(context, ref, currentError);
  }

  Widget _buildErrorDisplay(BuildContext context, WidgetRef ref, CarNowError error) {
    switch (variant) {
      case ErrorDisplayVariant.full:
        return _buildFullErrorDisplay(context, ref, error);
      case ErrorDisplayVariant.compact:
        return _buildCompactErrorDisplay(context, ref, error);
      case ErrorDisplayVariant.inline:
        return _buildInlineErrorDisplay(context, ref, error);
      case ErrorDisplayVariant.snackbar:
        return _buildSnackbarErrorDisplay(context, ref, error);
    }
  }

  /// Full error display with all features
  Widget _buildFullErrorDisplay(BuildContext context, WidgetRef ref, CarNowError error) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final locale = Localizations.localeOf(context);

    return Container(
      margin: margin ?? const EdgeInsets.all(16),
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getErrorBackgroundColor(colorScheme, error.severity),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getErrorBorderColor(colorScheme, error.severity),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Error header with icon and severity
          Row(
            children: [
              Icon(
                _getErrorIcon(error.severity),
                color: _getErrorIconColor(colorScheme, error.severity),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  _getSeverityLabel(error.severity, locale),
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: _getErrorTextColor(colorScheme, error.severity),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (showDismissButton)
                IconButton(
                  onPressed: () {
                    ref.read(errorStateProvider.notifier).clearError();
                    onDismiss?.call();
                  },
                  icon: Icon(
                    Icons.close,
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                    size: 20,
                  ),
                  tooltip: locale.languageCode == 'ar' ? 'إغلاق' : 'Close',
                ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Error message
          Text(
            error.getLocalizedMessage(locale),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.87),
            ),
          ),
          
          // Error details (if available)
          if (error.details != null && error.details!.isNotEmpty) ...[
            const SizedBox(height: 8),
            ExpansionTile(
              title: Text(
                locale.languageCode == 'ar' ? 'تفاصيل الخطأ' : 'Error Details',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                  fontWeight: FontWeight.w500,
                ),
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: SelectableText(
                    error.details!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ],
            ),
          ],
          
          // User action suggestion
          if (error.userAction != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.lightbulb_outline,
                    color: colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      error.userAction!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          // Action buttons
          if (showRetryButton && error.retryable || onRetry != null) ...[
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (showRetryButton && (error.retryable || onRetry != null))
                  ElevatedButton.icon(
                    onPressed: () {
                      ref.read(errorStateProvider.notifier).clearError();
                      onRetry?.call();
                    },
                    icon: const Icon(Icons.refresh, size: 18),
                    label: Text(
                      locale.languageCode == 'ar' ? 'إعادة المحاولة' : 'Retry',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.primary,
                      foregroundColor: colorScheme.onPrimary,
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Compact error display for smaller spaces
  Widget _buildCompactErrorDisplay(BuildContext context, WidgetRef ref, CarNowError error) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final locale = Localizations.localeOf(context);

    return Container(
      margin: margin ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: padding ?? const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getErrorBackgroundColor(colorScheme, error.severity),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getErrorBorderColor(colorScheme, error.severity),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getErrorIcon(error.severity),
            color: _getErrorIconColor(colorScheme, error.severity),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              error.getLocalizedMessage(locale),
              style: theme.textTheme.bodySmall?.copyWith(
                color: _getErrorTextColor(colorScheme, error.severity),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (showRetryButton && error.retryable)
            IconButton(
              onPressed: () {
                ref.read(errorStateProvider.notifier).clearError();
                onRetry?.call();
              },
              icon: const Icon(Icons.refresh, size: 18),
              tooltip: locale.languageCode == 'ar' ? 'إعادة المحاولة' : 'Retry',
            ),
          if (showDismissButton)
            IconButton(
              onPressed: () {
                ref.read(errorStateProvider.notifier).clearError();
                onDismiss?.call();
              },
              icon: const Icon(Icons.close, size: 18),
              tooltip: locale.languageCode == 'ar' ? 'إغلاق' : 'Close',
            ),
        ],
      ),
    );
  }

  /// Inline error display for form fields
  Widget _buildInlineErrorDisplay(BuildContext context, WidgetRef ref, CarNowError error) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final locale = Localizations.localeOf(context);

    return Padding(
      padding: margin ?? const EdgeInsets.only(top: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.error_outline,
            color: colorScheme.error,
            size: 16,
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              error.getLocalizedMessage(locale),
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Snackbar-style error display
  Widget _buildSnackbarErrorDisplay(BuildContext context, WidgetRef ref, CarNowError error) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final locale = Localizations.localeOf(context);

    // Show as actual SnackBar
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                _getErrorIcon(error.severity),
                color: colorScheme.onError,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  error.getLocalizedMessage(locale),
                  style: TextStyle(color: colorScheme.onError),
                ),
              ),
            ],
          ),
          backgroundColor: _getErrorBackgroundColor(colorScheme, error.severity),
          duration: autoHideDuration,
          action: error.retryable && onRetry != null
              ? SnackBarAction(
                  label: locale.languageCode == 'ar' ? 'إعادة المحاولة' : 'Retry',
                  textColor: colorScheme.onError,
                  onPressed: () {
                    ref.read(errorStateProvider.notifier).clearError();
                    onRetry?.call();
                  },
                )
              : null,
        ),
      );
    });

    return const SizedBox.shrink();
  }

  // Helper methods for styling
  Color _getErrorBackgroundColor(ColorScheme colorScheme, ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return colorScheme.primaryContainer.withValues(alpha: 0.1);
      case ErrorSeverity.warning:
        return Colors.orange.withValues(alpha: 0.1);
      case ErrorSeverity.error:
        return colorScheme.errorContainer.withValues(alpha: 0.1);
      case ErrorSeverity.critical:
        return Colors.deepPurple.withValues(alpha: 0.1);
    }
  }

  Color _getErrorBorderColor(ColorScheme colorScheme, ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return colorScheme.primary.withValues(alpha: 0.3);
      case ErrorSeverity.warning:
        return Colors.orange.withValues(alpha: 0.3);
      case ErrorSeverity.error:
        return colorScheme.error.withValues(alpha: 0.3);
      case ErrorSeverity.critical:
        return Colors.deepPurple.withValues(alpha: 0.3);
    }
  }

  Color _getErrorIconColor(ColorScheme colorScheme, ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return colorScheme.primary;
      case ErrorSeverity.warning:
        return Colors.orange;
      case ErrorSeverity.error:
        return colorScheme.error;
      case ErrorSeverity.critical:
        return Colors.deepPurple;
    }
  }

  Color _getErrorTextColor(ColorScheme colorScheme, ErrorSeverity severity) {
    return colorScheme.onSurface;
  }

  IconData _getErrorIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return Icons.info_outline;
      case ErrorSeverity.warning:
        return Icons.warning_amber_outlined;
      case ErrorSeverity.error:
        return Icons.error_outline;
      case ErrorSeverity.critical:
        return Icons.dangerous_outlined;
    }
  }

  String _getSeverityLabel(ErrorSeverity severity, Locale locale) {
    final isArabic = locale.languageCode == 'ar';
    switch (severity) {
      case ErrorSeverity.info:
        return isArabic ? 'معلومات' : 'Information';
      case ErrorSeverity.warning:
        return isArabic ? 'تحذير' : 'Warning';
      case ErrorSeverity.error:
        return isArabic ? 'خطأ' : 'Error';
      case ErrorSeverity.critical:
        return isArabic ? 'خطأ حرج' : 'Critical Error';
    }
  }
}

/// Error display variants
enum ErrorDisplayVariant {
  full,
  compact,
  inline,
  snackbar,
}

/// Success display widget for positive feedback
class EnhancedSuccessDisplay extends StatelessWidget {
  const EnhancedSuccessDisplay({
    super.key,
    required this.message,
    this.onDismiss,
    this.autoHide = true,
    this.autoHideDuration = const Duration(seconds: 3),
    this.showDismissButton = true,
    this.margin,
    this.padding,
  });

  final String message;
  final VoidCallback? onDismiss;
  final bool autoHide;
  final Duration autoHideDuration;
  final bool showDismissButton;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: margin ?? const EdgeInsets.all(16),
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.green.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle_outline,
            color: Colors.green,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.87),
              ),
            ),
          ),
          if (showDismissButton)
            IconButton(
              onPressed: onDismiss,
              icon: Icon(
                Icons.close,
                color: colorScheme.onSurface.withValues(alpha: 0.6),
                size: 20,
              ),
            ),
        ],
      ),
    );
  }
}

/// Global error listener widget
class GlobalErrorListener extends ConsumerWidget {
  const GlobalErrorListener({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<CarNowError?>(errorStateProvider, (previous, next) {
      if (next != null) {
        // Show error using the appropriate variant
        // This can be customized based on error type or app state
        _showErrorDialog(context, ref, next);
      }
    });

    return child;
  }

  void _showErrorDialog(BuildContext context, WidgetRef ref, CarNowError error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        content: EnhancedErrorDisplay(
          error: error,
          variant: ErrorDisplayVariant.full,
          onRetry: error.retryable ? () {
            Navigator.of(context).pop();
            // Trigger retry logic here
          } : null,
          onDismiss: () {
            Navigator.of(context).pop();
            ref.read(errorStateProvider.notifier).clearError();
          },
        ),
      ),
    );
  }
}
