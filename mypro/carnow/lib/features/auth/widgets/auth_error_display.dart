/// ============================================================================
/// AUTH ERROR DISPLAY - Material 3 Design System
/// ============================================================================
/// 
/// Enhanced error display component with Material 3 design and localized messages
/// Task 6: Create authentication UI components
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ============================================================================
library;

import 'package:flutter/material.dart';
import '../../../core/theme/carnow_colors.dart';

/// Enhanced error display for authentication screens
class AuthErrorDisplay extends StatefulWidget {
  const AuthErrorDisplay({
    super.key,
    required this.error,
    this.onRetry,
    this.onDismiss,
    this.showRetryButton = true,
    this.showDismissButton = true,
    this.autoHide = false,
    this.autoHideDuration = const Duration(seconds: 5),
  });

  final String error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showRetryButton;
  final bool showDismissButton;
  final bool autoHide;
  final Duration autoHideDuration;

  @override
  State<AuthErrorDisplay> createState() => _AuthErrorDisplayState();
}

class _AuthErrorDisplayState extends State<AuthErrorDisplay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Start animation
    _animationController.forward();

    // Auto hide if enabled
    if (widget.autoHide) {
      Future.delayed(widget.autoHideDuration, () {
        if (mounted) {
          _handleDismiss();
        }
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value * 50),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.error.withOpacity(0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.error.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildErrorHeader(theme),
                  const SizedBox(height: 8),
                  _buildErrorMessage(theme),
                  if (widget.showRetryButton || widget.showDismissButton)
                    const SizedBox(height: 12),
                  if (widget.showRetryButton || widget.showDismissButton)
                    _buildActionButtons(theme),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorHeader(ThemeData theme) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: theme.colorScheme.error.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.error_outline,
            size: 20,
            color: theme.colorScheme.error,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'حدث خطأ',
            style: theme.textTheme.titleSmall?.copyWith(
              color: theme.colorScheme.onErrorContainer,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        if (widget.showDismissButton)
          IconButton(
            onPressed: _handleDismiss,
            icon: Icon(
              Icons.close,
              size: 20,
              color: theme.colorScheme.onErrorContainer.withOpacity(0.7),
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
      ],
    );
  }

  Widget _buildErrorMessage(ThemeData theme) {
    return SelectableText.rich(
      TextSpan(
        text: _getLocalizedErrorMessage(widget.error),
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.colorScheme.onErrorContainer,
          height: 1.4,
        ),
      ),
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (widget.showRetryButton && widget.onRetry != null)
          TextButton.icon(
            onPressed: widget.onRetry,
            icon: Icon(
              Icons.refresh,
              size: 16,
              color: theme.colorScheme.error,
            ),
            label: Text(
              'إعادة المحاولة',
              style: theme.textTheme.labelMedium?.copyWith(
                color: theme.colorScheme.error,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
      ],
    );
  }

  String _getLocalizedErrorMessage(String error) {
    // Map common error codes to Arabic messages
    final errorMappings = {
      'INVALID_CREDENTIALS': 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
      'EMAIL_EXISTS': 'البريد الإلكتروني مستخدم بالفعل',
      'RATE_LIMIT_EXCEEDED': 'محاولات كثيرة، يرجى المحاولة لاحقاً',
      'SERVICE_UNAVAILABLE': 'الخدمة غير متاحة حالياً',
      'NETWORK_ERROR': 'خطأ في الاتصال، يرجى التحقق من الإنترنت',
      'EMAIL_NOT_VERIFIED': 'يرجى تأكيد البريد الإلكتروني أولاً',
      'ACCOUNT_DISABLED': 'الحساب معطل، يرجى التواصل مع الدعم',
      'WEAK_PASSWORD': 'كلمة المرور ضعيفة، يرجى اختيار كلمة مرور أقوى',
      'USER_NOT_FOUND': 'المستخدم غير موجود',
      'GOOGLE_SIGN_IN_FAILED': 'فشل تسجيل الدخول بـ Google',
      'GOOGLE_SIGN_IN_CANCELLED': 'تم إلغاء تسجيل الدخول بـ Google',
    };

    // Check if error contains any of the known error codes
    for (final entry in errorMappings.entries) {
      if (error.toUpperCase().contains(entry.key)) {
        return entry.value;
      }
    }

    // Return the original error if no mapping found, but clean it up
    return _cleanErrorMessage(error);
  }

  String _cleanErrorMessage(String error) {
    // Remove technical prefixes and make it more user-friendly
    String cleaned = error
        .replaceAll('Exception:', '')
        .replaceAll('Error:', '')
        .replaceAll('FirebaseAuthException:', '')
        .replaceAll('PlatformException:', '')
        .trim();

    // If the message is too technical or empty, provide a generic message
    if (cleaned.isEmpty || cleaned.length < 5 || cleaned.contains('null')) {
      return 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى';
    }

    return cleaned;
  }

  void _handleDismiss() {
    _animationController.reverse().then((_) {
      if (mounted) {
        widget.onDismiss?.call();
      }
    });
  }
}

/// Compact error display for inline use
class AuthCompactErrorDisplay extends StatelessWidget {
  const AuthCompactErrorDisplay({
    super.key,
    required this.error,
    this.onDismiss,
  });

  final String error;
  final VoidCallback? onDismiss;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.error.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            size: 16,
            color: theme.colorScheme.error,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onErrorContainer,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (onDismiss != null)
            InkWell(
              onTap: onDismiss,
              borderRadius: BorderRadius.circular(4),
              child: Padding(
                padding: const EdgeInsets.all(4),
                child: Icon(
                  Icons.close,
                  size: 14,
                  color: theme.colorScheme.onErrorContainer.withOpacity(0.7),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Success display component for positive feedback
class AuthSuccessDisplay extends StatefulWidget {
  const AuthSuccessDisplay({
    super.key,
    required this.message,
    this.onDismiss,
    this.autoHide = true,
    this.autoHideDuration = const Duration(seconds: 3),
  });

  final String message;
  final VoidCallback? onDismiss;
  final bool autoHide;
  final Duration autoHideDuration;

  @override
  State<AuthSuccessDisplay> createState() => _AuthSuccessDisplayState();
}

class _AuthSuccessDisplayState extends State<AuthSuccessDisplay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();

    if (widget.autoHide) {
      Future.delayed(widget.autoHideDuration, () {
        if (mounted) {
          _handleDismiss();
        }
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value * 50),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: CarnowColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: CarnowColors.success.withOpacity(0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: CarnowColors.success.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: CarnowColors.success.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.check_circle_outline,
                      size: 20,
                      color: CarnowColors.success,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.message,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: CarnowColors.success,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  if (widget.onDismiss != null)
                    IconButton(
                      onPressed: _handleDismiss,
                      icon: Icon(
                        Icons.close,
                        size: 20,
                        color: CarnowColors.success.withOpacity(0.7),
                      ),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleDismiss() {
    _animationController.reverse().then((_) {
      if (mounted) {
        widget.onDismiss?.call();
      }
    });
  }
}
