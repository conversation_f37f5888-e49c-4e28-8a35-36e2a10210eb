/// ============================================================================
/// AUTH WIDGETS INDEX - Material 3 Design System
/// ============================================================================
/// 
/// Export file for all authentication UI components
/// Task 6: Create authentication UI components
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ============================================================================
library;

// Authentication Screens

// Input Components
export 'auth_input_field.dart';

// Button Components
export 'auth_loading_button.dart';
export 'auth_google_button.dart';

// Display Components
export 'auth_error_display.dart';

// Legacy Components (for backward compatibility)
export 'google_oauth_button.dart';
