/// ============================================================================
/// AUTH LOADING BUTTON - Material 3 Design System
/// ============================================================================
/// 
/// Enhanced loading button component with Material 3 design and animations
/// Task 6: Create authentication UI components
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ============================================================================
library;

import 'package:flutter/material.dart';
import '../../../core/theme/carnow_colors.dart';

/// Enhanced loading button for authentication actions
class AuthLoadingButton extends StatefulWidget {
  const AuthLoadingButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.icon,
    this.isLoading = false,
    this.enabled = true,
    this.width,
    this.height = 56,
    this.borderRadius = 12,
    this.backgroundColor,
    this.foregroundColor,
    this.loadingColor,
  });

  final VoidCallback? onPressed;
  final String text;
  final IconData? icon;
  final bool isLoading;
  final bool enabled;
  final double? width;
  final double height;
  final double borderRadius;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? loadingColor;

  @override
  State<AuthLoadingButton> createState() => _AuthLoadingButtonState();
}

class _AuthLoadingButtonState extends State<AuthLoadingButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isButtonEnabled = widget.enabled && !widget.isLoading && widget.onPressed != null;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: SizedBox(
              width: widget.width ?? double.infinity,
              height: widget.height,
              child: ElevatedButton(
                onPressed: isButtonEnabled ? _handlePressed : null,
                style: _buildButtonStyle(theme, isButtonEnabled),
                child: _buildButtonContent(theme),
              ),
            ),
          ),
        );
      },
    );
  }

  ButtonStyle _buildButtonStyle(ThemeData theme, bool isEnabled) {
    final backgroundColor = widget.backgroundColor ?? CarnowColors.primary;
    final foregroundColor = widget.foregroundColor ?? Colors.white;
    
    return ElevatedButton.styleFrom(
      backgroundColor: isEnabled 
          ? backgroundColor 
          : backgroundColor.withValues(alpha: 0.38),
      foregroundColor: isEnabled 
          ? foregroundColor 
          : foregroundColor.withValues(alpha: 0.38),
      disabledBackgroundColor: backgroundColor.withValues(alpha: 0.12),
      disabledForegroundColor: theme.colorScheme.onSurface.withValues(alpha: 0.38),
      
      // Material 3 elevation
      elevation: isEnabled ? 1 : 0,
      shadowColor: backgroundColor.withValues(alpha: 0.3),
      
      // Shape and border
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      
      // Padding
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    );
  }

  Widget _buildButtonContent(ThemeData theme) {
    if (widget.isLoading) {
      return _buildLoadingContent(theme);
    }
    
    if (widget.icon != null) {
      return _buildIconContent(theme);
    }
    
    return _buildTextContent(theme);
  }

  Widget _buildLoadingContent(ThemeData theme) {
    final loadingColor = widget.loadingColor ?? 
        (widget.foregroundColor ?? Colors.white);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2.5,
            valueColor: AlwaysStoppedAnimation<Color>(loadingColor),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          'جاري المعالجة...',
          style: theme.textTheme.labelLarge?.copyWith(
            color: loadingColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildIconContent(ThemeData theme) {
    final foregroundColor = widget.foregroundColor ?? Colors.white;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          widget.icon,
          size: 20,
          color: foregroundColor,
        ),
        const SizedBox(width: 12),
        Text(
          widget.text,
          style: theme.textTheme.labelLarge?.copyWith(
            color: foregroundColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildTextContent(ThemeData theme) {
    final foregroundColor = widget.foregroundColor ?? Colors.white;
    
    return Text(
      widget.text,
      style: theme.textTheme.labelLarge?.copyWith(
        color: foregroundColor,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  void _handlePressed() {
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
    
    widget.onPressed?.call();
  }
}

/// Secondary variant of the loading button
class AuthSecondaryLoadingButton extends StatelessWidget {
  const AuthSecondaryLoadingButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.icon,
    this.isLoading = false,
    this.enabled = true,
    this.width,
    this.height = 56,
    this.borderRadius = 12,
  });

  final VoidCallback? onPressed;
  final String text;
  final IconData? icon;
  final bool isLoading;
  final bool enabled;
  final double? width;
  final double height;
  final double borderRadius;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AuthLoadingButton(
      onPressed: onPressed,
      text: text,
      icon: icon,
      isLoading: isLoading,
      enabled: enabled,
      width: width,
      height: height,
      borderRadius: borderRadius,
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      foregroundColor: CarnowColors.primary,
      loadingColor: CarnowColors.primary,
    );
  }
}

/// Outlined variant of the loading button
class AuthOutlinedLoadingButton extends StatelessWidget {
  const AuthOutlinedLoadingButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.icon,
    this.isLoading = false,
    this.enabled = true,
    this.width,
    this.height = 56,
    this.borderRadius = 12,
  });

  final VoidCallback? onPressed;
  final String text;
  final IconData? icon;
  final bool isLoading;
  final bool enabled;
  final double? width;
  final double height;
  final double borderRadius;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isButtonEnabled = enabled && !isLoading && onPressed != null;

    return SizedBox(
      width: width ?? double.infinity,
      height: height,
      child: OutlinedButton(
        onPressed: isButtonEnabled ? onPressed : null,
        style: OutlinedButton.styleFrom(
          foregroundColor: CarnowColors.primary,
          disabledForegroundColor: theme.colorScheme.onSurface.withValues(alpha: 0.38),
          side: BorderSide(
            color: isButtonEnabled 
                ? CarnowColors.primary 
                : theme.colorScheme.outline.withValues(alpha: 0.12),
            width: 1.5,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
        child: isLoading
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.5,
                      valueColor: AlwaysStoppedAnimation<Color>(CarnowColors.primary),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'جاري المعالجة...',
                    style: theme.textTheme.labelLarge?.copyWith(
                      color: CarnowColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              )
            : icon != null
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(icon, size: 20),
                      const SizedBox(width: 12),
                      Text(
                        text,
                        style: theme.textTheme.labelLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  )
                : Text(
                    text,
                    style: theme.textTheme.labelLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
      ),
    );
  }
}
