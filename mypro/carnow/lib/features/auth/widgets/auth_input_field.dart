/// ============================================================================
/// AUTH INPUT FIELD - Material 3 Design System
/// ============================================================================
/// 
/// Enhanced input field component with Material 3 design and validation
/// Task 6: Create authentication UI components
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ============================================================================
library;

import 'package:flutter/material.dart';
import '../../../core/theme/carnow_colors.dart';

/// Enhanced input field for authentication forms
class AuthInputField extends StatefulWidget {
  const AuthInputField({
    super.key,
    required this.controller,
    required this.labelText,
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.keyboardType,
    this.obscureText = false,
    this.enabled = true,
    this.maxLines = 1,
    this.onChanged,
    this.onFieldSubmitted,
  });

  final TextEditingController controller;
  final String labelText;
  final String? hintText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final bool obscureText;
  final bool enabled;
  final int maxLines;
  final void Function(String)? onChanged;
  final void Function(String)? onFieldSubmitted;

  @override
  State<AuthInputField> createState() => _AuthInputFieldState();
}

class _AuthInputFieldState extends State<AuthInputField> {
  bool _isFocused = false;
  bool _hasError = false;
  String? _errorText;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Focus(
          onFocusChange: (hasFocus) {
            setState(() {
              _isFocused = hasFocus;
            });
          },
          child: TextFormField(
            controller: widget.controller,
            validator: (value) {
              final error = widget.validator?.call(value);
              setState(() {
                _hasError = error != null;
                _errorText = error;
              });
              return error;
            },
            keyboardType: widget.keyboardType,
            obscureText: widget.obscureText,
            enabled: widget.enabled,
            maxLines: widget.maxLines,
            onChanged: widget.onChanged,
            onFieldSubmitted: widget.onFieldSubmitted,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: widget.enabled 
                  ? theme.colorScheme.onSurface
                  : theme.colorScheme.onSurface.withOpacity(0.38),
            ),
            decoration: InputDecoration(
              labelText: widget.labelText,
              hintText: widget.hintText,
              prefixIcon: widget.prefixIcon != null
                  ? Icon(
                      widget.prefixIcon,
                      color: _getIconColor(theme),
                    )
                  : null,
              suffixIcon: widget.suffixIcon,
              
              // Material 3 styling
              filled: true,
              fillColor: _getFillColor(theme),
              
              // Border styling
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: theme.colorScheme.outline,
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.5),
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: CarnowColors.primary,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: theme.colorScheme.error,
                  width: 1,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: theme.colorScheme.error,
                  width: 2,
                ),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.12),
                  width: 1,
                ),
              ),
              
              // Label styling
              labelStyle: theme.textTheme.bodyLarge?.copyWith(
                color: _getLabelColor(theme),
              ),
              floatingLabelStyle: theme.textTheme.bodySmall?.copyWith(
                color: _getFloatingLabelColor(theme),
                fontWeight: FontWeight.w500,
              ),
              
              // Hint styling
              hintStyle: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant.withOpacity(0.6),
              ),
              
              // Error styling
              errorStyle: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.error,
                fontWeight: FontWeight.w500,
              ),
              
              // Content padding
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
          ),
        ),
        
        // Custom error display with animation
        if (_hasError && _errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 8, left: 16),
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  size: 16,
                  color: theme.colorScheme.error,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _errorText!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.error,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Color _getFillColor(ThemeData theme) {
    if (!widget.enabled) {
      return theme.colorScheme.onSurface.withOpacity(0.04);
    }
    if (_hasError) {
      return theme.colorScheme.error.withOpacity(0.04);
    }
    if (_isFocused) {
      return CarnowColors.primary.withOpacity(0.04);
    }
    return theme.colorScheme.surfaceVariant.withOpacity(0.4);
  }

  Color _getIconColor(ThemeData theme) {
    if (!widget.enabled) {
      return theme.colorScheme.onSurface.withOpacity(0.38);
    }
    if (_hasError) {
      return theme.colorScheme.error;
    }
    if (_isFocused) {
      return CarnowColors.primary;
    }
    return theme.colorScheme.onSurfaceVariant;
  }

  Color _getLabelColor(ThemeData theme) {
    if (!widget.enabled) {
      return theme.colorScheme.onSurface.withOpacity(0.38);
    }
    if (_hasError) {
      return theme.colorScheme.error;
    }
    if (_isFocused) {
      return CarnowColors.primary;
    }
    return theme.colorScheme.onSurfaceVariant;
  }

  Color _getFloatingLabelColor(ThemeData theme) {
    if (!widget.enabled) {
      return theme.colorScheme.onSurface.withOpacity(0.38);
    }
    if (_hasError) {
      return theme.colorScheme.error;
    }
    if (_isFocused) {
      return CarnowColors.primary;
    }
    return theme.colorScheme.onSurfaceVariant;
  }
}
