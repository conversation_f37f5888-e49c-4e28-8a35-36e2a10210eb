import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/auth/auth_models.dart';

part 'auth_stream_provider.g.dart';

/// Stream provider for authentication state changes
@riverpod
Stream<AuthState> authStream(Ref ref) {
  // Watch the unified auth provider directly
  return Stream.periodic(const Duration(milliseconds: 500), (_) {
    final authState = ref.read(currentAuthStateProvider);
    return authState;
  }).distinct();
}
