/// شاشة استعادة كلمة المرور
///
/// تساعد المستخدمين الذين نسوا كلمة المرور الخاصة بهم على استعادة الوصول إلى حساباتهم.
/// يطلب من المستخدم إدخال بريده الإلكتروني المسجل،
/// وبعد التحقق، يتم إرسال رابط لإعادة تعيين كلمة المرور إلى هذا البريد.
/// Uses UnifiedAuthProvider for ALL authentication operations
library;

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/auth/auth_models.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/validators.dart';
import '../../../shared/widgets/primary_button.dart';

final _logger = Logger('ForgotPasswordScreen');

class ForgotPasswordScreen extends HookConsumerWidget {
  const ForgotPasswordScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final emailController = useTextEditingController();
    final formKey = useMemoized(GlobalKey<FormState>.new);
    final isEmailSent = useState(false);

    // Watch auth state for loading indicator
    final authState = ref.watch(unifiedAuthProviderProvider);
    final isLoading = authState is AuthStateLoading;

    Future<void> handleForgotPassword() async {
      if (formKey.currentState!.validate()) {
        try {
          _logger.info(
            'Sending password reset email to: ${emailController.text.trim()}',
          );

          // Use Go API via UnifiedAuthProvider - Forever Plan Compliance
          // Flutter UI Only → Go API → Supabase Data
          final authProvider = ref.read(unifiedAuthProviderProvider.notifier);
          await authProvider.resetPassword(emailController.text.trim());

          if (!context.mounted) return;

          // Set email sent state
          isEmailSent.value = true;

          _logger.info('Password reset email sent successfully');
        } catch (e) {
          if (!context.mounted) return;

          var errorMessage = 'حدث خطأ غير متوقع';
          final errorString = e.toString().toLowerCase();
          if (errorString.contains('user not found')) {
            errorMessage = 'البريد الإلكتروني غير مسجل في النظام';
          } else if (errorString.contains('email rate limit exceeded')) {
            errorMessage = 'تم تجاوز حد إرسال الرسائل، حاول مرة أخرى لاحقاً';
          } else {
            errorMessage = 'فشل في إرسال رسالة إعادة تعيين كلمة المرور';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: SelectableText.rich(
                TextSpan(
                  text: errorMessage,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onError,
                  ),
                ),
                style: TextStyle(color: Theme.of(context).colorScheme.onError),
              ),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('نسيت كلمة المرور'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppTheme.spacingL),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Logo
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(
                    child: Text(
                      'CarNow',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: AppTheme.spacingXL),

                if (!isEmailSent.value) ...[
                  // Title and description
                  const Text(
                    'نسيت كلمة المرور؟',
                    style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  Text(
                    'أدخل بريدك الإلكتروني وسنرسل لك رابط لإعادة تعيين كلمة المرور',
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppTheme.spacingXL),

                  // Form
                  Form(
                    key: formKey,
                    child: Column(
                      children: [
                        // Email field
                        TextFormField(
                          controller: emailController,
                          decoration: const InputDecoration(
                            labelText: 'البريد الإلكتروني',
                            prefixIcon: Icon(Icons.email_outlined),
                            hintText: '<EMAIL>',
                          ),
                          keyboardType: TextInputType.emailAddress,
                          textInputAction: TextInputAction.done,
                          validator: Validators.validateEmail,
                          autocorrect: false,
                          onFieldSubmitted: (_) => handleForgotPassword(),
                        ),
                        const SizedBox(height: AppTheme.spacingL),

                        // Submit button
                        PrimaryButton(
                          onPressed: handleForgotPassword,
                          isLoading: isLoading,
                          text: 'إرسال رابط إعادة التعيين',
                        ),
                      ],
                    ),
                  ),
                ] else ...[
                  // Success state
                  Container(
                    padding: const EdgeInsets.all(AppTheme.spacingL),
                    decoration: BoxDecoration(
                      color: Colors.green.withAlpha((0.1 * 255).toInt()),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.green.withAlpha((0.3 * 255).toInt()),
                      ),
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.mark_email_read_outlined,
                          size: 64,
                          color: Colors.green.shade600,
                        ),
                        const SizedBox(height: AppTheme.spacingM),
                        Text(
                          'تم إرسال الرسالة!',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: AppTheme.spacingS),
                        Text(
                          'تحقق من بريدك الإلكتروني واتبع التعليمات لإعادة تعيين كلمة المرور',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.green.shade600,
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingL),

                  // Resend button
                  TextButton(
                    onPressed: () {
                      if (context.mounted) {
                        isEmailSent.value = false;
                      }
                      emailController.clear();
                    },
                    child: const Text('إرسال إلى بريد إلكتروني آخر'),
                  ),
                ],

                const SizedBox(height: AppTheme.spacingL),

                // Back to login
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('تذكرت كلمة المرور؟ '),
                    GestureDetector(
                      onTap: () => context.go('/login'),
                      child: Text(
                        'تسجيل الدخول',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),

                // Show auth state messages
                const SizedBox(height: AppTheme.spacingM),
                authState.when(
                  initial: () => const SizedBox.shrink(),
                  loading: (message, operation) =>
                      const Center(child: CircularProgressIndicator()),
                  authenticated:
                      (user, token, refreshToken, tokenExpiry, sessionStart) =>
                          const SizedBox.shrink(),
                  unauthenticated: (reason) => const SizedBox.shrink(),
                  error:
                      (
                        message,
                        errorCode,
                        errorType,
                        isRecoverable,
                        originalException,
                      ) => Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red[200]!),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.red[600],
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                message,
                                style: TextStyle(
                                  color: Colors.red[700],
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  emailVerificationPending: (email, sentAt) =>
                      const SizedBox.shrink(),
                  sessionExpired: (expiredAt, autoRefreshAttempted) =>
                      const SizedBox.shrink(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
