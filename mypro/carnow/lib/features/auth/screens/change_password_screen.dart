/// شاشة تغيير كلمة المرور
///
/// تتيح هذه الشاشة للمستخدم الذي قام بتسجيل الدخول بالفعل تغيير كلمة المرور الخاصة به.
/// تطلب من المستخدم إدخال كلمة المرور الحالية للتأكيد،
/// ثم إدخال كلمة المرور الجديدة وتأكيدها قبل التحديث.
/// Uses UnifiedAuthProvider for ALL authentication operations
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';


import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/auth/auth_models.dart';
import '../../../core/theme/app_theme.dart';
import '../../../shared/widgets/primary_button.dart';

class ChangePasswordScreen extends ConsumerStatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  ConsumerState<ChangePasswordScreen> createState() =>
      _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends ConsumerState<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _obscureCurrentPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _changePassword() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      // Use Go API via UnifiedAuthProvider - Forever Plan Compliance
      final authProvider = ref.read(unifiedAuthProviderProvider.notifier);
      final result = await authProvider.updatePassword(
        newPassword: _newPasswordController.text,
      );

      if (mounted) {
        result.when(
          success: (user, token, refreshToken, tokenExpiry, metadata) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تغيير كلمة المرور بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
            context.pop();
          },
          failure: (error, errorCode, errorType, isRecoverable, details) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(error),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          },
          cancelled: (reason) {
            // Handle cancellation if needed
          },
          pending: (message, pendingAction, actionData) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(message),
                backgroundColor: Colors.orange,
              ),
            );
          },
        );
      }
    } catch (error) {
      if (mounted) {
        String errorMessage = 'خطأ في تغيير كلمة المرور';

        // Handle API errors - Forever Plan Compliance
        if (error.toString().contains('invalid')) {
          errorMessage = 'كلمة المرور الحالية غير صحيحة';
        } else if (error.toString().contains('weak')) {
          errorMessage = 'كلمة المرور الجديدة ضعيفة جداً';
        } else if (error.toString().contains('same')) {
          errorMessage = 'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(unifiedAuthProviderProvider);
    final isLoading = authState is AuthStateLoading;

    return Scaffold(
      appBar: AppBar(
        title: const Text('تغيير كلمة المرور'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // معلومات توضيحية
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingM),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                    const SizedBox(width: AppTheme.spacingS),
                    Expanded(
                      child: Text(
                        'أدخل كلمة المرور الحالية وكلمة المرور الجديدة',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: AppTheme.spacingXL),

              // كلمة المرور الحالية
              TextFormField(
                controller: _currentPasswordController,
                obscureText: _obscureCurrentPassword,
                decoration: InputDecoration(
                  labelText: 'كلمة المرور الحالية',
                  hintText: 'أدخل كلمة المرور الحالية',
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureCurrentPassword
                          ? Icons.visibility
                          : Icons.visibility_off,
                    ),
                    onPressed: () => setState(
                      () => _obscureCurrentPassword = !_obscureCurrentPassword,
                    ),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'كلمة المرور الحالية مطلوبة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppTheme.spacingL),

              // كلمة المرور الجديدة
              TextFormField(
                controller: _newPasswordController,
                obscureText: _obscureNewPassword,
                decoration: InputDecoration(
                  labelText: 'كلمة المرور الجديدة',
                  hintText: 'أدخل كلمة المرور الجديدة',
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureNewPassword
                          ? Icons.visibility
                          : Icons.visibility_off,
                    ),
                    onPressed: () => setState(
                      () => _obscureNewPassword = !_obscureNewPassword,
                    ),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'كلمة المرور الجديدة مطلوبة';
                  }
                  if (value.length < 6) {
                    return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                  }
                  if (value == _currentPasswordController.text) {
                    return 'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppTheme.spacingL),

              // تأكيد كلمة المرور الجديدة
              TextFormField(
                controller: _confirmPasswordController,
                obscureText: _obscureConfirmPassword,
                decoration: InputDecoration(
                  labelText: 'تأكيد كلمة المرور الجديدة',
                  hintText: 'أعد إدخال كلمة المرور الجديدة',
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureConfirmPassword
                          ? Icons.visibility
                          : Icons.visibility_off,
                    ),
                    onPressed: () => setState(
                      () => _obscureConfirmPassword = !_obscureConfirmPassword,
                    ),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'تأكيد كلمة المرور مطلوب';
                  }
                  if (value != _newPasswordController.text) {
                    return 'كلمة المرور غير متطابقة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppTheme.spacingXL),

              // زر تغيير كلمة المرور
              PrimaryButton(
                onPressed: isLoading ? null : _changePassword,
                text: isLoading ? 'جاري التغيير...' : 'تغيير كلمة المرور',
                isLoading: isLoading,
              ),

              // Show auth state messages
              const SizedBox(height: AppTheme.spacingL),
              authState.when(
                initial: () => const SizedBox.shrink(),
                loading: (message, operation) =>
                    const Center(child: CircularProgressIndicator()),
                authenticated:
                    (user, token, refreshToken, tokenExpiry, sessionStart) =>
                        const SizedBox.shrink(),
                unauthenticated: (reason) => const SizedBox.shrink(),
                error:
                    (
                      message,
                      errorCode,
                      errorType,
                      isRecoverable,
                      originalException,
                    ) => Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.red[600],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              message,
                              style: TextStyle(
                                color: Colors.red[700],
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                emailVerificationPending: (email, sentAt) =>
                    const SizedBox.shrink(),
                sessionExpired: (expiredAt, autoRefreshAttempted) => Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.amber[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.amber[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        color: Colors.amber[600],
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى',
                          style: TextStyle(
                            color: Colors.amber[700],
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
