import 'package:logging/logging.dart';

import '../../../../core/networking/simple_api_client.dart';
import '../../../../core/errors/app_error.dart';
import '../models/models.dart';

/// مستودع إدارة المستخدمين للمشرفين
class AdminUserRepository {
  AdminUserRepository(this._apiClient);

  final SimpleApiClient _apiClient;
  final Logger _logger = Logger('AdminUserRepository');

  // لم يعد الخط الأمامي يسجل الإجراءات يدوياً؛ الخادم يقوم بذلك.

  /// جلب جميع المستخدمين مع إمكانية الفلترة والترتيب
  Future<List<AdminUserModel>> getUsers({
    UserStatus? statusFilter,
    String? searchQuery,
    int limit = 50,
    int offset = 0,
    String orderBy = 'created_at',
    bool ascending = false,
  }) async {
    try {
      _logger.info('Fetching users with filters');

      // Build query params
      final queryParams = <String, dynamic>{
        'limit': limit,
        'offset': offset,
        'order_by': orderBy,
        'ascending': ascending,
        if (statusFilter != null) 'status': statusFilter.dbValue,
        if (searchQuery != null && searchQuery.isNotEmpty) 'search': searchQuery,
      };

      final response = await _apiClient.getApi<Map<String, dynamic>>(
        '/admin/users',
        queryParameters: queryParams,
      );

      if (!response.isSuccess || response.data == null) {
        throw Exception(response.message ?? 'Failed to fetch users');
      }

      final usersList = response.data!['data'] as List<dynamic>? ?? [];

      return usersList.map<AdminUserModel>((userData) {
        // استخراج عدد الطلبات - سيتم حسابه بشكل منفصل
        final ordersCount = userData['total_orders'] as int? ?? 0;

        // التحقق من حالة البائع
        final isSeller = userData['seller_type'] != null;

        // التحقق من الإدارة
        final role = userData['role'] as String? ?? 'user';
        final isAdmin = role == 'admin' || role == 'super_admin';

        return AdminUserModel(
          id: userData['id'] as String,
          email: userData['email'] as String?,
          fullName: userData['full_name'] as String?,
          phoneNumber: userData['phone'] as String?,
          status: UserStatusExtension.fromDbValue(
            userData['status'] as String? ?? 'active',
          ),
          bannedReason: userData['banned_reason'] as String?,
          bannedAt: userData['banned_at'] != null
              ? DateTime.parse(userData['banned_at'] as String)
              : null,
          bannedBy: userData['banned_by'] as String?,
          frozenReason: userData['frozen_reason'] as String?,
          frozenAt: userData['frozen_at'] != null
              ? DateTime.parse(userData['frozen_at'] as String)
              : null,
          frozenBy: userData['frozen_by'] as String?,
          frozenUntil: userData['frozen_until'] != null
              ? DateTime.parse(userData['frozen_until'] as String)
              : null,
          isEmailVerified: userData['is_email_verified'] as bool? ?? false,
          isPhoneVerified: userData['is_phone_verified'] as bool? ?? false,
          isSeller: isSeller,
          isAdmin: isAdmin,
          lastLoginAt: userData['last_login_at'] != null
              ? DateTime.parse(userData['last_login_at'] as String)
              : null,
          createdAt: DateTime.parse(userData['created_at'] as String),
          updatedAt: userData['updated_at'] != null
              ? DateTime.parse(userData['updated_at'] as String)
              : null,
          totalOrders: ordersCount,
          totalSpent: (userData['total_spent'] as num?)?.toDouble() ?? 0.0,
          violationsCount: userData['violations_count'] as int? ?? 0,
          notes: userData['admin_notes'] as String?,
        );
      }).toList();
    } catch (e, st) {
      _logger.severe('Error fetching users', e, st);
      throw AppError.database(
        message: 'فشل جلب قائمة المستخدمين',
        originalError: e,
      );
    }
  }

  /// حظر مستخدم
  Future<void> banUser({required String userId, required String reason}) async {
    try {
      _logger.info('Banning user: $userId');

      final response = await _apiClient.postApi<Map<String, dynamic>>(
        '/admin/users/$userId/ban',
        data: {'reason': reason},
      );

      if (!response.isSuccess) {
        throw Exception(response.message ?? 'Failed to ban user');
      }

      _logger.info('User banned successfully');
    } catch (e, st) {
      _logger.severe('Error banning user', e, st);
      throw AppError.database(message: 'فشل حظر المستخدم', originalError: e);
    }
  }

  /// إلغاء حظر مستخدم
  Future<void> unbanUser({required String userId, String? reason}) async {
    try {
      _logger.info('Unbanning user: $userId');

      final response = await _apiClient.postApi<Map<String, dynamic>>(
        '/admin/users/$userId/unban',
        data: {'reason': reason},
      );

      if (!response.isSuccess) {
        throw Exception(response.message ?? 'Failed to unban user');
      }

      _logger.info('User unbanned successfully');
    } catch (e, st) {
      _logger.severe('Error unbanning user', e, st);
      throw AppError.database(
        message: 'فشل إلغاء حظر المستخدم',
        originalError: e,
      );
    }
  }

  /// تجميد حساب مستخدم
  Future<void> freezeUser({
    required String userId,
    required String reason,
    DateTime? until,
  }) async {
    try {
      _logger.info('Freezing user: $userId');

      final response = await _apiClient.postApi<Map<String, dynamic>>(
        '/admin/users/$userId/freeze',
        data: {
          'reason': reason,
          if (until != null) 'until': until.toUtc().toIso8601String(),
        },
      );

      if (!response.isSuccess) {
        throw Exception(response.message ?? 'Failed to freeze user');
      }

      _logger.info('User frozen successfully');
    } catch (e, st) {
      _logger.severe('Error freezing user', e, st);
      throw AppError.database(
        message: 'فشل تجميد حساب المستخدم',
        originalError: e,
      );
    }
  }

  /// إلغاء تجميد حساب مستخدم
  Future<void> unfreezeUser({required String userId, String? reason}) async {
    try {
      _logger.info('Unfreezing user: $userId');

      final response = await _apiClient.postApi<Map<String, dynamic>>(
        '/admin/users/$userId/unfreeze',
        data: {'reason': reason},
      );

      if (!response.isSuccess) {
        throw Exception(response.message ?? 'Failed to unfreeze user');
      }

      _logger.info('User unfrozen successfully');
    } catch (e, st) {
      _logger.severe('Error unfreezing user', e, st);
      throw AppError.database(
        message: 'فشل إلغاء تجميد حساب المستخدم',
        originalError: e,
      );
    }
  }

  /// حذف مستخدم نهائياً
  Future<void> deleteUser({
    required String userId,
    required String reason,
  }) async {
    try {
      _logger.info('Deleting user: $userId');

      final response = await _apiClient.deleteApi<Map<String, dynamic>>(
        '/admin/users/$userId',
        queryParameters: {'reason': reason},
      );

      if (!response.isSuccess) {
        throw Exception(response.message ?? 'Failed to delete user');
      }

      _logger.info('User deleted successfully');
    } catch (e, st) {
      _logger.severe('Error deleting user', e, st);
      throw AppError.database(message: 'فشل حذف المستخدم', originalError: e);
    }
  }

  /// تحديث ملاحظات المشرف على المستخدم
  Future<void> updateAdminNotes({
    required String userId,
    required String notes,
  }) async {
    try {
      _logger.info('Updating admin notes for user: $userId');

      final response = await _apiClient.putApi<Map<String, dynamic>>(
        '/admin/users/$userId/notes',
        data: {'notes': notes},
      );

      if (!response.isSuccess) {
        throw Exception(response.message ?? 'Failed to update notes');
      }

      _logger.info('Admin notes updated successfully');
    } catch (e, st) {
      _logger.severe('Error updating admin notes', e, st);
      throw AppError.database(message: 'فشل تحديث الملاحظات', originalError: e);
    }
  }

  /// جلب سجل إجراءات المستخدم
  Future<List<UserManagementAction>> getUserActions(String userId) async {
    try {
      _logger.info('Fetching user actions for: $userId');

      final response = await _apiClient.getApi<Map<String, dynamic>>(
        '/admin/users/$userId/actions',
      );

      if (!response.isSuccess || response.data == null) {
        throw Exception(response.message ?? 'Failed to fetch actions');
      }

      final actions = response.data!['data'] as List<dynamic>? ?? [];
      return actions
          .map((json) => UserManagementAction.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e, st) {
      _logger.severe('Error fetching user actions', e, st);
      throw AppError.database(
        message: 'فشل جلب سجل الإجراءات',
        originalError: e,
      );
    }
  }
}
