import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/networking/simple_api_client.dart';
import '../models/models.dart';
import '../repositories/admin_user_repository.dart';

part 'admin_user_providers.g.dart';

/// مزود مستودع إدارة المستخدمين
final adminUserRepositoryProvider = Provider<AdminUserRepository>((ref) {
  final apiClient = ref.read(simpleApiClientProvider);
  return AdminUserRepository(apiClient);
});

/// مزود قائمة المستخدمين مع إمكانية البحث والفلترة
@riverpod
class AdminUsersList extends _$AdminUsersList {
  @override
  Future<List<AdminUserModel>> build({
    UserStatus? statusFilter,
    String? searchQuery,
  }) async {
    final repository = ref.watch(adminUserRepositoryProvider);

    return repository.getUsers(
      statusFilter: statusFilter,
      searchQuery: searchQuery,
    );
  }

  /// تحديث قائمة المستخدمين
  Future<void> refresh() async {
    ref.invalidateSelf();
  }

  /// حظر مستخدم
  Future<void> banUser(String userId, String reason) async {
    final repository = ref.read(adminUserRepositoryProvider);

    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      await repository.banUser(userId: userId, reason: reason);
      return repository.getUsers(
        statusFilter: statusFilter,
        searchQuery: searchQuery,
      );
    });
  }

  /// إلغاء حظر مستخدم
  Future<void> unbanUser(String userId, {String? reason}) async {
    final repository = ref.read(adminUserRepositoryProvider);

    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      await repository.unbanUser(userId: userId, reason: reason);
      return repository.getUsers(
        statusFilter: statusFilter,
        searchQuery: searchQuery,
      );
    });
  }

  /// تجميد حساب مستخدم
  Future<void> freezeUser(
    String userId,
    String reason, {
    DateTime? until,
  }) async {
    final repository = ref.read(adminUserRepositoryProvider);

    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      await repository.freezeUser(userId: userId, reason: reason, until: until);
      return repository.getUsers(
        statusFilter: statusFilter,
        searchQuery: searchQuery,
      );
    });
  }

  /// إلغاء تجميد حساب مستخدم
  Future<void> unfreezeUser(String userId, {String? reason}) async {
    final repository = ref.read(adminUserRepositoryProvider);

    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      await repository.unfreezeUser(userId: userId, reason: reason);
      return repository.getUsers(
        statusFilter: statusFilter,
        searchQuery: searchQuery,
      );
    });
  }

  /// حذف مستخدم نهائياً
  Future<void> deleteUser(String userId, String reason) async {
    final repository = ref.read(adminUserRepositoryProvider);

    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      await repository.deleteUser(userId: userId, reason: reason);
      // حذف المستخدم من القائمة المحلية
      final currentUsers = state.value ?? [];
      return currentUsers.where((user) => user.id != userId).toList();
    });
  }

  /// تحديث ملاحظات المشرف
  Future<void> updateAdminNotes(String userId, String notes) async {
    final repository = ref.read(adminUserRepositoryProvider);

    await repository.updateAdminNotes(userId: userId, notes: notes);
    // تحديث القائمة
    await refresh();
  }
}

/// مزود تفاصيل مستخدم محدد
@riverpod
Future<AdminUserModel?> adminUserDetails(Ref ref, String userId) async {
  final repository = ref.watch(adminUserRepositoryProvider);
  final users = await repository.getUsers();

  return users.firstWhere(
    (user) => user.id == userId,
    orElse: () => throw Exception('المستخدم غير موجود'),
  );
}

/// مزود سجل إجراءات المستخدم
@riverpod
Future<List<UserManagementAction>> userManagementActions(
  Ref ref,
  String userId,
) async {
  final repository = ref.watch(adminUserRepositoryProvider);
  return repository.getUserActions(userId);
}

/// مزود إحصائيات المستخدمين
@riverpod
Future<UserStatistics> userStatistics(Ref ref) async {
  final users = await ref.watch(adminUsersListProvider().future);

  final activeCount = users.where((u) => u.status == UserStatus.active).length;
  final bannedCount = users.where((u) => u.status == UserStatus.banned).length;
  final frozenCount = users.where((u) => u.status == UserStatus.frozen).length;
  final sellersCount = users.where((u) => u.isSeller).length;
  final verifiedCount = users.where((u) => u.isEmailVerified).length;

  return UserStatistics(
    totalUsers: users.length,
    activeUsers: activeCount,
    bannedUsers: bannedCount,
    frozenUsers: frozenCount,
    sellers: sellersCount,
    verifiedUsers: verifiedCount,
  );
}

/// إحصائيات المستخدمين
class UserStatistics {
  const UserStatistics({
    required this.totalUsers,
    required this.activeUsers,
    required this.bannedUsers,
    required this.frozenUsers,
    required this.sellers,
    required this.verifiedUsers,
  });

  final int totalUsers;
  final int activeUsers;
  final int bannedUsers;
  final int frozenUsers;
  final int sellers;
  final int verifiedUsers;
}
