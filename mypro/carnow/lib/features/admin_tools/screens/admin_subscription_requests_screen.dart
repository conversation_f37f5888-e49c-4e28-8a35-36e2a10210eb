import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/widgets/error_display.dart';
import '../../../core/widgets/loading_indicator.dart';
import '../../../core/utils/ui_utils.dart';
import '../../seller/models/subscription_request_model.dart';
import '../../seller/providers/admin_subscription_provider.dart';
import '../../seller/models/seller_enums.dart';

final _logger = Logger('AdminSubscriptionRequestsScreen');

/// شاشة إدارة طلبات الاشتراك للبائعين
/// Admin screen for managing seller subscription requests
class AdminSubscriptionRequestsScreen extends ConsumerWidget {
  const AdminSubscriptionRequestsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final subscriptionRequestsAsync = ref.watch(allSellerSubscriptionRequestsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('طلبات الاشتراك'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        elevation: 0,
      ),
      body: subscriptionRequestsAsync.when<Widget>(
        data: (requests) {
          if (requests.isEmpty) {
            return const _EmptyRequestsView();
          }
          return _RequestsListView(requests: requests);
        },
        loading: () => const LoadingIndicator(
          message: 'جاري تحميل طلبات الاشتراك...',
        ),
        error: (error, stack) {
          _logger.severe('Error loading subscription requests', error, stack);
          return ErrorDisplay(
            error: error,
            onRetry: () => ref.refresh(allSellerSubscriptionRequestsProvider),
          );
        },
      ),
    );
  }
}

/// عرض فارغ عندما لا توجد طلبات
class _EmptyRequestsView extends StatelessWidget {
  const _EmptyRequestsView();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد طلبات اشتراك',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر طلبات الاشتراك الجديدة هنا',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }
}

/// قائمة طلبات الاشتراك
class _RequestsListView extends ConsumerWidget {
  const _RequestsListView({required this.requests});

  final List<SellerSubscriptionRequest> requests;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.refresh(allSellerSubscriptionRequestsProvider);
        await Future.delayed(const Duration(milliseconds: 100));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: requests.length,
        itemBuilder: (context, index) {
          final request = requests[index];
          return _RequestCard(
            request: request,
            onStatusUpdate: (status, notes) async {
              try {
                await ref.read(adminSubscriptionProvider.notifier).updateSubscriptionStatus(
                  subscriptionId: request.id,
                  newStatus: status,
                  adminNotes: notes,
                );
                
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم تحديث حالة الطلب بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (error) {
                _logger.severe('Error updating subscription status', error);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('فشل في تحديث حالة الطلب: $error'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
          );
        },
      ),
    );
  }
}

/// بطاقة طلب اشتراك فردية
class _RequestCard extends StatelessWidget {
  const _RequestCard({
    required this.request,
    required this.onStatusUpdate,
  });

  final SellerSubscriptionRequest request;
  final Function(SubscriptionStatus status, String notes) onStatusUpdate;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status badge
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'طلب اشتراك #${request.id.substring(0, 8)}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'تاريخ الطلب: ${UIUtils.formatDate(request.requestDate)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                _StatusBadge(
                  status: SubscriptionStatus.values.firstWhere(
                    (e) => e.name == request.status,
                    orElse: () => SubscriptionStatus.pendingApproval,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Plan details
            _PlanDetails(request: request),
            const SizedBox(height: 16),
            
            // Action buttons (only show for pending requests)
            if (request.status == SubscriptionStatus.pendingApproval.name)
              _ActionButtons(
                request: request,
                onStatusUpdate: onStatusUpdate,
              ),
          ],
        ),
      ),
    );
  }
}

/// تفاصيل الخطة
class _PlanDetails extends StatelessWidget {
  const _PlanDetails({required this.request});

  final SellerSubscriptionRequest request;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.business_center,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'نوع الخطة: ${request.requestedTier}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.payments,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'السعر المطلوب: ${UIUtils.formatPrice(request.requestedPriceLD)}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.schedule,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'دورة الفوترة: ${request.billingCycle}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// شارة الحالة
class _StatusBadge extends StatelessWidget {
  const _StatusBadge({required this.status});

  final SubscriptionStatus status;

  @override
  Widget build(BuildContext context) {
    Color color;
    String text;

    switch (status) {
      case SubscriptionStatus.pendingApproval:
        color = Colors.orange;
        text = 'قيد الانتظار';
        break;
      case SubscriptionStatus.active:
        color = Colors.green;
        text = 'نشط';
        break;
      case SubscriptionStatus.rejected:
        color = Colors.red;
        text = 'مرفوض';
        break;
      case SubscriptionStatus.canceled:
        color = Colors.grey;
        text = 'ملغي';
        break;
      default:
        color = Colors.grey;
        text = 'غير محدد';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

/// أزرار الإجراءات
class _ActionButtons extends StatelessWidget {
  const _ActionButtons({
    required this.request,
    required this.onStatusUpdate,
  });

  final SellerSubscriptionRequest request;
  final Function(SubscriptionStatus status, String notes) onStatusUpdate;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _showApprovalDialog(context),
            icon: const Icon(Icons.check),
            label: const Text('موافقة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _showRejectionDialog(context),
            icon: const Icon(Icons.close),
            label: const Text('رفض'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  void _showApprovalDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => _ApprovalDialog(
        request: request,
        onApprove: (notes) => onStatusUpdate(SubscriptionStatus.active, notes),
      ),
    );
  }

  void _showRejectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => _RejectionDialog(
        request: request,
        onReject: (reason, notes) => onStatusUpdate(SubscriptionStatus.rejected, '$reason: $notes'),
      ),
    );
  }
}

/// حوار الموافقة
class _ApprovalDialog extends StatefulWidget {
  const _ApprovalDialog({
    required this.request,
    required this.onApprove,
  });

  final SellerSubscriptionRequest request;
  final Function(String notes) onApprove;

  @override
  State<_ApprovalDialog> createState() => _ApprovalDialogState();
}

class _ApprovalDialogState extends State<_ApprovalDialog> {
  final TextEditingController _notesController = TextEditingController();

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('موافقة على الطلب'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'هل أنت متأكد من الموافقة على طلب الاشتراك؟',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: 'ملاحظات (اختياري)',
              hintText: 'أضف أي ملاحظات للبائع...',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onApprove(_notesController.text.trim());
            Navigator.of(context).pop();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
          child: const Text('موافقة'),
        ),
      ],
    );
  }
}

/// حوار الرفض
class _RejectionDialog extends StatefulWidget {
  const _RejectionDialog({
    required this.request,
    required this.onReject,
  });

  final SellerSubscriptionRequest request;
  final Function(String reason, String notes) onReject;

  @override
  State<_RejectionDialog> createState() => _RejectionDialogState();
}

class _RejectionDialogState extends State<_RejectionDialog> {
  final TextEditingController _notesController = TextEditingController();
  String _selectedReason = 'معلومات غير مكتملة';

  final List<String> _rejectionReasons = [
    'معلومات غير مكتملة',
    'وثائق غير صحيحة',
    'عدم استيفاء الشروط',
    'مشكلة في الدفع',
    'أخرى',
  ];

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('رفض الطلب'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'سبب الرفض:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: _selectedReason,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            items: _rejectionReasons.map((reason) {
              return DropdownMenuItem(
                value: reason,
                child: Text(reason),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedReason = value;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: 'ملاحظات إضافية',
              hintText: 'أضف تفاصيل سبب الرفض...',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onReject(_selectedReason, _notesController.text.trim());
            Navigator.of(context).pop();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
          child: const Text('رفض'),
        ),
      ],
    );
  }
}
