import 'package:carnow/core/errors/result.dart';
import 'package:carnow/core/repositories/base_repository.dart';
import '../models/vehicle_trim.dart';

class VehicleTrimRepository extends BaseRepository {
  VehicleTrimRepository(super.ref);

  @override
  String get apiPath => '/api/v1/admin/vehicle-trims';

  Future<Result<List<VehicleTrim>>> getTrims({int? modelId}) =>
      getAll<VehicleTrim>(
        fromJson: VehicleTrim.fromJson,
        queryParams: modelId != null 
            ? {
                'model_id': modelId.toString(),
                'sort': 'name',
                'order': 'asc',
              }
            : {
                'sort': 'name',
                'order': 'asc',
              },
      );

  Future<Result<VehicleTrim>> createTrim(VehicleTrim trim) =>
      create<VehicleTrim>(
        trim.toJson()..removeWhere((k, v) => v == null),
        fromJson: VehicleTrim.fromJson,
      );

  Future<Result<VehicleTrim>> updateTrim(String id, VehicleTrim trim) =>
      update<VehicleTrim>(
        id,
        trim.toJson()..removeWhere((k, v) => v == null),
        fromJson: VehicleTrim.fromJson,
      );

  Future<Result<void>> deleteTrim(String id) => delete(id);
}

final vehicleTrimRepositoryProvider =
    RepositoryProvider.create<VehicleTrimRepository>(VehicleTrimRepository.new);


