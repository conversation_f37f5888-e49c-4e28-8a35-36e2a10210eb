import 'package:carnow/core/repositories/base_repository.dart';
import 'package:carnow/core/errors/result.dart';
import '../models/vehicle_make.dart';

class VehicleMakeRepository extends BaseRepository {
  VehicleMakeRepository(super.ref);

  @override
  String get apiPath => '/api/v1/admin/vehicle-makes';

  Future<Result<List<VehicleMake>>> getMakes() => getAll<VehicleMake>(
    fromJson: VehicleMake.fromJson,
    queryParams: {
      'sort': 'name',
      'order': 'asc',
    },
  );

  Future<Result<VehicleMake>> createMake(VehicleMake make) =>
      create<VehicleMake>(
        make.toJson()..removeWhere((k, v) => v == null),
        fromJson: VehicleMake.fromJson,
      );

  Future<Result<VehicleMake>> updateMake(String id, VehicleMake make) =>
      update<VehicleMake>(
        id,
        make.toJson()..removeWhere((k, v) => v == null),
        fromJson: VehicleMake.fromJson,
      );

  Future<Result<void>> deleteMake(String id) => delete(id);

  Future<Result<List<VehicleMake>>> searchMakes(String query) =>
      getAll<VehicleMake>(
        fromJson: VehicleMake.fromJson,
        queryParams: {'search': query},
      );
}

final vehicleMakeRepositoryProvider =
    RepositoryProvider.create<VehicleMakeRepository>(VehicleMakeRepository.new);
