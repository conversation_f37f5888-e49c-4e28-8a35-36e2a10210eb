
import 'package:carnow/core/errors/result.dart';
import 'package:carnow/core/repositories/base_repository.dart';
import '../models/vehicle_model.dart';

class VehicleModelRepository extends BaseRepository {
  VehicleModelRepository(super.ref);

  @override
  String get apiPath => '/api/v1/admin/vehicle-models';

  Future<Result<List<VehicleModel>>> getModels({int? makeId}) =>
      getAll<VehicleModel>(
        fromJson: VehicleModel.fromJson,
        queryParams: makeId != null 
            ? {
                'make_id': makeId.toString(),
                'sort': 'name',
                'order': 'asc',
              }
            : {
                'sort': 'name', 
                'order': 'asc',
              },
      );

  Future<Result<VehicleModel>> createModel(VehicleModel model) =>
      create<VehicleModel>(
        model.toJson()..removeWhere((k, v) => v == null),
        fromJson: VehicleModel.fromJson,
      );

  Future<Result<VehicleModel>> updateModel(String id, VehicleModel model) =>
      update<VehicleModel>(
        id,
        model.toJson()..removeWhere((k, v) => v == null),
        fromJson: VehicleModel.fromJson,
      );

  Future<Result<void>> deleteModel(String id) => delete(id);

  Future<Result<List<VehicleModel>>> searchModels(String query, {int? makeId}) =>
      getAll<VehicleModel>(
        fromJson: VehicleModel.fromJson,
        queryParams: {
          'search': query,
          if (makeId != null) 'make_id': makeId.toString(),
        },
      );
}

final vehicleModelRepositoryProvider =
    RepositoryProvider.create<VehicleModelRepository>(VehicleModelRepository.new);
