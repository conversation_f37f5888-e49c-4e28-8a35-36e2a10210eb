import 'package:carnow/core/errors/result.dart';
import 'package:carnow/core/repositories/base_repository.dart';
import '../models/vehicle_generation.dart';

class VehicleGenerationRepository extends BaseRepository {
  VehicleGenerationRepository(super.ref);

  @override
  String get apiPath => '/api/v1/admin/vehicle-generations';

  Future<Result<List<VehicleGeneration>>> getGenerations({int? modelId}) =>
      getAll<VehicleGeneration>(
        fromJson: VehicleGeneration.fromJson,
        queryParams: modelId != null 
            ? {
                'model_id': modelId.toString(),
                'sort': 'name',
                'order': 'asc',
              }
            : {
                'sort': 'name',
                'order': 'asc',
              },
      );

  Future<Result<VehicleGeneration>> createGeneration(
    VehicleGeneration generation,
  ) => create<VehicleGeneration>(
    generation.toJson()..removeWhere((k, v) => v == null),
    fromJson: VehicleGeneration.fromJson,
  );

  Future<Result<VehicleGeneration>> updateGeneration(
    String id,
    VehicleGeneration generation,
  ) => update<VehicleGeneration>(
    id,
    generation.toJson()..removeWhere((k, v) => v == null),
    fromJson: VehicleGeneration.fromJson,
  );

  Future<Result<void>> deleteGeneration(String id) => delete(id);
}

final vehicleGenerationRepositoryProvider =
    RepositoryProvider.create<VehicleGenerationRepository>(VehicleGenerationRepository.new);


