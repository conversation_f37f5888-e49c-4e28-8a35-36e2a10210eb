import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:carnow/core/networking/simple_api_client.dart';
import 'package:carnow/core/errors/app_error.dart';
import '../models/vehicle_makes_model.dart';

part 'vehicle_providers.g.dart';

/// مقدم ماركات المركبات
/// Vehicle Makes Provider
@riverpod
class VehicleMakes extends _$VehicleMakes {
  @override
  Future<List<VehicleMakeModel>> build() async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      final response = await apiClient.get('/api/v1/admin/vehicle-makes');
      
      final List<dynamic> data = response.data as List<dynamic>;
      return data
          .map((json) => VehicleMakeModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to load vehicle makes: $e',
        originalError: e,
      );
    }
  }

  /// إضافة ماركة جديدة
  /// Add new vehicle make
  Future<void> addMake(VehicleMakeModel make) async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    try {
      await apiClient.post(
        '/api/v1/admin/vehicle-makes',
        data: make.toJson(),
      );
      ref.invalidateSelf();
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to add vehicle make: $e',
        originalError: e,
      );
    }
  }
}

/// مقدم موديلات المركبات
/// Vehicle Models Provider
@riverpod
class VehicleModels extends _$VehicleModels {
  @override
  Future<List<VehicleMakeModel>> build(String makeId) async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      final response = await apiClient.get(
        '/api/v1/admin/vehicle-models',
        queryParameters: {'make_id': makeId},
      );
      
      final List<dynamic> data = response.data as List<dynamic>;
      return data
          .map((json) => VehicleMakeModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to load vehicle models: $e',
        originalError: e,
      );
    }
  }
}

/// مقدم تحديث ماركة المركبة
/// Update Vehicle Make Provider
@riverpod
class UpdateVehicleMake extends _$UpdateVehicleMake {
  @override
  Future<void> build() async {
    // Empty initial state
  }

  /// تحديث ماركة المركبة
  /// Update vehicle make
  Future<void> updateMake(String id, VehicleMakeModel make) async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    try {
      await apiClient.put(
        '/api/v1/admin/vehicle-makes/$id',
        data: make.toJson(),
      );
      
      // Invalidate related providers
      ref.invalidate(vehicleMakesProvider);
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to update vehicle make: $e',
        originalError: e,
      );
    }
  }
}

/// مقدم حذف ماركة المركبة
/// Delete Vehicle Make Provider
@riverpod
class DeleteVehicleMake extends _$DeleteVehicleMake {
  @override
  Future<void> build() async {
    // Empty initial state
  }

  /// حذف ماركة المركبة
  /// Delete vehicle make
  Future<void> deleteMake(String id) async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    try {
      await apiClient.delete('/api/v1/admin/vehicle-makes/$id');
      
      // Invalidate related providers
      ref.invalidate(vehicleMakesProvider);
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to delete vehicle make: $e',
        originalError: e,
      );
    }
  }
}

/// مقدم البحث في ماركات المركبات
/// Search Vehicle Makes Provider
@riverpod
class SearchVehicleMakes extends _$SearchVehicleMakes {
  @override
  Future<List<VehicleMakeModel>> build(String query) async {
    if (query.isEmpty) return [];
    
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      final response = await apiClient.get(
        '/api/v1/admin/vehicle-makes/search',
        queryParameters: {'q': query},
      );
      
      final List<dynamic> data = response.data as List<dynamic>;
      return data
          .map((json) => VehicleMakeModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to search vehicle makes: $e',
        originalError: e,
      );
    }
  }
}

/// مقدم إحصائيات ماركات المركبات
/// Vehicle Makes Statistics Provider
@riverpod
class VehicleMakeStats extends _$VehicleMakeStats {
  @override
  Future<Map<String, dynamic>> build() async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      final response = await apiClient.get('/api/v1/admin/vehicle-makes/stats');
      return response.data as Map<String, dynamic>;
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to load vehicle make statistics: $e',
        originalError: e,
      );
    }
  }
}

/// مقدم المحرك الفعلي للبحث السريع
/// Quick Search Engine Provider  
@riverpod
class QuickSearchVehicles extends _$QuickSearchVehicles {
  @override
  Future<List<Map<String, dynamic>>> build(String searchTerm) async {
    if (searchTerm.isEmpty) return [];
    
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      final response = await apiClient.get(
        '/api/v1/admin/vehicles/quick-search',
        queryParameters: {'term': searchTerm},
      );
      
      final List<dynamic> data = response.data as List<dynamic>;
      return data.cast<Map<String, dynamic>>();
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to perform quick search: $e',
        originalError: e,
      );
    }
  }
}
