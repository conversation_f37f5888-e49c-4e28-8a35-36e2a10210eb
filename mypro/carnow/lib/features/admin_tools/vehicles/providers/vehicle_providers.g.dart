// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$vehicleMakesHash() => r'cdfc7678277006815070f46c1b5b85dd71266fd3';

/// مقدم ماركات المركبات
/// Vehicle Makes Provider
///
/// Copied from [VehicleMakes].
@ProviderFor(VehicleMakes)
final vehicleMakesProvider =
    AutoDisposeAsyncNotifierProvider<
      VehicleMakes,
      List<VehicleMakeModel>
    >.internal(
      VehicleMakes.new,
      name: r'vehicleMakesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$vehicleMakesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$VehicleMakes = AutoDisposeAsyncNotifier<List<VehicleMakeModel>>;
String _$vehicleModelsHash() => r'9bc8ed6eaeb1a40c486854c24145ee0801806871';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$VehicleModels
    extends BuildlessAutoDisposeAsyncNotifier<List<VehicleMakeModel>> {
  late final String makeId;

  FutureOr<List<VehicleMakeModel>> build(String makeId);
}

/// مقدم موديلات المركبات
/// Vehicle Models Provider
///
/// Copied from [VehicleModels].
@ProviderFor(VehicleModels)
const vehicleModelsProvider = VehicleModelsFamily();

/// مقدم موديلات المركبات
/// Vehicle Models Provider
///
/// Copied from [VehicleModels].
class VehicleModelsFamily extends Family<AsyncValue<List<VehicleMakeModel>>> {
  /// مقدم موديلات المركبات
  /// Vehicle Models Provider
  ///
  /// Copied from [VehicleModels].
  const VehicleModelsFamily();

  /// مقدم موديلات المركبات
  /// Vehicle Models Provider
  ///
  /// Copied from [VehicleModels].
  VehicleModelsProvider call(String makeId) {
    return VehicleModelsProvider(makeId);
  }

  @override
  VehicleModelsProvider getProviderOverride(
    covariant VehicleModelsProvider provider,
  ) {
    return call(provider.makeId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehicleModelsProvider';
}

/// مقدم موديلات المركبات
/// Vehicle Models Provider
///
/// Copied from [VehicleModels].
class VehicleModelsProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          VehicleModels,
          List<VehicleMakeModel>
        > {
  /// مقدم موديلات المركبات
  /// Vehicle Models Provider
  ///
  /// Copied from [VehicleModels].
  VehicleModelsProvider(String makeId)
    : this._internal(
        () => VehicleModels()..makeId = makeId,
        from: vehicleModelsProvider,
        name: r'vehicleModelsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$vehicleModelsHash,
        dependencies: VehicleModelsFamily._dependencies,
        allTransitiveDependencies:
            VehicleModelsFamily._allTransitiveDependencies,
        makeId: makeId,
      );

  VehicleModelsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.makeId,
  }) : super.internal();

  final String makeId;

  @override
  FutureOr<List<VehicleMakeModel>> runNotifierBuild(
    covariant VehicleModels notifier,
  ) {
    return notifier.build(makeId);
  }

  @override
  Override overrideWith(VehicleModels Function() create) {
    return ProviderOverride(
      origin: this,
      override: VehicleModelsProvider._internal(
        () => create()..makeId = makeId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        makeId: makeId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<VehicleModels, List<VehicleMakeModel>>
  createElement() {
    return _VehicleModelsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehicleModelsProvider && other.makeId == makeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, makeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehicleModelsRef
    on AutoDisposeAsyncNotifierProviderRef<List<VehicleMakeModel>> {
  /// The parameter `makeId` of this provider.
  String get makeId;
}

class _VehicleModelsProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          VehicleModels,
          List<VehicleMakeModel>
        >
    with VehicleModelsRef {
  _VehicleModelsProviderElement(super.provider);

  @override
  String get makeId => (origin as VehicleModelsProvider).makeId;
}

String _$updateVehicleMakeHash() => r'afb979f587fc23c106de4909b81dc5bd3bfcd403';

/// مقدم تحديث ماركة المركبة
/// Update Vehicle Make Provider
///
/// Copied from [UpdateVehicleMake].
@ProviderFor(UpdateVehicleMake)
final updateVehicleMakeProvider =
    AutoDisposeAsyncNotifierProvider<UpdateVehicleMake, void>.internal(
      UpdateVehicleMake.new,
      name: r'updateVehicleMakeProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$updateVehicleMakeHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$UpdateVehicleMake = AutoDisposeAsyncNotifier<void>;
String _$deleteVehicleMakeHash() => r'd53b9582c82c06f28dc6def5dccb692b50910580';

/// مقدم حذف ماركة المركبة
/// Delete Vehicle Make Provider
///
/// Copied from [DeleteVehicleMake].
@ProviderFor(DeleteVehicleMake)
final deleteVehicleMakeProvider =
    AutoDisposeAsyncNotifierProvider<DeleteVehicleMake, void>.internal(
      DeleteVehicleMake.new,
      name: r'deleteVehicleMakeProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$deleteVehicleMakeHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$DeleteVehicleMake = AutoDisposeAsyncNotifier<void>;
String _$searchVehicleMakesHash() =>
    r'141c5acb34fbd539cd15d9b35709519240714fa7';

abstract class _$SearchVehicleMakes
    extends BuildlessAutoDisposeAsyncNotifier<List<VehicleMakeModel>> {
  late final String query;

  FutureOr<List<VehicleMakeModel>> build(String query);
}

/// مقدم البحث في ماركات المركبات
/// Search Vehicle Makes Provider
///
/// Copied from [SearchVehicleMakes].
@ProviderFor(SearchVehicleMakes)
const searchVehicleMakesProvider = SearchVehicleMakesFamily();

/// مقدم البحث في ماركات المركبات
/// Search Vehicle Makes Provider
///
/// Copied from [SearchVehicleMakes].
class SearchVehicleMakesFamily
    extends Family<AsyncValue<List<VehicleMakeModel>>> {
  /// مقدم البحث في ماركات المركبات
  /// Search Vehicle Makes Provider
  ///
  /// Copied from [SearchVehicleMakes].
  const SearchVehicleMakesFamily();

  /// مقدم البحث في ماركات المركبات
  /// Search Vehicle Makes Provider
  ///
  /// Copied from [SearchVehicleMakes].
  SearchVehicleMakesProvider call(String query) {
    return SearchVehicleMakesProvider(query);
  }

  @override
  SearchVehicleMakesProvider getProviderOverride(
    covariant SearchVehicleMakesProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'searchVehicleMakesProvider';
}

/// مقدم البحث في ماركات المركبات
/// Search Vehicle Makes Provider
///
/// Copied from [SearchVehicleMakes].
class SearchVehicleMakesProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          SearchVehicleMakes,
          List<VehicleMakeModel>
        > {
  /// مقدم البحث في ماركات المركبات
  /// Search Vehicle Makes Provider
  ///
  /// Copied from [SearchVehicleMakes].
  SearchVehicleMakesProvider(String query)
    : this._internal(
        () => SearchVehicleMakes()..query = query,
        from: searchVehicleMakesProvider,
        name: r'searchVehicleMakesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$searchVehicleMakesHash,
        dependencies: SearchVehicleMakesFamily._dependencies,
        allTransitiveDependencies:
            SearchVehicleMakesFamily._allTransitiveDependencies,
        query: query,
      );

  SearchVehicleMakesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  FutureOr<List<VehicleMakeModel>> runNotifierBuild(
    covariant SearchVehicleMakes notifier,
  ) {
    return notifier.build(query);
  }

  @override
  Override overrideWith(SearchVehicleMakes Function() create) {
    return ProviderOverride(
      origin: this,
      override: SearchVehicleMakesProvider._internal(
        () => create()..query = query,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    SearchVehicleMakes,
    List<VehicleMakeModel>
  >
  createElement() {
    return _SearchVehicleMakesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SearchVehicleMakesProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SearchVehicleMakesRef
    on AutoDisposeAsyncNotifierProviderRef<List<VehicleMakeModel>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _SearchVehicleMakesProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          SearchVehicleMakes,
          List<VehicleMakeModel>
        >
    with SearchVehicleMakesRef {
  _SearchVehicleMakesProviderElement(super.provider);

  @override
  String get query => (origin as SearchVehicleMakesProvider).query;
}

String _$vehicleMakeStatsHash() => r'0ae1fa2be5eaa1678e3a54ec59ac9f7158a4860c';

/// مقدم إحصائيات ماركات المركبات
/// Vehicle Makes Statistics Provider
///
/// Copied from [VehicleMakeStats].
@ProviderFor(VehicleMakeStats)
final vehicleMakeStatsProvider =
    AutoDisposeAsyncNotifierProvider<
      VehicleMakeStats,
      Map<String, dynamic>
    >.internal(
      VehicleMakeStats.new,
      name: r'vehicleMakeStatsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$vehicleMakeStatsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$VehicleMakeStats = AutoDisposeAsyncNotifier<Map<String, dynamic>>;
String _$quickSearchVehiclesHash() =>
    r'f4bef4a656d1cb0a418e907c6b664a2fb99c7eae';

abstract class _$QuickSearchVehicles
    extends BuildlessAutoDisposeAsyncNotifier<List<Map<String, dynamic>>> {
  late final String searchTerm;

  FutureOr<List<Map<String, dynamic>>> build(String searchTerm);
}

/// مقدم المحرك الفعلي للبحث السريع
/// Quick Search Engine Provider
///
/// Copied from [QuickSearchVehicles].
@ProviderFor(QuickSearchVehicles)
const quickSearchVehiclesProvider = QuickSearchVehiclesFamily();

/// مقدم المحرك الفعلي للبحث السريع
/// Quick Search Engine Provider
///
/// Copied from [QuickSearchVehicles].
class QuickSearchVehiclesFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// مقدم المحرك الفعلي للبحث السريع
  /// Quick Search Engine Provider
  ///
  /// Copied from [QuickSearchVehicles].
  const QuickSearchVehiclesFamily();

  /// مقدم المحرك الفعلي للبحث السريع
  /// Quick Search Engine Provider
  ///
  /// Copied from [QuickSearchVehicles].
  QuickSearchVehiclesProvider call(String searchTerm) {
    return QuickSearchVehiclesProvider(searchTerm);
  }

  @override
  QuickSearchVehiclesProvider getProviderOverride(
    covariant QuickSearchVehiclesProvider provider,
  ) {
    return call(provider.searchTerm);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'quickSearchVehiclesProvider';
}

/// مقدم المحرك الفعلي للبحث السريع
/// Quick Search Engine Provider
///
/// Copied from [QuickSearchVehicles].
class QuickSearchVehiclesProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          QuickSearchVehicles,
          List<Map<String, dynamic>>
        > {
  /// مقدم المحرك الفعلي للبحث السريع
  /// Quick Search Engine Provider
  ///
  /// Copied from [QuickSearchVehicles].
  QuickSearchVehiclesProvider(String searchTerm)
    : this._internal(
        () => QuickSearchVehicles()..searchTerm = searchTerm,
        from: quickSearchVehiclesProvider,
        name: r'quickSearchVehiclesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$quickSearchVehiclesHash,
        dependencies: QuickSearchVehiclesFamily._dependencies,
        allTransitiveDependencies:
            QuickSearchVehiclesFamily._allTransitiveDependencies,
        searchTerm: searchTerm,
      );

  QuickSearchVehiclesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.searchTerm,
  }) : super.internal();

  final String searchTerm;

  @override
  FutureOr<List<Map<String, dynamic>>> runNotifierBuild(
    covariant QuickSearchVehicles notifier,
  ) {
    return notifier.build(searchTerm);
  }

  @override
  Override overrideWith(QuickSearchVehicles Function() create) {
    return ProviderOverride(
      origin: this,
      override: QuickSearchVehiclesProvider._internal(
        () => create()..searchTerm = searchTerm,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        searchTerm: searchTerm,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    QuickSearchVehicles,
    List<Map<String, dynamic>>
  >
  createElement() {
    return _QuickSearchVehiclesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is QuickSearchVehiclesProvider &&
        other.searchTerm == searchTerm;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, searchTerm.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin QuickSearchVehiclesRef
    on AutoDisposeAsyncNotifierProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `searchTerm` of this provider.
  String get searchTerm;
}

class _QuickSearchVehiclesProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          QuickSearchVehicles,
          List<Map<String, dynamic>>
        >
    with QuickSearchVehiclesRef {
  _QuickSearchVehiclesProviderElement(super.provider);

  @override
  String get searchTerm => (origin as QuickSearchVehiclesProvider).searchTerm;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
