import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:dio/dio.dart';

import '../models/admin_financial_models.dart';
import '../../../core/providers/dio_provider.dart';
import '../../../core/errors/app_error.dart';
import '../../../core/config/backend_config.dart';

part 'admin_financial_providers.g.dart';

/// ================================================
/// ADMIN FINANCIAL SERVICE (SIMPLE)
/// ================================================

@riverpod
AdminFinancialService adminFinancialService(Ref ref) {
  final dio = ref.watch(dioProvider);
  return AdminFinancialService(dio);
}

class AdminFinancialService {
  AdminFinancialService(this._dio);

  final Dio _dio;

  String get _baseUrl => CarnowBackendConfig.apiBaseUrlSync;

  /// ================================================
  /// DASHBOARD METHODS
  /// ================================================

  Future<AdminDashboard> getDashboard() async {
    try {
      final response = await _dio.get('$_baseUrl/api/v1/admin/financial/dashboard');
      return AdminDashboard.fromJson(response.data['data']);
    } catch (e) {
      throw AppError.network(
        message: 'فشل تحميل لوحة التحكم',
        code: 'DASHBOARD_LOAD_FAILED',
        originalError: e,
      );
    }
  }

  /// ================================================
  /// FINANCIAL ALERTS METHODS
  /// ================================================

  Future<List<FinancialAlert>> getFinancialAlerts() async {
    try {
      final response = await _dio.get('$_baseUrl/api/v1/admin/financial/alerts');
      final data = response.data as Map<String, dynamic>;
      final alertsData = data['data'] as List;
      return alertsData
          .map((json) => FinancialAlert.fromJson(json))
          .toList();
    } catch (e) {
      throw AppError.network(
        message: 'فشل في تحميل التنبيهات المالية',
        code: 'FINANCIAL_ALERTS_FAILED',
        originalError: e,
      );
    }
  }

  /// ================================================
  /// ADMIN LOGS METHODS
  /// ================================================

  Future<List<AdminActionLog>> getAdminActionLogs() async {
    try {
      final response = await _dio.get('$_baseUrl/api/v1/admin/system/logs');
      final data = response.data as Map<String, dynamic>;
      final logsData = data['data'] as List;
      return logsData
          .map((json) => AdminActionLog.fromJson(json))
          .toList();
    } catch (e) {
      throw AppError.network(
        message: 'فشل في تحميل سجل الأنشطة الإدارية',
        code: 'ADMIN_LOGS_FAILED',
        originalError: e,
      );
    }
  }

  /// ================================================
  /// WALLET MANAGEMENT METHODS
  /// ================================================

  Future<WalletAdjustmentResponse> adjustWalletBalance(
    WalletAdjustmentRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/api/v1/admin/wallets/${request.walletId}/adjust',
        data: request.toJson(),
      );
      return WalletAdjustmentResponse.fromJson(response.data['data']);
    } catch (e) {
      throw AppError.network(
        message: 'فشل تعديل رصيد المحفظة',
        code: 'WALLET_ADJUSTMENT_FAILED',
        originalError: e,
      );
    }
  }

  Future<WalletOverview> getWalletOverview(String walletId) async {
    try {
      final response = await _dio.get('$_baseUrl/api/v1/admin/wallets/$walletId');
      return WalletOverview.fromJson(response.data['data']);
    } catch (e) {
      throw AppError.network(
        message: 'فشل تحميل تفاصيل المحفظة',
        code: 'WALLET_OVERVIEW_FAILED',
        originalError: e,
      );
    }
  }

  Future<List<WalletListItem>> getWallets({
    String? searchTerm,
    int limit = 100,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'limit': limit,
        if (searchTerm != null && searchTerm.trim().isNotEmpty)
          'query': searchTerm,
      };

      final response = await _dio.get(
        '$_baseUrl/api/v1/admin/financial/wallets',
        queryParameters: queryParams,
      );

      final data = response.data['data'] as List<dynamic>? ?? [];
      return data
          .map((item) => WalletListItem.fromJson(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw AppError.network(
        message: 'فشل تحميل قائمة المحافظ',
        code: 'WALLETS_LIST_FAILED',
        originalError: e,
      );
    }
  }

  /// ================================================
  /// ALERT MANAGEMENT METHODS
  /// ================================================

  Future<List<FinancialAlert>> getAlerts({
    AlertSeverity? severity,
    bool? isResolved,
    int? limit,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        if (severity != null) 'severity': severity.name,
        if (isResolved != null) 'is_resolved': isResolved,
        if (limit != null) 'limit': limit,
      };

      final response = await _dio.get(
        '$_baseUrl/api/v1/admin/financial/alerts',
        queryParameters: queryParams,
      );

      final data = response.data['data'] as List<dynamic>? ?? [];
      return data
          .map((item) => FinancialAlert.fromJson(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw AppError.network(
        message: 'فشل تحميل التنبيهات المالية',
        code: 'ALERTS_LOAD_FAILED',
        originalError: e,
      );
    }
  }

  Future<FinancialAlert> createAlert(CreateAlertRequest request) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/api/v1/admin/alerts',
        data: request.toJson(),
      );
      return FinancialAlert.fromJson(response.data['data']);
    } catch (e) {
      throw AppError.network(
        message: 'فشل إنشاء التنبيه المالي',
        code: 'ALERT_CREATE_FAILED',
        originalError: e,
      );
    }
  }

  Future<FinancialAlert> resolveAlert(
    String alertId,
    ResolveAlertRequest request,
  ) async {
    try {
      final response = await _dio.put(
        '$_baseUrl/api/v1/admin/financial/alerts/$alertId',
        data: request.toJson(),
      );
      return FinancialAlert.fromJson(response.data['data']);
    } catch (e) {
      throw AppError.network(
        message: 'فشل حل التنبيه المالي',
        code: 'ALERT_RESOLVE_FAILED',
        originalError: e,
      );
    }
  }

  /// ================================================
  /// TRANSACTION METHODS
  /// ================================================

  Future<Map<String, dynamic>> reverseTransaction(String transactionId) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/api/v1/admin/transactions/$transactionId/reverse',
      );
      return response.data['data'] as Map<String, dynamic>;
    } catch (e) {
      throw AppError.network(
        message: 'فشل عكس المعاملة',
        code: 'TRANSACTION_REVERSE_FAILED',
        originalError: e,
      );
    }
  }

  Future<TransactionAnalysis> analyzeTransaction(String transactionId) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/api/v1/admin/transactions/$transactionId/analyze',
      );
      return TransactionAnalysis.fromJson(response.data['data']);
    } catch (e) {
      throw AppError.network(
        message: 'فشل تحليل المعاملة',
        code: 'TRANSACTION_ANALYSIS_FAILED',
        originalError: e,
      );
    }
  }

  /// ================================================
  /// AUDIT LOG METHODS
  /// ================================================

  Future<List<AdminActionLog>> getAuditLogs({
    String? adminUserId,
    AdminActionType? actionType,
    DateTime? startDate,
    DateTime? endDate,
    int? page,
    int? limit,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        if (adminUserId != null) 'admin_user_id': adminUserId,
        if (actionType != null) 'action_type': actionType.name,
        if (startDate != null) 'start_date': startDate.toIso8601String(),
        if (endDate != null) 'end_date': endDate.toIso8601String(),
        if (page != null) 'page': page,
        if (limit != null) 'limit': limit,
      };

      final response = await _dio.get(
        '$_baseUrl/api/v1/admin/audit-logs',
        queryParameters: queryParams,
      );

      final data = response.data['data'] as List<dynamic>? ?? [];
      return data
          .map((item) => AdminActionLog.fromJson(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw AppError.network(
        message: 'فشل تحميل سجلات التدقيق',
        code: 'AUDIT_LOGS_FAILED',
        originalError: e,
      );
    }
  }

  /// ================================================
  /// REPORT METHODS
  /// ================================================

  Future<List<AdminReport>> getReports({
    ReportType? reportType,
    DateTime? startDate,
    DateTime? endDate,
    int? page,
    int? limit,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        if (reportType != null) 'report_type': reportType.name,
        if (startDate != null) 'start_date': startDate.toIso8601String(),
        if (endDate != null) 'end_date': endDate.toIso8601String(),
        if (page != null) 'page': page,
        if (limit != null) 'limit': limit,
      };

      final response = await _dio.get(
        '$_baseUrl/api/v1/admin/financial/reports',
        queryParameters: queryParams,
      );

      final data = response.data['data'] as List<dynamic>? ?? [];
      return data
          .map((item) => AdminReport.fromJson(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw AppError.network(
        message: 'فشل تحميل التقارير',
        code: 'REPORTS_LOAD_FAILED',
        originalError: e,
      );
    }
  }

  Future<AdminReport> generateReport(GenerateReportRequest request) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/api/v1/admin/reports/generate',
        data: request.toJson(),
      );
      return AdminReport.fromJson(response.data['data']);
    } catch (e) {
      throw AppError.network(
        message: 'فشل إنشاء التقرير',
        code: 'REPORT_GENERATE_FAILED',
        originalError: e,
      );
    }
  }

  /// ================================================
  /// HEALTH CHECK METHOD
  /// ================================================

  Future<Map<String, dynamic>> healthCheck() async {
    try {
      final response = await _dio.get('$_baseUrl/api/v1/admin/financial/health');
      return response.data['data'] as Map<String, dynamic>;
    } catch (e) {
      throw AppError.network(
        message: 'فشل فحص حالة النظام',
        code: 'HEALTH_CHECK_FAILED',
        originalError: e,
      );
    }
  }
}

/// ================================================
/// PROVIDERS (SIMPLE)
/// ================================================

@riverpod
Future<AdminDashboard> adminDashboard(Ref ref) async {
  final service = ref.watch(adminFinancialServiceProvider);
  return service.getDashboard();
}

@riverpod
class WalletAdjustmentNotifier extends _$WalletAdjustmentNotifier {
  @override
  FutureOr<WalletAdjustmentResponse?> build() => null;

  Future<void> adjustWalletBalance(WalletAdjustmentRequest request) async {
    state = const AsyncLoading();
    try {
      final service = ref.read(adminFinancialServiceProvider);
      final result = await service.adjustWalletBalance(request);
      state = AsyncData(result);
    } catch (e, st) {
      state = AsyncError(e, st);
      rethrow;
    }
  }
}

@riverpod
Future<WalletOverview> walletOverview(Ref ref, String walletId) async {
  final service = ref.watch(adminFinancialServiceProvider);
  return service.getWalletOverview(walletId);
}

@riverpod
class AlertsNotifier extends _$AlertsNotifier {
  @override
  FutureOr<List<FinancialAlert>> build() async {
    final service = ref.read(adminFinancialServiceProvider);
    return service.getAlerts();
  }

  Future<void> createAlert(CreateAlertRequest request) async {
    state = const AsyncLoading();
    try {
      final service = ref.read(adminFinancialServiceProvider);
      final newAlert = await service.createAlert(request);
      state = AsyncData([newAlert, ...state.value ?? []]);
    } catch (e, st) {
      state = AsyncError(e, st);
      rethrow;
    }
  }

  Future<void> resolveAlert(String alertId, ResolveAlertRequest request) async {
    state = const AsyncLoading();
    try {
      final service = ref.read(adminFinancialServiceProvider);
      final resolvedAlert = await service.resolveAlert(alertId, request);

      final currentAlerts = state.value ?? [];
      final updatedAlerts = currentAlerts.map((alert) {
        return alert.id == alertId ? resolvedAlert : alert;
      }).toList();

      state = AsyncData(updatedAlerts);
    } catch (e, st) {
      state = AsyncError(e, st);
      rethrow;
    }
  }

  Future<void> refresh() async {
    state = const AsyncLoading();
    try {
      final service = ref.read(adminFinancialServiceProvider);
      final alerts = await service.getAlerts();
      state = AsyncData(alerts);
    } catch (e, st) {
      state = AsyncError(e, st);
      rethrow;
    }
  }
}

@riverpod
class TransactionAnalysisNotifier extends _$TransactionAnalysisNotifier {
  @override
  FutureOr<TransactionAnalysis?> build() => null;

  Future<void> analyzeTransaction(String transactionId) async {
    state = const AsyncLoading();
    try {
      final service = ref.read(adminFinancialServiceProvider);
      final analysis = await service.analyzeTransaction(transactionId);
      state = AsyncData(analysis);
    } catch (e, st) {
      state = AsyncError(e, st);
      rethrow;
    }
  }

  Future<void> reverseTransaction(String transactionId) async {
    state = const AsyncLoading();
    try {
      final service = ref.read(adminFinancialServiceProvider);
      await service.reverseTransaction(transactionId);
      await analyzeTransaction(transactionId);
    } catch (e, st) {
      state = AsyncError(e, st);
      rethrow;
    }
  }
}

@riverpod
Future<List<AdminActionLog>> auditLogs(
  Ref ref, {
  String? adminUserId,
  AdminActionType? actionType,
  DateTime? startDate,
  DateTime? endDate,
  int? page,
  int? limit,
}) async {
  final service = ref.watch(adminFinancialServiceProvider);
  return service.getAuditLogs(
    adminUserId: adminUserId,
    actionType: actionType,
    startDate: startDate,
    endDate: endDate,
    page: page,
    limit: limit,
  );
}

@riverpod
Future<List<AdminReport>> adminReports(
  Ref ref, {
  ReportType? reportType,
  DateTime? startDate,
  DateTime? endDate,
  int? page,
  int? limit,
}) async {
  final service = ref.watch(adminFinancialServiceProvider);
  return service.getReports(
    reportType: reportType,
    startDate: startDate,
    endDate: endDate,
    page: page,
    limit: limit,
  );
}

@riverpod
class ReportGeneratorNotifier extends _$ReportGeneratorNotifier {
  @override
  FutureOr<AdminReport?> build() => null;

  Future<void> generateReport(GenerateReportRequest request) async {
    state = const AsyncLoading();
    try {
      final service = ref.read(adminFinancialServiceProvider);
      final report = await service.generateReport(request);
      state = AsyncData(report);
    } catch (e, st) {
      state = AsyncError(e, st);
      rethrow;
    }
  }
}

@riverpod
Future<List<WalletListItem>> adminWallets(Ref ref, String searchTerm) async {
  final service = ref.watch(adminFinancialServiceProvider);
  return service.getWallets(searchTerm: searchTerm);
}

/// ================================================
/// SIMPLE PROVIDERS FOR ALERTS AND LOGS
/// ================================================

@riverpod
Future<List<FinancialAlert>> financialAlerts(Ref ref) async {
  final service = ref.watch(adminFinancialServiceProvider);
  return service.getFinancialAlerts();
}

@riverpod
Future<List<AdminActionLog>> adminActionLogs(Ref ref) async {
  final service = ref.watch(adminFinancialServiceProvider);
  return service.getAdminActionLogs();
}
