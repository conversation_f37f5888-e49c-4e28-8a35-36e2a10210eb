import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/warranty.dart';
import '../../../core/networking/simple_api_client.dart';
import '../../../core/utils/unified_logger.dart';

part 'warranty_repository.g.dart';

@riverpod
WarrantyRepository warrantyRepository(Ref ref) {
  final apiClient = ref.read(simpleApiClientProvider);
  return WarrantyRepository(apiClient);
}

class WarrantyRepository {
  const WarrantyRepository(this._apiClient);
  final SimpleApiClient _apiClient;

  /// إنشاء ضمان جديد
  Future<Warranty?> createWarranty(Warranty warranty) async {
    try {
      UnifiedLogger.info(
        'Creating warranty in database: ${warranty.id}',
        tag: 'WarrantyRepository',
      );

      final response = await _apiClient.post(
        '/warranties',
        data: warranty.toJson(),
      );

      return Warranty.fromJson(response.data);
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to create warranty in database',
        tag: 'WarrantyRepository',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// إضافة مطالبة ضمان
  Future<WarrantyClaim?> submitClaim(WarrantyClaim claim) async {
    try {
      UnifiedLogger.info(
        'Submitting claim to database: ${claim.id}',
        tag: 'WarrantyRepository',
      );

      final response = await _apiClient.post(
        '/warranty-claims',
        data: claim.toJson(),
      );

      return WarrantyClaim.fromJson(response.data);
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to submit claim to database',
        tag: 'WarrantyRepository',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// الحصول على ضمانات المستخدم
  Future<List<Warranty>> getUserWarranties({String? userId}) async {
    try {
      if (userId == null) {
        UnifiedLogger.warning(
          'No user ID provided for getting warranties',
          tag: 'WarrantyRepository',
        );
        return [];
      }

      UnifiedLogger.info(
        'Fetching warranties for user: $userId',
        tag: 'WarrantyRepository',
      );

      final response = await _apiClient.get('/warranties/user/$userId');

      return (response.data as List)
          .map<Warranty>((json) => Warranty.fromJson(json))
          .toList();
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to fetch user warranties',
        tag: 'WarrantyRepository',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// الحصول على ضمانات المنتج
  Future<List<Warranty>> getProductWarranties(String productId) async {
    try {
      UnifiedLogger.info(
        'Fetching warranties for product: $productId',
        tag: 'WarrantyRepository',
      );

      final response = await _apiClient.get('/warranties/product/$productId');

      return (response.data as List)
          .map<Warranty>((json) => Warranty.fromJson(json))
          .toList();
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to fetch product warranties',
        tag: 'WarrantyRepository',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// تحديث حالة الضمان
  Future<bool> updateWarrantyStatus(
    String warrantyId,
    WarrantyStatus status,
  ) async {
    try {
      UnifiedLogger.info(
        'Updating warranty status: $warrantyId',
        tag: 'WarrantyRepository',
      );

      await _apiClient.put(
        '/warranties/$warrantyId',
        data: {
          'status': status.name,
          'updated_at': DateTime.now().toIso8601String(),
        },
      );

      return true;
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to update warranty status',
        tag: 'WarrantyRepository',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// تحديث حالة المطالبة
  Future<bool> updateClaimStatus(
    String claimId,
    WarrantyClaimStatus status, {
    String? resolution,
  }) async {
    try {
      UnifiedLogger.info(
        'Updating claim status: $claimId',
        tag: 'WarrantyRepository',
      );

      final updateData = {
        'status': status.name,
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (resolution != null) {
        updateData['resolution'] = resolution;
        updateData['resolved_at'] = DateTime.now().toIso8601String();
      }

      await _apiClient.put('/warranty-claims/$claimId', data: updateData);

      return true;
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to update claim status',
        tag: 'WarrantyRepository',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// الحصول على ضمان واحد بالتفصيل
  Future<Warranty?> getWarrantyById(String warrantyId) async {
    try {
      UnifiedLogger.info(
        'Fetching warranty by ID: $warrantyId',
        tag: 'WarrantyRepository',
      );

      final response = await _apiClient.get('/warranties/$warrantyId');

      return Warranty.fromJson(response.data);
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to fetch warranty by ID',
        tag: 'WarrantyRepository',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// البحث في الضمانات
  Future<List<Warranty>> searchWarranties({
    String? query,
    WarrantyStatus? status,
    WarrantyType? type,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      UnifiedLogger.info(
        'Searching warranties with filters',
        tag: 'WarrantyRepository',
      );

      final queryParams = <String, dynamic>{};

      if (query != null && query.isNotEmpty) {
        queryParams['query'] = query;
      }

      if (status != null) {
        queryParams['status'] = status.name;
      }

      if (type != null) {
        queryParams['type'] = type.name;
      }

      if (startDate != null) {
        queryParams['startDate'] = startDate.toIso8601String();
      }

      if (endDate != null) {
        queryParams['endDate'] = endDate.toIso8601String();
      }

      final response = await _apiClient.get(
        '/warranties/search',
        queryParameters: queryParams,
      );

      return (response.data as List)
          .map<Warranty>((json) => Warranty.fromJson(json))
          .toList();
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to search warranties',
        tag: 'WarrantyRepository',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }
}
