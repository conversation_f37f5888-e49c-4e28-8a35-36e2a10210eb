import 'package:flutter/material.dart';
import '../models/cart_item_model.dart';

/// ويدجت عنصر سلة التسوق
class CartItemWidget extends StatelessWidget {
  const CartItemWidget({
    required this.item,
    super.key,
    this.onRemove,
    this.onQuantityChanged,
  });
  final CartItemModel item;
  final VoidCallback? onRemove;
  final ValueChanged<int>? onQuantityChanged;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // Check if product data is valid
    if (item.productName.isEmpty) {
      return Card(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            'Product information is unavailable.',
            style: TextStyle(color: theme.colorScheme.error),
          ),
        ),
      );
    }

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Placeholder for part image
            SizedBox(
              width: 80,
              height: 80,
              child: DecoratedBox(
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.settings_input_component_outlined,
                  size: 40,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.productName,
                    style: theme.textTheme.titleMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  if (item.description != null && item.description!.isNotEmpty)
                    Text(
                      item.description!,
                      style: theme.textTheme.bodySmall,
                    ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.remove),
                        onPressed: item.quantity > 1
                            ? () => onQuantityChanged?.call(item.quantity - 1)
                            : null,
                      ),
                      Text(
                        item.quantity.toString(),
                        style: theme.textTheme.bodyLarge,
                      ),
                      IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: onQuantityChanged != null
                            ? () => onQuantityChanged!(item.quantity + 1)
                            : null,
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(
                          Icons.delete_outline,
                          color: Colors.red,
                        ),
                        onPressed: onRemove,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
