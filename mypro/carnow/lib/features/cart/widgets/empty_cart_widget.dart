import 'package:flutter/material.dart';

import '../../../core/theme/carnow_colors.dart';

/// Empty Cart Widget following Forever Plan architecture
/// 
/// Displays when cart is empty with call-to-action
/// Material 3 design system with proper accessibility
class EmptyCartWidget extends StatelessWidget {
  const EmptyCartWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Empty cart illustration
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: CarnowColors.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.shopping_cart_outlined,
                size: 64,
                color: CarnowColors.primary.withValues(alpha: 0.6),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Title
            Text(
              'العربة فارغة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 12),
            
            // Description
            Text(
              'لم تقم بإضافة أي منتجات إلى عربة التسوق بعد.\nابدأ بتصفح المنتجات وأضف ما يعجبك!',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 32),
            
            // Call to action button
            ElevatedButton.icon(
              onPressed: () => _navigateToProducts(context),
              icon: const Icon(Icons.shopping_bag_outlined),
              label: const Text('تصفح المنتجات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: CarnowColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Secondary action
            TextButton.icon(
              onPressed: () => _navigateToCategories(context),
              icon: const Icon(Icons.category_outlined),
              label: const Text('تصفح الفئات'),
              style: TextButton.styleFrom(
                foregroundColor: CarnowColors.primary,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToProducts(BuildContext context) {
    // TODO: Navigate to products screen
    Navigator.of(context).pushReplacementNamed('/products');
  }

  void _navigateToCategories(BuildContext context) {
    // TODO: Navigate to categories screen
    Navigator.of(context).pushReplacementNamed('/categories');
  }
}
