import 'package:flutter/material.dart';

import '../../../core/theme/carnow_colors.dart';
import '../models/cart_model.dart';

/// Cart Summary Card following Forever Plan architecture
/// 
/// Displays cart totals, tax breakdown, and item count
/// with Material 3 design system and proper accessibility
class CartSummaryCard extends StatelessWidget {
  const CartSummaryCard({
    super.key,
    required this.cart,
  });

  final CartModel cart;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  color: CarnowColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'ملخص الطلب',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: CarnowColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Item count
            _buildSummaryRow(
              context,
              label: 'عدد العناصر',
              value: '${cart.itemCount} عنصر',
              isSubtotal: false,
            ),
            
            const SizedBox(height: 8),
            
            // Subtotal
            _buildSummaryRow(
              context,
              label: 'المجموع الفرعي',
              value: cart.formattedSubtotal,
              isSubtotal: false,
            ),
            
            const SizedBox(height: 8),
            
            // Tax
            _buildSummaryRow(
              context,
              label: 'الضريبة (10%)',
              value: cart.formattedTax,
              isSubtotal: false,
            ),
            
            const Divider(height: 24),
            
            // Total
            _buildSummaryRow(
              context,
              label: 'المجموع الكلي',
              value: cart.formattedTotal,
              isSubtotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context, {
    required String label,
    required String value,
    required bool isSubtotal,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: isSubtotal
              ? Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                )
              : Theme.of(context).textTheme.bodyLarge,
        ),
        Text(
          value,
          style: isSubtotal
              ? Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: CarnowColors.primary,
                )
              : Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
        ),
      ],
    );
  }
}
