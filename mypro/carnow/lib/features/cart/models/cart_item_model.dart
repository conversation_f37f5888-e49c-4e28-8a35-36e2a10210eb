import 'package:freezed_annotation/freezed_annotation.dart';

part 'cart_item_model.freezed.dart';
part 'cart_item_model.g.dart';

@freezed
abstract class CartItemModel with _$CartItemModel {
  const factory CartItemModel({
    required String id,
    required String productId,
    required String productName,
    required double price,
    required int quantity,
    String? sellerId,
    String? productImage,
    String? description,
    String? cartId,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _CartItemModel;

  factory CartItemModel.fromJson(Map<String, dynamic> json) =>
      _$CartItemModelFromJson(json);
}

extension CartItemModelExtension on CartItemModel {
  double get total => price * quantity;
}