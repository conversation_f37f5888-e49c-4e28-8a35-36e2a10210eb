import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:carnow/features/products/models/product_model.dart';
import 'cart_item_model.dart';

part 'cart_model.freezed.dart';
part 'cart_model.g.dart';

/// Enhanced Cart Model following Forever Plan architecture
/// Represents a complete shopping cart with items, totals, and metadata
/// Focus on Products only (Amazon/eBay style)
@freezed
abstract class CartModel with _$CartModel {
  const factory CartModel({
    required String id,
    required String userId,
    required List<CartItemModel> items,
    required double subtotal,
    required double tax,
    required double total,
    required int itemCount,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? expiresAt,
  }) = _CartModel;

  factory CartModel.fromJson(Map<String, dynamic> json) =>
      _$CartModelFromJson(json);
}


/// Cart Summary for quick access and display
@freezed
abstract class CartSummaryModel with _$CartSummaryModel {
  const factory CartSummaryModel({
    required int itemCount,
    required double total,
    required bool isEmpty,
    required bool isLoading,
  }) = _CartSummaryModel;
}

/// Complete Cart Item Model combining cart item with product details
/// Used for display purposes in UI
@freezed
abstract class CompleteCartItemModel with _$CompleteCartItemModel {
  const factory CompleteCartItemModel({
    required CartItemModel item,
    required ProductModel product,
  }) = _CompleteCartItemModel;
}

/// Extensions for cart calculations and utilities
extension CartModelX on CartModel {
  /// Check if cart is empty
  bool get isEmpty => items.isEmpty;

  /// Check if cart is not empty
  bool get isNotEmpty => items.isNotEmpty;

  /// Get total quantity of all items
  int get totalQuantity => items.fold(0, (sum, item) => sum + item.quantity);

  /// Get cart summary
  CartSummaryModel get summary => CartSummaryModel(
    itemCount: itemCount,
    total: total,
    isEmpty: isEmpty,
    isLoading: false,
  );

  /// Check if cart is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Get formatted total price
  String get formattedTotal => '${total.toStringAsFixed(3)} د.ل';

  /// Get formatted subtotal
  String get formattedSubtotal => '${subtotal.toStringAsFixed(3)} د.ل';

  /// Get formatted tax
  String get formattedTax => '${tax.toStringAsFixed(3)} د.ل';
}

extension CartItemModelX on CartItemModel {
  /// Get total price for this item (price * quantity)
  double get itemTotal => price * quantity;

  /// Get formatted item total
  String get formattedItemTotal => '${itemTotal.toStringAsFixed(3)} د.ل';

  /// Get formatted unit price
  String get formattedPrice => '${price.toStringAsFixed(3)} د.ل';

  /// Get product name (already available in CartItemModel)
  String get displayName => productName;

  /// Check if has valid product data
  bool get hasValidProduct => productId.isNotEmpty;
}
