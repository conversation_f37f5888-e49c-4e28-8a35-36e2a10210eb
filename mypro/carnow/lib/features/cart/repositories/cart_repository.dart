import '../../../core/networking/simple_api_client.dart';
import '../../../core/utils/unified_logger.dart';
import '../models/cart_model.dart';
import '../models/cart_item_model.dart';

/// Enhanced Cart Repository following Forever Plan architecture
/// Handles all cart-related API operations with proper error handling
class CartRepository {
  final SimpleApiClient _apiClient;

  CartRepository(this._apiClient);

  /// Get user's complete cart with totals and items
  Future<CartModel?> getCart() async {
    try {
      UnifiedLogger.info('Getting user cart');

      final response = await _apiClient.getApi('/cart');

      if (response.isSuccess && response.data != null) {
        final cartData = response.data as Map<String, dynamic>;

        // Parse cart items and enrich with product details
        final items = await _parseCartItems(cartData['items'] as List? ?? []);

        // Create cart model with enriched items
        final cartWithItems = Map<String, dynamic>.from(cartData);
        cartWithItems['items'] = items.map((item) => item.toJson()).toList();

        return CartModel.fromJson(cartWithItems);
      }

      return null;
    } catch (e) {
      UnifiedLogger.error('Error getting cart', error: e);
      throw CartRepositoryException('فشل في الحصول على العربة: $e');
    }
  }

  // Legacy PartCartItemModel methods removed - use CartModel methods instead

  // Legacy methods removed - use CartModel-based methods instead

  /// تحديث كمية عنصر في السلة
  Future<bool> updateCartItemQuantity(int cartItemId, int newQuantity) async {
    try {
      if (cartItemId <= 0) {
        throw ArgumentError('Invalid cart item ID');
      }
      if (newQuantity <= 0) {
        await removeFromCart(cartItemId.toString());
        return true; // Return true to indicate successful update (removal)
      }

      await _apiClient.putApi(
        '/cart/items/$cartItemId',
        data: {'quantity': newQuantity},
      );

      return true;
    } catch (e) {
      UnifiedLogger.error('Error updating cart item quantity', error: e);
      rethrow;
    }
  }

  /// إزالة عنصر من السلة
  Future<void> removeFromCart(String itemId) async {
    try {
      UnifiedLogger.info('Removing item from cart: $itemId');

      await _apiClient.deleteApi('/cart/items/$itemId');

      UnifiedLogger.info('Item removed from cart successfully');
    } catch (e) {
      UnifiedLogger.error('Error removing item from cart', error: e);
      rethrow;
    }
  }

  /// تفريغ سلة التسوق
  Future<void> clearCart(String userId) async {
    try {
      UnifiedLogger.info('Clearing cart for user: $userId');

      if (userId.isEmpty) {
        throw ArgumentError('User ID cannot be empty');
      }

      await _apiClient.deleteApi('/cart/clear');

      UnifiedLogger.info('Cart cleared successfully');
    } catch (e) {
      UnifiedLogger.error('Error clearing cart', error: e);
      rethrow;
    }
  }

  /// Enhanced cart operations following Forever Plan architecture

  /// Add item to cart using enhanced API
  Future<void> addItemToCart(String productId, int quantity) async {
    try {
      UnifiedLogger.info('Adding item to cart: $productId, quantity: $quantity');

      final response = await _apiClient.postApi('/cart/items', data: {
        'product_id': productId,
        'quantity': quantity,
      });

      if (!response.isSuccess) {
        throw CartRepositoryException('فشل في إضافة العنصر للعربة: ${response.error}');
      }

      UnifiedLogger.info('Item added to cart successfully');
    } catch (e) {
      UnifiedLogger.error('Error adding item to cart', error: e);
      if (e is CartRepositoryException) rethrow;
      throw CartRepositoryException('فشل في إضافة العنصر للعربة: $e');
    }
  }

  /// Update cart item quantity using enhanced API
  Future<void> updateItemQuantity(String itemId, int quantity) async {
    try {
      UnifiedLogger.info('Updating cart item: $itemId, quantity: $quantity');

      final response = await _apiClient.putApi('/cart/items/$itemId', data: {
        'quantity': quantity,
      });

      if (!response.isSuccess) {
        throw CartRepositoryException('فشل في تحديث كمية العنصر: ${response.error}');
      }

      UnifiedLogger.info('Cart item updated successfully');
    } catch (e) {
      UnifiedLogger.error('Error updating cart item', error: e);
      if (e is CartRepositoryException) rethrow;
      throw CartRepositoryException('فشل في تحديث كمية العنصر: $e');
    }
  }

  /// Remove item from cart using enhanced API
  Future<void> removeItemFromCart(String itemId) async {
    try {
      UnifiedLogger.info('Removing cart item: $itemId');

      final response = await _apiClient.deleteApi('/cart/items/$itemId');

      if (!response.isSuccess) {
        throw CartRepositoryException('فشل في إزالة العنصر من العربة: ${response.error}');
      }

      UnifiedLogger.info('Cart item removed successfully');
    } catch (e) {
      UnifiedLogger.error('Error removing cart item', error: e);
      if (e is CartRepositoryException) rethrow;
      throw CartRepositoryException('فشل في إزالة العنصر من العربة: $e');
    }
  }

  /// Clear entire cart using enhanced API
  Future<void> clearEntireCart() async {
    try {
      UnifiedLogger.info('Clearing entire cart');

      final response = await _apiClient.deleteApi('/cart');

      if (!response.isSuccess) {
        throw CartRepositoryException('فشل في تفريغ العربة: ${response.error}');
      }

      UnifiedLogger.info('Cart cleared successfully');
    } catch (e) {
      UnifiedLogger.error('Error clearing cart', error: e);
      if (e is CartRepositoryException) rethrow;
      throw CartRepositoryException('فشل في تفريغ العربة: $e');
    }
  }

  /// Parse cart items and enrich with product details
  /// Focus on Products only (Amazon/eBay style)
  Future<List<CartItemModel>> _parseCartItems(List<dynamic> itemsData) async {
    final items = <CartItemModel>[];

    for (final itemData in itemsData) {
      try {
        final data = itemData as Map<String, dynamic>;

        // Only process items with product_id (Products table focus)
        if (data['product_id'] == null) {
          UnifiedLogger.warning('Cart item missing product_id, skipping');
          continue;
        }

        final item = CartItemModel(
          id: data['id']?.toString() ?? '',
          productId: data['product_id'].toString(),
          productName: data['product_name']?.toString() ?? 'منتج غير معروف',
          quantity: data['quantity'] ?? 1,
          price: (data['price'] ?? 0.0).toDouble(),
          cartId: data['cart_id']?.toString(),
          userId: data['user_id']?.toString(),
          createdAt: DateTime.tryParse(data['created_at']?.toString() ?? ''),
          updatedAt: DateTime.tryParse(data['updated_at']?.toString() ?? ''),
        );

        items.add(item);
      } catch (e) {
        UnifiedLogger.error('Error parsing cart item', error: e);
        // Skip invalid items
      }
    }

    return items;
  }
}

/// Exception class for cart repository errors
class CartRepositoryException implements Exception {
  final String message;

  const CartRepositoryException(this.message);

  @override
  String toString() => 'CartRepositoryException: $message';
}
