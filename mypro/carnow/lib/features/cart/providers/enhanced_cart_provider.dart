import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../models/cart_model.dart';
import '../repositories/cart_repository.dart';

part 'enhanced_cart_provider.g.dart';

final _logger = Logger('EnhancedCartProvider');

/// Enhanced Cart Repository Provider
@riverpod
CartRepository cartRepository(CartRepositoryRef ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return CartRepository(apiClient);
}

/// Enhanced Cart Provider following Forever Plan architecture
/// Manages the complete cart state with proper error handling and caching
@riverpod
class EnhancedCart extends _$EnhancedCart {
  @override
  Future<CartModel?> build() async {
    final repository = ref.read(cartRepositoryProvider);
    
    try {
      _logger.info('Loading cart data');
      final cart = await repository.getCart();
      
      if (cart != null) {
        _logger.info('Cart loaded successfully with ${cart.itemCount} items');
      } else {
        _logger.info('No cart found for user');
      }
      
      return cart;
    } catch (e, stackTrace) {
      _logger.severe('Failed to load cart', e, stackTrace);
      // Return null instead of throwing to allow graceful handling
      return null;
    }
  }

  /// Add item to cart with optimistic updates
  Future<void> addItem(String productId, int quantity) async {
    if (quantity <= 0) {
      throw ArgumentError('Quantity must be greater than zero');
    }

    final repository = ref.read(cartRepositoryProvider);
    
    try {
      _logger.info('Adding item to cart: $productId, quantity: $quantity');
      
      // Show loading state
      state = const AsyncValue.loading();
      
      // Add item via repository
      await repository.addItemToCart(productId, quantity);
      
      // Refresh cart data
      await refresh();
      
      _logger.info('Item added to cart successfully');
    } catch (e, stackTrace) {
      _logger.severe('Failed to add item to cart', e, stackTrace);
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Update item quantity with optimistic updates
  Future<void> updateItemQuantity(String itemId, int quantity) async {
    if (quantity < 0) {
      throw ArgumentError('Quantity cannot be negative');
    }

    final repository = ref.read(cartRepositoryProvider);
    
    try {
      _logger.info('Updating cart item: $itemId, quantity: $quantity');
      
      // Show loading state
      state = const AsyncValue.loading();
      
      // Update item via repository
      await repository.updateItemQuantity(itemId, quantity);
      
      // Refresh cart data
      await refresh();
      
      _logger.info('Cart item updated successfully');
    } catch (e, stackTrace) {
      _logger.severe('Failed to update cart item', e, stackTrace);
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Remove item from cart
  Future<void> removeItem(String itemId) async {
    final repository = ref.read(cartRepositoryProvider);
    
    try {
      _logger.info('Removing cart item: $itemId');
      
      // Show loading state
      state = const AsyncValue.loading();
      
      // Remove item via repository
      await repository.removeItemFromCart(itemId);
      
      // Refresh cart data
      await refresh();
      
      _logger.info('Cart item removed successfully');
    } catch (e, stackTrace) {
      _logger.severe('Failed to remove cart item', e, stackTrace);
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Clear entire cart
  Future<void> clearCart() async {
    final repository = ref.read(cartRepositoryProvider);
    
    try {
      _logger.info('Clearing entire cart');
      
      // Show loading state
      state = const AsyncValue.loading();
      
      // Clear cart via repository
      await repository.clearEntireCart();
      
      // Set empty state
      state = const AsyncValue.data(null);
      
      _logger.info('Cart cleared successfully');
    } catch (e, stackTrace) {
      _logger.severe('Failed to clear cart', e, stackTrace);
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Refresh cart data
  Future<void> refresh() async {
    final repository = ref.read(cartRepositoryProvider);
    
    try {
      final cart = await repository.getCart();
      state = AsyncValue.data(cart);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Force invalidate and rebuild
  void invalidate() {
    ref.invalidateSelf();
  }
}

/// Cart Summary Provider for quick access
@riverpod
CartSummaryModel cartSummary(CartSummaryRef ref) {
  final cartAsync = ref.watch(enhancedCartProvider);
  
  return cartAsync.when(
    data: (cart) => cart?.summary ?? const CartSummaryModel(
      itemCount: 0,
      total: 0.0,
      isEmpty: true,
      isLoading: false,
    ),
    loading: () => const CartSummaryModel(
      itemCount: 0,
      total: 0.0,
      isEmpty: true,
      isLoading: true,
    ),
    error: (_, __) => const CartSummaryModel(
      itemCount: 0,
      total: 0.0,
      isEmpty: true,
      isLoading: false,
    ),
  );
}

/// Cart Item Count Provider for badges and quick display
@riverpod
int cartItemCount(CartItemCountRef ref) {
  final summary = ref.watch(cartSummaryProvider);
  return summary.itemCount;
}

/// Cart Total Provider for quick display
@riverpod
double cartTotal(CartTotalRef ref) {
  final summary = ref.watch(cartSummaryProvider);
  return summary.total;
}

/// Cart Empty State Provider
@riverpod
bool isCartEmpty(IsCartEmptyRef ref) {
  final summary = ref.watch(cartSummaryProvider);
  return summary.isEmpty;
}

/// Cart Loading State Provider
@riverpod
bool isCartLoading(IsCartLoadingRef ref) {
  final summary = ref.watch(cartSummaryProvider);
  return summary.isLoading;
}
