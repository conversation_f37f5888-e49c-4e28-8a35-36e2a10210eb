import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:collection/collection.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../products/models/product_model.dart';
import '../models/cart_model.dart';
import '../models/cart_item_model.dart';


part 'cart_provider.g.dart';

final _logger = Logger('CartProvider');

@riverpod
class Cart extends _$Cart {
  @override
  Future<List<CompleteCartItemModel>> build() async {
    final apiClient = ref.watch(simpleApiClientProvider);

    try {
      // Get cart items from API
      final cartItemsResponse = await apiClient.getApi('/cart/items');
      
      if (cartItemsResponse.data == null || (cartItemsResponse.data as List).isEmpty) {
        return [];
      }

      // Get product IDs from cart items
      final cartItemsData = cartItemsResponse.data as List;
      final productIds = cartItemsData
          .map((item) => item['product_id'] as String)
          .toList();

      // Get products separately
      final productsResponse = await apiClient.getApi('/products', 
        queryParameters: {'ids': productIds.join(',')});

      // Create a map of products for quick lookup
      final productsMap = <String, ProductModel>{};
      final productsData = productsResponse.data as List;
      for (final productData in productsData) {
        final product = ProductModel.fromJson(productData);
        productsMap[product.id] = product;
      }

      // Combine cart items with products
      final cartItems = <CompleteCartItemModel>[];
      for (final itemData in cartItemsData) {
        final cartItem = CartItemModel.fromJson(itemData);
        final product = productsMap[cartItem.productId];

        if (product != null) {
          cartItems.add(
            CompleteCartItemModel(item: cartItem, product: product),
          );
        }
      }

      return cartItems;
    } catch (e, stackTrace) {
      // Enhanced error handling with proper logging
      _logger.severe('Failed to load cart items', e, stackTrace);
      state = AsyncValue.error('Failed to load cart items: $e', stackTrace);
      throw Exception('فشل في تحميل عناصر العربة: $e');
    }
  }

  Future<void> addItem(String productId) async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      // Check if the item already exists in the cart
      final existingItem = state.value?.firstWhereOrNull(
        (element) => element.item.productId == productId,
      );

      if (existingItem != null) {
        // If it exists, update the quantity
        await updateQuantity(
          existingItem.item.id,
          existingItem.item.quantity + 1,
        );
      } else {
        // If not, insert a new item
        await apiClient.postApi('/cart/items', data: {
          'product_id': productId,
          'quantity': 1,
        });
      }

      ref.invalidateSelf();
    } catch (e, stackTrace) {
      // Enhanced error handling with user-friendly messages
      _logger.warning('Failed to add item to cart', e, stackTrace);
      final errorMessage = e.toString().contains('duplicate key')
          ? 'العنصر موجود في العربة بالفعل'
          : 'فشل في إضافة العنصر للعربة: $e';
      throw Exception(errorMessage);
    }
  }

  Future<void> removeItem(String itemId) async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      await apiClient.deleteApi('/cart/items/$itemId');
      ref.invalidateSelf();
    } catch (e, stackTrace) {
      _logger.warning('Failed to remove item from cart', e, stackTrace);
      throw Exception('فشل في إزالة العنصر من العربة: $e');
    }
  }

  Future<void> updateQuantity(String itemId, int quantity) async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      if (quantity <= 0) {
        await removeItem(itemId);
      } else {
        await apiClient.putApi('/cart/items/$itemId', data: {
          'quantity': quantity,
        });
      }
      ref.invalidateSelf();
    } catch (e, stackTrace) {
      _logger.warning('Failed to update item quantity', e, stackTrace);
      throw Exception('فشل في تحديث كمية العنصر: $e');
    }
  }

  Future<void> clearCart() async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      await apiClient.deleteApi('/cart/clear');
      ref.invalidateSelf();
    } catch (e, stackTrace) {
      _logger.warning('Failed to clear cart', e, stackTrace);
      throw Exception('فشل في تفريغ العربة: $e');
    }
  }

  double get totalPrice {
    return state.value?.fold<double>(
          0,
          (total, item) => total + (item.product.price * item.item.quantity),
        ) ??
        0;
  }
}
