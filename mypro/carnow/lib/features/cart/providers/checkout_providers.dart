import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../models/cart_item_model.dart';

part 'checkout_providers.g.dart';

final _logger = Logger('CheckoutProviders');

/// Simple Checkout Provider following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
@riverpod
class CheckoutNotifier extends _$CheckoutNotifier {
  @override
  AsyncValue<void> build() => const AsyncValue.data(null);

  Future<void> placeOrder({
    required List<CartItemModel> items,
    required String customerName,
    required String customerPhone,
    required String customerAddress,
    required double totalAmount,
    String? notes,
  }) async {
    state = const AsyncValue.loading();

    try {
      _logger.info('Placing order for ${items.length} items');

      final apiClient = ref.read(simpleApiClientProvider);

      // إنشاء الطلب عبر Go backend
      final orderData = {
        'customer_name': customerName,
        'customer_phone': customerPhone,
        'customer_address': customerAddress,
        'total_amount': totalAmount,
        'notes': notes,
        'items': items.map((item) => item.toJson()).toList(),
      };

      final response = await apiClient.postApi<Map<String, dynamic>>(
        '/orders',
        data: orderData,
      );

      if (!response.isSuccess) {
        throw Exception('فشل في إنشاء الطلب: ${response.message}');
      }

      state = const AsyncValue.data(null);
      _logger.info('Order placed successfully');
    } catch (e) {
      _logger.severe('Error placing order: $e');
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }
}
