import 'package:logger/logger.dart';

import '../../../core/networking/simple_api_client.dart';
import '../models/vehicle_specifications_model.dart';
import '../models/vehicle_search_result_model.dart';

final _logger = Logger();

/// مستودع البحث التدريجي للمركبات
/// ✅ Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
class VehicleSearchRepository {
  VehicleSearchRepository(this._apiClient);
  final SimpleApiClient _apiClient;

  /// الخطوة 1: الحصول على جميع الماركات المتاحة
  Future<List<VehicleSearchStepModel>> getAvailableMakes() async {
    try {
      _logger.i('جلب الماركات المتاحة...');

      final response = await _apiClient.get('/api/v1/vehicles/makes');
      
      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final List<dynamic> makesData = responseData['data'] ?? responseData;
        final makes = makesData.map((json) => VehicleSearchStepModel.fromJson(json as Map<String, dynamic>)).toList();

        _logger.i('تم جلب ${makes.length} ماركة');
        return makes;
      } else {
        _logger.e('فشل في جلب الماركات: ${response.error}');
        throw Exception('Failed to fetch vehicle makes from backend: ${response.error}');
      }
    } catch (e, stack) {
      _logger.e('خطأ في جلب الماركات: $e', stackTrace: stack);
      // Forever Plan Architecture: NO MOCK DATA - throw proper error
      throw Exception('Failed to fetch vehicle makes from backend: $e');
    }
  }

  /// الخطوة 2: الحصول على الموديلات حسب الماركة
  Future<List<ModelsByMakeModel>> getModelsByMake({
    int? makeId,
    String? makeName,
  }) async {
    try {
      _logger.i('جلب الموديلات للماركة: $makeName');

      final response = await _apiClient.get('/api/v1/vehicles/models', queryParameters: {'make_id': makeId, 'make_name': makeName});

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final List<dynamic> modelsData = responseData['data'] ?? responseData;
        final models = modelsData.map((json) => ModelsByMakeModel.fromJson(json as Map<String, dynamic>)).toList();

        _logger.i('تم جلب ${models.length} موديل');
        return models;
      } else {
        _logger.e('فشل في جلب الموديلات: ${response.error}');
        return [];
      }
    } catch (e, stack) {
      _logger.e('خطأ في جلب الموديلات: $e', stackTrace: stack);
      rethrow;
    }
  }

  /// الخطوة 3: الحصول على السنوات حسب الموديل
  Future<List<YearsByModelModel>> getYearsByModel({
    int? modelId,
    String? makeName,
    String? modelName,
  }) async {
    try {
      _logger.i('جلب السنوات للموديل: $modelName');

      final response = await _apiClient.get('/api/v1/vehicles/years', queryParameters: {
        'model_id': modelId,
        'make_name': makeName,
        'model_name': modelName,
      });

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final List<dynamic> yearsData = responseData['data'] ?? responseData;
        final years = yearsData.map((json) => YearsByModelModel.fromJson(json as Map<String, dynamic>)).toList();

        _logger.i('تم جلب ${years.length} سنة');
        return years;
      } else {
        _logger.e('فشل في جلب السنوات: ${response.error}');
        return [];
      }
    } catch (e, stack) {
      _logger.e('خطأ في جلب السنوات: $e', stackTrace: stack);
      rethrow;
    }
  }

  /// الخطوة 4: الحصول على المواصفات النهائية
  Future<VehicleSpecificationsModel?> getFinalSpecifications({
    required String makeName,
    required String modelName,
    required int year,
  }) async {
    try {
      _logger.i('جلب المواصفات النهائية: $makeName $modelName $year');

      final response = await _apiClient.get('/api/v1/vehicles/specifications', queryParameters: {
        'make_name': makeName,
        'model_name': modelName,
        'year': year,
      });

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final List<dynamic> specificationsData = responseData['data'] ?? responseData;
        if (specificationsData.isEmpty) {
          _logger.i('لم يتم العثور على مواصفات');
          return null;
        }

        final specifications = VehicleSpecificationsModel.fromJson(specificationsData[0] as Map<String, dynamic>);

        _logger.i('تم جلب المواصفات بنجاح');
        return specifications;
      } else {
        _logger.e('فشل في جلب المواصفات: ${response.error}');
        return null;
      }
    } catch (e, stack) {
      _logger.e('خطأ في جلب المواصفات: $e', stackTrace: stack);
      rethrow;
    }
  }

  /// البحث عن قطع الغيار المتوافقة
  Future<CompatiblePartsModel?> findCompatibleParts({
    required String makeName,
    required String modelName,
    required int year,
    String partCategory = '',
  }) async {
    try {
      _logger.i('البحث عن قطع الغيار المتوافقة...');

      final response = await _apiClient.get('/api/v1/vehicles/compatible-parts', queryParameters: {
        'make_name': makeName,
        'model_name': modelName,
        'year': year,
        'part_category': partCategory,
      });

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final List<dynamic> compatiblePartsData = responseData['data'] ?? responseData;
        if (compatiblePartsData.isEmpty) {
          _logger.i('لم يتم العثور على قطع غيار متوافقة');
          return null;
        }

        return CompatiblePartsModel.fromJson(compatiblePartsData[0] as Map<String, dynamic>);
      } else {
        _logger.e('فشل في البحث عن قطع الغيار: ${response.error}');
        return null;
      }
    } catch (e, stack) {
      _logger.e('خطأ في البحث عن قطع الغيار: $e', stackTrace: stack);
      rethrow;
    }
  }

  /// البحث عن المركبات المتوافقة مع منتج معين
  Future<List<VehicleForProductModel>> getVehiclesForPart({
    required String partCategory,
    String engineSize = '',
    String fuelType = '',
    int yearRangeStart = 2000,
    int yearRangeEnd = 2020,
  }) async {
    try {
      _logger.i('البحث عن المركبات المتوافقة مع: $partCategory');

      final response = await _apiClient.get('/api/v1/vehicles/compatible-vehicles', queryParameters: {
        'part_category': partCategory,
        'engine_size': engineSize,
        'fuel_type': fuelType,
        'year_range_start': yearRangeStart,
        'year_range_end': yearRangeEnd,
      });

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final List<dynamic> vehiclesData = responseData['data'] ?? responseData;
        return vehiclesData.map((json) => VehicleForProductModel.fromJson(json as Map<String, dynamic>)).toList();
      } else {
        _logger.e('فشل في البحث عن المركبات المتوافقة: ${response.error}');
        return [];
      }
    } catch (e, stack) {
      _logger.e('خطأ في البحث عن المركبات المتوافقة: $e', stackTrace: stack);
      rethrow;
    }
  }

  /// البحث السريع في المركبات
  Future<List<VehicleSearchResultModel>> quickVehicleSearch(
    String query,
  ) async {
    try {
      _logger.i('البحث السريع: $query');

      final response = await _apiClient.get('/api/v1/vehicles/search', queryParameters: {'search_query': query});

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final List<dynamic> searchResultsData = responseData['data'] ?? responseData;
        return searchResultsData.map((json) => VehicleSearchResultModel.fromJson(json as Map<String, dynamic>)).toList();
      } else {
        _logger.e('فشل في البحث السريع: ${response.error}');
        return [];
      }
    } catch (e, stack) {
      _logger.e('خطأ في البحث السريع: $e', stackTrace: stack);
      rethrow;
    }
  }

  // Mock data for graceful degradation
  // Forever Plan Architecture Compliance:
  // ❌ REMOVED: _getMockMakes() method - violates ZERO MOCK DATA POLICY
  // ✅ REQUIRED: Use real vehicle data from Supabase via Go backend
  // ✅ ARCHITECTURE: Flutter UI Only → Go API → Supabase Data
  //
  // This method has been removed to comply with Forever Plan Architecture.
  // All vehicle data must come from real database via Go backend endpoints.
}
