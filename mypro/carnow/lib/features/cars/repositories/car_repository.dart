
import '../../../core/networking/simple_api_client.dart';
import '../models/vehicle_make.dart';
import '../models/vehicle_model.dart';

class CarRepository {
  CarRepository(this._apiClient);
  final SimpleApiClient _apiClient;

  Future<List<VehicleMake>> getVehicleMakes() async {
    try {
      final response = await _apiClient.get('/vehicles/makes');
      final makes = (response.data as List)
          .map((e) => VehicleMake.fromJson(e as Map<String, dynamic>))
          .toList();
      return makes;
    } catch (e) {
      throw Exception('Failed to fetch vehicle makes: $e');
    }
  }

  Future<List<VehicleModel>> getVehicleModels(int makeId) async {
    try {
      final response = await _apiClient.get('/vehicles/makes/$makeId/models');
      final models = (response.data as List)
          .map((e) => VehicleModel.fromJson(e as Map<String, dynamic>))
          .toList();
      return models;
    } catch (e) {
      throw Exception('Failed to fetch vehicle models for make ID $makeId: $e');
    }
  }
}
