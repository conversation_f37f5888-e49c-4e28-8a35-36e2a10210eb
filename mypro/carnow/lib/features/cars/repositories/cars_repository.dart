import '../../../core/networking/simple_api_client.dart';
import '../models/car_model.dart';

/// مستودع السيارات للوصول إلى بيانات السيارات
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
class CarsRepository {
  CarsRepository(this._apiClient);
  final SimpleApiClient _apiClient;

  /// الحصول على قائمة السيارات
  Future<List<CarModel>> getCars() async {
    try {
      final response = await _apiClient.get('/api/v1/cars');
      
      if (response.isSuccess && response.data != null) {
        final data = response.data['data'] as List;
        return data.map((json) => CarModel.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      // التعامل مع الخطأ
      return [];
    }
  }

  /// الحصول على تفاصيل سيارة محددة
  Future<CarModel?> getCarById(int id) async {
    try {
      final response = await _apiClient.get('/api/v1/cars/$id');
      
      if (response.isSuccess && response.data != null) {
        return CarModel.fromJson(response.data['data']);
      }
      return null;
    } catch (e) {
      // التعامل مع الخطأ
      return null;
    }
  }
}
