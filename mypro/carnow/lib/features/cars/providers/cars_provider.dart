import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/unified_auth_provider.dart';

part 'cars_provider.g.dart';

final _logger = Logger('CarsProvider');

/// مزود لسيارات المستخدم - CLEAN: Uses HTTP API only
@riverpod
Future<List<Car>> userCars(Ref ref) async {
  final client = ref.read(simpleApiClientProvider);
  final isAuthenticated = ref.read(isAuthenticatedProvider);

  if (!isAuthenticated) {
    throw Exception('يجب تسجيل الدخول أولاً للوصول إلى سياراتك');
  }

  try {
    final response = await client.getApi('/user/cars');

    if (response.isSuccess && response.data != null) {
      final List<Car> cars = (response.data as List)
          .map((json) => Car.fromJson(json))
          .toList();
      _logger.info('Successfully loaded ${cars.length} cars');
      return cars;
    }

    return [];
  } catch (e) {
    _logger.severe('Error loading user cars', e);
    throw Exception('فشل تحميل السيارات: ${_formatErrorMessage(e)}');
  }
}

/// إضافة سيارة جديدة
@riverpod
class CarsNotifier extends _$CarsNotifier {
  @override
  Future<List<Car>> build() => ref.watch(userCarsProvider.future);

  Future<void> addCar({
    required String make,
    required String model,
    required int year,
    String? color,
    String? licensePlate,
    String? vin,
  }) async {
    final client = ref.read(simpleApiClientProvider);
    final isAuthenticated = ref.read(isAuthenticatedProvider);

    if (!isAuthenticated) {
      throw Exception('يجب تسجيل الدخول لإضافة سيارة');
    }

    try {
      await client.postApi(
        '/user/cars',
        data: {
          'make': make,
          'model': model,
          'year': year,
          'color': color,
          'license_plate': licensePlate,
          'vin': vin,
        },
      );

      // Refresh the cars list
      ref.invalidate(userCarsProvider);
      _logger.info('Car added successfully');
    } catch (e) {
      _logger.severe('Error adding car', e);
      throw Exception('فشل في إضافة السيارة: ${_formatErrorMessage(e)}');
    }
  }

  Future<void> updateCar({
    required String carId,
    String? make,
    String? model,
    int? year,
    String? color,
    String? licensePlate,
    String? vin,
  }) async {
    final client = ref.read(simpleApiClientProvider);
    final isAuthenticated = ref.read(isAuthenticatedProvider);

    if (!isAuthenticated) {
      throw Exception('يجب تسجيل الدخول لتحديث السيارة');
    }

    final updates = <String, dynamic>{};
    if (make != null) updates['make'] = make;
    if (model != null) updates['model'] = model;
    if (year != null) updates['year'] = year;
    if (color != null) updates['color'] = color;
    if (licensePlate != null) updates['license_plate'] = licensePlate;
    if (vin != null) updates['vin'] = vin;

    if (updates.isEmpty) return;

    try {
      await client.putApi(
        '/user/cars/$carId',
        data: updates,
      );

      // Refresh the cars list
      ref.invalidate(userCarsProvider);
      _logger.info('Car updated successfully');
    } catch (e) {
      _logger.severe('Error updating car', e);
      throw Exception('فشل في تحديث السيارة: ${_formatErrorMessage(e)}');
    }
  }

  Future<void> deleteCar(String carId) async {
    final client = ref.read(simpleApiClientProvider);
    final isAuthenticated = ref.read(isAuthenticatedProvider);

    if (!isAuthenticated) {
      throw Exception('يجب تسجيل الدخول لحذف السيارة');
    }

    try {
      await client.deleteApi('/user/cars/$carId');

      // Refresh the cars list
      ref.invalidate(userCarsProvider);
      _logger.info('Car deleted successfully');
    } catch (e) {
      _logger.severe('Error deleting car', e);
      throw Exception('فشل في حذف السيارة: ${_formatErrorMessage(e)}');
    }
  }
}

/// مساعد لتنسيق رسائل الخطأ بشكل أفضل للمستخدمين
String _formatErrorMessage(dynamic error) {
  final errorMsg = error.toString().toLowerCase();

  if (errorMsg.contains('network') || errorMsg.contains('connection')) {
    return 'تعذر الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت.';
  } else if (errorMsg.contains('timeout')) {
    return 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.';
  } else if (errorMsg.contains('permission') || errorMsg.contains('access')) {
    return 'ليس لديك صلاحية الوصول إلى هذه البيانات.';
  } else if (errorMsg.contains('not found') || errorMsg.contains('no rows')) {
    return 'لم يتم العثور على البيانات المطلوبة.';
  }

  return error.toString();
}

/// نموذج السيارة
class Car {
  Car({
    required this.make,
    required this.model,
    required this.year,
    this.id,
    this.userId,
    this.color,
    this.licensePlate,
    this.vin,
    this.createdAt,
    this.updatedAt,
    this.isDeleted,
  });

  factory Car.fromJson(Map<String, dynamic> json) => Car(
        id: json['id']?.toString(),
        userId: json['user_id']?.toString(),
        make: json['make'] as String? ?? '',
        model: json['model'] as String? ?? '',
        year: json['year'] as int? ?? 2000,
        color: json['color'] as String?,
        licensePlate: json['license_plate'] as String?,
        vin: json['vin'] as String?,
        createdAt: json['created_at'] != null
            ? DateTime.parse(json['created_at'] as String)
            : null,
        updatedAt: json['updated_at'] != null
            ? DateTime.parse(json['updated_at'] as String)
            : null,
        isDeleted: json['is_deleted'] as bool? ?? false,
      );

  final String? id;
  final String? userId;
  final String make;
  final String model;
  final int year;
  final String? color;
  final String? licensePlate;
  final String? vin;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool? isDeleted;

  Map<String, dynamic> toJson() => {
        if (id != null) 'id': id,
        if (userId != null) 'user_id': userId,
        'make': make,
        'model': model,
        'year': year,
        if (color != null) 'color': color,
        if (licensePlate != null) 'license_plate': licensePlate,
        if (vin != null) 'vin': vin,
        if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
        if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
        if (isDeleted != null) 'is_deleted': isDeleted,
      };
}