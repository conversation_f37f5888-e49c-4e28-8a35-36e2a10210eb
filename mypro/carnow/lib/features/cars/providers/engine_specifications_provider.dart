import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/networking/simple_api_client.dart';
import '../../../core/errors/app_error.dart';
import '../models/engine_specifications_model.dart';

part 'engine_specifications_provider.g.dart';

/// مقدم مواصفات المحركات
/// Engine Specifications Provider
@riverpod
class EngineSpecifications extends _$EngineSpecifications {
  @override
  Future<List<EngineSpecificationsModel>> build() async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      final response = await apiClient.get('/api/v1/cars/engine-specifications');
      
      final List<dynamic> data = response.data as List<dynamic>;
      return data
          .map((json) => EngineSpecificationsModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to load engine specifications: $e',
        originalError: e,
      );
    }
  }

  /// الحصول على مواصفات محرك بالمعرف
  /// Get engine specifications by ID
  Future<EngineSpecificationsModel?> getById(String id) async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      final response = await apiClient.get('/api/v1/cars/engine-specifications/$id');
      return EngineSpecificationsModel.fromJson(response.data as Map<String, dynamic>);
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to load engine specification: $e',
        originalError: e,
      );
    }
  }

  /// البحث في مواصفات المحركات
  /// Search engine specifications
  Future<List<EngineSpecificationsModel>> search(String query) async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      final response = await apiClient.get(
        '/api/v1/cars/engine-specifications/search',
        queryParameters: {'q': query},
      );
      
      final List<dynamic> data = response.data as List<dynamic>;
      return data
          .map((json) => EngineSpecificationsModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to search engine specifications: $e',
        originalError: e,
      );
    }
  }

  /// الحصول على مواصفات المحركات حسب نوع المحرك
  /// Get engine specifications by engine type
  Future<List<EngineSpecificationsModel>> getByEngineType(String engineType) async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      final response = await apiClient.get(
        '/api/v1/cars/engine-specifications',
        queryParameters: {'engine_type': engineType},
      );
      
      final List<dynamic> data = response.data as List<dynamic>;
      return data
          .map((json) => EngineSpecificationsModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to load engine specifications by type: $e',
        originalError: e,
      );
    }
  }

  /// الحصول على مواصفات المحركات للمركبة
  /// Get engine specifications for vehicle
  Future<List<EngineSpecificationsModel>> getForVehicle({
    required String makeId,
    required String modelId,
    String? year,
  }) async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      final response = await apiClient.get(
        '/api/v1/cars/engine-specifications/vehicle',
        queryParameters: {
          'make_id': makeId,
          'model_id': modelId,
          if (year != null) 'year': year,
        },
      );
      
      final List<dynamic> data = response.data as List<dynamic>;
      return data
          .map((json) => EngineSpecificationsModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to load engine specifications for vehicle: $e',
        originalError: e,
      );
    }
  }

  /// تحديث مواصفات المحرك
  /// Update engine specifications
  Future<void> updateSpecifications(String id, EngineSpecificationsModel specs) async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      await apiClient.put(
        '/api/v1/cars/engine-specifications/$id',
        data: specs.toJson(),
      );
      
      ref.invalidateSelf();
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to update engine specifications: $e',
        originalError: e,
      );
    }
  }

  /// إضافة مواصفات محرك جديدة
  /// Add new engine specifications
  Future<void> addSpecifications(EngineSpecificationsModel specs) async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      await apiClient.post(
        '/api/v1/cars/engine-specifications',
        data: specs.toJson(),
      );
      
      ref.invalidateSelf();
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to add engine specifications: $e',
        originalError: e,
      );
    }
  }
}

/// مقدم أداء المحرك
/// Engine Performance Provider
@riverpod
class EnginePerformance extends _$EnginePerformance {
  @override
  Future<List<EnginePerformanceModel>> build() async {
    return [];
  }

  /// الحصول على أداء محرك بالمعرف
  /// Get engine performance by engine ID
  Future<EnginePerformanceModel?> getByEngineId(String engineId) async {
    final apiClient = ref.read(simpleApiClientProvider);

    try {
      final response = await apiClient.get('/api/v1/cars/engine-performance/$engineId');
      return EnginePerformanceModel.fromJson(response.data as Map<String, dynamic>);
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to load engine performance: $e',
        originalError: e,
      );
    }
  }
}

/// مقدم المحرك الكامل (مواصفات + أداء)
/// Complete Engine Provider (specifications + performance)
@riverpod
Future<CompleteEngineModel?> completeEngine(Ref ref, String engineId) async {
  try {
    // الحصول على المواصفات
    final specificationsProvider = ref.read(
      engineSpecificationsProvider.notifier,
    );
    final specifications = await specificationsProvider.getById(engineId);

    if (specifications == null) return null;

    // الحصول على الأداء
    final performanceProvider = ref.read(enginePerformanceProvider.notifier);
    final performance = await performanceProvider.getByEngineId(engineId);

    return CompleteEngineModel(
      specifications: specifications,
      performance: performance,
    );
  } catch (error) {
    throw Exception('فشل في تحميل بيانات المحرك الكاملة: $error');
  }
}
