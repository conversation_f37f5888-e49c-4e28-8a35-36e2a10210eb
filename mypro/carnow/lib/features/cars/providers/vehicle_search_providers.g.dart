// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_search_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$vehicleSearchRepositoryHash() =>
    r'd490f5b1d57ed9cae9a9b577cc48882e18e6700a';

/// مزود مستودع البحث التدريجي
///
/// Copied from [vehicleSearchRepository].
@ProviderFor(vehicleSearchRepository)
final vehicleSearchRepositoryProvider =
    AutoDisposeProvider<VehicleSearchRepository>.internal(
      vehicleSearchRepository,
      name: r'vehicleSearchRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$vehicleSearchRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef VehicleSearchRepositoryRef =
    AutoDisposeProviderRef<VehicleSearchRepository>;
String _$availableMakesHash() => r'0b660da56398bbbe4315f469070c2923bb9e2833';

/// مزود الماركات المتاحة
///
/// Copied from [availableMakes].
@ProviderFor(availableMakes)
final availableMakesProvider =
    AutoDisposeFutureProvider<List<VehicleSearchStepModel>>.internal(
      availableMakes,
      name: r'availableMakesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$availableMakesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AvailableMakesRef =
    AutoDisposeFutureProviderRef<List<VehicleSearchStepModel>>;
String _$modelsByMakeHash() => r'1e4503bb051366f8591a496393ff7af1999d0db8';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// مزود الموديلات حسب الماركة
///
/// Copied from [modelsByMake].
@ProviderFor(modelsByMake)
const modelsByMakeProvider = ModelsByMakeFamily();

/// مزود الموديلات حسب الماركة
///
/// Copied from [modelsByMake].
class ModelsByMakeFamily extends Family<AsyncValue<List<ModelsByMakeModel>>> {
  /// مزود الموديلات حسب الماركة
  ///
  /// Copied from [modelsByMake].
  const ModelsByMakeFamily();

  /// مزود الموديلات حسب الماركة
  ///
  /// Copied from [modelsByMake].
  ModelsByMakeProvider call({int? makeId, String? makeName}) {
    return ModelsByMakeProvider(makeId: makeId, makeName: makeName);
  }

  @override
  ModelsByMakeProvider getProviderOverride(
    covariant ModelsByMakeProvider provider,
  ) {
    return call(makeId: provider.makeId, makeName: provider.makeName);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'modelsByMakeProvider';
}

/// مزود الموديلات حسب الماركة
///
/// Copied from [modelsByMake].
class ModelsByMakeProvider
    extends AutoDisposeFutureProvider<List<ModelsByMakeModel>> {
  /// مزود الموديلات حسب الماركة
  ///
  /// Copied from [modelsByMake].
  ModelsByMakeProvider({int? makeId, String? makeName})
    : this._internal(
        (ref) => modelsByMake(
          ref as ModelsByMakeRef,
          makeId: makeId,
          makeName: makeName,
        ),
        from: modelsByMakeProvider,
        name: r'modelsByMakeProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$modelsByMakeHash,
        dependencies: ModelsByMakeFamily._dependencies,
        allTransitiveDependencies:
            ModelsByMakeFamily._allTransitiveDependencies,
        makeId: makeId,
        makeName: makeName,
      );

  ModelsByMakeProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.makeId,
    required this.makeName,
  }) : super.internal();

  final int? makeId;
  final String? makeName;

  @override
  Override overrideWith(
    FutureOr<List<ModelsByMakeModel>> Function(ModelsByMakeRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ModelsByMakeProvider._internal(
        (ref) => create(ref as ModelsByMakeRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        makeId: makeId,
        makeName: makeName,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ModelsByMakeModel>> createElement() {
    return _ModelsByMakeProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ModelsByMakeProvider &&
        other.makeId == makeId &&
        other.makeName == makeName;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, makeId.hashCode);
    hash = _SystemHash.combine(hash, makeName.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ModelsByMakeRef on AutoDisposeFutureProviderRef<List<ModelsByMakeModel>> {
  /// The parameter `makeId` of this provider.
  int? get makeId;

  /// The parameter `makeName` of this provider.
  String? get makeName;
}

class _ModelsByMakeProviderElement
    extends AutoDisposeFutureProviderElement<List<ModelsByMakeModel>>
    with ModelsByMakeRef {
  _ModelsByMakeProviderElement(super.provider);

  @override
  int? get makeId => (origin as ModelsByMakeProvider).makeId;
  @override
  String? get makeName => (origin as ModelsByMakeProvider).makeName;
}

String _$yearsByModelHash() => r'3d42b6a19f21d84d283603b9cddf475ffde7e418';

/// مزود السنوات حسب الموديل
///
/// Copied from [yearsByModel].
@ProviderFor(yearsByModel)
const yearsByModelProvider = YearsByModelFamily();

/// مزود السنوات حسب الموديل
///
/// Copied from [yearsByModel].
class YearsByModelFamily extends Family<AsyncValue<List<YearsByModelModel>>> {
  /// مزود السنوات حسب الموديل
  ///
  /// Copied from [yearsByModel].
  const YearsByModelFamily();

  /// مزود السنوات حسب الموديل
  ///
  /// Copied from [yearsByModel].
  YearsByModelProvider call({
    int? modelId,
    String? makeName,
    String? modelName,
  }) {
    return YearsByModelProvider(
      modelId: modelId,
      makeName: makeName,
      modelName: modelName,
    );
  }

  @override
  YearsByModelProvider getProviderOverride(
    covariant YearsByModelProvider provider,
  ) {
    return call(
      modelId: provider.modelId,
      makeName: provider.makeName,
      modelName: provider.modelName,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'yearsByModelProvider';
}

/// مزود السنوات حسب الموديل
///
/// Copied from [yearsByModel].
class YearsByModelProvider
    extends AutoDisposeFutureProvider<List<YearsByModelModel>> {
  /// مزود السنوات حسب الموديل
  ///
  /// Copied from [yearsByModel].
  YearsByModelProvider({int? modelId, String? makeName, String? modelName})
    : this._internal(
        (ref) => yearsByModel(
          ref as YearsByModelRef,
          modelId: modelId,
          makeName: makeName,
          modelName: modelName,
        ),
        from: yearsByModelProvider,
        name: r'yearsByModelProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$yearsByModelHash,
        dependencies: YearsByModelFamily._dependencies,
        allTransitiveDependencies:
            YearsByModelFamily._allTransitiveDependencies,
        modelId: modelId,
        makeName: makeName,
        modelName: modelName,
      );

  YearsByModelProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.modelId,
    required this.makeName,
    required this.modelName,
  }) : super.internal();

  final int? modelId;
  final String? makeName;
  final String? modelName;

  @override
  Override overrideWith(
    FutureOr<List<YearsByModelModel>> Function(YearsByModelRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: YearsByModelProvider._internal(
        (ref) => create(ref as YearsByModelRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        modelId: modelId,
        makeName: makeName,
        modelName: modelName,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<YearsByModelModel>> createElement() {
    return _YearsByModelProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is YearsByModelProvider &&
        other.modelId == modelId &&
        other.makeName == makeName &&
        other.modelName == modelName;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, modelId.hashCode);
    hash = _SystemHash.combine(hash, makeName.hashCode);
    hash = _SystemHash.combine(hash, modelName.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin YearsByModelRef on AutoDisposeFutureProviderRef<List<YearsByModelModel>> {
  /// The parameter `modelId` of this provider.
  int? get modelId;

  /// The parameter `makeName` of this provider.
  String? get makeName;

  /// The parameter `modelName` of this provider.
  String? get modelName;
}

class _YearsByModelProviderElement
    extends AutoDisposeFutureProviderElement<List<YearsByModelModel>>
    with YearsByModelRef {
  _YearsByModelProviderElement(super.provider);

  @override
  int? get modelId => (origin as YearsByModelProvider).modelId;
  @override
  String? get makeName => (origin as YearsByModelProvider).makeName;
  @override
  String? get modelName => (origin as YearsByModelProvider).modelName;
}

String _$vehicleSpecificationsHash() =>
    r'0eaef1d2af615cfc8e1807e91beb44597239d741';

/// مزود المواصفات النهائية
///
/// Copied from [vehicleSpecifications].
@ProviderFor(vehicleSpecifications)
const vehicleSpecificationsProvider = VehicleSpecificationsFamily();

/// مزود المواصفات النهائية
///
/// Copied from [vehicleSpecifications].
class VehicleSpecificationsFamily
    extends Family<AsyncValue<VehicleSpecificationsModel?>> {
  /// مزود المواصفات النهائية
  ///
  /// Copied from [vehicleSpecifications].
  const VehicleSpecificationsFamily();

  /// مزود المواصفات النهائية
  ///
  /// Copied from [vehicleSpecifications].
  VehicleSpecificationsProvider call({
    required String makeName,
    required String modelName,
    required int year,
  }) {
    return VehicleSpecificationsProvider(
      makeName: makeName,
      modelName: modelName,
      year: year,
    );
  }

  @override
  VehicleSpecificationsProvider getProviderOverride(
    covariant VehicleSpecificationsProvider provider,
  ) {
    return call(
      makeName: provider.makeName,
      modelName: provider.modelName,
      year: provider.year,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehicleSpecificationsProvider';
}

/// مزود المواصفات النهائية
///
/// Copied from [vehicleSpecifications].
class VehicleSpecificationsProvider
    extends AutoDisposeFutureProvider<VehicleSpecificationsModel?> {
  /// مزود المواصفات النهائية
  ///
  /// Copied from [vehicleSpecifications].
  VehicleSpecificationsProvider({
    required String makeName,
    required String modelName,
    required int year,
  }) : this._internal(
         (ref) => vehicleSpecifications(
           ref as VehicleSpecificationsRef,
           makeName: makeName,
           modelName: modelName,
           year: year,
         ),
         from: vehicleSpecificationsProvider,
         name: r'vehicleSpecificationsProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$vehicleSpecificationsHash,
         dependencies: VehicleSpecificationsFamily._dependencies,
         allTransitiveDependencies:
             VehicleSpecificationsFamily._allTransitiveDependencies,
         makeName: makeName,
         modelName: modelName,
         year: year,
       );

  VehicleSpecificationsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.makeName,
    required this.modelName,
    required this.year,
  }) : super.internal();

  final String makeName;
  final String modelName;
  final int year;

  @override
  Override overrideWith(
    FutureOr<VehicleSpecificationsModel?> Function(
      VehicleSpecificationsRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VehicleSpecificationsProvider._internal(
        (ref) => create(ref as VehicleSpecificationsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        makeName: makeName,
        modelName: modelName,
        year: year,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<VehicleSpecificationsModel?>
  createElement() {
    return _VehicleSpecificationsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehicleSpecificationsProvider &&
        other.makeName == makeName &&
        other.modelName == modelName &&
        other.year == year;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, makeName.hashCode);
    hash = _SystemHash.combine(hash, modelName.hashCode);
    hash = _SystemHash.combine(hash, year.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehicleSpecificationsRef
    on AutoDisposeFutureProviderRef<VehicleSpecificationsModel?> {
  /// The parameter `makeName` of this provider.
  String get makeName;

  /// The parameter `modelName` of this provider.
  String get modelName;

  /// The parameter `year` of this provider.
  int get year;
}

class _VehicleSpecificationsProviderElement
    extends AutoDisposeFutureProviderElement<VehicleSpecificationsModel?>
    with VehicleSpecificationsRef {
  _VehicleSpecificationsProviderElement(super.provider);

  @override
  String get makeName => (origin as VehicleSpecificationsProvider).makeName;
  @override
  String get modelName => (origin as VehicleSpecificationsProvider).modelName;
  @override
  int get year => (origin as VehicleSpecificationsProvider).year;
}

String _$compatiblePartsHash() => r'f512477c60664aeddc70eb5dbd50e9e964659e30';

/// مزود قطع الغيار المتوافقة
///
/// Copied from [compatibleParts].
@ProviderFor(compatibleParts)
const compatiblePartsProvider = CompatiblePartsFamily();

/// مزود قطع الغيار المتوافقة
///
/// Copied from [compatibleParts].
class CompatiblePartsFamily extends Family<AsyncValue<CompatiblePartsModel?>> {
  /// مزود قطع الغيار المتوافقة
  ///
  /// Copied from [compatibleParts].
  const CompatiblePartsFamily();

  /// مزود قطع الغيار المتوافقة
  ///
  /// Copied from [compatibleParts].
  CompatiblePartsProvider call({
    required String makeName,
    required String modelName,
    required int year,
    String partCategory = '',
  }) {
    return CompatiblePartsProvider(
      makeName: makeName,
      modelName: modelName,
      year: year,
      partCategory: partCategory,
    );
  }

  @override
  CompatiblePartsProvider getProviderOverride(
    covariant CompatiblePartsProvider provider,
  ) {
    return call(
      makeName: provider.makeName,
      modelName: provider.modelName,
      year: provider.year,
      partCategory: provider.partCategory,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'compatiblePartsProvider';
}

/// مزود قطع الغيار المتوافقة
///
/// Copied from [compatibleParts].
class CompatiblePartsProvider
    extends AutoDisposeFutureProvider<CompatiblePartsModel?> {
  /// مزود قطع الغيار المتوافقة
  ///
  /// Copied from [compatibleParts].
  CompatiblePartsProvider({
    required String makeName,
    required String modelName,
    required int year,
    String partCategory = '',
  }) : this._internal(
         (ref) => compatibleParts(
           ref as CompatiblePartsRef,
           makeName: makeName,
           modelName: modelName,
           year: year,
           partCategory: partCategory,
         ),
         from: compatiblePartsProvider,
         name: r'compatiblePartsProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$compatiblePartsHash,
         dependencies: CompatiblePartsFamily._dependencies,
         allTransitiveDependencies:
             CompatiblePartsFamily._allTransitiveDependencies,
         makeName: makeName,
         modelName: modelName,
         year: year,
         partCategory: partCategory,
       );

  CompatiblePartsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.makeName,
    required this.modelName,
    required this.year,
    required this.partCategory,
  }) : super.internal();

  final String makeName;
  final String modelName;
  final int year;
  final String partCategory;

  @override
  Override overrideWith(
    FutureOr<CompatiblePartsModel?> Function(CompatiblePartsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CompatiblePartsProvider._internal(
        (ref) => create(ref as CompatiblePartsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        makeName: makeName,
        modelName: modelName,
        year: year,
        partCategory: partCategory,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CompatiblePartsModel?> createElement() {
    return _CompatiblePartsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CompatiblePartsProvider &&
        other.makeName == makeName &&
        other.modelName == modelName &&
        other.year == year &&
        other.partCategory == partCategory;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, makeName.hashCode);
    hash = _SystemHash.combine(hash, modelName.hashCode);
    hash = _SystemHash.combine(hash, year.hashCode);
    hash = _SystemHash.combine(hash, partCategory.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CompatiblePartsRef
    on AutoDisposeFutureProviderRef<CompatiblePartsModel?> {
  /// The parameter `makeName` of this provider.
  String get makeName;

  /// The parameter `modelName` of this provider.
  String get modelName;

  /// The parameter `year` of this provider.
  int get year;

  /// The parameter `partCategory` of this provider.
  String get partCategory;
}

class _CompatiblePartsProviderElement
    extends AutoDisposeFutureProviderElement<CompatiblePartsModel?>
    with CompatiblePartsRef {
  _CompatiblePartsProviderElement(super.provider);

  @override
  String get makeName => (origin as CompatiblePartsProvider).makeName;
  @override
  String get modelName => (origin as CompatiblePartsProvider).modelName;
  @override
  int get year => (origin as CompatiblePartsProvider).year;
  @override
  String get partCategory => (origin as CompatiblePartsProvider).partCategory;
}

String _$vehiclesForProductHash() =>
    r'572763a9a741a962b52c84680f11dd9476ce36ac';

/// مزود المركبات المتوافقة مع منتج معين
///
/// Copied from [vehiclesForProduct].
@ProviderFor(vehiclesForProduct)
const vehiclesForProductProvider = VehiclesForProductFamily();

/// مزود المركبات المتوافقة مع منتج معين
///
/// Copied from [vehiclesForProduct].
class VehiclesForProductFamily
    extends Family<AsyncValue<List<VehicleForProductModel>>> {
  /// مزود المركبات المتوافقة مع منتج معين
  ///
  /// Copied from [vehiclesForProduct].
  const VehiclesForProductFamily();

  /// مزود المركبات المتوافقة مع منتج معين
  ///
  /// Copied from [vehiclesForProduct].
  VehiclesForProductProvider call({
    required String partCategory,
    String engineSize = '',
    String fuelType = '',
    int yearRangeStart = 2000,
    int yearRangeEnd = 2020,
  }) {
    return VehiclesForProductProvider(
      partCategory: partCategory,
      engineSize: engineSize,
      fuelType: fuelType,
      yearRangeStart: yearRangeStart,
      yearRangeEnd: yearRangeEnd,
    );
  }

  @override
  VehiclesForProductProvider getProviderOverride(
    covariant VehiclesForProductProvider provider,
  ) {
    return call(
      partCategory: provider.partCategory,
      engineSize: provider.engineSize,
      fuelType: provider.fuelType,
      yearRangeStart: provider.yearRangeStart,
      yearRangeEnd: provider.yearRangeEnd,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehiclesForProductProvider';
}

/// مزود المركبات المتوافقة مع منتج معين
///
/// Copied from [vehiclesForProduct].
class VehiclesForProductProvider
    extends AutoDisposeFutureProvider<List<VehicleForProductModel>> {
  /// مزود المركبات المتوافقة مع منتج معين
  ///
  /// Copied from [vehiclesForProduct].
  VehiclesForProductProvider({
    required String partCategory,
    String engineSize = '',
    String fuelType = '',
    int yearRangeStart = 2000,
    int yearRangeEnd = 2020,
  }) : this._internal(
         (ref) => vehiclesForProduct(
           ref as VehiclesForProductRef,
           partCategory: partCategory,
           engineSize: engineSize,
           fuelType: fuelType,
           yearRangeStart: yearRangeStart,
           yearRangeEnd: yearRangeEnd,
         ),
         from: vehiclesForProductProvider,
         name: r'vehiclesForProductProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$vehiclesForProductHash,
         dependencies: VehiclesForProductFamily._dependencies,
         allTransitiveDependencies:
             VehiclesForProductFamily._allTransitiveDependencies,
         partCategory: partCategory,
         engineSize: engineSize,
         fuelType: fuelType,
         yearRangeStart: yearRangeStart,
         yearRangeEnd: yearRangeEnd,
       );

  VehiclesForProductProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.partCategory,
    required this.engineSize,
    required this.fuelType,
    required this.yearRangeStart,
    required this.yearRangeEnd,
  }) : super.internal();

  final String partCategory;
  final String engineSize;
  final String fuelType;
  final int yearRangeStart;
  final int yearRangeEnd;

  @override
  Override overrideWith(
    FutureOr<List<VehicleForProductModel>> Function(
      VehiclesForProductRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VehiclesForProductProvider._internal(
        (ref) => create(ref as VehiclesForProductRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        partCategory: partCategory,
        engineSize: engineSize,
        fuelType: fuelType,
        yearRangeStart: yearRangeStart,
        yearRangeEnd: yearRangeEnd,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VehicleForProductModel>>
  createElement() {
    return _VehiclesForProductProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehiclesForProductProvider &&
        other.partCategory == partCategory &&
        other.engineSize == engineSize &&
        other.fuelType == fuelType &&
        other.yearRangeStart == yearRangeStart &&
        other.yearRangeEnd == yearRangeEnd;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, partCategory.hashCode);
    hash = _SystemHash.combine(hash, engineSize.hashCode);
    hash = _SystemHash.combine(hash, fuelType.hashCode);
    hash = _SystemHash.combine(hash, yearRangeStart.hashCode);
    hash = _SystemHash.combine(hash, yearRangeEnd.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehiclesForProductRef
    on AutoDisposeFutureProviderRef<List<VehicleForProductModel>> {
  /// The parameter `partCategory` of this provider.
  String get partCategory;

  /// The parameter `engineSize` of this provider.
  String get engineSize;

  /// The parameter `fuelType` of this provider.
  String get fuelType;

  /// The parameter `yearRangeStart` of this provider.
  int get yearRangeStart;

  /// The parameter `yearRangeEnd` of this provider.
  int get yearRangeEnd;
}

class _VehiclesForProductProviderElement
    extends AutoDisposeFutureProviderElement<List<VehicleForProductModel>>
    with VehiclesForProductRef {
  _VehiclesForProductProviderElement(super.provider);

  @override
  String get partCategory =>
      (origin as VehiclesForProductProvider).partCategory;
  @override
  String get engineSize => (origin as VehiclesForProductProvider).engineSize;
  @override
  String get fuelType => (origin as VehiclesForProductProvider).fuelType;
  @override
  int get yearRangeStart =>
      (origin as VehiclesForProductProvider).yearRangeStart;
  @override
  int get yearRangeEnd => (origin as VehiclesForProductProvider).yearRangeEnd;
}

String _$quickVehicleSearchHash() =>
    r'c453c7da342ece9b8fc8457520b5d8b64a85df91';

/// مزود البحث السريع
///
/// Copied from [quickVehicleSearch].
@ProviderFor(quickVehicleSearch)
const quickVehicleSearchProvider = QuickVehicleSearchFamily();

/// مزود البحث السريع
///
/// Copied from [quickVehicleSearch].
class QuickVehicleSearchFamily
    extends Family<AsyncValue<List<VehicleSearchResultModel>>> {
  /// مزود البحث السريع
  ///
  /// Copied from [quickVehicleSearch].
  const QuickVehicleSearchFamily();

  /// مزود البحث السريع
  ///
  /// Copied from [quickVehicleSearch].
  QuickVehicleSearchProvider call(String query) {
    return QuickVehicleSearchProvider(query);
  }

  @override
  QuickVehicleSearchProvider getProviderOverride(
    covariant QuickVehicleSearchProvider provider,
  ) {
    return call(provider.query);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'quickVehicleSearchProvider';
}

/// مزود البحث السريع
///
/// Copied from [quickVehicleSearch].
class QuickVehicleSearchProvider
    extends AutoDisposeFutureProvider<List<VehicleSearchResultModel>> {
  /// مزود البحث السريع
  ///
  /// Copied from [quickVehicleSearch].
  QuickVehicleSearchProvider(String query)
    : this._internal(
        (ref) => quickVehicleSearch(ref as QuickVehicleSearchRef, query),
        from: quickVehicleSearchProvider,
        name: r'quickVehicleSearchProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$quickVehicleSearchHash,
        dependencies: QuickVehicleSearchFamily._dependencies,
        allTransitiveDependencies:
            QuickVehicleSearchFamily._allTransitiveDependencies,
        query: query,
      );

  QuickVehicleSearchProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<List<VehicleSearchResultModel>> Function(
      QuickVehicleSearchRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: QuickVehicleSearchProvider._internal(
        (ref) => create(ref as QuickVehicleSearchRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VehicleSearchResultModel>>
  createElement() {
    return _QuickVehicleSearchProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is QuickVehicleSearchProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin QuickVehicleSearchRef
    on AutoDisposeFutureProviderRef<List<VehicleSearchResultModel>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _QuickVehicleSearchProviderElement
    extends AutoDisposeFutureProviderElement<List<VehicleSearchResultModel>>
    with QuickVehicleSearchRef {
  _QuickVehicleSearchProviderElement(super.provider);

  @override
  String get query => (origin as QuickVehicleSearchProvider).query;
}

String _$vehicleSearchStateHash() =>
    r'24de1105a42c0802497cc23e8e1c13e399611170';

/// مزود حالة البحث التدريجي
///
/// Copied from [VehicleSearchState].
@ProviderFor(VehicleSearchState)
final vehicleSearchStateProvider =
    AutoDisposeNotifierProvider<
      VehicleSearchState,
      VehicleSearchStateData
    >.internal(
      VehicleSearchState.new,
      name: r'vehicleSearchStateProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$vehicleSearchStateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$VehicleSearchState = AutoDisposeNotifier<VehicleSearchStateData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
