// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'engine_specifications_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$completeEngineHash() => r'912c31959e94477ccb456f6e3eaef9fb83fab7f5';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// مقدم المحرك الكامل (مواصفات + أداء)
/// Complete Engine Provider (specifications + performance)
///
/// Copied from [completeEngine].
@ProviderFor(completeEngine)
const completeEngineProvider = CompleteEngineFamily();

/// مقدم المحرك الكامل (مواصفات + أداء)
/// Complete Engine Provider (specifications + performance)
///
/// Copied from [completeEngine].
class CompleteEngineFamily extends Family<AsyncValue<CompleteEngineModel?>> {
  /// مقدم المحرك الكامل (مواصفات + أداء)
  /// Complete Engine Provider (specifications + performance)
  ///
  /// Copied from [completeEngine].
  const CompleteEngineFamily();

  /// مقدم المحرك الكامل (مواصفات + أداء)
  /// Complete Engine Provider (specifications + performance)
  ///
  /// Copied from [completeEngine].
  CompleteEngineProvider call(String engineId) {
    return CompleteEngineProvider(engineId);
  }

  @override
  CompleteEngineProvider getProviderOverride(
    covariant CompleteEngineProvider provider,
  ) {
    return call(provider.engineId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'completeEngineProvider';
}

/// مقدم المحرك الكامل (مواصفات + أداء)
/// Complete Engine Provider (specifications + performance)
///
/// Copied from [completeEngine].
class CompleteEngineProvider
    extends AutoDisposeFutureProvider<CompleteEngineModel?> {
  /// مقدم المحرك الكامل (مواصفات + أداء)
  /// Complete Engine Provider (specifications + performance)
  ///
  /// Copied from [completeEngine].
  CompleteEngineProvider(String engineId)
    : this._internal(
        (ref) => completeEngine(ref as CompleteEngineRef, engineId),
        from: completeEngineProvider,
        name: r'completeEngineProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$completeEngineHash,
        dependencies: CompleteEngineFamily._dependencies,
        allTransitiveDependencies:
            CompleteEngineFamily._allTransitiveDependencies,
        engineId: engineId,
      );

  CompleteEngineProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.engineId,
  }) : super.internal();

  final String engineId;

  @override
  Override overrideWith(
    FutureOr<CompleteEngineModel?> Function(CompleteEngineRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CompleteEngineProvider._internal(
        (ref) => create(ref as CompleteEngineRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        engineId: engineId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CompleteEngineModel?> createElement() {
    return _CompleteEngineProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CompleteEngineProvider && other.engineId == engineId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, engineId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CompleteEngineRef on AutoDisposeFutureProviderRef<CompleteEngineModel?> {
  /// The parameter `engineId` of this provider.
  String get engineId;
}

class _CompleteEngineProviderElement
    extends AutoDisposeFutureProviderElement<CompleteEngineModel?>
    with CompleteEngineRef {
  _CompleteEngineProviderElement(super.provider);

  @override
  String get engineId => (origin as CompleteEngineProvider).engineId;
}

String _$engineSpecificationsHash() =>
    r'6698d73c9f4c6f24863cc803efbe8056c5305ae0';

/// مقدم مواصفات المحركات
/// Engine Specifications Provider
///
/// Copied from [EngineSpecifications].
@ProviderFor(EngineSpecifications)
final engineSpecificationsProvider =
    AutoDisposeAsyncNotifierProvider<
      EngineSpecifications,
      List<EngineSpecificationsModel>
    >.internal(
      EngineSpecifications.new,
      name: r'engineSpecificationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$engineSpecificationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$EngineSpecifications =
    AutoDisposeAsyncNotifier<List<EngineSpecificationsModel>>;
String _$enginePerformanceHash() => r'c0ad83eb32e297b54c1c57fae1b2903275af0189';

/// مقدم أداء المحرك
/// Engine Performance Provider
///
/// Copied from [EnginePerformance].
@ProviderFor(EnginePerformance)
final enginePerformanceProvider =
    AutoDisposeAsyncNotifierProvider<
      EnginePerformance,
      List<EnginePerformanceModel>
    >.internal(
      EnginePerformance.new,
      name: r'enginePerformanceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$enginePerformanceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$EnginePerformance =
    AutoDisposeAsyncNotifier<List<EnginePerformanceModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
