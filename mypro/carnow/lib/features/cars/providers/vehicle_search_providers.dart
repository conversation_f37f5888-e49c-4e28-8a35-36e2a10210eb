import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/networking/simple_api_client.dart';
import '../repositories/vehicle_search_repository.dart';
import '../models/vehicle_specifications_model.dart';
import '../models/vehicle_search_result_model.dart';

part 'vehicle_search_providers.g.dart';

/// مزود مستودع البحث التدريجي
@riverpod
VehicleSearchRepository vehicleSearchRepository(Ref ref) =>
    VehicleSearchRepository(ref.watch(simpleApiClientProvider));

/// مزود الماركات المتاحة
@riverpod
Future<List<VehicleSearchStepModel>> availableMakes(Ref ref) async =>
    ref.watch(vehicleSearchRepositoryProvider).getAvailableMakes();

/// مزود الموديلات حسب الماركة
@riverpod
Future<List<ModelsByMakeModel>> modelsByMake(
  Ref ref, {
  int? makeId,
  String? makeName,
}) async => ref
    .watch(vehicleSearchRepositoryProvider)
    .getModelsByMake(makeId: makeId, makeName: makeName);

/// مزود السنوات حسب الموديل
@riverpod
Future<List<YearsByModelModel>> yearsByModel(
  Ref ref, {
  int? modelId,
  String? makeName,
  String? modelName,
}) async => ref
    .watch(vehicleSearchRepositoryProvider)
    .getYearsByModel(
      modelId: modelId,
      makeName: makeName,
      modelName: modelName,
    );

/// مزود المواصفات النهائية
@riverpod
Future<VehicleSpecificationsModel?> vehicleSpecifications(
  Ref ref, {
  required String makeName,
  required String modelName,
  required int year,
}) async => ref
    .watch(vehicleSearchRepositoryProvider)
    .getFinalSpecifications(
      makeName: makeName,
      modelName: modelName,
      year: year,
    );

/// مزود قطع الغيار المتوافقة
@riverpod
Future<CompatiblePartsModel?> compatibleParts(
  Ref ref, {
  required String makeName,
  required String modelName,
  required int year,
  String partCategory = '',
}) async => ref
    .watch(vehicleSearchRepositoryProvider)
    .findCompatibleParts(
      makeName: makeName,
      modelName: modelName,
      year: year,
      partCategory: partCategory,
    );

/// مزود المركبات المتوافقة مع منتج معين
@riverpod
Future<List<VehicleForProductModel>> vehiclesForProduct(
  Ref ref, {
  required String partCategory,
  String engineSize = '',
  String fuelType = '',
  int yearRangeStart = 2000,
  int yearRangeEnd = 2020,
}) async => ref
    .watch(vehicleSearchRepositoryProvider)
    .getVehiclesForPart(
      partCategory: partCategory,
      engineSize: engineSize,
      fuelType: fuelType,
      yearRangeStart: yearRangeStart,
      yearRangeEnd: yearRangeEnd,
    );

/// مزود البحث السريع
@riverpod
Future<List<VehicleSearchResultModel>> quickVehicleSearch(
  Ref ref,
  String query,
) async {
  if (query.trim().isEmpty) {
    return [];
  }

  return ref.watch(vehicleSearchRepositoryProvider).quickVehicleSearch(query);
}

/// مزود حالة البحث التدريجي
@riverpod
class VehicleSearchState extends _$VehicleSearchState {
  @override
  VehicleSearchStateData build() => const VehicleSearchStateData();

  /// تحديد الماركة والانتقال للخطوة التالية
  void selectMake(VehicleSearchStepModel make) {
    state = state.copyWith(
      selectedMake: make,
      selectedModel: null,
      selectedYear: null,
      currentStep: 2,
    );
  }

  /// تحديد الموديل والانتقال للخطوة التالية
  void selectModel(ModelsByMakeModel model) {
    state = state.copyWith(
      selectedModel: model,
      selectedYear: null,
      currentStep: 3,
    );
  }

  /// تحديد السنة والانتقال للخطوة التالية
  void selectYear(YearsByModelModel year) {
    state = state.copyWith(selectedYear: year, currentStep: 4);
  }

  /// إعادة تعيين البحث
  void reset() {
    state = const VehicleSearchStateData();
  }

  /// العودة للخطوة السابقة
  void goBack() {
    final currentStep = state.currentStep;

    if (currentStep > 1) {
      switch (currentStep) {
        case 2:
          state = state.copyWith(selectedMake: null, currentStep: 1);
          break;
        case 3:
          state = state.copyWith(selectedModel: null, currentStep: 2);
          break;
        case 4:
          state = state.copyWith(selectedYear: null, currentStep: 3);
          break;
      }
    }
  }

  /// التحقق من اكتمال البحث
  bool get isSearchComplete =>
      state.currentStep == 4 &&
      state.selectedMake != null &&
      state.selectedModel != null &&
      state.selectedYear != null;
}
