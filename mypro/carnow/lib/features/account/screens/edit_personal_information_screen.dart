import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logging/logging.dart';

import 'package:dio/dio.dart' as dio;

import '../../../core/errors/app_error.dart';
import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/networking/simple_api_client.dart';
import '../../../core/providers/users_provider.dart';

import '../../../l10n/app_localizations.dart';
import '../../../shared/providers/location_provider.dart';
import '../../../shared/widgets/primary_button.dart';
import '../../../core/validators/input_validators.dart';

final _logger = Logger('EditPersonalInformationScreen');

/// شاشة تعديل البيانات الشخصية
///
/// توفر واجهة للمستخدم لتحديث معلومات ملفه الشخصي، بما في ذلك الاسم،
/// رقم الهاتف، المدينة، العنوان، وصورة الملف الشخصي.
/// تدعم الشاشة اختيار صورة من المعرض أو الكاميرا، وتعرض رسائل للمستخدم
/// عند النجاح أو الفشل في حفظ التغييرات.
class EditPersonalInformationScreen extends HookConsumerWidget {
  const EditPersonalInformationScreen({
    super.key,
    this.canGoBack = true,
    this.redirectAfterSave,
  });
  final bool canGoBack;
  final String? redirectAfterSave;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    final currentUserProfile = ref.watch(currentUserStreamProvider).value;
    final formKey = useMemoized(GlobalKey<FormState>.new);
    final nameController = useTextEditingController(
      text: currentUserProfile?.name,
    );
    final phoneController = useTextEditingController(
      text: currentUserProfile?.phone,
    );
    final selectedCityName = useState<String?>(
      currentUserProfile?.cityName,
    );
    final addressController = useTextEditingController(
      text: currentUserProfile?.address,
    );

    final profileImageFile = useState<File?>(null);
    final hasProfileImageChanged = useState(false);
    final isLoading = useState(false);
    final errorMessage = useState<String?>(null);
    final successMessage = useState<String?>(null);

    Future<void> pickImage(ImageSource source) async {
      try {
        final picker = ImagePicker();
        final pickedImage = await picker.pickImage(
          source: source,
          imageQuality: 80,
          maxWidth: 800,
        );
        if (pickedImage != null) {
          profileImageFile.value = File(pickedImage.path);
          hasProfileImageChanged.value = true;
        }
      } catch (e, st) {
        _logger.severe('Error picking image', e, st);
        errorMessage.value = l10n?.unexpectedError;
      }
    }

    void showImageSourceOptions() {
      showModalBottomSheet<void>(
        context: context,
        builder: (ctx) => SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: Text(l10n?.gallery ?? 'Gallery'),
                onTap: () {
                  Navigator.of(ctx).pop();
                  pickImage(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: Text(l10n?.camera ?? 'Camera'),
                onTap: () {
                  Navigator.of(ctx).pop();
                  pickImage(ImageSource.camera);
                },
              ),
              if (profileImageFile.value != null ||
                  (currentUserProfile?.profileImageUrl?.isNotEmpty ??
                      false))
                ListTile(
                  leading: Icon(Icons.delete, color: theme.colorScheme.error),
                  title: Text(
                    l10n?.removeImage ?? 'Remove Image',
                    style: TextStyle(color: theme.colorScheme.error),
                  ),
                  onTap: () {
                    profileImageFile.value = null;
                    hasProfileImageChanged.value = true;
                    Navigator.of(ctx).pop();
                  },
                ),
            ],
          ),
        ),
      );
    }

    ImageProvider getProfileImage() {
      if (profileImageFile.value != null) {
        return FileImage(profileImageFile.value!);
      }
      if (currentUserProfile?.profileImageUrl != null &&
          currentUserProfile!.profileImageUrl!.isNotEmpty) {
        return NetworkImage(currentUserProfile.profileImageUrl!);
      }
      return const AssetImage('assets/images/placeholder.png');
    }

    Future<void> saveProfile() async {
      if (!(formKey.currentState?.validate() ?? false)) return;

      isLoading.value = true;
      errorMessage.value = null;
      successMessage.value = null;

      try {
        final user = ref.read(currentUserProvider);
        if (user == null) {
          throw const AppError.authentication(message: 'User not found.');
        }

        String? newImageUrl;
        if (hasProfileImageChanged.value) {
          final imageFile = profileImageFile.value;
          if (imageFile != null) {
            // Upload image using Go backend
            final apiClient = ref.read(simpleApiClientProvider);
                                        final response = await apiClient.uploadFormData<Map<String, dynamic>>(
                '/storage/upload',
                dio.FormData.fromMap({
                'file': await dio.MultipartFile.fromFile(imageFile.path),
                'folder': 'profile_images',
              }),
            );
            
            if (response.isSuccess && response.data != null) {
              newImageUrl = response.data!['url'] as String?;
            }
          } else {
            // Image was removed
            newImageUrl = '';
          }
        }

        // Update profile using Go backend
        final apiClient = ref.read(simpleApiClientProvider);
        final updates = {
          'full_name': nameController.text.trim(),
          'phone': phoneController.text.trim(),
          'store_city': selectedCityName.value,
          'address': addressController.text.trim(),
          if (newImageUrl != null) 'avatar_url': newImageUrl,
        };

                      final response = await apiClient.put<Map<String, dynamic>>(
          '/user/profile',
          data: updates,
        );

        if (!response.isSuccess) {
          throw Exception('Failed to update profile: ${response.message}');
        }

        // Refresh user data
        ref.invalidate(currentUserProvider);
        successMessage.value = l10n?.profileUpdatedSuccessfully;
        
        if (context.mounted) {
          if (redirectAfterSave != null) {
            context.go(redirectAfterSave!);
          } else if (canGoBack) {
            context.pop();
          }
        }
      } catch (e) {
        _logger.severe('Failed to update profile', e);
        errorMessage.value = 'Error saving profile data. Please try again.';
      } finally {
        isLoading.value = false;
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.editProfile ?? 'Edit Profile'),
        leading: canGoBack
            ? null
            : IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => context.go('/'),
              ),
      ),
      body: currentUserProfile == null
          ? Center(child: Text(l10n?.userNotFound ?? 'User not found.'))
          : Form(
            key: formKey,
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                Center(
                  child: Stack(
                    children: [
                      CircleAvatar(
                        radius: 50,
                        backgroundImage: getProfileImage(),
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: IconButton(
                          icon: const Icon(Icons.camera_alt),
                          onPressed: showImageSourceOptions,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                TextFormField(
                  controller: nameController,
                  decoration: InputDecoration(labelText: l10n?.fullName),
                  validator: (value) =>
                      InputValidators.validateName(value, l10n),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: phoneController,
                  decoration: InputDecoration(labelText: l10n?.phoneNumber),
                  keyboardType: TextInputType.phone,
                  validator: (value) =>
                      InputValidators.validatePhone(value, l10n),
                ),
                const SizedBox(height: 16),

                // City Dropdown
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Consumer(
                    builder: (context, ref, child) {
                      final citiesAsync = ref.watch(citiesProvider);
                      return citiesAsync.when(
                        loading: () =>
                            const Center(child: CircularProgressIndicator()),
                        error: (err, stack) =>
                            Center(child: Text('Error: $err')),
                        data: (cities) {
                          return DropdownButtonFormField<String>(
                            value: selectedCityName.value,
                            items: cities.map((city) {
                              return DropdownMenuItem<String>(
                                value: city.nameArabic,
                                child: Text(city.nameArabic),
                              );
                            }).toList(),
                            onChanged: (value) {
                              selectedCityName.value = value;
                            },
                            decoration: const InputDecoration(
                              labelText: 'المدينة',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الرجاء اختيار المدينة';
                              }
                              return null;
                            },
                          );
                        },
                      );
                    },
                  ),
                ),
                const SizedBox(height: 16),

                // Detailed Address Field
                TextFormField(
                  controller: addressController,
                  decoration: const InputDecoration(
                    labelText: 'العنوان التفصيلي',
                  ),
                  validator: (value) {
                    // Optional field
                    return null;
                  },
                ),
                const SizedBox(height: 32),
                if (isLoading.value)
                  const Center(child: CircularProgressIndicator())
                else
                  PrimaryButton(
                    onPressed: saveProfile,
                    text: l10n?.saveChanges ?? 'Save Changes',
                  ),
                if (errorMessage.value != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Text(
                      errorMessage.value!,
                      style: TextStyle(color: theme.colorScheme.error),
                      textAlign: TextAlign.center,
                    ),
                  ),
                if (successMessage.value != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Text(
                      successMessage.value!,
                      style: TextStyle(color: theme.colorScheme.primary),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),
    );
  }
}
