import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/networking/simple_api_client.dart';
import '../../../core/providers/users_provider.dart';

import '../../../shared/widgets/primary_button.dart';
import '../../../../core/widgets/loading_indicators.dart';
import '../../../l10n/app_localizations.dart';
import '../../../core/models/city_model.dart';
import '../../../shared/providers/location_provider.dart';

/// شاشة إكمال البيانات الشخصية بعد تسجيل الدخول باستخدام Google
class ProfileCompletionScreen extends HookConsumerWidget {
  const ProfileCompletionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final currentUserAsync = ref.watch(currentUserStreamProvider);
    final currentUser = currentUserAsync.value;

    final nameController = useTextEditingController();
    final phoneController = useTextEditingController();
    final addressController = useTextEditingController();
    final selectedCityId = useState<int?>(null);
    final isLoading = useState(false);
    final formKey = useRef(GlobalKey<FormState>());

    // تعبئة البيانات الحالية إذا كانت متوفرة
    useEffect(() {
      final user = currentUser;
      if (user != null) {
        nameController.text = user.name ?? '';
        phoneController.text = user.phone ?? '';
        selectedCityId.value = user.cityId;
        addressController.text = user.address ?? '';
      }
      return null;
    });

    Future<void> completeProfile() async {
      if (!formKey.value.currentState!.validate()) return;

      isLoading.value = true;
      try {
        // استخدام SimpleApiClient للتحديث
        final apiClient = ref.read(simpleApiClientProvider);
        final user = currentUser;
        if (user == null) throw Exception('User not found');

        // تحديث الملف الشخصي عبر Go backend
                      final response = await apiClient.put<Map<String, dynamic>>(
          '/user/profile',
          data: {
            'full_name': nameController.text.trim(),
            'phone': phoneController.text.trim(),
            'city': selectedCityId.value,
            'address': addressController.text.trim(),
          },
        );
        
        if (!response.isSuccess) {
          throw Exception('Failed to update profile: ${response.message}');
        }

        // Refresh the current user provider
        ref.invalidate(currentUserProvider);

        // Navigate to home after successful profile completion
        if (context.mounted) {
          context.go('/');
        }
      } catch (error) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('فشل في حفظ البيانات الشخصية'),
              backgroundColor: theme.colorScheme.error,
            ),
          );
        }
      } finally {
        isLoading.value = false;
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.completeProfileTitle),
        centerTitle: true,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: formKey.value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // العنوان والوصف
                Consumer(
                  builder: (context, ref, child) {
                    final userAsync = ref.watch(currentUserStreamProvider);
                    final user = userAsync.value;
                    final hasExistingData = user?.name?.isNotEmpty == true;

                    return Column(
                      children: [
                        Text(
                          hasExistingData
                              ? 'تحديث البيانات الشخصية'
                              : l10n.completeProfileWelcome,
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          hasExistingData
                              ? 'يمكنك مراجعة وتحديث بياناتك الشخصية أدناه.'
                              : 'لتحسين تجربتك في التطبيق، يرجى إكمال بياناتك الشخصية أدناه.',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(
                              alpha: 0.70,
                            ),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 32),

                // حقل الاسم
                TextFormField(
                  controller: nameController,
                  decoration: InputDecoration(
                    labelText: l10n.fullName,
                    hintText: l10n.enterFullName,
                    prefixIcon: const Icon(Icons.person_outline),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  textCapitalization: TextCapitalization.words,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'الاسم مطلوب';
                    }
                    if (value.trim().length < 2) {
                      return 'الاسم قصير جداً';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // حقل رقم الهاتف
                TextFormField(
                  controller: phoneController,
                  decoration: InputDecoration(
                    labelText: l10n.phoneNumber,
                    hintText: l10n.enterPhoneNumber,
                    prefixIcon: const Icon(Icons.phone_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  keyboardType: TextInputType.phone,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  validator: (value) {
                    // جعل رقم الهاتف اختياري للمستخدمين الموجودين
                    if (value != null &&
                        value.trim().isNotEmpty &&
                        value.trim().length < 8) {
                      return 'رقم هاتف غير صالح';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // City Dropdown
                Consumer(
                  builder: (context, ref, child) {
                    final citiesAsync = ref.watch(citiesProvider);
                    return citiesAsync.when(
                      loading: () => Center(child: LoadingIndicators.primary()),
                      error: (err, stack) => Center(child: Text('خطأ: $err')),
                      data: (cities) {
                        return DropdownButtonFormField<int>(
                          value: selectedCityId.value,
                          items: cities.map((City city) {
                            return DropdownMenuItem<int>(
                              value: city.id,
                              child: Text(city.nameArabic),
                            );
                          }).toList(),
                          onChanged: (int? newValue) {
                            selectedCityId.value = newValue;
                          },
                          decoration: InputDecoration(
                            labelText: l10n.location,
                            hintText: 'اختر مدينتك',
                            prefixIcon: const Icon(Icons.location_on_outlined),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          validator: (value) {
                            // Can be made mandatory if needed
                            // if (value == null) {
                            //   return 'الرجاء اختيار المدينة';
                            // }
                            return null;
                          },
                        );
                      },
                    );
                  },
                ),

                const SizedBox(height: 16),

                // Detailed Address Field
                TextFormField(
                  controller: addressController,
                  decoration: InputDecoration(
                    labelText: 'العنوان التفصيلي',
                    hintText: 'مثال: شارع الجمهورية، عمارة 5',
                    prefixIcon: const Icon(Icons.maps_home_work_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  textCapitalization: TextCapitalization.sentences,
                  validator: (value) {
                    // Make it optional for now, can be changed later
                    return null;
                  },
                ),

                const Spacer(),

                // أزرار الحفظ والتخطي
                Consumer(
                  builder: (context, ref, child) {
                    final userAsync = ref.watch(currentUserStreamProvider);
                    final user = userAsync.value;
                    final hasExistingData = user?.name?.isNotEmpty == true;

                    return Column(
                      children: [
                        PrimaryButton(
                          onPressed: isLoading.value ? null : completeProfile,
                          isLoading: isLoading.value,
                          text: hasExistingData
                              ? 'حفظ التغييرات'
                              : l10n.saveAndContinue,
                        ),
                        if (hasExistingData) ...[
                          const SizedBox(height: 12),
                          TextButton(
                            onPressed: () {
                              context.go('/login');
                            },
                            child: const Text('العودة'),
                          ),
                        ],
                      ],
                    );
                  },
                ),
                const SizedBox(height: 16),

                // نص إرشادي
                Text(
                  'ستساعدنا هذه البيانات في تقديم خدمة أفضل لك.',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(
                      (0.6 * 255).toInt(),
                    ),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
