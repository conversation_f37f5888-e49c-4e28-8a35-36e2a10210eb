import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/networking/simple_api_client.dart';
import '../models/user_model.dart';

part 'account_provider.g.dart';

/// مزود لإدارة بيانات الحساب وملف المستخدم الشخصي - CLEAN: Uses HTTP API only
@riverpod
class AccountNotifier extends _$AccountNotifier {
  final _logger = Logger('AccountNotifier');

  @override
  Future<UserModel?> build() async {
    final isAuthenticated = ref.read(isAuthenticatedProvider);

    if (!isAuthenticated) {
      _logger.info('No authenticated user found');
      return null;
    }

    try {
      return await _fetchProfile();
    } catch (e) {
      _logger.severe('Error loading user profile', e);
      throw Exception('Failed to load user profile: $e');
    }
  }

  Future<UserModel?> _fetchProfile() async {
    final client = ref.read(simpleApiClientProvider);

    try {
      final response = await client.get<Map<String, dynamic>>('/user/profile');
      
      if (!response.isSuccess || response.data == null) {
        return null;
      }

      return UserModel.fromJson(response.data!);
    } catch (e) {
      _logger.warning('Error fetching user profile', e);
      return null;
    }
  }

  /// تحديث ملف المستخدم الشخصي
  Future<void> updateUserProfile({
    String? name,
    String? phone,
    int? cityId,
    String? address,
    String? profileImageUrl,
  }) async {
    final client = ref.read(simpleApiClientProvider);
    final isAuthenticated = ref.read(isAuthenticatedProvider);

    if (!isAuthenticated) {
      throw Exception('User not authenticated');
    }

    final updates = <String, dynamic>{};
    if (name != null) updates['name'] = name;
    if (phone != null) updates['phone'] = phone;
    if (cityId != null) updates['city_id'] = cityId;
    if (address != null) updates['address'] = address;
    if (profileImageUrl != null) updates['profile_image_url'] = profileImageUrl;

    if (updates.isEmpty) {
      _logger.info('No changes to update');
      return;
    }

    try {
      await client.put('/user/profile', data: updates);

      // Invalidate to trigger refetch
      ref.invalidateSelf();

      _logger.info('Profile updated successfully');
    } catch (e) {
      _logger.severe('Error updating profile', e);
      throw Exception('Failed to update profile: $e');
    }
  }

  /// حذف الحساب
  Future<void> deleteAccount() async {
    final client = ref.read(simpleApiClientProvider);
    final isAuthenticated = ref.read(isAuthenticatedProvider);

    if (!isAuthenticated) {
      throw Exception('User not authenticated');
    }

    try {
      await client.delete('/user/profile');

      // Sign out after successful deletion
      await ref.read(unifiedAuthProviderProvider.notifier).signOut();

      _logger.info('Account deleted successfully');
    } catch (e) {
      _logger.severe('Error deleting account', e);
      throw Exception('Failed to delete account: $e');
    }
  }
}
