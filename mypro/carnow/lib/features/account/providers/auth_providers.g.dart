// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentUserStreamHash() => r'4373060723088218e1209cc14204bd68f829e74c';

/// Compatibility provider that converts the new User model to the old UserModel
/// format that the account screen expects
///
/// Copied from [currentUserStream].
@ProviderFor(currentUserStream)
final currentUserStreamProvider =
    AutoDisposeStreamProvider<UserModel?>.internal(
      currentUserStream,
      name: r'currentUserStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentUserStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserStreamRef = AutoDisposeStreamProviderRef<UserModel?>;
String _$currentUserHash() => r'd06b1e3852958d0b2df4ab31281f8e603effd93f';

/// Provider for the current user as UserModel (compatibility)
///
/// Copied from [currentUser].
@ProviderFor(currentUser)
final currentUserProvider = AutoDisposeProvider<UserModel?>.internal(
  currentUser,
  name: r'currentUserProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentUserHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserRef = AutoDisposeProviderRef<UserModel?>;
String _$isAuthenticatedHash() => r'1e024fd5621d8ed202f211770fa6591780a9ba89';

/// Provider for authentication status (compatibility)
///
/// Copied from [isAuthenticated].
@ProviderFor(isAuthenticated)
final isAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  isAuthenticated,
  name: r'isAuthenticatedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsAuthenticatedRef = AutoDisposeProviderRef<bool>;
String _$nativeGoogleAuthNotifierHash() =>
    r'0cc42693753a2c6a924036e224051a7977fc7fb7';

/// See also [NativeGoogleAuthNotifier].
@ProviderFor(NativeGoogleAuthNotifier)
final nativeGoogleAuthNotifierProvider =
    AutoDisposeStreamNotifierProvider<NativeGoogleAuthNotifier, User?>.internal(
      NativeGoogleAuthNotifier.new,
      name: r'nativeGoogleAuthNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$nativeGoogleAuthNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$NativeGoogleAuthNotifier = AutoDisposeStreamNotifier<User?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
