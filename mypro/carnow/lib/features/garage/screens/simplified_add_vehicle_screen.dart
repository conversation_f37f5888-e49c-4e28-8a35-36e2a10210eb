import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/utils/error_handler.dart';
import '../../../core/widgets/error_boundary.dart';
import '../../../shared/widgets/primary_button.dart';
import '../models/vehicle_data_models.dart';
import '../providers/garage_provider.dart';
import '../../admin_tools/vehicles/providers/vehicle_list_providers.dart'
    as admin_providers;
import '../../admin_tools/vehicles/models/models.dart' as admin_models;
import '../models/user_vehicle.dart';
import '../../../core/auth/unified_auth_provider.dart';

final _logger = Logger('AddVehicleScreen');

/// شاشة مبسطة وموحدة لإضافة سيارة جديدة
class SimplifiedAddVehicleScreen extends HookConsumerWidget {
  const SimplifiedAddVehicleScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(GlobalKey<FormState>.new);
    final isLoading = useState(false);

    // Selected vehicle data states
    final selectedMake = useState<VehicleMake?>(null);
    final selectedModel = useState<VehicleModel?>(null);
    final selectedGeneration = useState<admin_models.VehicleGeneration?>(null);
    final yearController = useTextEditingController();
    final mileageController = useTextEditingController();
    final vinController = useTextEditingController();
    final colorController = useTextEditingController();
    final plateNumberController = useTextEditingController();
    final notesController = useTextEditingController();

    // Use dynamic vehicle data from admin tools
    final makesAsync = ref.watch(admin_providers.vehicleMakesProvider);
    final modelsAsync = selectedMake.value != null
        ? ref.watch(
            admin_providers.vehicleModelsProvider(
              makeId: selectedMake.value!.id,
            ),
          )
        : const AsyncValue.data(<admin_models.VehicleModel>[]);
    final generationsAsync = selectedModel.value != null
        ? ref.watch(
            admin_providers.vehicleGenerationsProvider(
              modelId: selectedModel.value!.id,
            ),
          )
        : const AsyncValue.data(<admin_models.VehicleGeneration>[]);

    // Effect to update year field when generation is selected
    useEffect(() {
      yearController.text =
          selectedGeneration.value?.yearStart.toString() ?? '';
      return null;
    });

    void resetModel() {
      selectedModel.value = null;
      selectedGeneration.value = null;
    }

    void resetGeneration() {
      selectedGeneration.value = null;
    }

    Future<void> submitForm() async {
      if (!formKey.currentState!.validate()) return;

      if (selectedMake.value == null ||
          selectedModel.value == null ||
          selectedGeneration.value == null) {
        if (!context.mounted) return;
        ErrorHandler.showWarning(
          context,
          'Please complete all required fields',
        );
        return;
      }

      isLoading.value = true;

      try {
        final currentUser = ref.read(currentUserProvider);
        if (currentUser == null) {
          throw Exception('User not authenticated');
        }

        final newVehicle = UserVehicle(
          userId: currentUser.id,
          make: selectedMake.value!.name,
          model: selectedModel.value!.name,
          year: selectedGeneration.value!.yearStart,
          trim: selectedGeneration.value!.generationName,
          vin: vinController.text.isEmpty ? null : vinController.text,
          mileage: mileageController.text.isEmpty
              ? null
              : int.tryParse(mileageController.text),
        );

        await ref.read(garageDataProvider.notifier).addVehicle(newVehicle);

        if (!context.mounted) return;
        ErrorHandler.showSuccessMessage(context, 'Vehicle added successfully!');
        if (context.mounted) {
          context.pop();
        }
      } catch (e, st) {
        _logger.severe('Failed to add vehicle', e, st);
        if (!context.mounted) return;
        ErrorHandler.handleError(
          context,
          e,
          customMessage: 'Could not add the vehicle. Please try again.',
        );
      } finally {
        isLoading.value = false;
      }
    }

    return Scaffold(
      appBar: AppBar(title: const Text('Add a New Vehicle')),
      body: Form(
        key: formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              ErrorBoundary(
                asyncValue: makesAsync,
                dataBuilder: (makes) {
                  // Cast makes to the correct type
                  final vehicleMakes = makes
                      .map((make) => VehicleMake(id: make.id, name: make.name))
                      .toList();

                  return DropdownButtonFormField<VehicleMake>(
                    value: selectedMake.value,
                    decoration: const InputDecoration(labelText: 'Make'),
                    hint: const Text('Select Make'),
                    isExpanded: true,
                    items: vehicleMakes.map((make) {
                      return DropdownMenuItem(
                        value: make,
                        child: Text(make.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != selectedMake.value) {
                        selectedMake.value = value;
                        resetModel();
                      }
                    },
                    validator: (value) =>
                        value == null ? 'Please select a make' : null,
                  );
                },
              ),
              const SizedBox(height: 16),
              ErrorBoundary(
                asyncValue: modelsAsync,
                dataBuilder: (models) {
                  // Cast models to the correct type
                  final vehicleModels = models
                      .map(
                        (model) => VehicleModel(
                          id: model.id,
                          makeId: model.makeId,
                          name: model.name,
                          yearStart: model.yearStart,
                          yearEnd: model.yearEnd,
                        ),
                      )
                      .toList();

                  return DropdownButtonFormField<VehicleModel>(
                    value: selectedModel.value,
                    decoration: const InputDecoration(labelText: 'Model'),
                    hint: const Text('Select Model'),
                    isExpanded: true,
                    items: vehicleModels.map((model) {
                      return DropdownMenuItem(
                        value: model,
                        child: Text(model.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != selectedModel.value) {
                        selectedModel.value = value;
                        resetGeneration();
                      }
                    },
                    validator: (value) =>
                        value == null ? 'Please select a model' : null,
                  );
                },
              ),
              const SizedBox(height: 16),
              if (selectedModel.value != null)
                DropdownButtonFormField<admin_models.VehicleGeneration>(
                  value: selectedGeneration.value,
                  decoration: const InputDecoration(labelText: 'Generation'),
                  hint: const Text('Select Generation'),
                  isExpanded: true,
                  items: generationsAsync.when(
                    data: (generations) => generations.map((generation) {
                      return DropdownMenuItem(
                        value: generation,
                        child: Text(generation.yearStart.toString()),
                      );
                    }).toList(),
                    loading: () => const [
                      DropdownMenuItem(child: Text('Loading generations...')),
                    ],
                    error: (e, st) => [
                      DropdownMenuItem(
                        child: Text('Error loading generations: $e'),
                      ),
                    ],
                  ),
                  onChanged: (value) {
                    selectedGeneration.value = value;
                  },
                  validator: (value) =>
                      value == null ? 'Please select a generation' : null,
                ),
              const SizedBox(height: 16),
              TextFormField(
                controller: yearController,
                decoration: const InputDecoration(labelText: 'Year'),
                readOnly: true,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: mileageController,
                decoration: const InputDecoration(labelText: 'Mileage'),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: vinController,
                decoration: const InputDecoration(labelText: 'VIN'),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: colorController,
                decoration: const InputDecoration(labelText: 'Color'),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: plateNumberController,
                decoration: const InputDecoration(labelText: 'Plate Number'),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: notesController,
                decoration: const InputDecoration(labelText: 'Notes'),
                maxLines: null,
              ),
              const SizedBox(height: 32),
              PrimaryButton(
                text: 'Add Vehicle',
                onPressed: submitForm,
                isLoading: isLoading.value,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
