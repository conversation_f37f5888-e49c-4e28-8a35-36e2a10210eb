import '../../../core/networking/simple_api_client.dart';
import '../../../core/errors/failure.dart';
import '../models/user_vehicle.dart';

class GarageRepository {
  GarageRepository(this._apiClient);
  final SimpleApiClient _apiClient;

  Future<void> addVehicle(UserVehicle vehicle) async {
    try {
      final response = await _apiClient.post('/garage/vehicles', data: vehicle.toJson());
      if (!response.isSuccess) {
        throw ServerFailure(message: 'Failed to add vehicle: ${response.error}');
      }
    } catch (e) {
      throw ServerFailure(message: 'Failed to add vehicle: $e');
    }
  }

  Future<List<UserVehicle>> getUserVehicles(String userId) async {
    try {
      final response = await _apiClient.get('/garage/vehicles/$userId');
      
      if (response.isSuccess && response.data != null) {
        return (response.data as List)
            .map((item) => UserVehicle.fromJson(item))
            .toList();
      } else {
        throw ServerFailure(message: 'Failed to get user vehicles: ${response.error}');
      }
    } catch (e) {
      throw ServerFailure(message: 'Failed to get user vehicles: $e');
    }
  }

  Future<void> updateVehicle(UserVehicle vehicle) async {
    try {
      if (vehicle.id == null) {
        throw const ServerFailure(message: 'Vehicle ID is required for update');
      }
      final response = await _apiClient.put('/garage/vehicles/${vehicle.id}', data: vehicle.toJson());
      if (!response.isSuccess) {
        throw ServerFailure(message: 'Failed to update vehicle: ${response.error}');
      }
    } catch (e) {
      throw ServerFailure(message: 'Failed to update vehicle: $e');
    }
  }

  Future<void> removeVehicle(String vehicleId) async {
    try {
      // Soft delete
      final response = await _apiClient.delete('/garage/vehicles/$vehicleId');
      if (!response.isSuccess) {
        throw ServerFailure(message: 'Failed to remove vehicle: ${response.error}');
      }
    } catch (e) {
      throw ServerFailure(message: 'Failed to remove vehicle: $e');
    }
  }

  Future<void> setDefaultVehicle(String userId, String vehicleId) async {
    try {
      final response = await _apiClient.put('/garage/vehicles/$vehicleId/set-default', data: {
        'user_id': userId,
      });
      if (!response.isSuccess) {
        throw ServerFailure(message: 'Failed to set default vehicle: ${response.error}');
      }
    } catch (e) {
      throw ServerFailure(message: 'Failed to set default vehicle: $e');
    }
  }
}
