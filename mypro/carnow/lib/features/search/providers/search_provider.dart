import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';
import 'package:carnow/features/products/models/product_model.dart';

import '../../../core/networking/simple_api_client.dart';

part 'search_provider.g.dart';

final _logger = Logger('SearchProvider');

/// مزود يحفظ آخر استعلامات البحث
@riverpod
class RecentSearches extends _$RecentSearches {
  @override
  List<String> build() => [];

  void addSearch(String query) {
    if (query.trim().isEmpty) return;

    // إزالة البحث إذا كان موجودًا سابقًا (لمنع التكرار)
    final newState = state.where((item) => item != query).toList();

    // إضافة البحث الجديد في بداية القائمة
    state = [query, ...newState];

    // الاحتفاظ بأحدث 10 عمليات بحث فقط
    if (state.length > 10) {
      state = state.sublist(0, 10);
    }
  }

  void clearSearches() {
    state = [];
  }

  void removeSearch(String query) {
    state = state.where((item) => item != query).toList();
  }
}

/// مزود يحفظ عمليات البحث المحفوظة
@riverpod
class SavedSearches extends _$SavedSearches {
  @override
  List<String> build() => [];

  void toggleSavedSearch(String query) {
    if (query.trim().isEmpty) return;

    if (state.contains(query)) {
      // إزالة البحث إذا كان محفوظًا بالفعل
      state = state.where((item) => item != query).toList();
    } else {
      // إضافة البحث إلى المحفوظات
      state = [...state, query];
    }
  }

  bool isSearchSaved(String query) => state.contains(query);

  void clearSavedSearches() {
    state = [];
  }
}

/// مزود نتائج البحث استنادًا إلى استعلام
@riverpod
Future<List<ProductModel>> searchResults(SearchResultsRef ref, String query) async {
  if (query.trim().isEmpty) {
    return [];
  }

  _logger.info('البحث عن منتجات بالاستعلام: "$query"');

  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    // البحث عبر API الخلفي
    final response = await apiClient.get(
      '/api/products/search',
      queryParameters: {
        'q': query,
        'limit': 50,
      },
    );

    final products = (response.data['products'] as List)
        .map((json) => ProductModel.fromJson(json))
        .toList();

    _logger.info(
      'تم العثور على ${products.length} منتج للاستعلام: "$query"',
    );

    // إضافة الاستعلام إلى عمليات البحث الأخيرة
    ref.read(recentSearchesProvider.notifier).addSearch(query);

    return products;
  } catch (e, stack) {
    _logger.severe('خطأ في البحث عن المنتجات:', e, stack);
    // إرجاع قائمة فارغة في حالة الخطأ
    return [];
  }
}

/// مزود البحث في المركبات
@riverpod
Future<List<Map<String, dynamic>>> vehicleSearch(VehicleSearchRef ref, String query) async {
  if (query.trim().isEmpty) {
    return [];
  }

  _logger.info('البحث عن مركبات بالاستعلام: "$query"');

  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    // البحث عبر API الخلفي
    final response = await apiClient.get(
      '/api/vehicles/search',
      queryParameters: {
        'q': query,
        'limit': 20,
      },
    );

    final vehicles = response.data['vehicles'] as List<Map<String, dynamic>>;

    _logger.info(
      'تم العثور على ${vehicles.length} مركبة للاستعلام: "$query"',
    );

    return vehicles;
  } catch (e, stack) {
    _logger.severe('خطأ في البحث عن المركبات:', e, stack);
    // إرجاع قائمة فارغة في حالة الخطأ
    return [];
  }
}

/// مزود البحث في قطع الغيار - Clean implementation
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
@riverpod
Future<List<Map<String, dynamic>>> partsSearch(PartsSearchRef ref, String query) async {
  if (query.trim().isEmpty) {
    return [];
  }

  _logger.info('البحث عن قطع غيار بالاستعلام: "$query"');

  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    // البحث عبر Go backend API
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/search/parts',
      queryParameters: {
        'q': query.trim(),
        'limit': 30,
      },
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('فشل البحث عن قطع الغيار: ${response.message}');
      return [];
    }

    final data = response.data!;
    final parts = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data.containsKey('parts') && data['parts'] is List
            ? data['parts'] as List<dynamic>
            : <dynamic>[];

    final result = parts.map((part) => part as Map<String, dynamic>).toList();

    _logger.info('تم العثور على ${result.length} قطعة غيار للاستعلام: "$query"');

    return result;
  } catch (e, stack) {
    _logger.severe('خطأ في البحث عن قطع الغيار:', e, stack);
    return [];
  }
}

/// مزود البحث العالمي
@riverpod
Future<Map<String, List<dynamic>>> universalSearch(UniversalSearchRef ref, String query) async {
  if (query.trim().isEmpty) {
    return {
      'products': <ProductModel>[],
      'vehicles': <Map<String, dynamic>>[],
      'parts': <Map<String, dynamic>>[],
    };
  }

  _logger.info('البحث العالمي بالاستعلام: "$query"');

  final products = await ref.read(searchResultsProvider(query).future);
  final vehicles = await ref.read(vehicleSearchProvider(query).future);
  final parts = await ref.read(partsSearchProvider(query).future);

  _logger.info(
    'تم العثور على ${products.length} منتج، ${vehicles.length} مركبة، ${parts.length} قطعة غيار للاستعلام: "$query"',
  );

  return {
    'products': products,
    'vehicles': vehicles,
    'parts': parts,
  };
}
