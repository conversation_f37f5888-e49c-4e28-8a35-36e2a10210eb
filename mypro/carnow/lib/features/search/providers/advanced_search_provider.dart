import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:carnow/features/products/models/product_model.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/models/enums.dart';
import '../models/advanced_search_filters.dart';

part 'advanced_search_provider.g.dart';

enum SortOption { priceAsc, priceDesc, newest, relevance }

/// Provider for advanced search with complex filtering
@riverpod
class AdvancedSearchNotifier extends _$AdvancedSearchNotifier {
  static const int _pageSize = 20;

  @override
  Future<List<ProductModel>> build({
    String? query,
    String? make,
    String? model,
    int? yearFrom,
    int? yearTo,
    String? categoryId,
    double? priceMin,
    double? priceMax,
    ProductCondition? condition,
    SortOption sortOption = SortOption.relevance,
    int page = 0,
  }) async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);

      // Build query parameters for backend search endpoint
      final queryParams = <String, dynamic>{
        if (query != null && query.isNotEmpty) 'q': query,
        if (make != null) 'make': make,
        if (model != null) 'model': model,
        if (yearFrom != null) 'year_from': yearFrom,
        if (yearTo != null) 'year_to': yearTo,
        if (categoryId != null) 'category_id': categoryId,
        if (priceMin != null) 'price_min': priceMin,
        if (priceMax != null) 'price_max': priceMax,
        if (condition != null) 'condition': describeEnum(condition),
        'sort': describeEnum(sortOption),
        'page': page,
        'limit': _pageSize,
      };

      final response = await apiClient.getApi<Map<String, dynamic>>(
        '/search/advanced',
        queryParameters: queryParams,
      );

      if (!response.isSuccess || response.data == null) {
        throw Exception('Search failed: ${response.message}');
      }

      final data = response.data!;
      final List<dynamic> productsJson = data.containsKey('data')
          ? data['data'] as List<dynamic>
          : data as List<dynamic>;

      return productsJson
          .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e, stackTrace) {
      debugPrint('Error in advanced search: $e');
      debugPrintStack(stackTrace: stackTrace);
      throw Exception('Failed to search for products. Please try again later.');
    }
  }

  // The following private helper methods for Supabase have been removed.
  // Advanced filtering is now handled by backend endpoint.

  Future<void> refresh() async {
    ref.invalidateSelf();
  }
}

/// Advanced search provider with complex filtering capabilities
@riverpod
class AdvancedSearch extends _$AdvancedSearch {
  @override
  AdvancedSearchFilters build() => const AdvancedSearchFilters();

  void updateQuery(String query) {
    state = state.copyWith(query: query);
  }

  void updateCategory(String? categoryId) {
    state = state.copyWith(category: categoryId);
  }

  void updateCondition(String? condition) {
    state = state.copyWith(condition: condition);
  }

  void updatePriceRange(double? minPrice, double? maxPrice) {
    state = state.copyWith(priceMin: minPrice, priceMax: maxPrice);
  }

  void updateLocation(String? location) {
    state = state.copyWith(location: location);
  }

  void updateSellerId(String? sellerId) {
    state = state.copyWith(sellerId: sellerId);
  }

  void updateSortBy(String? sortBy) {
    state = state.copyWith(sortBy: sortBy ?? 'relevance');
  }

  void updateVehicleFilters({
    String? make,
    String? model,
    int? yearFrom,
    int? yearTo,
  }) {
    state = state.copyWith(
      make: make,
      model: model,
      yearFrom: yearFrom,
      yearTo: yearTo,
    );
  }

  void clearFilters() {
    state = const AdvancedSearchFilters();
  }
}

/// Provider for performing advanced search
@riverpod
Future<List<ProductModel>> performAdvancedSearch(
  Ref ref,
  AdvancedSearchFilters filter,
) async {
  if (filter.query?.isEmpty ?? true) {
    return [];
  }

  try {
    return await ref.watch(
      advancedSearchNotifierProvider(
        query: filter.query,
        make: filter.make,
        model: filter.model,
        yearFrom: filter.yearFrom,
        yearTo: filter.yearTo,
        categoryId: filter.category,
        priceMin: filter.priceMin,
        priceMax: filter.priceMax,
        condition: filter.condition != null
            ? _parseConditionFromString(filter.condition!)
            : null,
        sortOption: _parseSortOptionFromString(filter.sortBy),
      ).future,
    );
  } catch (e) {
    throw Exception('Search failed: $e');
  }
}

ProductCondition? _parseConditionFromString(String condition) {
  switch (condition.toLowerCase()) {
    case 'new':
      return ProductCondition.new_;
    case 'used':
      return ProductCondition.used;
    case 'like_new':
    case 'likenew':
      return ProductCondition.likeNew;
    case 'fair':
      return ProductCondition.fair;
    case 'poor':
      return ProductCondition.poor;
    case 'refurbished':
      return ProductCondition.refurbished;
    default:
      return null;
  }
}

SortOption _parseSortOptionFromString(String sortBy) {
  switch (sortBy) {
    case 'price_asc':
      return SortOption.priceAsc;
    case 'price_desc':
      return SortOption.priceDesc;
    case 'newest':
      return SortOption.newest;
    case 'relevance':
    default:
      return SortOption.relevance;
  }
}
