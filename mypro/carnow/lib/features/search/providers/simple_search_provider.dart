import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../models/search_models.dart';

part 'simple_search_provider.g.dart';

final _logger = Logger('SimpleSearchProvider');

/// Search results provider - FIXED: Converted to @riverpod pattern
@riverpod
Future<List<SearchResultModel>> searchResults(SearchResultsRef ref, String query) async {
  if (query.trim().isEmpty) {
    return [];
  }
  
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Searching for: "$query"');
    
    final response = await apiClient.getApi(
      '/search',
      queryParameters: {
        'q': query,
        'limit': 20,
      },
    );
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Search failed: ${response.message}');
    }

    return (response.data! as List<dynamic>)
        .map((json) => SearchResultModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    _logger.severe('Error searching: $e');
    rethrow;
  }
}

/// Search suggestions provider - FIXED: Converted to @riverpod pattern
@riverpod
Future<List<String>> searchSuggestions(SearchSuggestionsRef ref, String query) async {
  if (query.trim().isEmpty) {
    return [];
  }
  
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi(
      '/search/suggestions',
      queryParameters: {
        'q': query,
        'limit': 10,
      },
    );
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    return response.data!.cast<String>();
  } catch (e) {
    _logger.warning('Error getting suggestions: $e');
    return [];
  }
}

/// Recent searches provider
final recentSearchesProvider = FutureProvider<List<String>>((ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi(
      '/search/recent',
      queryParameters: {
        'limit': 10,
      },
    );
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    return response.data!.cast<String>();
  } catch (e) {
    _logger.warning('Error getting recent searches: $e');
    return [];
  }
});

/// Search actions provider for managing search state
final searchActionsProvider = Provider<SearchActions>((ref) {
  return SearchActions(ref);
});

/// Search actions class for managing search operations
class SearchActions {
  SearchActions(this._ref);
  
  final Ref _ref;
  
  /// Add a search term to recent searches
  Future<void> addSearchTerm(String term) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      await apiClient.postApi<Map<String, dynamic>>(
        '/search/recent',
        data: {
          'query': term,
        },
      );
      
      // Refresh the recent searches list
      _ref.invalidate(recentSearchesProvider);
    } catch (e) {
      _logger.warning('Error adding search term: $e');
    }
  }
  
  /// Clear all recent searches
  Future<void> clearRecentSearches() async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      await apiClient.deleteApi('/search/recent');
      
      // Refresh the recent searches list
      _ref.invalidate(recentSearchesProvider);
    } catch (e) {
      _logger.warning('Error clearing recent searches: $e');
    }
  }
} 