import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/networking/simple_api_client.dart';

part 'universal_search_provider.g.dart';

final _logger = Logger('UniversalSearchProvider');

/// Universal Search State
class UniversalSearchState {
  const UniversalSearchState({
    this.query = '',
    this.results = const [],
    this.isLoading = false,
    this.hasError = false,
    this.errorMessage,
  });

  final String query;
  final List<dynamic> results;
  final bool isLoading;
  final bool hasError;
  final String? errorMessage;

  UniversalSearchState copyWith({
    String? query,
    List<dynamic>? results,
    bool? isLoading,
    bool? hasError,
    String? errorMessage,
  }) {
    return UniversalSearchState(
      query: query ?? this.query,
      results: results ?? this.results,
      isLoading: isLoading ?? this.isLoading,
      hasError: hasError ?? this.hasError,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// Universal Search Provider
@riverpod
class UniversalSearch extends _$UniversalSearch {
  @override
  UniversalSearchState build() {
    return const UniversalSearchState();
  }

  /// Perform search
  Future<void> search(String query) async {
    if (query.trim().isEmpty) {
      state = const UniversalSearchState();
      return;
    }

    state = state.copyWith(isLoading: true, hasError: false);

    try {
      final apiClient = ref.read(simpleApiClientProvider);
      
      final response = await apiClient.getApi<Map<String, dynamic>>(
        '/search',
        queryParameters: {
          'q': query,
          'limit': 20,
        },
      );
      
      if (!response.isSuccess || response.data == null) {
        throw Exception('Search failed: ${response.message}');
      }

      final data = response.data!;
      final List<dynamic> results = data.containsKey('data') && data['data'] is List
          ? data['data'] as List<dynamic>
          : data is List
              ? data as List<dynamic>
              : [];

      state = state.copyWith(
        query: query,
        results: results,
        isLoading: false,
        hasError: false,
      );
    } catch (e) {
      _logger.severe('Search error: $e');
      state = state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      );
    }
  }

  /// Clear search results
  void clearResults() {
    state = const UniversalSearchState();
  }
}

/// Categories Provider
@riverpod
Future<List<dynamic>> universalCategories(UniversalCategoriesRef ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>('/categories');
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    final data = response.data!;
    final List<dynamic> categories = data.containsKey('categories') && data['categories'] is List
        ? data['categories'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : [];

    return categories;
  } catch (e) {
    _logger.severe('Categories error: $e');
    return [];
  }
}

/// Quick Search Provider
@riverpod
class QuickSearch extends _$QuickSearch {
  @override
  List<String> build() {
    return [];
  }

  /// Add quick search term
  void addTerm(String term) {
    if (term.trim().isEmpty) return;
    
    final currentTerms = [...state];
    currentTerms.remove(term); // Remove if exists
    currentTerms.insert(0, term); // Add to front
    
    // Keep only last 10 terms
    if (currentTerms.length > 10) {
      currentTerms.removeRange(10, currentTerms.length);
    }
    
    state = currentTerms;
  }

  /// Clear all quick search terms
  void clear() {
    state = [];
  }
}

/// Search History Provider
@riverpod
class SearchHistory extends _$SearchHistory {
  @override
  List<String> build() {
    return [];
  }

  /// Add search term to history
  void addSearch(String term) {
    if (term.trim().isEmpty) return;
    
    final currentHistory = [...state];
    currentHistory.remove(term); // Remove if exists
    currentHistory.insert(0, term); // Add to front
    
    // Keep only last 20 searches
    if (currentHistory.length > 20) {
      currentHistory.removeRange(20, currentHistory.length);
    }
    
    state = currentHistory;
  }

  /// Clear search history
  void clear() {
    state = [];
  }

  /// Remove specific search term
  void remove(String term) {
    state = state.where((item) => item != term).toList();
  }
}

/// Simple search provider following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
@riverpod
Future<List<dynamic>> searchResults(SearchResultsRef ref, String query) async {
  if (query.trim().isEmpty) {
    return [];
  }

  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/search',
      queryParameters: {
        'q': query,
        'limit': 20,
      },
    );
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Search failed: ${response.message}');
    }

    final data = response.data!;
    
    // Handle different response formats from Go backend
    final List<dynamic> results = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : [];

    return results;
  } catch (e) {
    _logger.severe('Search error: $e');
    return [];
  }
}

/// Search suggestions provider
@riverpod
Future<List<String>> searchSuggestions(Ref ref, String query) async {
  if (query.trim().isEmpty) {
    return [];
  }
  
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/search/suggestions',
      queryParameters: {
        'q': query,
        'limit': 10,
      },
    );
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    final data = response.data!;
    final List<dynamic> suggestions = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : [];

    return suggestions.map((s) => s.toString()).toList();
  } catch (e) {
    _logger.warning('Error getting suggestions: $e');
    return [];
  }
}

/// Recent searches provider
@riverpod
Future<List<String>> recentSearches(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/search/recent',
      queryParameters: {
        'limit': 10,
      },
    );
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    final data = response.data!;
    final List<dynamic> searches = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : [];

    return searches.map((s) => s.toString()).toList();
  } catch (e) {
    _logger.warning('Error getting recent searches: $e');
    return [];
  }
}

/// Current search query state
final searchQueryProvider = StateProvider<String>((ref) => '');

/// Search actions provider for managing search operations
final searchActionsProvider = Provider<SearchActions>((ref) {
  return SearchActions(ref);
});

/// Search actions class for managing search operations
class SearchActions {
  SearchActions(this._ref);
  
  final Ref _ref;
  
  /// Add a search term to recent searches
  Future<void> addSearchTerm(String term) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      await apiClient.postApi<Map<String, dynamic>>(
        '/search/recent',
        data: {
          'query': term,
        },
      );
      
      // Refresh the recent searches list
      _ref.invalidate(recentSearchesProvider);
    } catch (e) {
      _logger.warning('Error adding search term: $e');
    }
  }
  
  /// Clear all recent searches
  Future<void> clearRecentSearches() async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      await apiClient.deleteApi('/search/recent');
      
      // Refresh the recent searches list
      _ref.invalidate(recentSearchesProvider);
    } catch (e) {
      _logger.warning('Error clearing recent searches: $e');
    }
  }
  
  /// Update search query
  void updateQuery(String query) {
    _ref.read(searchQueryProvider.notifier).state = query;
  }
  
  /// Clear search query
  void clearQuery() {
    _ref.read(searchQueryProvider.notifier).state = '';
  }
} 