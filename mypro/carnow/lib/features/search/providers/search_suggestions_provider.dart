import 'dart:async';

import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/services/search_api_service.dart';

part 'search_suggestions_provider.g.dart';

final _logger = Logger('SearchSuggestionsProvider');

/// Provider that returns auto-complete suggestions based on the user's
/// search input - FIXED: Uses SimpleApiClient instead of direct Supabase calls
@riverpod
class SearchSuggestions extends _$SearchSuggestions {
  Timer? _debounce;

  @override
  Future<List<String>> build(String query) async {
    // Clean up debounce timer when provider is disposed
    ref.onDispose(() {
      _debounce?.cancel();
    });

    // If query is too short, don't show suggestions
    if (query.trim().length < 2) {
      return [];
    }

    // Use debouncing to reduce API calls
    final completer = Completer<List<String>>();

    _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () async {
      try {
        final suggestions = await _fetchSuggestions(query);
        if (!completer.isCompleted) {
          completer.complete(suggestions);
        }
      } catch (e) {
        if (!completer.isCompleted) {
          completer.completeError(e);
        }
      }
    });

    return completer.future;
  }

  /// Fetch search suggestions using API service instead of direct Supabase
  Future<List<String>> _fetchSuggestions(String query) async {
    final searchTerm = query.trim();

    try {
      _logger.info('Fetching suggestions for "$searchTerm" - FIXED: Using API service');
      
      // FIXED: Using SimpleApiClient instead of direct Supabase calls
      final apiService = ref.read(searchApiServiceProvider);
      final apiSuggestions = await apiService.getSearchSuggestions(searchTerm);

      _logger.info('Found ${apiSuggestions.length} suggestions for "$searchTerm"');
      return apiSuggestions;
    } catch (e, stackTrace) {
      _logger.severe('Error fetching suggestions for "$searchTerm": $e', e, stackTrace);
      return [];
    }
  }
}

/// Provider for getting recent searches from local storage
@riverpod
class RecentSearches extends _$RecentSearches {
  static const String _recentSearchesKey = 'recent_searches';
  static const int _maxRecentSearches = 10;

  @override
  Future<List<String>> build() async {
    final prefs = await SharedPreferences.getInstance();
    final recentSearches = prefs.getStringList(_recentSearchesKey) ?? [];
    return recentSearches;
  }

  /// Add a search term to recent searches
  Future<void> addRecentSearch(String searchTerm) async {
    if (searchTerm.trim().isEmpty) return;

    final prefs = await SharedPreferences.getInstance();
    final current = List<String>.from(state.value ?? []);
    
    // Remove if already exists to avoid duplicates
    current.remove(searchTerm);
    
    // Add to beginning
    current.insert(0, searchTerm);
    
    // Limit to max recent searches
    if (current.length > _maxRecentSearches) {
      current.removeRange(_maxRecentSearches, current.length);
    }

    await prefs.setStringList(_recentSearchesKey, current);
    state = AsyncData(current);
  }

  /// Clear all recent searches
  Future<void> clearRecentSearches() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_recentSearchesKey);
    state = const AsyncData([]);
  }
}

/// Provider for popular searches (from API)
@riverpod
Future<List<String>> popularSearches(Ref ref) async {
  try {
    _logger.info('Fetching popular searches - FIXED: Using API service');
    final apiService = ref.watch(searchApiServiceProvider);
    return await apiService.getPopularSearches();
  } catch (e) {
    _logger.severe('Error fetching popular searches: $e');
    // Return empty list on error
    return [];
  }
}

/// Helper function to track search analytics
@riverpod
class SearchTracker extends _$SearchTracker {
  @override
  void build() {
    // No initial state needed
  }

  /// Track a search query for analytics
  Future<void> trackSearch(String query, {String? userId}) async {
    try {
      final apiService = ref.read(searchApiServiceProvider);
      await apiService.trackSearch(query, userId: userId);
      _logger.info('Tracked search: $query');
    } catch (e) {
      _logger.warning('Failed to track search: $e');
      // Don't throw error for analytics tracking failures
    }
  }
}
