import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/unified_auth_provider.dart';
import '../models/search_suggestion.dart';

part 'smart_search_provider.g.dart';

final _logger = Logger('SmartSearchProvider');

/// Smart Search Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// ✅ Uses Go Backend API ONLY for search
/// ✅ NO direct Supabase calls
/// ✅ Clean async/await patterns

/// Provider for search suggestions with smart recommendations
@riverpod
Future<List<SearchSuggestion>> smartSearchSuggestions(
  Ref ref,
  String query,
) async {
  if (query.trim().isEmpty) {
    return [];
  }

  try {
    _logger.info('Fetching smart search suggestions for: $query');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/search/suggestions',
      queryParameters: {
        'query': query.trim(),
        'limit': '10',
      },
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch search suggestions: ${response.message}');
      throw Exception('Failed to fetch search suggestions from backend: ${response.message}');
    }

    final suggestionsData = response.data!['suggestions'] as List<dynamic>? ?? [];
    final suggestions = suggestionsData
        .cast<Map<String, dynamic>>()
        .map((data) => SearchSuggestion.fromJson(data))
        .toList();

    _logger.info('Smart search suggestions fetched: ${suggestions.length}');
    return suggestions;
  } catch (e) {
    _logger.severe('Error fetching smart search suggestions: $e');
    throw Exception('Failed to fetch search suggestions from backend: $e');
  }
}

/// Provider for popular searches
@riverpod
Future<List<String>> popularSearches(Ref ref) async {
  try {
    _logger.info('Fetching popular searches from Go backend');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/search/popular',
      queryParameters: {'limit': '10'},
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch popular searches: ${response.message}');
      throw Exception('Failed to fetch popular searches from backend: ${response.message}');
    }

    final searchesData = response.data!['searches'] as List<dynamic>? ?? [];
    final searches = searchesData.cast<String>().toList();

    _logger.info('Popular searches fetched: ${searches.length}');
    return searches;
  } catch (e) {
    _logger.severe('Error fetching popular searches: $e');
    throw Exception('Failed to fetch popular searches from backend: $e');
  }
}

/// Provider for user's search history
@riverpod
Future<List<String>> userSearchHistory(Ref ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.info('User not authenticated, no search history');
      return [];
    }

    _logger.info('Fetching user search history from Go backend');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/search/history',
      queryParameters: {'limit': '20'},
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch search history: ${response.message}');
      return [];
    }

    final historyData = response.data!['history'] as List<dynamic>? ?? [];
    final history = historyData.cast<String>().toList();

    _logger.info('User search history fetched: ${history.length}');
    return history;
  } catch (e) {
    _logger.severe('Error fetching user search history: $e');
    return [];
  }
}

/// Provider for saved searches
@riverpod
Future<List<String>> savedSearches(Ref ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.info('User not authenticated, no saved searches');
      return [];
    }

    _logger.info('Fetching saved searches from Go backend');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/search/saved',
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch saved searches: ${response.message}');
      return [];
    }

    final savedData = response.data!['saved_searches'] as List<dynamic>? ?? [];
    final saved = savedData.cast<String>().toList();

    _logger.info('Saved searches fetched: ${saved.length}');
    return saved;
  } catch (e) {
    _logger.severe('Error fetching saved searches: $e');
    return [];
  }
}

/// Search management functions
class SearchManager {
  const SearchManager(this.ref);
  
  final Ref ref;

  /// Save a search query through Go backend
  Future<bool> saveSearch(String query) async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        _logger.warning('User not authenticated, cannot save search');
        return false;
      }

      _logger.info('Saving search query: $query');

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.postApi<Map<String, dynamic>>(
        '/search/save',
        data: {'query': query.trim()},
      );

      if (!response.isSuccess) {
        _logger.warning('Failed to save search: ${response.message}');
        return false;
      }

      // Refresh saved searches
      ref.invalidate(savedSearchesProvider);

      _logger.info('Search saved successfully: $query');
      return true;
    } catch (e) {
      _logger.severe('Error saving search: $e');
      return false;
    }
  }

  /// Remove a saved search
  Future<bool> removeSavedSearch(String query) async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        _logger.warning('User not authenticated, cannot remove saved search');
        return false;
      }

      _logger.info('Removing saved search: $query');

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.deleteApi<Map<String, dynamic>>(
        '/search/saved',
        queryParameters: {'query': query.trim()},
      );

      if (!response.isSuccess) {
        _logger.warning('Failed to remove saved search: ${response.message}');
        return false;
      }

      // Refresh saved searches
      ref.invalidate(savedSearchesProvider);

      _logger.info('Saved search removed successfully: $query');
      return true;
    } catch (e) {
      _logger.severe('Error removing saved search: $e');
      return false;
    }
  }

  /// Track search query for analytics through Go backend
  Future<void> trackSearchQuery(String query) async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        return; // No tracking for anonymous users
      }

      _logger.info('Tracking search query: $query');

      final apiClient = ref.read(simpleApiClientProvider);
      await apiClient.postApi<Map<String, dynamic>>(
        '/search/track',
        data: {
          'query': query.trim(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // Refresh search history
      ref.invalidate(userSearchHistoryProvider);
    } catch (e) {
      _logger.warning('Error tracking search query: $e');
      // Don't throw error for tracking failures
    }
  }
}

/// Provider for search manager
@riverpod
SearchManager searchManager(Ref ref) {
  return SearchManager(ref);
}

// ❌ REMOVED: All mock data functions - violates ZERO MOCK DATA POLICY
// ✅ REQUIRED: Use real search data from Supabase via Go backend
// ✅ ARCHITECTURE: Flutter UI Only → Go API → Supabase Data
//
// Mock data functions have been removed to comply with Forever Plan Architecture.
// All search data must come from real database via Go backend endpoints.
