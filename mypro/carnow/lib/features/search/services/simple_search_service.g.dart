// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'simple_search_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$simpleSearchServiceHash() =>
    r'7bb05307ee7cd5944cbc04aac710b1cffead8b16';

/// Provider for simple search service
///
/// Copied from [simpleSearchService].
@ProviderFor(simpleSearchService)
final simpleSearchServiceProvider =
    AutoDisposeFutureProvider<SimpleSearchService>.internal(
      simpleSearchService,
      name: r'simpleSearchServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$simpleSearchServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SimpleSearchServiceRef =
    AutoDisposeFutureProviderRef<SimpleSearchService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
