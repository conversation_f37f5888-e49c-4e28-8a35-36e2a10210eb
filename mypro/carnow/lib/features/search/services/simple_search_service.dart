import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:carnow/features/products/models/product_model.dart';

import '../../../core/networking/simple_api_client.dart';

part 'simple_search_service.g.dart';

final _logger = Logger('SimpleSearchService');

/// Simple search service with history and suggestions via Go Backend API
class SimpleSearchService {
  SimpleSearchService(this._apiClient, this._prefs);
  final SimpleApiClient _apiClient;
  final SharedPreferences _prefs;

  /// Search products with simple query via Go Backend API
  Future<List<ProductModel>> searchProducts({
    required String query,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      _logger.info(
        '🔍 Searching products with query: "$query" via Go Backend API',
      );

      // Save to search history
      await _saveToSearchHistory(query);

      final response = await _apiClient.get(
        '/products/search',
        queryParameters: {
          'q': query,
          'limit': limit.toString(),
          'offset': offset.toString(),
        },
      );

      if (response.isSuccess && response.data != null) {
        final products = (response.data as List)
            .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
            .toList();

        _logger.info('✅ Found ${products.length} products');
        return products;
      } else {
        _logger.warning('⚠️ Search failed: ${response.error}');
        return [];
      }
    } catch (e) {
      _logger.severe('❌ Error searching products: $e');
      return [];
    }
  }

  /// Get search suggestions based on query via Go Backend API
  Future<List<String>> getSearchSuggestions(String query) async {
    try {
      if (query.length < 2) {
        return [];
      }

      _logger.info(
        '🔍 Getting search suggestions for: "$query" via Go Backend API',
      );

      final response = await _apiClient.get(
        '/products/suggestions',
        queryParameters: {'q': query, 'limit': '10'},
      );

      if (response.isSuccess && response.data != null) {
        final suggestions = (response.data as List)
            .map((item) => item.toString())
            .toSet() // Remove duplicates
            .toList();

        _logger.info('✅ Found ${suggestions.length} suggestions');
        return suggestions.take(10).toList();
      } else {
        _logger.warning('⚠️ Failed to get suggestions: ${response.error}');
        return [];
      }
    } catch (e) {
      _logger.severe('❌ Error getting search suggestions: $e');
      return [];
    }
  }

  /// Save search to history
  Future<void> _saveToSearchHistory(String query) async {
    try {
      if (query.trim().isEmpty) {
        return;
      }

      const key = 'search_history';
      final history = _getSearchHistory();

      // Remove if already exists and add to beginning
      history
        ..remove(query)
        ..insert(0, query);

      // Keep only last 20 searches
      if (history.length > 20) {
        history.removeRange(20, history.length);
      }

      await _prefs.setStringList(key, history);
    } catch (e) {
      _logger.warning('Error saving search history: $e');
    }
  }

  /// Get search history
  List<String> _getSearchHistory() {
    try {
      return _prefs.getStringList('search_history') ?? [];
    } catch (e) {
      _logger.warning('Error getting search history: $e');
      return [];
    }
  }

  /// Get search history
  List<String> getSearchHistory() => _getSearchHistory();

  /// Clear search history
  Future<void> clearSearchHistory() async {
    try {
      await _prefs.remove('search_history');
    } catch (e) {
      _logger.warning('Error clearing search history: $e');
    }
  }

  /// Remove item from search history
  Future<void> removeFromHistory(String query) async {
    try {
      const key = 'search_history';
      final history = _getSearchHistory()..remove(query);
      await _prefs.setStringList(key, history);
    } catch (e) {
      _logger.warning('Error removing from search history: $e');
    }
  }

  /// Get popular search terms
  Future<List<String>> getPopularSearches() async {
    try {
      _logger.info('Getting popular search terms');

      // Return some static popular terms for now
      return [
        'BMW',
        'Mercedes',
        'Toyota',
        'Honda',
        'Audi',
        'Ford',
        'Volkswagen',
        'Nissan',
        'محرك',
        'فرامل',
        'إطارات',
        'زيوت',
      ];
    } catch (e) {
      _logger.severe('Error getting popular searches: $e');
      return [];
    }
  }

  /// Search products by category via Go Backend API
  Future<List<ProductModel>> searchByCategory({
    required String categoryId,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      _logger.info(
        '🔍 Searching products in category: $categoryId via Go Backend API',
      );

      final response = await _apiClient.get(
        '/products/category/$categoryId',
        queryParameters: {
          'limit': limit.toString(),
          'offset': offset.toString(),
        },
      );

      if (response.isSuccess && response.data != null) {
        final products = (response.data as List)
            .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
            .toList();

        _logger.info('✅ Found ${products.length} products in category');
        return products;
      } else {
        _logger.warning('⚠️ Failed to search by category: ${response.error}');
        return [];
      }
    } catch (e) {
      _logger.severe('❌ Error searching by category: $e');
      return [];
    }
  }

  /// Search products with price filter via Go Backend API
  Future<List<ProductModel>> searchWithPriceFilter({
    required String query,
    double? minPrice,
    double? maxPrice,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      _logger.info(
        '🔍 Searching with price filter: $minPrice - $maxPrice via Go Backend API',
      );

      await _saveToSearchHistory(query);

      final queryParams = <String, String>{
        'q': query,
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      if (minPrice != null) {
        queryParams['min_price'] = minPrice.toString();
      }
      if (maxPrice != null) {
        queryParams['max_price'] = maxPrice.toString();
      }

      final response = await _apiClient.get(
        '/products/search/price-filter',
        queryParameters: queryParams,
      );

      if (response.isSuccess && response.data != null) {
        final products = (response.data as List)
            .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
            .toList();

        _logger.info('✅ Found ${products.length} products with price filter');
        return products;
      } else {
        _logger.warning(
          '⚠️ Failed to search with price filter: ${response.error}',
        );
        return [];
      }
    } catch (e) {
      _logger.severe('❌ Error searching with price filter: $e');
      return [];
    }
  }
}

/// Provider for simple search service
@riverpod
Future<SimpleSearchService> simpleSearchService(Ref ref) async {
  final apiClient = ref.watch(simpleApiClientProvider);
  final prefs = await SharedPreferences.getInstance();
  return SimpleSearchService(apiClient, prefs);
}
