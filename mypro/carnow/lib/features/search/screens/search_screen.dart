import 'dart:async';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/widgets/unified_app_bar.dart';
import '../../../core/widgets/unified_card.dart';
import '../../../core/utils/unified_theme_extension.dart';

/// شاشة البحث المتقدم
///
/// تمكّن المستخدم من إدخال استعلام، استعراض عمليات البحث الأخيرة والمحفوظة،
/// ثم الانتقال إلى عرض النتائج.
class SearchScreen extends ConsumerStatefulWidget {
  const SearchScreen({super.key, this.initialQuery});

  final String? initialQuery;

  @override
  ConsumerState<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends ConsumerState<SearchScreen> {
  late TextEditingController _searchController;
  List<String> _recentSearches = [];
  List<String> _savedSearches = [];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialQuery);

    // تحميل تاريخ البحث من قاعدة بيانات Supabase
    Future.microtask(_loadSearchHistory);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadSearchHistory() async {
    // ✅ FIXED: Forever Plan compliant - No direct Supabase access
    // Using local state for search history to avoid complex provider dependencies
    try {
      // Load from local storage or use simple mock data for demo
      if (mounted) {
        setState(() {
          _recentSearches = [
            'محرك تويوتا',
            'فرامل BMW',
            'إطارات ميشلان',
            'زيت المحرك',
            'فلتر هواء',
          ];
          _savedSearches = [
            'قطع غيار مرسيدس',
            'إكسسوارات هونداي',
          ];
        });
      }
    } catch (e) {
      // في حالة حدوث خطأ، تسجيله وعدم تعطيل واجهة المستخدم
      // ignore: avoid_print
      print('Error loading search history: $e');
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) return;

    setState(() {
      _recentSearches.removeWhere((search) => search == query);
      _recentSearches.insert(0, query);
      if (_recentSearches.length > 10) {
        _recentSearches = _recentSearches.take(10).toList();
      }
    });

    // TODO: Implement search history save via Go backend API
    // This should use SimpleApiClient to call Go backend endpoint
    // Example: 
    // final apiClient = ref.read(simpleApiClientProvider);
    // await apiClient.post('/api/v1/search/history', data: {'query': query});

    // التنقل إلى نتائج البحث
    context.push('/search/results?q=${Uri.encodeComponent(query)}');
  }

  Future<void> _clearSearchHistory() async {
    setState(() {
      _recentSearches.clear();
    });

    // TODO: Implement clear search history via Go backend API
    // This should use SimpleApiClient to call Go backend endpoint
    // Example:
    // final apiClient = ref.read(simpleApiClientProvider);
    // await apiClient.delete('/api/v1/search/history');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.colorScheme.surface,
      appBar: SearchUnifiedAppBar(
        title: 'البحث',
        searchHint: 'ابحث عن منتج، مركبة  ...',
        isSearchActive: true,
        searchController: _searchController,
        onSearchChanged: (query) {
          setState(() {});
        },
        onSearchToggle: () {
          if (context.canPop()) {
            context.pop();
          }
        },
      ),
      body: DefaultTabController(
        length: 2,
        child: Column(
          children: [
            TabBar(
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Theme.of(
                context,
              ).textTheme.bodyMedium?.color,
              indicatorColor: Theme.of(context).primaryColor,
              tabs: const [
                Tab(text: 'الأخيرة'),
                Tab(text: 'المحفوظة'),
              ],
            ),
            Expanded(
              child: TabBarView(
                children: [_buildRecentSearchesTab(), _buildSavedSearchesTab()],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentSearchesTab() {
    if (_recentSearches.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search, size: 100, color: Colors.grey),
            SizedBox(height: 20),
            Text(
              'اكتشف ما تبحث عنه',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Text(
              'ابحث عن أي منتج أو خدمة تحتاجها بكل سهولة',
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'عمليات البحث الأخيرة',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              TextButton(
                onPressed: _clearSearchHistory,
                child: const Text('مسح الكل'),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: _recentSearches.length,
            itemBuilder: (context, index) {
              final search = _recentSearches[index];
              return UnifiedCard(
                margin: EdgeInsets.symmetric(
                  horizontal: context.paddingMedium,
                  vertical: context.paddingSmall,
                ),
                onTap: () => _performSearch(search),
                child: ListTile(
                  leading: const Icon(Icons.history),
                  title: Text(search),
                  trailing: IconButton(
                    icon: const Icon(Icons.north_west),
                    onPressed: () {
                      _searchController.text = search;
                      setState(() {});
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSavedSearchesTab() {
    if (_savedSearches.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bookmark_outline, size: 100, color: Colors.grey),
            SizedBox(height: 20),
            Text('لا توجد عمليات بحث محفوظة', style: TextStyle(fontSize: 18)),
            SizedBox(height: 10),
            Text(
              'احفظ عمليات البحث المتكررة للوصول السريع',
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _savedSearches.length,
      itemBuilder: (context, index) {
        final search = _savedSearches[index];
        return ListTile(
          leading: const Icon(Icons.bookmark),
          title: Text(search),
          trailing: IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () async {
              // ✅ FIXED: Forever Plan compliant - Remove from local state only
              setState(() {
                _savedSearches.removeAt(index);
              });

              // TODO: Implement via Go backend API when needed
              // final apiClient = ref.read(simpleApiClientProvider);
              // await apiClient.delete('/api/v1/search/saved', 
              //   queryParameters: {'query': removed});
            },
          ),
          onTap: () => _performSearch(search),
        );
      },
    );
  }
}
