import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';


/// Simplified search widgets to avoid compilation errors

class CategoryTabsWidget extends StatelessWidget {
  final List<dynamic> categories;
  final String? selectedCategory;
  final Function(String) onCategorySelected;
  
  const CategoryTabsWidget({
    super.key,
    required this.categories,
    this.selectedCategory,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final categoryName = category.toString();
          
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: ChoiceChip(
              label: Text(categoryName),
              selected: selectedCategory == categoryName,
              onSelected: (selected) {
                if (selected) {
                  onCategorySelected(categoryName);
                }
              },
            ),
          );
        },
      ),
    );
  }
}

class ActiveFiltersWidget extends StatelessWidget {
  final Map<String, dynamic> filters;
  final Function(String) onFilterRemoved;
  
  const ActiveFiltersWidget({
    super.key,
    required this.filters,
    required this.onFilterRemoved,
  });

  @override
  Widget build(BuildContext context) {
    if (filters.isEmpty) return const SizedBox.shrink();
    
    return Wrap(
      spacing: 8.0,
      children: filters.entries.map((entry) {
        return Chip(
          label: Text('${entry.key}: ${entry.value}'),
          onDeleted: () => onFilterRemoved(entry.key),
        );
      }).toList(),
    );
  }
}

class SmartFiltersWidget extends ConsumerWidget {
  final String categoryType;
  final Map<String, dynamic> activeFilters;
  final Function(Map<String, dynamic>) onFiltersChanged;
  
  const SmartFiltersWidget({
    super.key,
    required this.categoryType,
    required this.activeFilters,
    required this.onFiltersChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'فلاتر: $categoryType',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          // Simple filter controls
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () {
                    onFiltersChanged({'price_range': 'low'});
                  },
                  child: const Text('سعر منخفض'),
                ),
              ),
              Expanded(
                child: TextButton(
                  onPressed: () {
                    onFiltersChanged({'price_range': 'high'});
                  },
                  child: const Text('سعر مرتفع'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class SearchResultsWidget extends StatelessWidget {
  final List<dynamic> results;
  final Function(dynamic) onResultTapped;
  
  const SearchResultsWidget({
    super.key,
    required this.results,
    required this.onResultTapped,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        final result = results[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: ListTile(
            title: Text('نتيجة ${index + 1}'),
            subtitle: Text(result.toString()),
            onTap: () => onResultTapped(result),
          ),
        );
      },
    );
  }
}

class QuickSuggestionsWidget extends StatelessWidget {
  final List<String> suggestions;
  final Function(String) onSuggestionTapped;
  
  const QuickSuggestionsWidget({
    super.key,
    required this.suggestions,
    required this.onSuggestionTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'اقتراحات سريعة',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        ...suggestions.map((suggestion) => ListTile(
          leading: const Icon(Icons.search),
          title: Text(suggestion),
          onTap: () => onSuggestionTapped(suggestion),
        )),
      ],
    );
  }
}

class SearchHistoryWidget extends StatelessWidget {
  final List<String> history;
  final Function(String) onHistoryTapped;
  final Function(String) onHistoryRemoved;
  
  const SearchHistoryWidget({
    super.key,
    required this.history,
    required this.onHistoryTapped,
    required this.onHistoryRemoved,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'عمليات البحث السابقة',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        ...history.map((item) => ListTile(
          leading: const Icon(Icons.history),
          title: Text(item),
          trailing: IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () => onHistoryRemoved(item),
          ),
          onTap: () => onHistoryTapped(item),
        )),
      ],
    );
  }
}

class NoResultsWidget extends StatelessWidget {
  const NoResultsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'لا توجد نتائج',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
        ],
      ),
    );
  }
} 