/// شاشة تقديم طلب بائع
///
/// تُمكّن المستخدمين من تقديم طلب ليصبحوا بائعين على المنصة.
/// تتضمن نموذجاً لجمع المعلومات الضرورية مثل اسم المتجر، الوصف،
/// الشعار، ومعلومات الاتصال.
/// تتحقق من حالة الطلبات السابقة للمستخدم (هل هو بائع معتمد بالفعل أو لديه طلب قيد المراجعة)
/// وتعرض رسالة مناسبة بناءً على حالته.
library;

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/unified_logger.dart';
import '../../../core/widgets/app_error_widget.dart';
import '../models/seller_application_model.dart';
import '../models/seller_enums.dart';
import '../providers/seller_application_provider.dart';

/// شاشة تقديم طلب أن تصبح بائع - مبسطة
class SellerApplicationFormScreen extends HookConsumerWidget {
  const SellerApplicationFormScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // التحقق من حالة تسجيل الدخول
    final user = ref.read(currentUserProvider);

    // إعادة توجيه المستخدم غير المسجل دخول
    useEffect(() {
      if (user == null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          context.go('/login', extra: {'returnTo': '/seller-application'});
        });
      }
      return null;
    });

    if (user == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    final formKey = useMemoized(GlobalKey<FormState>.new);

    // Form fields - مبسطة للحد الأدنى المطلوب
    final storeNameController = useTextEditingController();
    final phoneController = useTextEditingController();
    final descriptionController = useTextEditingController();

    // التحقق من إمكانية التقديم
    final canApplyAsync = ref.watch(canApplyAsSellerProvider);
    final applicationAsync = ref.watch(sellerApplicationProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('طلب أن تصبح بائع'),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
      ),
      body: canApplyAsync.when(
        data: (canApply) => applicationAsync.when(
          data: (application) {
            if (!canApply || application != null) {
              return _CannotApplyMessage(application: application);
            }

            return _SimpleApplicationForm(
              formKey: formKey,
              storeNameController: storeNameController,
              phoneController: phoneController,
              descriptionController: descriptionController,
              onSubmit: () async {
                await _submitApplication(
                  context,
                  ref,
                  storeNameController,
                  phoneController,
                  descriptionController,
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => AppErrorWidget(
            message: 'خطأ في جلب البيانات',
            details: error.toString(),
            onRetry: () => ref.refresh(canApplyAsSellerProvider),
          ),
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => AppErrorWidget(
          message: 'خطأ في جلب البيانات',
          details: error.toString(),
          onRetry: () => ref.refresh(canApplyAsSellerProvider),
        ),
      ),
    );
  }

  Future<void> _submitApplication(
    BuildContext context,
    WidgetRef ref,
    TextEditingController storeNameController,
    TextEditingController phoneController,
    TextEditingController descriptionController,
  ) async {
    try {
      // إنشاء metadata للتدقيق
      final metadata = <String, dynamic>{
        'device_info': 'Flutter Mobile App',
        'app_version': '1.0',
        'timestamp': DateTime.now().toIso8601String(),
        'device_locale': Intl.getCurrentLocale(),
        'application_type': 'simplified', // نوع مبسط
      };

      // إنشاء نموذج الطلب - مبسط بأقل البيانات المطلوبة
      final application = SellerApplicationModel(
        userId: ref.read(currentUserProvider)?.id ?? '',
        storeName: storeNameController.text.trim(),
        phoneNumber: phoneController.text.trim(),
        businessDescription: descriptionController.text.trim().isEmpty
            ? null
            : descriptionController.text.trim(),
        sellerType: SellerType.individual, // افتراضي - فردي
        metadata: metadata,
      );

      // عرض مؤشر تحميل لمنع التفاعل أثناء الإرسال
      showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (_) =>
            const Center(child: CircularProgressIndicator(strokeWidth: 2)),
      );

      UnifiedLogger.info(
        'Submitting simplified seller application: ${application.id}',
      );
      try {
        await ref
            .read(sellerApplicationProvider.notifier)
            .submitApplication(application);
      } finally {
        if (Navigator.canPop(context)) {
          Navigator.of(context).pop(); // Close the progress dialog
        }
      }

      // تحديث ملخص الطلبات بعد الإرسال
      ref.invalidate(sellerApplicationProvider);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'تم إرسال طلبك بنجاح! سيتم مراجعته من قبل الإدارة وستحصل على إشعار عند الموافقة عليه.',
            ),
            backgroundColor: Colors.green,
          ),
        );
        context.go('/account');
      }
    } catch (e) {
      UnifiedLogger.error('Error submitting application: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال الطلب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class _CannotApplyMessage extends HookConsumerWidget {
  const _CannotApplyMessage({required this.application});

  final SellerApplicationModel? application;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pendingCount = application != null ? 1 : 0;
    final approvedCount = application?.status == ApplicationStatus.approved
        ? 1
        : 0;

    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      child: Column(
        children: [
          if (approvedCount > 0) ...[
            // البائع معتمد - رسالة بسيطة
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.green.shade600,
                    size: 48,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  Text(
                    'أنت بائع معتمد!',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.green.shade800,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingS),
                  Text(
                    'يمكنك الآن إدارة متجرك من خلال قسم أدوات البائع في حسابك.',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.green.shade700,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  ElevatedButton.icon(
                    onPressed: () {
                      context.pop(); // العودة للحساب
                    },
                    icon: const Icon(Icons.account_circle),
                    label: const Text('العودة إلى حسابي'),
                    style: FilledButton.styleFrom(
                      backgroundColor: Colors.green.shade600,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ] else if (pendingCount > 0) ...[
            // إذا كان الطلب معلقاً، نعرض رسالة الانتظار
            Icon(
              Icons.hourglass_empty,
              size: 80,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 24),
            Text(
              'طلبك قيد المراجعة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'طلبك قيد المراجعة حالياً من قبل الإدارة. سيتم إشعارك عند اتخاذ قرار.',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            FilledButton(
              onPressed: () => context.go('/account'),
              child: const Text('العودة إلى حسابي'),
            ),
          ] else ...[
            // إذا لم يكن يمكن التقديم لأسباب أخرى
            Icon(
              Icons.info_outline,
              size: 80,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 24),
            Text(
              'لا يمكنك التقديم حالياً',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'نعتذر، ولكن لا يمكنك التقديم في الوقت الحالي. يرجى المحاولة لاحقاً.',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            FilledButton(
              onPressed: () => context.go('/account'),
              child: const Text('العودة إلى حسابي'),
            ),
          ],
        ],
      ),
    );
  }
}

class _SimpleApplicationForm extends StatelessWidget {
  const _SimpleApplicationForm({
    required this.formKey,
    required this.storeNameController,
    required this.phoneController,
    required this.descriptionController,
    required this.onSubmit,
  });

  final GlobalKey<FormState> formKey;
  final TextEditingController storeNameController;
  final TextEditingController phoneController;
  final TextEditingController descriptionController;
  final VoidCallback onSubmit;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات توضيحية
            Card(
              color: Theme.of(
                context,
              ).colorScheme.primaryContainer.withValues(alpha: 0.3),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'طلب أن تصبح بائع',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'املأ البيانات التالية وسيتم مراجعة طلبك من قبل الإدارة. عند الموافقة ستحصل على إشعار وستتمكن من إدارة متجرك ونشر منتجاتك.',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // اسم المتجر
            TextFormField(
              controller: storeNameController,
              decoration: const InputDecoration(
                labelText: 'اسم المتجر أو المحل *',
                hintText: 'مثال: متجر قطع غيار أحمد',
                prefixIcon: Icon(Icons.store),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم المتجر';
                }
                if (value.trim().length < 3) {
                  return 'يجب أن يكون اسم المتجر أكثر من 3 أحرف';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // رقم الهاتف
            TextFormField(
              controller: phoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف *',
                hintText: 'مثال: 0501234567',
                prefixIcon: Icon(Icons.phone),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }
                if (value.trim().length < 10) {
                  return 'يرجى إدخال رقم هاتف صحيح';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // وصف النشاط التجاري
            TextFormField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف النشاط التجاري (اختياري)',
                hintText: 'مثال: بيع قطع غيار السيارات والإكسسوارات',
                prefixIcon: Icon(Icons.description),
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              maxLength: 500,
            ),
            const SizedBox(height: 24),

            // معلومات إضافية
            Card(
              color: Theme.of(context).colorScheme.surfaceContainer,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'ما الذي ستستفيده كبائع؟',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...[
                      'نشر منتجاتك وقطع الغيار',
                      'إدارة طلبات العملاء',
                      'التواصل المباشر مع المشترين',
                      'إحصائيات المبيعات',
                      'دعم فني مخصص',
                    ].map(
                      (feature) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.check_circle,
                              size: 20,
                              color: Colors.green,
                            ),
                            const SizedBox(width: 8),
                            Expanded(child: Text(feature)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 32),

            // زر الإرسال
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  if (formKey.currentState?.validate() ?? false) {
                    onSubmit();
                  }
                },
                icon: const Icon(Icons.send),
                label: const Text(
                  'إرسال طلب أن أصبح بائع',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // ملاحظة
            Text(
              '* الحقول المطلوبة\n'
              'سيتم مراجعة طلبك خلال 1-3 أيام عمل وستحصل على إشعار بالنتيجة.',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
