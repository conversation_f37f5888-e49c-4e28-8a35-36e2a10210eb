import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_color_extensions.dart';
import '../../../core/widgets/app_error_widget.dart';
import '../../../core/models/subscription_response.dart';
import '../../../core/models/subscription_error.dart';
import '../../../core/error/subscription_error_handler.dart';
import '../models/subscription_model.dart';
import '../models/seller_enums.dart';
import '../providers/subscription_provider.dart' as providers;
import '../navigation/subscription_navigation_manager.dart';

class SubscriptionManagementScreen extends HookConsumerWidget {
  const SubscriptionManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final subscriptionAsync = ref.watch(
      providers.sellerSubscriptionProviderProvider,
    );
    final quotaUsageAsync = ref.watch(providers.monthlyQuotaUsageProvider);
    final refreshKey = useState(0);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إدارة الاشتراك',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        foregroundColor: Theme.of(context).textTheme.bodyLarge?.color,
        actions: [
          IconButton(
            onPressed: () {
              // Refresh providers - variables store return values to satisfy linter
              // ignore: unused_local_variable
              final subscription = ref.refresh(
                providers.sellerSubscriptionProviderProvider,
              );
              // ignore: unused_local_variable
              final quotaUsage = ref.refresh(
                providers.monthlyQuotaUsageProvider,
              );
              refreshKey.value++;
            },
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: subscriptionAsync.when(
        data: (subscription) => subscription == null
            ? _buildNoSubscriptionState(context)
            : _buildSubscriptionContent(
                context,
                ref,
                subscription,
                quotaUsageAsync,
              ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => _buildErrorState(context, error, ref),
      ),
    );
  }

  Widget _buildNoSubscriptionState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 24),
            Text(
              'لا يوجد اشتراك نشط',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'اشترك في إحدى خططنا للبدء في بيع منتجاتك',
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () async {
                try {
                  await SubscriptionNavigationManager.navigateToSubscriptionPlans(
                    context: context,
                  );
                } catch (e) {
                  if (context.mounted) {
                    // Handle navigation error using core error handler
                    final navigationError = SubscriptionError.navigationError(
                      message: 'فشل في التنقل إلى صفحة خطط الاشتراك',
                      attemptedRoute: '/subscription/plans',
                      code: 'NAVIGATION_ERROR',
                    );

                    await SubscriptionErrorHandler.handleSubscriptionError(
                      context: context,
                      error: navigationError,
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'اختر خطة الاشتراك',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionContent(
    BuildContext context,
    WidgetRef ref,
    SellerSubscription subscription,
    AsyncValue<MonthlyQuotaUsage?> quotaUsageAsync,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Subscription status card (handles all statuses including canceled pending expiry)
          _buildSubscriptionStatus(context, subscription),
          const SizedBox(height: 16),

          // Show usage and actions only for active subscriptions
          if (subscription.canManageExistingListings) ...[
            // Usage overview
            _buildUsageOverviewCard(context, subscription, quotaUsageAsync),
            const SizedBox(height: 16),

            // Quick actions
            _buildQuickActionsCard(context, ref, subscription),
            const SizedBox(height: 16),
          ],

          // Special frozen account interface
          if (subscription.status == SubscriptionStatus.frozen) ...[
            _buildFrozenAccountInterface(context),
            const SizedBox(height: 16),
          ],

          // Management actions (always show for active/trial subscriptions)
          if (subscription.status == SubscriptionStatus.active ||
              subscription.status == SubscriptionStatus.trial)
            _buildManagementActionsCard(context, ref, subscription),
        ],
      ),
    );
  }

  Widget _buildUsageOverviewCard(
    BuildContext context,
    SellerSubscription subscription,
    AsyncValue<MonthlyQuotaUsage?> quotaUsageAsync,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'استخدام هذا الشهر',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          quotaUsageAsync.when(
            data: (usage) => _buildUsageContent(context, subscription, usage),
            loading: () => const Center(
              child: SizedBox(
                height: 40,
                width: 40,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
            error: (error, stackTrace) => AppErrorWidget(
              message: 'خطأ في تحميل البيانات',
              details: error.toString(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageContent(
    BuildContext context,
    SellerSubscription subscription,
    MonthlyQuotaUsage? usage,
  ) {
    final usedListings = usage?.freeListingsUsed ?? 0;
    final totalListings = subscription.monthlyListingQuota;
    final remainingListings = totalListings - usedListings;
    final usagePercentage = usedListings / totalListings;

    return Column(
      children: [
        // Progress indicator
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الإعلانات المستخدمة',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Text(
                  '$usedListings من $totalListings',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: usagePercentage,
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(
                usagePercentage > 0.8 ? Colors.orange : AppColors.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),

        // Usage stats
        Row(
          children: [
            Expanded(
              child: _buildUsageStat(
                context,
                'متبقي',
                '$remainingListings',
                'إعلان',
                context.colors.success,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildUsageStat(
                context,
                'إضافية',
                '${usage?.paidListingsCount ?? 0}',
                'إعلان',
                AppColors.secondary,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildUsageStat(
                context,
                'الرسوم',
                '${((usage?.paidListingsCount ?? 0) * subscription.additionalListingFeeLD).toStringAsFixed(1)} د.ل',
                '',
                Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildUsageStat(
    BuildContext context,
    String label,
    String value,
    String unit,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          if (unit.isNotEmpty)
            Text(
              unit,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
            ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsCard(
    BuildContext context,
    WidgetRef ref,
    SellerSubscription subscription,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجراءات سريعة',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildQuickAction(
                  context,
                  'ترقية الخطة',
                  Icons.upgrade,
                  AppColors.primary,
                  () async {
                    try {
                      await SubscriptionNavigationManager.navigateToSubscriptionPlans(
                        context: context,
                      );
                    } catch (e) {
                      if (context.mounted) {
                        // Handle navigation error using core error handler
                        final navigationError =
                            SubscriptionError.navigationError(
                              message: 'فشل في التنقل إلى صفحة خطط الاشتراك',
                              attemptedRoute: '/subscription/plans',
                              code: 'NAVIGATION_ERROR',
                            );

                        await SubscriptionErrorHandler.handleSubscriptionError(
                          context: context,
                          error: navigationError,
                        );
                      }
                    }
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickAction(
                  context,
                  'سجل الفواتير',
                  Icons.receipt_long,
                  AppColors.secondary,
                  () => context.push('/subscription/billing'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildQuickAction(
                  context,
                  'طرق الدفع',
                  Icons.payment,
                  Colors.orange,
                  () => context.push('/subscription/payment-methods'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickAction(
                  context,
                  'الدعم',
                  Icons.support_agent,
                  context.colors.success,
                  () {
                    // Navigate to support
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAction(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionDetailsCard(
    BuildContext context,
    SellerSubscription subscription,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل الاشتراك',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildDetailRow(
            context,
            'تاريخ البداية',
            '${subscription.startDate.day}/${subscription.startDate.month}/${subscription.startDate.year}',
          ),
          if (subscription.endDate != null)
            _buildDetailRow(
              context,
              'تاريخ الانتهاء',
              '${subscription.endDate!.day}/${subscription.endDate!.month}/${subscription.endDate!.year}',
            ),
          if (subscription.nextBillingDate != null)
            _buildDetailRow(
              context,
              'الدورة القادمة',
              '${subscription.nextBillingDate!.day}/${subscription.nextBillingDate!.month}/${subscription.nextBillingDate!.year}',
            ),
          _buildDetailRow(
            context,
            'التجديد التلقائي',
            subscription.autoRenewal ? 'مفعل' : 'معطل',
          ),
          if (subscription.hasPriorityListing)
            _buildDetailRow(context, 'الإعلانات المميزة', 'مفعل'),
          if (subscription.hasPrioritySupport)
            _buildDetailRow(context, 'الدعم المميز', 'مفعل'),
          if (subscription.hasDedicatedSupport)
            _buildDetailRow(context, 'الدعم المخصص', 'مفعل'),
        ],
      ),
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
          ),
          Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildManagementActionsCard(
    BuildContext context,
    WidgetRef ref,
    SellerSubscription subscription,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إدارة الاشتراك',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Auto-renewal toggle
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'التجديد التلقائي',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'تجديد الاشتراك تلقائياً عند انتهائه',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
              Switch(
                value: subscription.autoRenewal,
                onChanged: (value) {
                  ref
                      .read(
                        providers.sellerSubscriptionProviderProvider.notifier,
                      )
                      .updateAutoRenewal(value);
                },
                activeColor: AppColors.primary,
              ),
            ],
          ),

          const SizedBox(height: 20),
          const Divider(),
          const SizedBox(height: 20),

          // Cancel subscription
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () => _showCancelDialog(context, ref),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'إلغاء الاشتراك',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showCancelDialog(BuildContext context, WidgetRef ref) {
    final subscription = ref
        .watch(providers.sellerSubscriptionProviderProvider)
        .value;
    if (subscription == null) return;

    final endDate =
        subscription.endDate ??
        (subscription.nextBillingDate ??
            DateTime.now().add(const Duration(days: 30)));
    final daysRemaining = endDate
        .difference(DateTime.now())
        .inDays
        .clamp(0, double.infinity)
        .toInt();

    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.orange, size: 28),
            SizedBox(width: 12),
            Text('إلغاء الاشتراك'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'هل أنت متأكد من رغبتك في إلغاء الاشتراك؟',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue, size: 20),
                      SizedBox(width: 8),
                      Text(
                        'ما سيحدث بعد الإلغاء:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildInfoRow(
                    Icons.check_circle_outline,
                    Colors.green,
                    'سيتم إيقاف التجديد التلقائي فوراً',
                  ),
                  _buildInfoRow(
                    Icons.edit_outlined,
                    Colors.green,
                    'يمكنك إدارة إعلاناتك الحالية لمدة $daysRemaining يوم إضافي',
                  ),
                  _buildInfoRow(
                    Icons.calendar_today_outlined,
                    Colors.blue,
                    'ستنتهي صلاحية الاشتراك في ${_formatDate(endDate)}',
                  ),
                  _buildInfoRow(
                    Icons.block_outlined,
                    Colors.red,
                    'لن تتمكن من إنشاء إعلانات جديدة',
                  ),
                  _buildInfoRow(
                    Icons.refresh_outlined,
                    Colors.orange,
                    'يمكنك إعادة الاشتراك في أي وقت',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'هذا الإجراء لا يمكن التراجع عنه.',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('تراجع'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              try {
                // عرض مؤشر التحميل
                // ignore: unawaited_futures
                showDialog<void>(
                  context: context,
                  barrierDismissible: false,
                  builder: (dialogContext) =>
                      const Center(child: CircularProgressIndicator()),
                );

                final result = await ref
                    .read(providers.sellerSubscriptionProviderProvider.notifier)
                    .cancelSubscriptionGracefully(
                      cancellationReason: 'User requested cancellation',
                    );

                if (context.mounted) {
                  Navigator.of(context).pop(); // إغلاق مؤشر التحميل

                  // عرض نتيجة النجاح
                  _showCancellationSuccessDialog(context, result);
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.of(context).pop(); // إغلاق مؤشر التحميل

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في إلغاء الاشتراك: ${e.toString()}'),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 5),
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('تأكيد الإلغاء'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, Color color, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, color: color, size: 18),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
            ),
          ),
        ],
      ),
    );
  }

  void _showCancellationSuccessDialog(
    BuildContext context,
    providers.SubscriptionCancellationResult result,
  ) {
    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 32),
            SizedBox(width: 12),
            Text('تم إلغاء الاشتراك'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(result.message, style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.calendar_today,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'انتهاء فترة الإدارة: ${_formatDate(result.endDate)}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.timelapse, color: Colors.blue, size: 20),
                      const SizedBox(width: 8),
                      Text('${result.daysRemaining} يوم متبقي لإدارة إعلاناتك'),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildSubscriptionStatus(
    BuildContext context,
    SellerSubscription subscription,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      subscription.status,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getStatusIcon(subscription.status),
                    color: _getStatusColor(subscription.status),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'حالة الاشتراك',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      Text(
                        subscription.status.displayName,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: _getStatusColor(subscription.status),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Special handling for canceled pending expiry
            if (subscription.isCanceledPendingExpiry) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          color: Colors.orange.shade700,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'فترة إدارة الإعلانات الحالية',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade800,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      subscription.statusDescription,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.orange.shade700,
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          color: Colors.orange.shade700,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'ينتهي في: ${_formatDate(subscription.endDate ?? DateTime.now())}',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Quick actions for canceled subscription
              _buildCanceledSubscriptionActions(context),
            ] else ...[
              // Normal subscription info - use existing detailed card
              _buildSubscriptionDetailsCard(context, subscription),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCanceledSubscriptionActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات المتاحة:',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  // Navigate to manage listings
                  context.push('/seller/listings');
                },
                icon: const Icon(Icons.edit_outlined),
                label: const Text('إدارة الإعلانات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () async {
                  try {
                    await SubscriptionNavigationManager.navigateToSubscriptionPlans(
                      context: context,
                    );
                  } catch (e) {
                    if (context.mounted) {
                      // Handle navigation error using core error handler
                      final navigationError = SubscriptionError.navigationError(
                        message: 'فشل في التنقل إلى صفحة خطط الاشتراك',
                        attemptedRoute: '/seller/subscription/plans',
                        code: 'NAVIGATION_ERROR',
                      );

                      await SubscriptionErrorHandler.handleSubscriptionError(
                        context: context,
                        error: navigationError,
                      );
                    }
                  }
                },
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة الاشتراك'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  side: const BorderSide(color: AppColors.primary),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Color _getStatusColor(SubscriptionStatus status) {
    switch (status) {
      case SubscriptionStatus.active:
        return Colors.green;
      case SubscriptionStatus.trial:
        return Colors.blue;
      case SubscriptionStatus.canceledPendingExpiry:
        return Colors.orange;
      case SubscriptionStatus.canceled:
        return Colors.red;
      case SubscriptionStatus.expired:
        return Colors.grey;
      case SubscriptionStatus.frozen:
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(SubscriptionStatus status) {
    switch (status) {
      case SubscriptionStatus.active:
        return Icons.check_circle;
      case SubscriptionStatus.trial:
        return Icons.access_time;
      case SubscriptionStatus.canceledPendingExpiry:
        return Icons.schedule;
      case SubscriptionStatus.canceled:
        return Icons.cancel;
      case SubscriptionStatus.expired:
        return Icons.error;
      case SubscriptionStatus.frozen:
        return Icons.ac_unit;
      default:
        return Icons.help;
    }
  }

  Widget _buildFrozenAccountInterface(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.ac_unit, color: Colors.red.shade700, size: 24),
              const SizedBox(width: 12),
              Text(
                'حساب مجمد',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'تم تجميد حسابك من قبل الإدارة. لا يمكنك إضافة إعلانات جديدة أو تعديل الإعلانات الحالية.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.red.shade700,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // Navigate to support
                    _showSupportDialog(context);
                  },
                  icon: const Icon(Icons.support_agent),
                  label: const Text('اتصل بالدعم'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    // Show account details or status
                    _showAccountStatusDialog(context);
                  },
                  icon: const Icon(Icons.info_outline),
                  label: const Text('تفاصيل الحالة'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red.shade700,
                    side: BorderSide(color: Colors.red.shade300),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showSupportDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.support_agent, color: Colors.blue, size: 28),
            SizedBox(width: 12),
            Text('تواصل مع الدعم'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'للحصول على مساعدة بخصوص حسابك المجمد، يمكنك التواصل مع فريق الدعم عبر:',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 16),
            Text('📧 البريد الإلكتروني: <EMAIL>'),
            SizedBox(height: 8),
            Text('📱 الواتساب: +218 91 234 5678'),
            SizedBox(height: 8),
            Text('⏰ أوقات العمل: 9 صباحاً - 6 مساءً'),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showAccountStatusDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.info_outline, color: Colors.orange, size: 28),
            SizedBox(width: 12),
            Text('حالة الحساب'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'أسباب التجميد الشائعة:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange.shade800,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text('• انتهاك شروط الاستخدام'),
                  const Text('• شكاوى متكررة من العملاء'),
                  const Text('• منتجات مخالفة أو غير قانونية'),
                  const Text('• مشاكل في طرق الدفع'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'للحصول على تفاصيل أكثر حول سبب تجميد حسابك والخطوات المطلوبة لإعادة تفعيله، يرجى التواصل مع فريق الدعم.',
              style: TextStyle(fontSize: 14, height: 1.4),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  /// Build subscription status using core SubscriptionResponse model
  /// This demonstrates integration with the new core models
  Widget _buildSubscriptionStatusWithCoreModel(
    BuildContext context,
    SubscriptionResponse response,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getStatusColorFromResponse(response),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getStatusIconFromResponse(response),
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'حالة الاشتراك',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        response.statusMessageArabic,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: _getStatusColorFromResponse(response),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            // Display subscription details using the new model
            ...response.displayDetails.entries.map(
              (entry) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 100,
                      child: Text(
                        entry.key,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        entry.value,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Show error message if available
            if (response.errorMessageArabic != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: Colors.red.shade600,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        response.errorMessageArabic!,
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Get status color from SubscriptionResponse
  Color _getStatusColorFromResponse(SubscriptionResponse response) {
    if (response.isSuccess) {
      return Colors.green;
    } else if (response.isPending || response.isProcessing) {
      return Colors.orange;
    } else if (response.isFailed) {
      return Colors.red;
    } else {
      return Colors.grey;
    }
  }

  /// Get status icon from SubscriptionResponse
  IconData _getStatusIconFromResponse(SubscriptionResponse response) {
    if (response.isSuccess) {
      return Icons.check_circle;
    } else if (response.isPending) {
      return Icons.schedule;
    } else if (response.isProcessing) {
      return Icons.sync;
    } else if (response.isFailed) {
      return Icons.error;
    } else {
      return Icons.help_outline;
    }
  }

  /// Build error state using new SubscriptionErrorHandler
  Widget _buildErrorState(BuildContext context, Object error, WidgetRef ref) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 80, color: Colors.red.shade400),
            const SizedBox(height: 24),
            Text(
              'خطأ في تحميل بيانات الاشتراك',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ أثناء تحميل بيانات الاشتراك. يرجى المحاولة مرة أخرى.',
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () async {
                    // Handle error using core SubscriptionErrorHandler
                    final subscriptionError = SubscriptionError.databaseError(
                      message: 'Failed to load subscription data',
                      code: 'LOAD_SUBSCRIPTION_ERROR',
                      details: {'originalError': error.toString()},
                    );

                    await SubscriptionErrorHandler.handleSubscriptionError(
                      context: context,
                      error: subscriptionError,
                      onRetry: () {
                        // Refresh providers - variables store return values to satisfy linter
                        // ignore: unused_local_variable
                        final subscription = ref.refresh(
                          providers.sellerSubscriptionProviderProvider,
                        );
                        // ignore: unused_local_variable
                        final quotaUsage = ref.refresh(
                          providers.monthlyQuotaUsageProvider,
                        );
                      },
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text('إعادة المحاولة'),
                ),
                const SizedBox(width: 16),
                OutlinedButton(
                  onPressed: () {
                    // Navigate back using SubscriptionNavigationManager
                    SubscriptionNavigationManager.goBack(context);
                  },
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text('العودة'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
