/// شاشة تأكيد نجاح طلب الانضمام كبائع
///
/// تُعرض هذه الشاشة بعد أن يقوم المستخدم بتقديم طلب الانضمام كبائع بنجاح.
/// تؤكد للمستخدم أن طلبه قد تم استلامه وهو قيد المراجعة،
/// وتوضح الخطوات التالية المتوقعة.
library;

import 'package:flutter/material.dart';

/// شاشة نجاح تقديم طلب البائع
///
/// تُعرض هذه الشاشة بعد أن يكمل المستخدم تقديم طلبه ليصبح بائعاً.
/// تُعلمه بأن طلبه قيد المراجعة حالياً، وتوضح أن عملية المراجعة قد تستغرق
/// ما يصل إلى 48 ساعة، مع توفير زر للعودة إلى الشاشة الرئيسية.
class SellerApplicationSuccessScreen extends StatelessWidget {
  const SellerApplicationSuccessScreen({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(
      automaticallyImplyLeading: false,
      backgroundColor: Colors.transparent,
      elevation: 0,
    ),
    body: SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            const Spacer(),

            // Success Animation
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).colorScheme.primaryContainer,
              ),
              child: Icon(
                Icons.check_circle,
                size: 80,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),

            const SizedBox(height: 32),

            // Success Title
            Text(
              'تم إرسال طلبك بنجاح!',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Success Message
            Text(
              'شكراً لك على تقديم طلب أن تصبح بائع في كارناو. سيتم مراجعة طلبك من قبل فريقنا خلال 3-5 أيام عمل.',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // Status Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.orange.withAlpha((0.20 * 255).toInt()),
                        ),
                        child: const Icon(
                          Icons.schedule,
                          color: Colors.orange,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'حالة الطلب: في الانتظار',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'سيتم التواصل معك عبر البريد الإلكتروني أو الهاتف',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onSurfaceVariant,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Next Steps
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'الخطوات التالية',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(
                                context,
                              ).colorScheme.onPrimaryContainer,
                            ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildNextStep(context, '1', 'مراجعة البيانات والمستندات'),
                  _buildNextStep(context, '2', 'التواصل معك لأي استفسارات'),
                  _buildNextStep(context, '3', 'إشعارك بنتيجة الطلب'),
                  _buildNextStep(context, '4', 'تفعيل حسابك كبائع'),
                ],
              ),
            ),

            const Spacer(),

            // Action Buttons
            Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  child: FilledButton(
                    onPressed: () {
                      Navigator.of(
                        context,
                      ).pushNamedAndRemoveUntil('/account', (route) => false);
                    },
                    child: const Text('العودة إلى حسابي'),
                  ),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.of(
                        context,
                      ).pushNamed('/seller-application-status');
                    },
                    child: const Text('متابعة حالة الطلب'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ),
  );

  Widget _buildNextStep(BuildContext context, String number, String text) =>
      Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
              child: Center(
                child: Text(
                  number,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                text,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ),
          ],
        ),
      );
}
