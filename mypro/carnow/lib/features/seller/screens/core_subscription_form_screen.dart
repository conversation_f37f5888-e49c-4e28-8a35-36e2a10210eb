import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/models/subscription_error.dart';
import '../../../core/providers/subscription_flow_provider.dart';
import '../../../core/error/subscription_error_handler.dart';
import '../navigation/subscription_navigation_manager.dart';
import '../../../core/theme/app_colors.dart';
import '../../../shared/widgets/custom_text_field.dart';

/// Core subscription form screen using core models and providers
/// Follows Forever Plan Architecture: Flutter UI Only → Go API → Supabase Data
class CoreSubscriptionFormScreen extends ConsumerStatefulWidget {
  const CoreSubscriptionFormScreen({super.key});

  @override
  ConsumerState<CoreSubscriptionFormScreen> createState() => _CoreSubscriptionFormScreenState();
}

class _CoreSubscriptionFormScreenState extends ConsumerState<CoreSubscriptionFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _storeNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _cityController = TextEditingController();
  final _addressController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  String _selectedPlanType = '';
  double _selectedPrice = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  @override
  void dispose() {
    _storeNameController.dispose();
    _phoneController.dispose();
    _cityController.dispose();
    _addressController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _initializeForm() {
    // Initialize form with current data from core provider
    final formData = ref.read(coreSubscriptionFormDataProvider);
    _storeNameController.text = formData.storeName;
    _phoneController.text = formData.phone;
    _cityController.text = formData.city;
    _addressController.text = formData.address;
    _descriptionController.text = formData.description;
    _selectedPlanType = formData.planType;
    _selectedPrice = formData.price;
  }

  @override
  Widget build(BuildContext context) {
    final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
    final isLoading = ref.watch(isCoreSubscriptionLoadingProvider);
    final error = ref.watch(coreSubscriptionErrorProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء اشتراك جديد'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => SubscriptionNavigationManager.goBack(context),
        ),
      ),
      body: _buildBody(context, flowState, isLoading, error),
      bottomNavigationBar: _buildBottomActions(context, flowState, isLoading),
    );
  }

  Widget _buildBody(
    BuildContext context,
    CoreSubscriptionFlowState flowState,
    bool isLoading,
    SubscriptionError? error,
  ) {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري معالجة طلب الاشتراك...'),
          ],
        ),
      );
    }

    if (error != null) {
      return _buildErrorState(context, error);
    }

    switch (flowState.status) {
      case CoreSubscriptionFlowStatus.initial:
      case CoreSubscriptionFlowStatus.editing:
        return _buildFormStep(context, flowState);
      
      case CoreSubscriptionFlowStatus.validating:
        return _buildValidationStep(context, flowState);
      
      case CoreSubscriptionFlowStatus.submitting:
      case CoreSubscriptionFlowStatus.processing:
        return _buildProcessingStep(context, flowState);
      
      case CoreSubscriptionFlowStatus.completed:
        return _buildSuccessStep(context, flowState);
      
      case CoreSubscriptionFlowStatus.failed:
        return _buildFailureStep(context, flowState);
      
      case CoreSubscriptionFlowStatus.cancelled:
        return _buildCancelledStep(context, flowState);
    }
  }

  Widget _buildFormStep(BuildContext context, CoreSubscriptionFlowState flowState) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Plan Selection Section
            _buildPlanSelectionSection(context, flowState),
            const SizedBox(height: 24),
            
            // Store Information Section
            _buildStoreInformationSection(context, flowState),
            const SizedBox(height: 24),
            
            // Contact Information Section
            _buildContactInformationSection(context, flowState),
            const SizedBox(height: 24),
            
            // Validation Errors Display
            if (flowState.formData.validationErrors.isNotEmpty)
              _buildValidationErrorsSection(context, flowState.formData.validationErrors),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanSelectionSection(BuildContext context, CoreSubscriptionFlowState flowState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار خطة الاشتراك',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Plan Type Dropdown
            DropdownButtonFormField<String>(
              value: _selectedPlanType.isNotEmpty ? _selectedPlanType : null,
              decoration: const InputDecoration(
                labelText: 'نوع الخطة',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'basic', child: Text('الخطة الأساسية')),
                DropdownMenuItem(value: 'premium', child: Text('الخطة المميزة')),
                DropdownMenuItem(value: 'enterprise', child: Text('خطة المؤسسات')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedPlanType = value ?? '';
                  // Set price based on plan type
                  switch (value) {
                    case 'basic':
                      _selectedPrice = 99.0;
                      break;
                    case 'premium':
                      _selectedPrice = 199.0;
                      break;
                    case 'enterprise':
                      _selectedPrice = 499.0;
                      break;
                    default:
                      _selectedPrice = 0.0;
                  }
                });
                _updateFormData();
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار نوع الخطة';
                }
                return null;
              },
            ),
            
            if (_selectedPrice > 0) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.monetization_on, color: AppColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'السعر: ${_selectedPrice.toStringAsFixed(0)} ريال سعودي',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStoreInformationSection(BuildContext context, CoreSubscriptionFlowState flowState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المتجر',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _storeNameController,
              labelText: 'اسم المتجر',
              hintText: 'أدخل اسم المتجر',
              prefixIcon: Icon(Icons.store),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم المتجر';
                }
                if (value.trim().length < 3) {
                  return 'يجب أن يكون اسم المتجر 3 أحرف على الأقل';
                }
                return null;
              },
              onChanged: (_) => _updateFormData(),
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _descriptionController,
              labelText: 'وصف المتجر',
              hintText: 'أدخل وصف مختصر للمتجر',
              prefixIcon: Icon(Icons.description),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال وصف المتجر';
                }
                if (value.trim().length < 10) {
                  return 'يجب أن يكون الوصف 10 أحرف على الأقل';
                }
                return null;
              },
              onChanged: (_) => _updateFormData(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInformationSection(BuildContext context, CoreSubscriptionFlowState flowState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الاتصال والموقع',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _phoneController,
              labelText: 'رقم الهاتف',
              hintText: '05xxxxxxxx',
              prefixIcon: Icon(Icons.phone),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }
                if (!RegExp(r'^05\d{8}$').hasMatch(value.trim())) {
                  return 'يرجى إدخال رقم هاتف صحيح (05xxxxxxxx)';
                }
                return null;
              },
              onChanged: (_) => _updateFormData(),
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _cityController,
              labelText: 'المدينة',
              hintText: 'أدخل اسم المدينة',
              prefixIcon: Icon(Icons.location_city),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم المدينة';
                }
                return null;
              },
              onChanged: (_) => _updateFormData(),
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _addressController,
              labelText: 'العنوان',
              hintText: 'أدخل العنوان التفصيلي',
              prefixIcon: Icon(Icons.location_on),
              maxLines: 2,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال العنوان';
                }
                if (value.trim().length < 10) {
                  return 'يجب أن يكون العنوان 10 أحرف على الأقل';
                }
                return null;
              },
              onChanged: (_) => _updateFormData(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildValidationErrorsSection(BuildContext context, Map<String, String> errors) {
    return Card(
      color: Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.error_outline, color: Colors.red.shade700),
                const SizedBox(width: 8),
                Text(
                  'أخطاء في النموذج',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...errors.entries.map((entry) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                '• ${entry.value}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.red.shade600,
                ),
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildValidationStep(BuildContext context, CoreSubscriptionFlowState flowState) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري التحقق من البيانات...'),
        ],
      ),
    );
  }

  Widget _buildProcessingStep(BuildContext context, CoreSubscriptionFlowState flowState) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          const Text('جاري إنشاء الاشتراك...'),
          const SizedBox(height: 8),
          LinearProgressIndicator(value: flowState.progress),
          const SizedBox(height: 8),
          Text('${(flowState.progress * 100).toInt()}%'),
        ],
      ),
    );
  }

  Widget _buildSuccessStep(BuildContext context, CoreSubscriptionFlowState flowState) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              size: 80,
              color: Colors.green.shade400,
            ),
            const SizedBox(height: 24),
            Text(
              'تم إنشاء الاشتراك بنجاح!',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
            ),
            const SizedBox(height: 16),
            if (flowState.successMessageAr != null)
              Text(
                flowState.successMessageAr!,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFailureStep(BuildContext context, CoreSubscriptionFlowState flowState) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 24),
            Text(
              'فشل في إنشاء الاشتراك',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 16),
            if (flowState.error != null)
              Text(
                flowState.error!.userFriendlyMessageArabic,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCancelledStep(BuildContext context, CoreSubscriptionFlowState flowState) {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cancel_outlined,
              size: 80,
              color: Colors.orange,
            ),
            SizedBox(height: 24),
            Text(
              'تم إلغاء عملية الاشتراك',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, SubscriptionError error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 24),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              error.userFriendlyMessageArabic,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () async {
                await SubscriptionErrorHandler.handleSubscriptionError(
                  context: context,
                  error: error,
                  onRetry: () {
                    ref.read(coreSubscriptionFlowProviderProvider.notifier).retrySubmission();
                  },
                );
              },
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActions(BuildContext context, CoreSubscriptionFlowState flowState, bool isLoading) {
    if (isLoading || 
        flowState.status == CoreSubscriptionFlowStatus.submitting ||
        flowState.status == CoreSubscriptionFlowStatus.processing) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: 1.0,
          ),
        ),
      ),
      child: Row(
        children: [
          // Back button
          if (flowState.canNavigateBack) ...[
            Expanded(
              child: OutlinedButton(
                onPressed: () => SubscriptionNavigationManager.goBack(context),
                child: const Text('السابق'),
              ),
            ),
            const SizedBox(width: 12.0),
          ],
          
          // Primary action button
          Expanded(
            flex: flowState.canNavigateBack ? 1 : 2,
            child: _buildPrimaryActionButton(context, flowState),
          ),
        ],
      ),
    );
  }

  Widget _buildPrimaryActionButton(BuildContext context, CoreSubscriptionFlowState flowState) {
    switch (flowState.status) {
      case CoreSubscriptionFlowStatus.initial:
      case CoreSubscriptionFlowStatus.editing:
        return FilledButton(
          onPressed: flowState.formData.isValid ? _handleSubmit : null,
          child: const Text('إنشاء الاشتراك'),
        );
      
      case CoreSubscriptionFlowStatus.completed:
        return FilledButton(
          onPressed: () {
            final subscriptionId = flowState.response?.id;
            if (subscriptionId != null) {
              SubscriptionNavigationManager.navigateToSubscriptionStatus(
                context: context,
                subscriptionId: subscriptionId,
              );
            }
          },
          child: const Text('عرض الاشتراك'),
        );
      
      case CoreSubscriptionFlowStatus.failed:
        return FilledButton(
          onPressed: _handleRetry,
          child: const Text('إعادة المحاولة'),
        );
      
      case CoreSubscriptionFlowStatus.cancelled:
        return FilledButton(
          onPressed: () => context.pop(),
          child: const Text('إغلاق'),
        );
      
      default:
        return const SizedBox.shrink();
    }
  }

  // =============================================================================
  // EVENT HANDLERS
  // =============================================================================

  void _updateFormData() {
    final formData = CoreSubscriptionFormData(
      storeName: _storeNameController.text.trim(),
      phone: _phoneController.text.trim(),
      city: _cityController.text.trim(),
      address: _addressController.text.trim(),
      description: _descriptionController.text.trim(),
      planType: _selectedPlanType,
      price: _selectedPrice,
      userId: ref.read(coreSubscriptionFormDataProvider).userId,
    );

    ref.read(coreSubscriptionFlowProviderProvider.notifier).updateFormField(
      storeName: formData.storeName,
      phone: formData.phone,
      city: formData.city,
      address: formData.address,
      description: formData.description,
      planType: formData.planType,
      price: formData.price,
    );
  }

  void _handleSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      _updateFormData();
      ref.read(coreSubscriptionFlowProviderProvider.notifier).submitSubscription();
    }
  }

  void _handleRetry() {
    ref.read(coreSubscriptionFlowProviderProvider.notifier).retrySubmission();
  }
}
