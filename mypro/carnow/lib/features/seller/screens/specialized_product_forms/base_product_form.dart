import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/models/enums.dart';
import '../../../products/models/product_model.dart';
import '../../../../core/providers/product_provider.dart';
import '../../../../l10n/app_localizations.dart';
import '../../../../shared/widgets/primary_button.dart';
import '../../widgets/image_picker_widget.dart';
import '../../../../core/repositories/product_repository.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/auth/unified_auth_provider.dart';
import '../../../../core/models/product_model.dart' show ProductModelUtils;
import '../../../products/services/product_api_service.dart';

/// A base screen for adding/editing products, intended to be extended by
/// different product type forms.
abstract class BaseProductFormScreen extends HookConsumerWidget {
  const BaseProductFormScreen({
    super.key,
    this.productId,
  });

  final String? productId;

  /// Gets the title for the screen.
  String getScreenTitle(AppLocalizations l10n);

  /// Builds the specialized fields for each product type.
  Widget buildSpecializedFields(
    BuildContext context,
    GlobalKey<FormState> formKey,
    Map<String, TextEditingController> controllers,
    Map<String, dynamic> formState,
    void Function(String, dynamic) updateFormState,
  );

  /// Validates specialized fields.
  String? validateSpecializedFields(Map<String, dynamic> formState);

  /// Prepares product data for saving.
  Map<String, dynamic> prepareProductData(
    Map<String, TextEditingController> controllers,
    Map<String, dynamic> formState,
    String sellerId,
  );

  Future<ProductModel?> _loadExistingProduct(
    WidgetRef ref,
    String productId,
  ) async {
    final productAsyncValue = ref.watch(productProvider(productId));
    return productAsyncValue.asData?.value;
  }

  Future<void> _submitForm(
    BuildContext context,
    WidgetRef ref,
    GlobalKey<FormState> formKey,
    Map<String, TextEditingController> controllers,
    ValueNotifier<Map<String, dynamic>> formState,
    bool isEditing,
    String? productId,
  ) async {
    if (!formKey.currentState!.validate()) {
      return;
    }
    final specializedValidationError = validateSpecializedFields(
      formState.value,
    );
    if (specializedValidationError != null) {
      formState.value = {
        ...formState.value,
        'errorMessage': specializedValidationError,
      };
      return;
    }

    // Begin loading
    formState.value = {
      ...formState.value,
      'isLoading': true,
      'errorMessage': null,
    };

    try {
      // 1. Retrieve current authenticated user as the seller
      final seller = ref.read(currentUserProvider);
      if (seller == null) {
        throw Exception('User not authenticated');
      }
      final sellerId = seller.id;

      // 2. Determine product id – generate for create, use existing for edit
      final String effectiveProductId = isEditing
          ? productId!
          : const Uuid().v4();

      // 3. Handle image uploads (if any)
      final imageFiles = (formState.value['images'] as List<File>?) ?? [];
      final storageService = ref.read(storageServiceProvider);
      List<String> imageUrls = [];
      if (imageFiles.isNotEmpty) {
        imageUrls = await storageService.uploadMultipleProductImages(
          imageFiles,
          effectiveProductId,
        );
      }

      // 1. Collect basic form data
      final baseData = {
        'name': controllers['name']!.text,
        'description': controllers['description']!.text,
        'price': double.parse(controllers['price']!.text),
        'original_price': controllers['originalPrice']!.text.isNotEmpty
            ? double.parse(controllers['originalPrice']!.text)
            : null,
        'stock_quantity': int.parse(controllers['quantity']!.text),
        'location': controllers['location']!.text,
      };

      // 2. Collect specialized data from subclasses
      final specializedData = <String, dynamic>{}; // Empty for base class

      // 3. Combine data
      final combinedData = {...baseData, ...specializedData};

      // 4. Convert to ProductModel using utility for snake_case conversion
      final productModel = ProductModelUtils.fromMap(combinedData);

      // 5. Persist via API service
      final apiService = ref.read(productApiServiceProvider);
      if (isEditing) {
        await apiService.updateProduct(productId!, productModel.toJson());
      } else {
        final user = ref.read(currentUserProvider);
        await apiService.createProduct(user!.id, productModel.toJson());
      }

      // 8. Success feedback & pop
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isEditing
                  ? 'Product updated successfully'
                  : 'Product created successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } catch (e) {
      formState.value = {
        ...formState.value,
        'isLoading': false,
        'errorMessage': 'Failed to save product: $e',
      };
    } finally {
      if (formState.value['isLoading']) {
        formState.value = {...formState.value, 'isLoading': false};
      }
    }
  }

  void _confirmDelete(BuildContext context, WidgetRef ref, String productId) {
    showDialog<void>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Confirm Deletion'),
          content: const Text('Are you sure you want to delete this product?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(dialogContext).pop(),
            ),
            TextButton(
              child: const Text('Delete'),
              onPressed: () async {
                try {
                  // TODO: Implement delete logic via a repository.
                  // await ref.read(productRepositoryProvider).deleteProduct(productId);
                  Navigator.of(dialogContext).pop();
                  context.pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Product deleted'),
                      backgroundColor: Colors.red,
                    ),
                  );
                } catch (e) {
                  Navigator.of(dialogContext).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete product: $e'),
                      backgroundColor: Theme.of(context).colorScheme.error,
                    ),
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final isEditing = productId != null;

    final formKey = useMemoized(GlobalKey<FormState>.new);
    final controllers = useMemoized(
      () => {
        'name': TextEditingController(),
        'description': TextEditingController(),
        'price': TextEditingController(),
        'originalPrice': TextEditingController(),
        'quantity': TextEditingController(text: '1'),
        'location': TextEditingController(),
      },
    );

    final formState = useState<Map<String, dynamic>>({
      'condition': ProductCondition.used,
      'images': <File>[],
      'category': null,
      'isLoading': false,
      'errorMessage': null,
    });

    useEffect(() {
      if (isEditing) {
        _loadExistingProduct(ref, productId!).then((product) {
          if (product != null) {
            controllers['name']!.text = product.name;
            controllers['description']!.text = product.description ?? '';
            controllers['price']!.text = product.price.toString();
            controllers['originalPrice']!.text =
                product.originalPrice?.toString() ?? '';
            controllers['quantity']!.text = product.stockQuantity.toString();
            controllers['location']!.text = product.location ?? '';
            formState.value = {
              ...formState.value,
              'condition': product.condition,
              // TODO: Load additional product-specific data if necessary.
            };
          }
        });
      }
      return null;
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(getScreenTitle(l10n)),
        actions: [
          if (isEditing)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _confirmDelete(context, ref, productId!),
              tooltip: 'Delete', // TODO: l10n.delete
            ),
        ],
      ),
      body: Form(
        key: formKey,
        child: ListView(
          // Ensure content (especially the bottom action button) isn't hidden
          // behind system UI (e.g. gesture navigation bar) on physical devices.
          padding: EdgeInsets.fromLTRB(
            16,
            16,
            16,
            16 + MediaQuery.of(context).padding.bottom,
          ),
          children: [
            if (formState.value['errorMessage'] != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Text(
                  'Error: ${formState.value['errorMessage']}',
                  style: TextStyle(color: theme.colorScheme.error),
                ),
              ),
            _buildBasicInfoSection(context, controllers, formState),
            const SizedBox(height: 16),
            buildSpecializedFields(
              context,
              formKey,
              controllers,
              formState.value,
              (key, value) {
                formState.value = {...formState.value, key: value};
              },
            ),
            const SizedBox(height: 16),
            _buildImagesSection(context, formState),
            const SizedBox(height: 32),
            PrimaryButton(
              onPressed: formState.value['isLoading'] == true
                  ? null
                  : () => _submitForm(
                      context,
                      ref,
                      formKey,
                      controllers,
                      formState,
                      isEditing,
                      productId,
                    ),
              text: isEditing ? 'Update Product' : 'Add Product', // TODO
              isLoading: formState.value['isLoading'] == true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection(
    BuildContext context,
    Map<String, TextEditingController> controllers,
    ValueNotifier<Map<String, dynamic>> formState,
  ) {
    final theme = Theme.of(context);
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information', // TODO: l10n.basicInformation
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: controllers['name'],
              decoration: const InputDecoration(labelText: 'Product Name *'),
              textCapitalization: TextCapitalization.words,
              validator: (v) =>
                  v?.isEmpty ?? true ? 'This field is required' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: controllers['description'],
              decoration: const InputDecoration(
                labelText: 'Product Description *',
                alignLabelWithHint: true,
              ),
              maxLines: 3,
              textCapitalization: TextCapitalization.sentences,
              validator: (v) =>
                  v?.isEmpty ?? true ? 'This field is required' : null,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextFormField(
                    controller: controllers['price'],
                    decoration: const InputDecoration(
                      labelText: 'Price *',
                      suffixText: 'LYD',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    validator: (v) {
                      if (v?.isEmpty ?? true) return 'This field is required';
                      if (double.tryParse(v!) == null) {
                        return 'Please enter a valid number';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: TextFormField(
                    controller: controllers['originalPrice'],
                    decoration: const InputDecoration(
                      labelText: 'Original Price',
                      suffixText: 'LYD',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConditionDropdown(context, formState),
          ],
        ),
      ),
    );
  }

  Widget _buildConditionDropdown(
    BuildContext context,
    ValueNotifier<Map<String, dynamic>> formState,
  ) {
    return DropdownButtonFormField<ProductCondition>(
      value: formState.value['condition'],
      decoration: const InputDecoration(
        labelText: 'Condition',
        border: OutlineInputBorder(),
      ),
      items: ProductCondition.values
          .map(
            (condition) => DropdownMenuItem(
              value: condition,
              child: Text(_getConditionName(context, condition)),
            ),
          )
          .toList(),
      onChanged: (value) {
        if (value != null) {
          formState.value = {...formState.value, 'condition': value};
        }
      },
      validator: (value) => value == null ? 'Please select a condition' : null,
    );
  }

  String _getConditionName(BuildContext context, ProductCondition condition) {
    switch (condition) {
      case ProductCondition.new_:
        return 'New';
      case ProductCondition.likeNew:
        return 'Like New';
      case ProductCondition.used:
        return 'Used';
      case ProductCondition.good:
        return 'Good';
      case ProductCondition.fair:
        return 'Fair';
      case ProductCondition.poor:
        return 'Poor';
      case ProductCondition.forParts:
        return 'For Parts';
      case ProductCondition.refurbished:
        return 'Refurbished';
    }
  }

  Widget _buildImagesSection(
    BuildContext context,
    ValueNotifier<Map<String, dynamic>> formState,
  ) {
    final theme = Theme.of(context);
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Images',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ImagePickerWidget(
              onImagesChanged: (images) {
                formState.value = {...formState.value, 'images': images};
              },
            ),
          ],
        ),
      ),
    );
  }
}
