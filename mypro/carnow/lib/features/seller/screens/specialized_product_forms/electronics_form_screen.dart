import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../l10n/app_localizations.dart';
import '../../../../core/models/enums.dart';
import 'base_product_form.dart';

/// شاشة نموذج إضافة/تعديل المنتجات الإلكترونية
///
/// توفر نموذجًا متخصصًا لإدخال تفاصيل المنتجات التي تندرج تحت فئة الإلكترونيات.
/// تتضمن حقولاً خاصة مثل نوع الجهاز، الماركة، الموديل، سعة التخزين، وحالة البطارية.
/// ترث من `BaseProductFormScreen` وتضيف الحقول المخصصة لهذه الفئة.
class ElectronicsFormScreen extends BaseProductFormScreen {
  const ElectronicsFormScreen({super.key, super.productId});

  @override
  String getScreenTitle(AppLocalizations l10n) {
    return productId != null
        ? 'تعديل منتج إلكتروني'
        : 'إضافة منتج إلكتروني جديد';
  }

  @override
  Widget buildSpecializedFields(
    BuildContext context,
    GlobalKey<FormState> formKey,
    Map<String, TextEditingController> controllers,
    Map<String, dynamic> formState,
    Function(String, dynamic) updateFormState,
  ) {
    final theme = Theme.of(context);

    // Add specialized controllers
    controllers.putIfAbsent('brand', TextEditingController.new);
    controllers.putIfAbsent('model', TextEditingController.new);
    controllers.putIfAbsent('serialNumber', TextEditingController.new);
    controllers.putIfAbsent('color', TextEditingController.new);
    controllers.putIfAbsent('storage', TextEditingController.new);
    controllers.putIfAbsent('batteryHealth', TextEditingController.new);
    controllers.putIfAbsent('warranty', TextEditingController.new);
    controllers.putIfAbsent('accessories', TextEditingController.new);

    return Column(
      children: [
        // Device Information Card
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'معلومات الجهاز',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 16),

                // Electronics Category
                DropdownButtonFormField<ElectronicsCategory>(
                  value: formState['electronicsCategory'],
                  decoration: const InputDecoration(
                    labelText: 'نوع الجهاز *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.devices),
                  ),
                  items: ElectronicsCategory.values
                      .map(
                        (category) => DropdownMenuItem(
                          value: category,
                          child: Text(_getElectronicsCategoryName(category)),
                        ),
                      )
                      .toList(),
                  onChanged: (value) =>
                      updateFormState('electronicsCategory', value),
                  validator: (value) =>
                      value == null ? 'هذا الحقل مطلوب' : null,
                ),
                const SizedBox(height: 16),

                // Brand and Model
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: controllers['brand'],
                        decoration: const InputDecoration(
                          labelText: 'الماركة *',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.business),
                        ),
                        textCapitalization: TextCapitalization.words,
                        validator: (value) =>
                            value?.isEmpty ?? true ? 'هذا الحقل مطلوب' : null,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: controllers['model'],
                        decoration: const InputDecoration(
                          labelText: 'الموديل *',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.phone_android),
                        ),
                        textCapitalization: TextCapitalization.words,
                        validator: (value) =>
                            value?.isEmpty ?? true ? 'هذا الحقل مطلوب' : null,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Serial Number and Color
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: controllers['serialNumber'],
                        decoration: const InputDecoration(
                          labelText: 'الرقم التسلسلي',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.confirmation_number),
                        ),
                        textCapitalization: TextCapitalization.characters,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: controllers['color'],
                        decoration: const InputDecoration(
                          labelText: 'اللون',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.palette),
                        ),
                        textCapitalization: TextCapitalization.words,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Technical Specifications Card
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'المواصفات التقنية',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 16),

                // Storage
                TextFormField(
                  controller: controllers['storage'],
                  decoration: const InputDecoration(
                    labelText: 'سعة التخزين',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.storage),
                    hintText: 'مثال: 64GB, 128GB, 256GB',
                  ),
                ),
                const SizedBox(height: 16),

                // Battery Health (for devices with batteries)
                if (_shouldShowBatteryHealth(formState['electronicsCategory']))
                  Column(
                    children: [
                      TextFormField(
                        controller: controllers['batteryHealth'],
                        decoration: const InputDecoration(
                          labelText: 'حالة البطارية (%)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.battery_std),
                          hintText: 'مثال: 85, 90, 95',
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(3),
                        ],
                        validator: (value) {
                          if (value?.isNotEmpty == true) {
                            final health = int.tryParse(value!);
                            if (health == null || health < 0 || health > 100) {
                              return 'أدخل نسبة صحيحة (0-100)';
                            }
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),

                // Warranty
                TextFormField(
                  controller: controllers['warranty'],
                  decoration: const InputDecoration(
                    labelText: 'فترة الضمان',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.verified_user),
                    hintText: 'مثال: سنة واحدة، 6 أشهر، ضمان الوكيل',
                  ),
                ),
                const SizedBox(height: 16),

                // Accessories
                TextFormField(
                  controller: controllers['accessories'],
                  decoration: const InputDecoration(
                    labelText: 'الإكسسوارات المرفقة',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.extension),
                    hintText: 'مثال: شاحن، سماعات، جراب',
                  ),
                  maxLines: 2,
                  textCapitalization: TextCapitalization.sentences,
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Device Status Card
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حالة الجهاز',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 16),

                // Working condition checkboxes
                CheckboxListTile(
                  title: const Text('الجهاز يعمل بشكل طبيعي'),
                  subtitle: const Text('جميع الوظائف تعمل بدون مشاكل'),
                  value: formState['isWorking'] ?? true,
                  onChanged: (value) =>
                      updateFormState('isWorking', value ?? true),
                  controlAffinity: ListTileControlAffinity.leading,
                ),

                CheckboxListTile(
                  title: const Text('يحتاج إصلاح'),
                  subtitle: const Text('يوجد عطل أو مشكلة في الجهاز'),
                  value: formState['needsRepair'] ?? false,
                  onChanged: (value) =>
                      updateFormState('needsRepair', value ?? false),
                  controlAffinity: ListTileControlAffinity.leading,
                ),

                CheckboxListTile(
                  title: const Text('مفتوح/معدل'),
                  subtitle: const Text('تم فتح الجهاز أو تعديله'),
                  value: formState['isModified'] ?? false,
                  onChanged: (value) =>
                      updateFormState('isModified', value ?? false),
                  controlAffinity: ListTileControlAffinity.leading,
                ),

                CheckboxListTile(
                  title: const Text('مقفل بكلمة مرور'),
                  subtitle: const Text('محمي بكلمة مرور أو بصمة'),
                  value: formState['isLocked'] ?? false,
                  onChanged: (value) =>
                      updateFormState('isLocked', value ?? false),
                  controlAffinity: ListTileControlAffinity.leading,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  String? validateSpecializedFields(Map<String, dynamic> formState) {
    // Check if category is selected
    if (formState['electronicsCategory'] == null) {
      return 'يرجى اختيار نوع الجهاز';
    }

    return null;
  }

  @override
  Map<String, dynamic> prepareProductData(
    Map<String, TextEditingController> controllers,
    Map<String, dynamic> formState,
    String sellerId,
  ) {
    return {
      'name': controllers['name']!.text.trim(),
      'description': controllers['description']!.text.trim(),
      'price': double.parse(controllers['price']!.text),
      'originalPrice': controllers['originalPrice']!.text.isNotEmpty
          ? double.parse(controllers['originalPrice']!.text)
          : null,
      'condition': formState['condition'],
      'stockQuantity': int.parse(controllers['quantity']!.text),
      'location': controllers['location']!.text.trim(),
      'sellerId': sellerId,
      'productType': ProductType.electronics,
      'categoryId': formState['electronicsCategory'].toString(),

      // Electronics specific fields
      'brand': controllers['brand']!.text.trim(),
      'model': controllers['model']!.text.trim(),
      'serialNumber': controllers['serialNumber']!.text.trim(),
      'color': controllers['color']!.text.trim(),
      'storage': controllers['storage']!.text.trim(),
      'batteryHealth': controllers['batteryHealth']!.text.isNotEmpty
          ? int.parse(controllers['batteryHealth']!.text)
          : null,
      'warranty': controllers['warranty']!.text.trim(),
      'accessories': controllers['accessories']!.text
          .split(',')
          .map((a) => a.trim())
          .where((a) => a.isNotEmpty)
          .toList(),

      // Additional specifications
      'specifications': {
        'isWorking': formState['isWorking'] ?? true,
        'needsRepair': formState['needsRepair'] ?? false,
        'isModified': formState['isModified'] ?? false,
        'isLocked': formState['isLocked'] ?? false,
        'category': formState['electronicsCategory'].toString(),
      },

      // Images will be handled by the base class
      'images': formState['images'] ?? [],
    };
  }

  bool _shouldShowBatteryHealth(ElectronicsCategory? category) {
    if (category == null) return false;

    return [
      ElectronicsCategory.audio,
      ElectronicsCategory.video,
      ElectronicsCategory.navigation,
      ElectronicsCategory.security,
      ElectronicsCategory.sensors,
      ElectronicsCategory.accessories,
    ].contains(category);
  }

  String _getElectronicsCategoryName(ElectronicsCategory category) {
    switch (category) {
      case ElectronicsCategory.audio:
        return 'أجهزة صوتية';
      case ElectronicsCategory.video:
        return 'أجهزة فيديو';
      case ElectronicsCategory.navigation:
        return 'أجهزة ملاحة';
      case ElectronicsCategory.security:
        return 'أجهزة أمنية';
      case ElectronicsCategory.lighting:
        return 'إضاءة';
      case ElectronicsCategory.sensors:
        return 'حساسات';
      case ElectronicsCategory.controlUnits:
        return 'وحدات تحكم';
      case ElectronicsCategory.accessories:
        return 'ملحقات';
      case ElectronicsCategory.tools:
        return 'أدوات';
      case ElectronicsCategory.other:
        return 'أخرى';
    }
  }
}
