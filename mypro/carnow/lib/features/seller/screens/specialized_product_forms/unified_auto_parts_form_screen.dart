import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../l10n/app_localizations.dart';
import '../../../../core/models/enums.dart';
import 'base_product_form.dart';
import '../../providers/vehicle_data_provider.dart';

/// نموذج موحد محسن لإضافة/تعديل قطع غيار السيارات
///
/// يجمع أفضل العناصر من جميع التصاميم الموجودة ويضيف ميزات محسنة:
/// - واجهة مستخدم موحدة ومتسقة
/// - دعم للتصنيف المتقدم لقطع الغيار
/// - نظام توافق السيارات المحسن
/// - حقول مرنة حسب نوع القطعة
class UnifiedAutoPartsFormScreen extends BaseProductFormScreen {
  const UnifiedAutoPartsFormScreen({super.key, super.productId});

  @override
  String getScreenTitle(AppLocalizations l10n) {
    return productId != null ? 'تعديل قطعة غيار' : 'إضافة قطعة غيار جديدة';
  }

  @override
  Widget buildSpecializedFields(
    BuildContext context,
    GlobalKey<FormState> formKey,
    Map<String, TextEditingController> controllers,
    Map<String, dynamic> formState,
    Function(String, dynamic) updateFormState,
  ) {
    return _UnifiedAutoPartsFormBody(
      controllers: controllers,
      formState: formState,
      updateFormState: updateFormState,
      formKey: formKey,
    );
  }

  @override
  String? validateSpecializedFields(Map<String, dynamic> formState) {
    if (formState['autoPartCategory'] == null) {
      return 'يرجى اختيار نوع قطعة الغيار';
    }

    if (formState['compatibleVehicles'] == null ||
        (formState['compatibleVehicles'] as List).isEmpty) {
      return 'يرجى إضافة سيارة متوافقة واحدة على الأقل';
    }

    return null;
  }

  @override
  Map<String, dynamic> prepareProductData(
    Map<String, TextEditingController> controllers,
    Map<String, dynamic> formState,
    String sellerId,
  ) {
    return {
      'brand': controllers['brand']?.text.trim(),
      'partNumber': controllers['partNumber']?.text.trim(),
      'oemPartNumber': controllers['oemPartNumber']?.text.trim(),
      'manufacturer': controllers['manufacturer']?.text.trim(),
      'warranty': controllers['warranty']?.text.trim(),
      'autoPartCategory': formState['autoPartCategory']?.name,
      'compatibleVehicles': formState['compatibleVehicles'] ?? [],
      'isOEM': formState['isOEM'] ?? false,
      'isUniversal': formState['isUniversal'] ?? false,
      'installationDifficulty': formState['installationDifficulty'] ?? 'medium',
    };
  }
}

/// جسم النموذج الموحد
class _UnifiedAutoPartsFormBody extends HookConsumerWidget {
  const _UnifiedAutoPartsFormBody({
    required this.controllers,
    required this.formState,
    required this.updateFormState,
    required this.formKey,
  });

  final Map<String, TextEditingController> controllers;
  final Map<String, dynamic> formState;
  final Function(String, dynamic) updateFormState;
  final GlobalKey<FormState> formKey;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // إضافة المتحكمات المطلوبة
    _initializeControllers();

    return SingleChildScrollView(
      child: Column(
        children: [
          // معلومات قطعة الغيار الأساسية
          _buildBasicInfoCard(context, theme),

          const SizedBox(height: 16),

          // معلومات الشركة المصنعة
          _buildManufacturerCard(context, theme),

          const SizedBox(height: 16),

          // توافق المركبات
          _buildVehicleCompatibilityCard(context, theme),

          const SizedBox(height: 16),

          // خصائص إضافية
          _buildAdditionalPropertiesCard(context, theme),
        ],
      ),
    );
  }

  void _initializeControllers() {
    final requiredControllers = [
      'brand',
      'partNumber',
      'oemPartNumber',
      'manufacturer',
      'warranty',
      'dimensions',
      'weight',
    ];

    for (final controller in requiredControllers) {
      controllers.putIfAbsent(controller, TextEditingController.new);
    }
  }

  /// بطاقة المعلومات الأساسية
  Widget _buildBasicInfoCard(BuildContext context, ThemeData theme) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات قطعة الغيار',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            // نوع قطعة الغيار
            DropdownButtonFormField<AutoPartsCategory>(
              value: formState['autoPartCategory'],
              decoration: const InputDecoration(
                labelText: 'نوع قطعة الغيار *',
                prefixIcon: Icon(Icons.category),
                border: OutlineInputBorder(),
              ),
              items: AutoPartsCategory.values.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(_getAutoPartCategoryName(category)),
                );
              }).toList(),
              onChanged: (value) => updateFormState('autoPartCategory', value),
              validator: (value) => value == null ? 'هذا الحقل مطلوب' : null,
            ),

            const SizedBox(height: 16),

            // الماركة
            TextFormField(
              controller: controllers['brand'],
              decoration: const InputDecoration(
                labelText: 'الماركة/العلامة التجارية *',
                prefixIcon: Icon(Icons.business),
                border: OutlineInputBorder(),
                hintText: 'مثال: Bosch, Denso, Valeo',
              ),
              textCapitalization: TextCapitalization.words,
              validator: (value) =>
                  value?.isEmpty ?? true ? 'الماركة مطلوبة' : null,
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                // رقم القطعة
                Expanded(
                  child: TextFormField(
                    controller: controllers['partNumber'],
                    decoration: const InputDecoration(
                      labelText: 'رقم القطعة',
                      prefixIcon: Icon(Icons.qr_code_scanner),
                      border: OutlineInputBorder(),
                      hintText: 'مثال: 12345-ABC-123',
                    ),
                    textCapitalization: TextCapitalization.characters,
                  ),
                ),
                const SizedBox(width: 16),

                // رقم القطعة الأصلي
                Expanded(
                  child: TextFormField(
                    controller: controllers['oemPartNumber'],
                    decoration: const InputDecoration(
                      labelText: 'رقم القطعة الأصلي',
                      prefixIcon: Icon(Icons.verified),
                      border: OutlineInputBorder(),
                      hintText: 'OEM Number',
                    ),
                    textCapitalization: TextCapitalization.characters,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقة معلومات الشركة المصنعة
  Widget _buildManufacturerCard(BuildContext context, ThemeData theme) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الشركة المصنعة',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            // الشركة المصنعة
            TextFormField(
              controller: controllers['manufacturer'],
              decoration: const InputDecoration(
                labelText: 'الشركة المصنعة',
                prefixIcon: Icon(Icons.factory),
                border: OutlineInputBorder(),
                hintText: 'مثال: شركة تويوتا، هيونداي',
              ),
              textCapitalization: TextCapitalization.words,
            ),

            const SizedBox(height: 16),

            // فترة الضمان
            TextFormField(
              controller: controllers['warranty'],
              decoration: const InputDecoration(
                labelText: 'فترة الضمان',
                prefixIcon: Icon(Icons.security),
                border: OutlineInputBorder(),
                hintText: 'مثال: سنة واحدة، 6 أشهر، 10000 كم',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقة توافق المركبات
  Widget _buildVehicleCompatibilityCard(BuildContext context, ThemeData theme) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.directions_car, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'توافق المركبات',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => _showAddVehicleDialog(context),
                  icon: const Icon(Icons.add_circle),
                  tooltip: 'إضافة سيارة متوافقة',
                ),
              ],
            ),
            const SizedBox(height: 16),

            // قائمة السيارات المتوافقة
            _buildCompatibleVehiclesList(context, theme),
          ],
        ),
      ),
    );
  }

  /// بطاقة الخصائص الإضافية
  Widget _buildAdditionalPropertiesCard(BuildContext context, ThemeData theme) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'خصائص إضافية',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            // خيارات القطعة
            CheckboxListTile(
              title: const Text('قطعة غيار أصلية (OEM)'),
              subtitle: const Text('قطعة من الشركة المصنعة الأصلية للسيارة'),
              value: formState['isOEM'] ?? false,
              onChanged: (value) => updateFormState('isOEM', value ?? false),
              controlAffinity: ListTileControlAffinity.leading,
            ),

            CheckboxListTile(
              title: const Text('قطعة غيار عامة (Universal)'),
              subtitle: const Text('تناسب أكثر من نوع وموديل سيارة'),
              value: formState['isUniversal'] ?? false,
              onChanged: (value) =>
                  updateFormState('isUniversal', value ?? false),
              controlAffinity: ListTileControlAffinity.leading,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة السيارات المتوافقة
  Widget _buildCompatibleVehiclesList(BuildContext context, ThemeData theme) {
    final vehicles = formState['compatibleVehicles'] as List? ?? [];

    if (vehicles.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          border: Border.all(color: theme.colorScheme.outline),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(
              Icons.directions_car_outlined,
              size: 48,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم إضافة سيارات متوافقة بعد',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            TextButton.icon(
              onPressed: () => _showAddVehicleDialog(context),
              icon: const Icon(Icons.add),
              label: const Text('إضافة سيارة متوافقة'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: vehicles.asMap().entries.map((entry) {
        final index = entry.key;
        final vehicle = entry.value;

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: const Icon(Icons.directions_car),
            title: Text(vehicle['displayName'] ?? 'غير محدد'),
            subtitle: Text(
              '${vehicle['yearFrom'] ?? ''} - ${vehicle['yearTo'] ?? ''}',
            ),
            trailing: IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () {
                final updatedVehicles = List.from(vehicles);
                updatedVehicles.removeAt(index);
                updateFormState('compatibleVehicles', updatedVehicles);
              },
            ),
          ),
        );
      }).toList(),
    );
  }

  /// إظهار نافذة إضافة سيارة متوافقة
  void _showAddVehicleDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => _AddVehicleDialog(
        onVehicleAdded: (vehicle) {
          final vehicles = formState['compatibleVehicles'] as List? ?? [];
          final updatedVehicles = List.from(vehicles)..add(vehicle);
          updateFormState('compatibleVehicles', updatedVehicles);
        },
      ),
    );
  }

  /// الحصول على اسم فئة قطعة الغيار
  String _getAutoPartCategoryName(AutoPartsCategory category) {
    switch (category) {
      case AutoPartsCategory.engineParts:
        return 'قطع المحرك';
      case AutoPartsCategory.transmissionParts:
        return 'قطع ناقل الحركة';
      case AutoPartsCategory.brakeSystem:
        return 'نظام الفرامل';
      case AutoPartsCategory.suspensionSteering:
        return 'التعليق والتوجيه';
      case AutoPartsCategory.electricalElectronic:
        return 'النظام الكهربائي';
      case AutoPartsCategory.coolingAC:
        return 'نظام التبريد والتكييف';
      case AutoPartsCategory.exhaustSystem:
        return 'نظام العادم';
      case AutoPartsCategory.fuelSystem:
        return 'نظام الوقود';
      case AutoPartsCategory.bodyExterior:
        return 'هيكل السيارة';
      case AutoPartsCategory.interiorParts:
        return 'الأجزاء الداخلية';
      case AutoPartsCategory.tiresWheels:
        return 'العجلات والإطارات';
      case AutoPartsCategory.filters:
        return 'الفلاتر';
      case AutoPartsCategory.beltsChains:
        return 'الأحزمة والسلاسل';
      case AutoPartsCategory.lightingSystem:
        return 'نظام الإضاءة';
      default:
        return 'أخرى';
    }
  }
}

/// نافذة إضافة سيارة متوافقة
class _AddVehicleDialog extends HookConsumerWidget {
  const _AddVehicleDialog({required this.onVehicleAdded});

  final Function(Map<String, dynamic>) onVehicleAdded;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedMakeId = useState<int?>(null);
    final selectedMakeName = useState<String>('');
    final modelController = useTextEditingController();
    final yearFromController = useTextEditingController();
    final yearToController = useTextEditingController();

    // Get dynamic vehicle data
    final makesAsyncValue = ref.watch(sellerVehicleMakesProvider);
    final modelsAsyncValue = selectedMakeId.value != null
        ? ref.watch(sellerVehicleModelsProvider(makeId: selectedMakeId.value!))
        : const AsyncValue.data(<dynamic>[]);

    return AlertDialog(
      title: const Text('إضافة سيارة متوافقة'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Make Selection - Dynamic
            makesAsyncValue.when(
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, _) => Text('خطأ: $error'),
              data: (makes) => DropdownButtonFormField<int>(
                value: selectedMakeId.value,
                decoration: const InputDecoration(
                  labelText: 'الماركة *',
                  hintText: 'اختر الماركة',
                  border: OutlineInputBorder(),
                ),
                items: makes.map((make) {
                  return DropdownMenuItem(
                    value: make.id,
                    child: Text(make.name),
                  );
                }).toList(),
                onChanged: (value) {
                  selectedMakeId.value = value;
                  if (value != null) {
                    final make = makes.firstWhere((m) => m.id == value);
                    selectedMakeName.value = make.name;
                  }
                  modelController.clear(); // Reset model when make changes
                },
              ),
            ),
            const SizedBox(height: 16),

            // Model Selection - Dynamic
            modelsAsyncValue.when(
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, _) => TextField(
                controller: modelController,
                decoration: const InputDecoration(
                  labelText: 'الموديل *',
                  hintText: 'أدخل اسم الموديل',
                  border: OutlineInputBorder(),
                ),
              ),
              data: (models) {
                if (models.isEmpty) {
                  return TextField(
                    controller: modelController,
                    decoration: const InputDecoration(
                      labelText: 'الموديل *',
                      hintText: 'أدخل اسم الموديل',
                      border: OutlineInputBorder(),
                    ),
                  );
                }

                final modelItems = models.map((model) {
                  return DropdownMenuItem<String>(
                    value: model.name,
                    child: Text(
                      '${model.name} (${model.yearStart}-${model.yearEnd ?? "حالياً"})',
                    ),
                  );
                }).toList();

                return DropdownButtonFormField<String>(
                  value: modelController.text.isNotEmpty
                      ? modelController.text
                      : null,
                  decoration: const InputDecoration(
                    labelText: 'الموديل *',
                    hintText: 'اختر الموديل',
                    border: OutlineInputBorder(),
                  ),
                  items: modelItems,
                  onChanged: (value) {
                    modelController.text = value ?? '';
                  },
                );
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: yearFromController,
                    decoration: const InputDecoration(
                      labelText: 'من سنة',
                      hintText: 'مثال: 2015',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(4),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: yearToController,
                    decoration: const InputDecoration(
                      labelText: 'إلى سنة',
                      hintText: 'مثال: 2020',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(4),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        FilledButton(
          onPressed: () {
            if (selectedMakeName.value.isNotEmpty &&
                modelController.text.isNotEmpty) {
              final vehicle = {
                'make': selectedMakeName.value,
                'model': modelController.text.trim(),
                'yearFrom': yearFromController.text.trim(),
                'yearTo': yearToController.text.trim(),
                'displayName':
                    '${selectedMakeName.value} ${modelController.text.trim()}',
              };

              onVehicleAdded(vehicle);
              Navigator.of(context).pop();
            }
          },
          child: const Text('إضافة'),
        ),
      ],
    );
  }
}
