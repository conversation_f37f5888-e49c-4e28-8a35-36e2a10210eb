import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../l10n/app_localizations.dart';
import '../../../../core/models/enums.dart';
import 'base_product_form.dart';

/// شاشة نموذج إضافة/تعديل قطع غيار السيارات
///
/// توفر نموذجًا متخصصًا لإدخال تفاصيل المنتجات التي تندرج تحت فئة قطع الغيار.
/// تتضمن حقولاً خاصة مثل نوع القطعة، رقم القطعة (Part Number)، الشركة المصنعة،
/// والمركبات المتوافقة معها.
/// ترث من `BaseProductFormScreen` وتضيف الحقول المخصصة لهذه الفئة.
class AutoPartsFormScreen extends BaseProductFormScreen {
  const AutoPartsFormScreen({super.key, super.productId});

  @override
  String getScreenTitle(AppLocalizations l10n) {
    return productId != null ? 'تعديل قطعة غيار' : 'إضافة قطعة غيار جديدة';
  }

  @override
  Widget buildSpecializedFields(
    BuildContext context,
    GlobalKey<FormState> formKey,
    Map<String, TextEditingController> controllers,
    Map<String, dynamic> formState,
    Function(String, dynamic) updateFormState,
  ) {
    final theme = Theme.of(context);

    // Add specialized controllers
    controllers.putIfAbsent('brand', TextEditingController.new);
    controllers.putIfAbsent('partNumber', TextEditingController.new);
    controllers.putIfAbsent('manufacturer', TextEditingController.new);
    controllers.putIfAbsent('warranty', TextEditingController.new);
    controllers.putIfAbsent('compatibleVehicles', TextEditingController.new);
    controllers.putIfAbsent('yearFrom', TextEditingController.new);
    controllers.putIfAbsent('yearTo', TextEditingController.new);

    return Column(
      children: [
        // Part Information Card
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'معلومات قطعة الغيار',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 16),

                // Auto Part Category
                DropdownButtonFormField<AutoPartsCategory>(
                  value: formState['autoPartCategory'],
                  decoration: const InputDecoration(
                    labelText: 'نوع قطعة الغيار *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.category),
                  ),
                  items: AutoPartsCategory.values
                      .map(
                        (category) => DropdownMenuItem(
                          value: category,
                          child: Text(_getAutoPartCategoryName(category)),
                        ),
                      )
                      .toList(),
                  onChanged: (value) =>
                      updateFormState('autoPartCategory', value),
                  validator: (value) =>
                      value == null ? 'هذا الحقل مطلوب' : null,
                ),
                const SizedBox(height: 16),

                // Brand and Part Number
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: controllers['brand'],
                        decoration: const InputDecoration(
                          labelText: 'الماركة/العلامة التجارية *',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.business),
                        ),
                        textCapitalization: TextCapitalization.words,
                        validator: (value) =>
                            value?.isEmpty ?? true ? 'هذا الحقل مطلوب' : null,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: controllers['partNumber'],
                        decoration: const InputDecoration(
                          labelText: 'رقم القطعة',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.confirmation_number),
                        ),
                        textCapitalization: TextCapitalization.characters,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Manufacturer
                TextFormField(
                  controller: controllers['manufacturer'],
                  decoration: const InputDecoration(
                    labelText: 'الشركة المصنعة',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.factory),
                  ),
                  textCapitalization: TextCapitalization.words,
                ),
                const SizedBox(height: 16),

                // Warranty
                TextFormField(
                  controller: controllers['warranty'],
                  decoration: const InputDecoration(
                    labelText: 'فترة الضمان',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.verified_user),
                    hintText: 'مثال: سنة واحدة، 6 أشهر',
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Vehicle Compatibility Card
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'توافق المركبات',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 16),

                // Compatible Vehicles
                TextFormField(
                  controller: controllers['compatibleVehicles'],
                  decoration: const InputDecoration(
                    labelText: 'السيارات المتوافقة *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.directions_car),
                    hintText: 'مثال: تويوتا كامري، نيسان ألتيما',
                  ),
                  maxLines: 2,
                  textCapitalization: TextCapitalization.words,
                  validator: (value) =>
                      value?.isEmpty ?? true ? 'هذا الحقل مطلوب' : null,
                ),
                const SizedBox(height: 16),

                // Year Range
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: controllers['yearFrom'],
                        decoration: const InputDecoration(
                          labelText: 'من سنة',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(4),
                        ],
                        validator: (value) {
                          if (value?.isNotEmpty == true) {
                            final year = int.tryParse(value!);
                            if (year == null ||
                                year < 1900 ||
                                year > DateTime.now().year + 1) {
                              return 'أدخل سنة صحيحة';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: controllers['yearTo'],
                        decoration: const InputDecoration(
                          labelText: 'إلى سنة',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(4),
                        ],
                        validator: (value) {
                          if (value?.isNotEmpty == true) {
                            final year = int.tryParse(value!);
                            if (year == null ||
                                year < 1900 ||
                                year > DateTime.now().year + 1) {
                              return 'أدخل سنة صحيحة';
                            }

                            // Check if yearTo is not less than yearFrom
                            final yearFromText = controllers['yearFrom']?.text;
                            if (yearFromText?.isNotEmpty == true) {
                              final yearFrom = int.tryParse(yearFromText!);
                              if (yearFrom != null && year < yearFrom) {
                                return 'يجب أن تكون أكبر من سنة البداية';
                              }
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Original Equipment Manufacturer (OEM) checkbox
                CheckboxListTile(
                  title: const Text('قطعة غيار أصلية (OEM)'),
                  subtitle: const Text('قطعة غيار من الشركة المصنعة الأصلية'),
                  value: formState['isOEM'] ?? false,
                  onChanged: (value) =>
                      updateFormState('isOEM', value ?? false),
                  controlAffinity: ListTileControlAffinity.leading,
                ),

                // Universal Part checkbox
                CheckboxListTile(
                  title: const Text('قطعة غيار عامة'),
                  subtitle: const Text('تناسب أكثر من نوع سيارة'),
                  value: formState['isUniversal'] ?? false,
                  onChanged: (value) =>
                      updateFormState('isUniversal', value ?? false),
                  controlAffinity: ListTileControlAffinity.leading,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  String? validateSpecializedFields(Map<String, dynamic> formState) {
    // Check if category is selected
    if (formState['autoPartCategory'] == null) {
      return 'يرجى اختيار نوع قطعة الغيار';
    }

    return null;
  }

  @override
  Map<String, dynamic> prepareProductData(
    Map<String, TextEditingController> controllers,
    Map<String, dynamic> formState,
    String sellerId,
  ) {
    return {
      // ===== Base fields (camelCase) =====
      'name': controllers['name']!.text.trim(),
      'description': controllers['description']!.text.trim(),
      'price': double.parse(controllers['price']!.text),
      'originalPrice': controllers['originalPrice']!.text.isNotEmpty
          ? double.parse(controllers['originalPrice']!.text)
          : null,
      'condition': (formState['condition'] as ProductCondition).name,
      'stockQuantity': int.parse(controllers['quantity']!.text),
      'location': controllers['location']!.text.trim(),
      'sellerId': sellerId,

      // ===== Auto-parts specific =====
      'productType': ProductType.autoParts.name,
      'automotiveType': AutomotiveType.autoParts.name,
      'brand': controllers['brand']!.text.trim(),
      'partNumber': controllers['partNumber']!.text.trim(),
      'manufacturer': controllers['manufacturer']!.text.trim(),
      'categoryId': (formState['autoPartCategory'] as AutoPartsCategory?)?.name,

      // Specifications stored as nested map
      'specifications': {
        'warranty': controllers['warranty']!.text.trim(),
        'compatibleVehicles': controllers['compatibleVehicles']!.text
            .split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList(),
        'yearFrom': int.tryParse(controllers['yearFrom']!.text),
        'yearTo': int.tryParse(controllers['yearTo']!.text),
        'isOem': formState['isOEM'] ?? false,
        'isUniversal': formState['isUniversal'] ?? false,
      },
      // images handled by base class
    };
  }

  String _getAutoPartCategoryName(AutoPartsCategory category) {
    switch (category) {
      case AutoPartsCategory.engineParts:
        return 'قطع المحرك';
      case AutoPartsCategory.transmissionParts:
        return 'قطع ناقل الحركة';
      case AutoPartsCategory.suspensionSteering:
        return 'نظام التعليق والتوجيه';
      case AutoPartsCategory.brakeSystem:
        return 'نظام الفرامل';
      case AutoPartsCategory.electricalElectronic:
        return 'النظام الكهربائي والإلكتروني';
      case AutoPartsCategory.bodyExterior:
        return 'هيكل السيارة والخارجي';
      case AutoPartsCategory.interiorParts:
        return 'الأجزاء الداخلية';
      case AutoPartsCategory.tiresWheels:
        return 'الإطارات والعجلات';
      case AutoPartsCategory.exhaustSystem:
        return 'نظام العادم';
      case AutoPartsCategory.coolingAC:
        return 'نظام التبريد والتكييف';
      case AutoPartsCategory.fuelSystem:
        return 'نظام الوقود';
      case AutoPartsCategory.filters:
        return 'الفلاتر والمرشحات';
      case AutoPartsCategory.beltsChains:
        return 'الأحزمة والسلاسل';
      case AutoPartsCategory.hosestubes:
        return 'الخراطيم والأنابيب';
      case AutoPartsCategory.sensorsGauges:
        return 'الحساسات وأجهزة القياس';
      case AutoPartsCategory.lightingSystem:
        return 'نظام الإضاءة';
      case AutoPartsCategory.other:
        return 'أخرى';
    }
  }
}
