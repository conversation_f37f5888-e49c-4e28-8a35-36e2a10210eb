import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/subscription_model.dart';
import '../models/seller_enums.dart';
import '../providers/subscription_request_provider.dart';
import '../../../core/models/city_model.dart';
import '../../../core/providers/location_provider.dart';
import '../../../core/theme/app_colors.dart';
// Removed unused import

class SellerRegistrationDetailsScreen extends ConsumerStatefulWidget {
  const SellerRegistrationDetailsScreen({
    super.key,
    required this.selectedPlan,
    required this.selectedCycle,
  });
  final SubscriptionPlan selectedPlan;
  final BillingCycle selectedCycle;

  @override
  ConsumerState<SellerRegistrationDetailsScreen> createState() =>
      _SellerRegistrationDetailsScreenState();
}

class _SellerRegistrationDetailsScreenState
    extends ConsumerState<SellerRegistrationDetailsScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers for form fields
  late final TextEditingController storeNameController;
  late final TextEditingController phoneController;
  late final TextEditingController addressController;
  late final TextEditingController descriptionController;

  // Selected values
  late ValueNotifier<City?> selectedCity;

  @override
  void initState() {
    super.initState();
    storeNameController = TextEditingController();
    phoneController = TextEditingController();
    addressController = TextEditingController();
    descriptionController = TextEditingController();
    selectedCity = ValueNotifier<City?>(null);
  }

  @override
  void dispose() {
    storeNameController.dispose();
    phoneController.dispose();
    addressController.dispose();
    descriptionController.dispose();
    selectedCity.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'بيانات المتجر',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // ملخص الخطة المختارة
              _buildPlanSummary(),
              const SizedBox(height: 32),

              // عنوان البيانات
              const Text(
                'أدخل بيانات متجرك',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'تأكد من صحة البيانات لسرعة المراجعة والموافقة',
                style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
              ),
              const SizedBox(height: 24),

              // حقول البيانات
              _buildStoreNameField(),
              const SizedBox(height: 20),

              _buildPhoneField(),
              const SizedBox(height: 20),

              _buildCityField(),
              const SizedBox(height: 20),

              _buildAddressField(),
              const SizedBox(height: 20),

              _buildDescriptionField(),
              const SizedBox(height: 32),

              // معلومة المراجعة
              _buildReviewInfo(),
              const SizedBox(height: 32),

              // زر الإرسال
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlanSummary() {
    final price = widget.selectedCycle == BillingCycle.yearly
        ? widget.selectedPlan.yearlyPriceLD
        : widget.selectedPlan.monthlyPriceLD;
    final monthlyEquivalent = widget.selectedCycle == BillingCycle.yearly
        ? widget.selectedPlan.yearlyPriceLD / 12
        : widget.selectedPlan.monthlyPriceLD;
    final savings = widget.selectedCycle == BillingCycle.yearly
        ? (widget.selectedPlan.monthlyPriceLD * 12) -
              widget.selectedPlan.yearlyPriceLD
        : 0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'الخطة المختارة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.selectedPlan.nameAr,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${widget.selectedPlan.monthlyListingQuota} إعلان شهرياً',
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${price.toInt()} LD',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    widget.selectedCycle == BillingCycle.yearly
                        ? '${monthlyEquivalent.toStringAsFixed(0)} LD/شهر'
                        : '/شهر',
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ],
          ),

          if (widget.selectedCycle == BillingCycle.yearly && savings > 0) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.savings, size: 16, color: Colors.green),
                  const SizedBox(width: 6),
                  Text(
                    'توفر ${savings.toInt()} LD سنوياً',
                    style: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStoreNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'اسم المتجر أو المحل *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: storeNameController,
          decoration: InputDecoration(
            hintText: 'أدخل اسم متجرك',
            prefixIcon: const Icon(Icons.store),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.white,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'اسم المتجر مطلوب';
            }
            if (value.trim().length < 2) {
              return 'اسم المتجر يجب أن يكون على الأقل حرفين';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPhoneField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'رقم الهاتف *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: phoneController,
          keyboardType: TextInputType.phone,
          decoration: InputDecoration(
            hintText: 'أدخل رقم هاتفك',
            prefixIcon: const Icon(Icons.phone),
            helperText: '💡 يُنصح باستخدام رقم واتساب للدعم السريع',
            helperStyle: TextStyle(color: Colors.green.shade600, fontSize: 13),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.white,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'رقم الهاتف مطلوب';
            }
            if (value.trim().length < 8) {
              return 'رقم الهاتف غير صحيح';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildCityField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المدينة *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Consumer(
          builder: (context, ref, child) {
            final citiesAsync = ref.watch(citiesProvider);
            return citiesAsync.when(
              loading: () => Container(
                height: 60,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                child: const Center(child: CircularProgressIndicator()),
              ),
              error: (err, stack) => Container(
                height: 60,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.red.shade300),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                child: Center(
                  child: Text(
                    'خطأ في تحميل المدن',
                    style: TextStyle(color: Colors.red.shade600),
                  ),
                ),
              ),
              data: (cities) {
                return ValueListenableBuilder<City?>(
                  valueListenable: selectedCity,
                  builder: (context, city, _) {
                    return DropdownButtonFormField<City>(
                      value: city,
                      items: cities.map((City city) {
                        return DropdownMenuItem<City>(
                          value: city,
                          child: Text(city.nameArabic),
                        );
                      }).toList(),
                      onChanged: (City? newValue) {
                        selectedCity.value = newValue;
                      },
                      decoration: InputDecoration(
                        hintText: 'اختر مدينتك',
                        prefixIcon: const Icon(Icons.location_city),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      validator: (value) {
                        if (value == null) {
                          return 'المدينة مطلوبة';
                        }
                        return null;
                      },
                    );
                  },
                );
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildAddressField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'العنوان التفصيلي *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: addressController,
          maxLines: 2,
          decoration: InputDecoration(
            hintText: 'أدخل عنوانك التفصيلي (الشارع، الحي، إلخ)',
            prefixIcon: const Icon(Icons.location_on),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.white,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'العنوان التفصيلي مطلوب';
            }
            if (value.trim().length < 5) {
              return 'العنوان يجب أن يكون أكثر تفصيلاً';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'وصف النشاط التجاري (اختياري)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: descriptionController,
          maxLines: 4,
          decoration: InputDecoration(
            hintText: 'وصف موجز عن نشاطك التجاري وما تبيعه...',
            prefixIcon: const Icon(Icons.description),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildReviewInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.schedule, color: Colors.blue.shade700),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مراجعة الطلب',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
                const SizedBox(height: 4),
                const Text(
                  'سيتم مراجعة طلبك خلال 48 ساعة كحد أقصى وسنعلمك بالنتيجة',
                  style: TextStyle(color: Colors.blue, height: 1.4),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Consumer(
      builder: (context, ref, child) {
        final isLoading = ref
            .watch(sellerSubscriptionRequestProviderProvider)
            .isLoading;

        return SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: isLoading ? null : _submitRequest,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              disabledBackgroundColor: Colors.grey.shade300,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: isLoading ? 0 : 2,
            ),
            child: isLoading
                ? const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      ),
                      SizedBox(width: 12),
                      Text(
                        'جاري الإرسال...',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  )
                : const Text(
                    'إرسال طلب التسجيل',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
          ),
        );
      },
    );
  }

  Future<void> _submitRequest() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (selectedCity.value == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار المدينة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      // إنشاء بيانات إضافية للبائع
      final sellerInfo = {
        'store_name': storeNameController.text.trim(),
        'phone': phoneController.text.trim(),
        'city_id': selectedCity.value!.id,
        'address': addressController.text.trim(),
        'description': descriptionController.text.trim().isNotEmpty
            ? descriptionController.text.trim()
            : null,
      };

      // إرسال الطلب عبر الـ provider
      await ref
          .read(sellerSubscriptionRequestProviderProvider.notifier)
          .submitRequest(
            plan: widget.selectedPlan,
            billingCycle: widget.selectedCycle,
            sellerInfo: sellerInfo,
          );

      if (mounted) {
        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال طلبك بنجاح! سنراجعه خلال 48 ساعة'),
            backgroundColor: Colors.green,
          ),
        );

        // العودة للصفحة الرئيسية
        context.go('/');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
