import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:logging/logging.dart';

import '../../../core/error/app_error.dart';
import '../../../core/models/subscription_response.dart';

/// Subscription Navigation Manager - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// ✅ Uses GoRouter for navigation
/// ✅ Provides safe navigation with fallback strategies
/// ✅ Handles navigation errors gracefully
/// ✅ Compatible with page-based routing

class SubscriptionNavigationError {
  final String message;
  final String attemptedRoute;
  final Object? originalError;

  SubscriptionNavigationError({
    required this.message,
    required this.attemptedRoute,
    this.originalError,
  });

  @override
  String toString() {
    return 'SubscriptionNavigationError: $message (Route: $attemptedRoute)';
  }

  /// Convert to AppError for error handling
  AppError toAppError() {
    return AppError(
      type: AppErrorType.unknown,
      code: 'NAVIGATION_ERROR',
      message: message,
      messageAr: message,
      originalError: originalError,
    );
  }
}

class SubscriptionNavigationManager {
  static final Logger _logger = Logger('SubscriptionNavigationManager');

  // Route constants - must match routes defined in app_router.dart
  static const String subscriptionFormRoute = '/seller/subscription/form';
  static const String subscriptionStatusRoute = '/seller/subscription/status';
  static const String subscriptionSuccessRoute = '/seller/subscription/success';
  static const String subscriptionPlansRoute = '/seller/subscription/plans';
  static const String subscriptionUpgradeRoute = '/seller/subscription/upgrade';
  static const String subscriptionBillingRoute = '/seller/subscription/billing';
  static const String subscriptionHistoryRoute = '/seller/subscription/history';

  /// Navigate to subscription status screen with safe fallback
  static Future<void> navigateToSubscriptionStatus({
    required BuildContext context,
    required String subscriptionId,
  }) async {
    try {
      _logger.info('Navigating to subscription status for ID: $subscriptionId');
      
      await context.pushNamed(
        subscriptionStatusRoute,
        pathParameters: {'id': subscriptionId},
      );
    } catch (e, st) {
      _logger.severe(
        'Navigation to subscription status failed for ID: $subscriptionId',
        e,
        st,
      );
      
      // Fallback navigation
      try {
        await context.pushNamed(subscriptionStatusRoute);
      } catch (fallbackError, fallbackStack) {
        _logger.severe(
          'Fallback navigation to subscription status failed',
          fallbackError,
          fallbackStack,
        );
        
        throw SubscriptionNavigationError(
          message: 'فشل في التنقل إلى صفحة حالة الاشتراك',
          attemptedRoute: subscriptionStatusRoute,
          originalError: e,
        );
      }
    }
  }

  /// Navigate to subscription success screen with safe fallback
  static Future<void> navigateToSubscriptionSuccess({
    required BuildContext context,
    required SubscriptionResponse response,
  }) async {
    try {
      _logger.info('Navigating to subscription success for response: ${response.id}');
      
      await context.pushNamed(
        subscriptionSuccessRoute,
        pathParameters: {'id': response.id},
        extra: response,
      );
    } catch (e, st) {
      _logger.severe(
        'Navigation to subscription success failed for response: ${response.id}',
        e,
        st,
      );
      
      // Fallback navigation
      try {
        await context.pushNamed(subscriptionSuccessRoute);
      } catch (fallbackError, fallbackStack) {
        _logger.severe(
          'Fallback navigation to subscription success failed',
          fallbackError,
          fallbackStack,
        );
        
        throw SubscriptionNavigationError(
          message: 'فشل في التنقل إلى صفحة نجاح الاشتراك',
          attemptedRoute: subscriptionSuccessRoute,
          originalError: e,
        );
      }
    }
  }

  /// Navigate to subscription form with safe fallback
  static Future<void> navigateToSubscriptionForm({
    required BuildContext context,
  }) async {
    try {
      _logger.info('Navigating to subscription form');
      
      await context.pushNamed(subscriptionFormRoute);
    } catch (e, st) {
      _logger.severe(
        'Navigation to subscription form failed',
        e,
        st,
      );
      
      // Show error dialog as fallback
      if (!context.mounted) return;
      
      await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('خطأ في التنقل'),
          content: const Text('تعذر الانتقال إلى نموذج الاشتراك. يرجى المحاولة مرة أخرى.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
      
      throw SubscriptionNavigationError(
        message: 'فشل في التنقل إلى نموذج الاشتراك',
        attemptedRoute: subscriptionFormRoute,
        originalError: e,
      );
    }
  }

  /// Navigate to subscription plans selection
  static Future<void> navigateToSubscriptionPlans({
    required BuildContext context,
  }) async {
    try {
      _logger.info('Navigating to subscription plans');
      
      await context.pushNamed(subscriptionPlansRoute);
    } catch (e, st) {
      _logger.severe(
        'Navigation to subscription plans failed',
        e,
        st,
      );
      
      // Show error dialog as fallback
      if (!context.mounted) return;
      
      await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('خطأ في التنقل'),
          content: const Text('تعذر الانتقال إلى صفحة خطط الاشتراك. يرجى المحاولة مرة أخرى.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
      
      throw SubscriptionNavigationError(
        message: 'فشل في التنقل إلى صفحة خطط الاشتراك',
        attemptedRoute: subscriptionPlansRoute,
        originalError: e,
      );
    }
  }

  /// Navigate to subscription upgrade screen
  static Future<void> navigateToSubscriptionUpgrade({
    required BuildContext context,
    String? currentPlanId,
  }) async {
    try {
      _logger.info('Navigating to subscription upgrade');
      
      await context.pushNamed(
        subscriptionUpgradeRoute,
        queryParameters: {
          if (currentPlanId != null) 'plan_id': currentPlanId,
        },
      );
    } catch (e, st) {
      _logger.severe(
        'Navigation to subscription upgrade failed',
        e,
        st,
      );
      
      // Show error dialog as fallback
      if (!context.mounted) return;
      
      await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('خطأ في التنقل'),
          content: const Text('تعذر الانتقال إلى صفحة ترقية الاشتراك. يرجى المحاولة مرة أخرى.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
      
      throw SubscriptionNavigationError(
        message: 'فشل في التنقل إلى صفحة ترقية الاشتراك',
        attemptedRoute: subscriptionUpgradeRoute,
        originalError: e,
      );
    }
  }

  /// Handle navigation errors with user-friendly feedback
  static Future<void> handleNavigationError({
    required BuildContext context,
    required SubscriptionNavigationError error,
  }) async {
    _logger.severe(
      'Handling navigation error: ${error.message}',
      error.originalError,
    );
    
    // Log navigation error with context
    _logNavigationError(error);
    
    // Show user-friendly error dialog
    if (!context.mounted) return;
    
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ في التنقل'),
        content: Text(error.message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// Log navigation errors with detailed context
  static void _logNavigationError(SubscriptionNavigationError error) {
    _logger.severe(
      'Navigation Error Details:',
      {
        'message': error.message,
        'attemptedRoute': error.attemptedRoute,
        'timestamp': DateTime.now().toIso8601String(),
        'originalError': error.originalError?.toString(),
      },
    );
  }

  /// Go back with safe fallback
  static void goBack(BuildContext context) {
    try {
      if (context.canPop()) {
        context.pop();
      } else {
        // Fallback to root if can't pop
        context.go('/');
      }
    } catch (e, st) {
      _logger.severe('Error going back', e, st);
      // Last resort fallback
      try {
        context.go('/');
      } catch (fallbackError) {
        _logger.severe('Fallback navigation to root failed', fallbackError);
      }
    }
  }

  /// Navigate to root with safe fallback
  static void navigateToRoot(BuildContext context) {
    try {
      context.go('/');
    } catch (e, st) {
      _logger.severe('Error navigating to root', e, st);
      // Show error as last resort
      if (!context.mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('حدث خطأ في التنقل'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
