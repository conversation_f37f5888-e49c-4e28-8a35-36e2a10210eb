// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_stats_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sellerProductStatsHash() =>
    r'b58d25411cbe116451f4160577a1e71d9cfbc258';

/// Seller Statistics Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// ✅ Uses Go Backend API ONLY for statistics
/// ✅ NO direct Supabase calls
/// ✅ Clean async/await patterns
/// Provider for seller product statistics
///
/// Copied from [sellerProductStats].
@ProviderFor(sellerProductStats)
final sellerProductStatsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      sellerProductStats,
      name: r'sellerProductStatsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerProductStatsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SellerProductStatsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$sellerAnalyticsStatsHash() =>
    r'2c2cd2cf1afd911671401963eaf4698e35e9f802';

/// Provider for seller analytics/views statistics
///
/// Copied from [sellerAnalyticsStats].
@ProviderFor(sellerAnalyticsStats)
final sellerAnalyticsStatsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      sellerAnalyticsStats,
      name: r'sellerAnalyticsStatsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerAnalyticsStatsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SellerAnalyticsStatsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$sellerMessagesStatsHash() =>
    r'c01ac9f51ee183fc8b39fece988c43e5acabd990';

/// Provider for seller messages/communication statistics
///
/// Copied from [sellerMessagesStats].
@ProviderFor(sellerMessagesStats)
final sellerMessagesStatsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      sellerMessagesStats,
      name: r'sellerMessagesStatsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerMessagesStatsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SellerMessagesStatsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$sellerOrdersStatsHash() => r'21b72c1da5037b4f2fabf167da7b9414f96ee334';

/// Provider for seller orders statistics
///
/// Copied from [sellerOrdersStats].
@ProviderFor(sellerOrdersStats)
final sellerOrdersStatsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      sellerOrdersStats,
      name: r'sellerOrdersStatsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerOrdersStatsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SellerOrdersStatsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$sellerDashboardStatsHash() =>
    r'7dee3e4200a30bfccf924b395580f5db8d32cc1a';

/// Combined seller dashboard statistics
///
/// Copied from [sellerDashboardStats].
@ProviderFor(sellerDashboardStats)
final sellerDashboardStatsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      sellerDashboardStats,
      name: r'sellerDashboardStatsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerDashboardStatsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SellerDashboardStatsRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
