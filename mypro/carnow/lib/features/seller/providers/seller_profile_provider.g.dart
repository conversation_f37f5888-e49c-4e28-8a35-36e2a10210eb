// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_profile_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sellerProfileHash() => r'2dade9b4a96c90a339b65135e015ea63147f618f';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// مزود لملف البائع الشخصي
/// يتبع Forever Plan: Flutter → Go API → Supabase
///
/// Copied from [sellerProfile].
@ProviderFor(sellerProfile)
const sellerProfileProvider = SellerProfileFamily();

/// مزود لملف البائع الشخصي
/// يتبع Forever Plan: Flutter → Go API → Supabase
///
/// Copied from [sellerProfile].
class SellerProfileFamily extends Family<AsyncValue<SellerProfile?>> {
  /// مزود لملف البائع الشخصي
  /// يتبع Forever Plan: Flutter → Go API → Supabase
  ///
  /// Copied from [sellerProfile].
  const SellerProfileFamily();

  /// مزود لملف البائع الشخصي
  /// يتبع Forever Plan: Flutter → Go API → Supabase
  ///
  /// Copied from [sellerProfile].
  SellerProfileProvider call(String sellerId) {
    return SellerProfileProvider(sellerId);
  }

  @override
  SellerProfileProvider getProviderOverride(
    covariant SellerProfileProvider provider,
  ) {
    return call(provider.sellerId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sellerProfileProvider';
}

/// مزود لملف البائع الشخصي
/// يتبع Forever Plan: Flutter → Go API → Supabase
///
/// Copied from [sellerProfile].
class SellerProfileProvider extends AutoDisposeFutureProvider<SellerProfile?> {
  /// مزود لملف البائع الشخصي
  /// يتبع Forever Plan: Flutter → Go API → Supabase
  ///
  /// Copied from [sellerProfile].
  SellerProfileProvider(String sellerId)
    : this._internal(
        (ref) => sellerProfile(ref as SellerProfileRef, sellerId),
        from: sellerProfileProvider,
        name: r'sellerProfileProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$sellerProfileHash,
        dependencies: SellerProfileFamily._dependencies,
        allTransitiveDependencies:
            SellerProfileFamily._allTransitiveDependencies,
        sellerId: sellerId,
      );

  SellerProfileProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sellerId,
  }) : super.internal();

  final String sellerId;

  @override
  Override overrideWith(
    FutureOr<SellerProfile?> Function(SellerProfileRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SellerProfileProvider._internal(
        (ref) => create(ref as SellerProfileRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sellerId: sellerId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<SellerProfile?> createElement() {
    return _SellerProfileProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SellerProfileProvider && other.sellerId == sellerId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sellerId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SellerProfileRef on AutoDisposeFutureProviderRef<SellerProfile?> {
  /// The parameter `sellerId` of this provider.
  String get sellerId;
}

class _SellerProfileProviderElement
    extends AutoDisposeFutureProviderElement<SellerProfile?>
    with SellerProfileRef {
  _SellerProfileProviderElement(super.provider);

  @override
  String get sellerId => (origin as SellerProfileProvider).sellerId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
