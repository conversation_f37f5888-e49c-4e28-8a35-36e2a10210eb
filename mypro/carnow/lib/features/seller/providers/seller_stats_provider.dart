import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/unified_auth_provider.dart';

part 'seller_stats_provider.g.dart';

final _logger = Logger('SellerStatsProvider');

/// Seller Statistics Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// ✅ Uses Go Backend API ONLY for statistics
/// ✅ NO direct Supabase calls
/// ✅ Clean async/await patterns

/// Provider for seller product statistics
@riverpod
Future<Map<String, dynamic>> sellerProductStats(Ref ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.warning('User not authenticated, cannot fetch product stats');
      return {};
    }

    _logger.info('Fetching seller product stats from Go backend');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/seller/stats/products',
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch product stats: ${response.message}');
      throw Exception('فشل في تحميل إحصائيات المنتجات');
    }

    _logger.info('Product stats fetched successfully');
    return response.data!;
  } catch (e) {
    _logger.severe('Error fetching seller product stats: $e');
    throw Exception('فشل في تحميل إحصائيات المنتجات: $e');
  }
}

/// Provider for seller analytics/views statistics
@riverpod
Future<Map<String, dynamic>> sellerAnalyticsStats(Ref ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.warning('User not authenticated, cannot fetch analytics stats');
      return {};
    }

    _logger.info('Fetching seller analytics stats from Go backend');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/seller/stats/analytics',
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch analytics stats: ${response.message}');
      throw Exception('فشل في تحميل إحصائيات التحليل');
    }

    _logger.info('Analytics stats fetched successfully');
    return response.data!;
  } catch (e) {
    _logger.severe('Error fetching seller analytics stats: $e');
    throw Exception('فشل في تحميل إحصائيات التحليل: $e');
  }
}

/// Provider for seller messages/communication statistics
@riverpod
Future<Map<String, dynamic>> sellerMessagesStats(Ref ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.warning('User not authenticated, cannot fetch messages stats');
      return {};
    }

    _logger.info('Fetching seller messages stats from Go backend');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/seller/stats/messages',
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch messages stats: ${response.message}');
      throw Exception('فشل في تحميل إحصائيات الرسائل');
    }

    _logger.info('Messages stats fetched successfully');
    return response.data!;
  } catch (e) {
    _logger.severe('Error fetching seller messages stats: $e');
    throw Exception('فشل في تحميل إحصائيات الرسائل: $e');
  }
}

/// Provider for seller orders statistics
@riverpod
Future<Map<String, dynamic>> sellerOrdersStats(Ref ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.warning('User not authenticated, cannot fetch orders stats');
      return {};
    }

    _logger.info('Fetching seller orders stats from Go backend');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/seller/stats/orders',
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch orders stats: ${response.message}');
      throw Exception('فشل في تحميل إحصائيات الطلبات');
    }

    _logger.info('Orders stats fetched successfully');
    return response.data!;
  } catch (e) {
    _logger.severe('Error fetching seller orders stats: $e');
    throw Exception('فشل في تحميل إحصائيات الطلبات: $e');
  }
}

/// Combined seller dashboard statistics
@riverpod
Future<Map<String, dynamic>> sellerDashboardStats(Ref ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.warning('User not authenticated, cannot fetch dashboard stats');
      return {};
    }

    _logger.info('Fetching seller dashboard stats from Go backend');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/seller/stats/dashboard',
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch dashboard stats: ${response.message}');
      throw Exception('فشل في تحميل إحصائيات لوحة التحكم');
    }

    _logger.info('Dashboard stats fetched successfully');
    return response.data!;
  } catch (e) {
    _logger.severe('Error fetching seller dashboard stats: $e');
    throw Exception('فشل في تحميل إحصائيات البائع: $e');
  }
}
