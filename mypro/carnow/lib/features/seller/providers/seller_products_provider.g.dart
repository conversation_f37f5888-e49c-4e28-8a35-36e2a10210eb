// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_products_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sellerProductsHash() => r'a6f62efbb20185871bf83bcc6b8e2d45c624491f';

/// See also [SellerProducts].
@ProviderFor(SellerProducts)
final sellerProductsProvider =
    AutoDisposeAsyncNotifierProvider<
      SellerProducts,
      List<ProductModel>
    >.internal(
      SellerProducts.new,
      name: r'sellerProductsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerProductsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SellerProducts = AutoDisposeAsyncNotifier<List<ProductModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
