// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_activity_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sellerActivityHash() => r'4e5159ac10167c004f0411ef8c50c4a0bfa77f0e';

/// Seller Activity Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// ✅ Uses Go Backend API ONLY for activity data
/// ✅ NO direct Supabase calls
/// ✅ Clean async/await patterns
/// Provider for seller activity logs
///
/// Copied from [sellerActivity].
@ProviderFor(sellerActivity)
final sellerActivityProvider =
    AutoDisposeFutureProvider<List<Map<String, dynamic>>>.internal(
      sellerActivity,
      name: r'sellerActivityProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerActivityHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SellerActivityRef =
    AutoDisposeFutureProviderRef<List<Map<String, dynamic>>>;
String _$recentSellerActivityHash() =>
    r'365b8760315b5719ac4eb65536d25301a38bcc83';

/// Provider for recent seller activity (last 7 days)
///
/// Copied from [recentSellerActivity].
@ProviderFor(recentSellerActivity)
final recentSellerActivityProvider =
    AutoDisposeFutureProvider<List<Map<String, dynamic>>>.internal(
      recentSellerActivity,
      name: r'recentSellerActivityProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$recentSellerActivityHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RecentSellerActivityRef =
    AutoDisposeFutureProviderRef<List<Map<String, dynamic>>>;
String _$sellerActivitySummaryHash() =>
    r'f93b28d7fe2db68e6afbbb6bb2224d759bd09323';

/// Provider for activity summary/statistics
///
/// Copied from [sellerActivitySummary].
@ProviderFor(sellerActivitySummary)
final sellerActivitySummaryProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
      sellerActivitySummary,
      name: r'sellerActivitySummaryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerActivitySummaryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SellerActivitySummaryRef =
    AutoDisposeFutureProviderRef<Map<String, dynamic>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
