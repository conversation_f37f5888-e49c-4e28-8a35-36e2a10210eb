// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_subscription_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$allSellerSubscriptionRequestsHash() =>
    r'c00508b2be9fffd9d5e259d04ad2ebcdc1a8ba7b';

/// Provider لجلب جميع طلبات الاشتراك للبائعين (للإدارة)
///
/// Copied from [allSellerSubscriptionRequests].
@ProviderFor(allSellerSubscriptionRequests)
final allSellerSubscriptionRequestsProvider =
    AutoDisposeFutureProvider<List<SellerSubscriptionRequest>>.internal(
      allSellerSubscriptionRequests,
      name: r'allSellerSubscriptionRequestsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$allSellerSubscriptionRequestsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AllSellerSubscriptionRequestsRef =
    AutoDisposeFutureProviderRef<List<SellerSubscriptionRequest>>;
String _$sellerSubscriptionHash() =>
    r'4c875e7dd139f522d409e4498449da6ca4789181';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider لجلب الاشتراك النشط الحالي لبائع معين
///
/// Copied from [sellerSubscription].
@ProviderFor(sellerSubscription)
const sellerSubscriptionProvider = SellerSubscriptionFamily();

/// Provider لجلب الاشتراك النشط الحالي لبائع معين
///
/// Copied from [sellerSubscription].
class SellerSubscriptionFamily extends Family<AsyncValue<SellerSubscription?>> {
  /// Provider لجلب الاشتراك النشط الحالي لبائع معين
  ///
  /// Copied from [sellerSubscription].
  const SellerSubscriptionFamily();

  /// Provider لجلب الاشتراك النشط الحالي لبائع معين
  ///
  /// Copied from [sellerSubscription].
  SellerSubscriptionProvider call(String sellerId) {
    return SellerSubscriptionProvider(sellerId);
  }

  @override
  SellerSubscriptionProvider getProviderOverride(
    covariant SellerSubscriptionProvider provider,
  ) {
    return call(provider.sellerId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sellerSubscriptionProvider';
}

/// Provider لجلب الاشتراك النشط الحالي لبائع معين
///
/// Copied from [sellerSubscription].
class SellerSubscriptionProvider
    extends AutoDisposeFutureProvider<SellerSubscription?> {
  /// Provider لجلب الاشتراك النشط الحالي لبائع معين
  ///
  /// Copied from [sellerSubscription].
  SellerSubscriptionProvider(String sellerId)
    : this._internal(
        (ref) => sellerSubscription(ref as SellerSubscriptionRef, sellerId),
        from: sellerSubscriptionProvider,
        name: r'sellerSubscriptionProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$sellerSubscriptionHash,
        dependencies: SellerSubscriptionFamily._dependencies,
        allTransitiveDependencies:
            SellerSubscriptionFamily._allTransitiveDependencies,
        sellerId: sellerId,
      );

  SellerSubscriptionProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sellerId,
  }) : super.internal();

  final String sellerId;

  @override
  Override overrideWith(
    FutureOr<SellerSubscription?> Function(SellerSubscriptionRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SellerSubscriptionProvider._internal(
        (ref) => create(ref as SellerSubscriptionRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sellerId: sellerId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<SellerSubscription?> createElement() {
    return _SellerSubscriptionProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SellerSubscriptionProvider && other.sellerId == sellerId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sellerId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SellerSubscriptionRef
    on AutoDisposeFutureProviderRef<SellerSubscription?> {
  /// The parameter `sellerId` of this provider.
  String get sellerId;
}

class _SellerSubscriptionProviderElement
    extends AutoDisposeFutureProviderElement<SellerSubscription?>
    with SellerSubscriptionRef {
  _SellerSubscriptionProviderElement(super.provider);

  @override
  String get sellerId => (origin as SellerSubscriptionProvider).sellerId;
}

String _$adminSubscriptionHash() => r'a87ea126e1172ed0283068699a855ed7fa69e290';

/// Provider لإدارة اشتراكات البائعين من قبل المدير
///
/// Copied from [AdminSubscription].
@ProviderFor(AdminSubscription)
final adminSubscriptionProvider =
    AutoDisposeAsyncNotifierProvider<AdminSubscription, void>.internal(
      AdminSubscription.new,
      name: r'adminSubscriptionProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adminSubscriptionHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AdminSubscription = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
