import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/unified_auth_provider.dart';

part 'seller_activity_provider.g.dart';

final _logger = Logger('SellerActivityProvider');

/// Seller Activity Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// ✅ Uses Go Backend API ONLY for activity data
/// ✅ NO direct Supabase calls
/// ✅ Clean async/await patterns

/// Provider for seller activity logs
@riverpod
Future<List<Map<String, dynamic>>> sellerActivity(Ref ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.warning('User not authenticated, cannot fetch activity');
      return [];
    }

    _logger.info('Fetching seller activity from Go backend');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<List<dynamic>>(
      '/seller/activity',
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch activity: ${response.message}');
      throw Exception('فشل في تحميل بيانات النشاط');
    }

    final activityData = response.data!
        .cast<Map<String, dynamic>>()
        .toList();

    _logger.info('Activity data fetched successfully: ${activityData.length} items');
    return activityData;
  } catch (e) {
    _logger.severe('Error fetching seller activity: $e');
    throw Exception('فشل في تحميل بيانات النشاط: $e');
  }
}

/// Provider for recent seller activity (last 7 days)
@riverpod
Future<List<Map<String, dynamic>>> recentSellerActivity(Ref ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.warning('User not authenticated, cannot fetch recent activity');
      return [];
    }

    _logger.info('Fetching recent seller activity from Go backend');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<List<dynamic>>(
      '/seller/activity/recent',
      queryParameters: {'days': '7'},
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch recent activity: ${response.message}');
      throw Exception('فشل في تحميل بيانات النشاط الأخير');
    }

    final activityData = response.data!
        .cast<Map<String, dynamic>>()
        .toList();

    _logger.info('Recent activity data fetched successfully: ${activityData.length} items');
    return activityData;
  } catch (e) {
    _logger.severe('Error fetching recent seller activity: $e');
    throw Exception('فشل في تحميل بيانات النشاط الأخير: $e');
  }
}

/// Provider for activity summary/statistics
@riverpod
Future<Map<String, dynamic>> sellerActivitySummary(Ref ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.warning('User not authenticated, cannot fetch activity summary');
      return {};
    }

    _logger.info('Fetching seller activity summary from Go backend');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/seller/activity/summary',
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch activity summary: ${response.message}');
      throw Exception('فشل في تحميل ملخص النشاط');
    }

    _logger.info('Activity summary fetched successfully');
    return response.data!;
  } catch (e) {
    _logger.severe('Error fetching seller activity summary: $e');
    throw Exception('فشل في تحميل ملخص النشاط: $e');
  }
}


