import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/models/enums.dart';
import '../../../core/models/order_model.dart';
import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/networking/simple_api_client.dart';

part 'seller_orders_provider.g.dart';

final _logger = Logger('SellerOrdersProvider');

/// مزود للوصول إلى كافة الطلبات للبائع
@riverpod
class SellerOrders extends _$SellerOrders {
  @override
  Future<List<OrderModel>> build() async => _fetchOrders();

  /// جلب قائمة الطلبات من قاعدة البيانات
  Future<List<OrderModel>> _fetchOrders() async {
    try {
      final currentUser = ref.watch(currentUserProvider);
      final userId = currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      _logger.info('Fetching orders for seller: $userId');

      final apiClient = ref.read(simpleApiClientProvider);

      final response = await apiClient.get('/orders/seller', queryParameters: {
        'sellerId': userId.toString(),
      });

      final ordersData = response.data as List<dynamic>;
      final orders = ordersData
          .map((orderData) => OrderModel.fromJson(orderData as Map<String, dynamic>))
          .toList();

      _logger.info('Fetched ${orders.length} orders for seller: $userId');
      return orders;
    } catch (e, stackTrace) {
      _logger.severe('Error fetching orders', e, stackTrace);
      throw Exception('Failed to load orders: $e');
    }
  }

  /// تحديث حالة طلب
  Future<void> updateOrderStatus(int orderId, OrderStatus newStatus) async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);

      await apiClient.put('/orders/$orderId/status', data: {
        'status': newStatus.name,
      });

      _logger.info('Updated order $orderId status to: $newStatus');

      // تحديث القائمة المحلية
      state = const AsyncValue.loading();
      state = AsyncValue.data(await _fetchOrders());
    } catch (e) {
      _logger.severe('Error updating order status', e);
      throw Exception('Failed to update order status: $e');
    }
  }

  /// إضافة رقم تتبع الشحنة
  Future<void> updateTrackingNumber(int orderId, String trackingNumber) async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);

      await apiClient.put('/orders/$orderId/tracking', data: {
        'tracking_number': trackingNumber,
      });

      _logger.info('Updated tracking number for order $orderId');

      // تحديث القائمة المحلية
      state = const AsyncValue.loading();
      state = AsyncValue.data(await _fetchOrders());
    } catch (e) {
      _logger.severe('Error updating tracking number', e);
      throw Exception('Failed to update tracking number: $e');
    }
  }

  /// تحديث بيانات الطلبات
  Future<void> refresh() async {
    _logger.info('Refreshing orders');
    ref.invalidateSelf();
  }
}

/// مزود لتصفية الطلبات حسب الحالة
@riverpod
class OrderStatusFilter extends _$OrderStatusFilter {
  @override
  OrderStatus? build() => null; // البداية بدون تصفية

  void setFilter(OrderStatus? status) {
    state = status;
  }
}

/// مزود للطلبات المصفاة
@riverpod
Future<List<OrderModel>> filteredOrders(Ref ref) async {
  final orders = await ref.watch(sellerOrdersProvider.future);
  final statusFilter = ref.watch(orderStatusFilterProvider);

  if (statusFilter == null) {
    return orders;
  }

  return orders
      .where((OrderModel order) => order.status == statusFilter)
      .toList();
}
