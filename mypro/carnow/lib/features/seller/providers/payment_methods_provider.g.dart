// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_methods_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userPaymentMethodsHash() =>
    r'd2b439fa53f8e6e1474d2e1cfd1903fb9f593fe6';

/// Provider لجلب طرق الدفع للمستخدم الحالي
/// ✅ Uses Go backend API instead of direct Supabase calls
/// Follows Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [userPaymentMethods].
@ProviderFor(userPaymentMethods)
final userPaymentMethodsProvider =
    AutoDisposeFutureProvider<List<PaymentMethodModel>>.internal(
      userPaymentMethods,
      name: r'userPaymentMethodsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userPaymentMethodsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserPaymentMethodsRef =
    AutoDisposeFutureProviderRef<List<PaymentMethodModel>>;
String _$defaultPaymentMethodHash() =>
    r'a711a8fb0b1f97db51b61bd7fac715c48f6d5c01';

/// Provider للحصول على طريقة الدفع الافتراضية
/// ✅ Uses payment methods from Go backend API
///
/// Copied from [defaultPaymentMethod].
@ProviderFor(defaultPaymentMethod)
final defaultPaymentMethodProvider =
    AutoDisposeFutureProvider<PaymentMethodModel?>.internal(
      defaultPaymentMethod,
      name: r'defaultPaymentMethodProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$defaultPaymentMethodHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DefaultPaymentMethodRef =
    AutoDisposeFutureProviderRef<PaymentMethodModel?>;
String _$paymentMethodsManagerHash() =>
    r'bfd28118c94e48f7e585929accfe3d5fe8132d9e';

/// Provider لإدارة طرق الدفع
/// ✅ Uses SimpleApiClient for ALL operations
///
/// Copied from [PaymentMethodsManager].
@ProviderFor(PaymentMethodsManager)
final paymentMethodsManagerProvider =
    AutoDisposeAsyncNotifierProvider<
      PaymentMethodsManager,
      List<PaymentMethodModel>
    >.internal(
      PaymentMethodsManager.new,
      name: r'paymentMethodsManagerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$paymentMethodsManagerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PaymentMethodsManager =
    AutoDisposeAsyncNotifier<List<PaymentMethodModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
