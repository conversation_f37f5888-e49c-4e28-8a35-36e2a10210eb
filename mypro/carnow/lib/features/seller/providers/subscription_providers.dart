import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/subscription_model.dart';
import '../models/discount_model.dart';
import '../../../core/api/subscription_api_client.dart';

part 'subscription_providers.g.dart';

/// Provider للحصول على خطط الاشتراك
@riverpod
Future<List<SubscriptionPlan>> subscriptionPlans(Ref ref) async {
  final apiClient = ref.watch(subscriptionApiClientProvider);
  return await apiClient.getSubscriptionPlans();
}

/// Provider للحصول على خطة اشتراك محددة
@riverpod
Future<SubscriptionPlan> subscriptionPlan(
  Ref ref,
  int planId,
) async {
  final apiClient = ref.watch(subscriptionApiClientProvider);
  return await apiClient.getSubscriptionPlan(planId);
}

/// Provider لحساب سعر الاشتراك
@riverpod
Future<DiscountCalculation> subscriptionPriceCalculation(
  Ref ref, {
  required int planId,
  required String billingCycle,
  String? discountCode,
}) async {
  final apiClient = ref.watch(subscriptionApiClientProvider);
  return await apiClient.calculateSubscriptionPrice(
    planId: planId,
    billingCycle: billingCycle,
    discountCode: discountCode,
  );
}

/// Provider لإنشاء اشتراك جديد
@riverpod
class CreateSubscriptionNotifier extends _$CreateSubscriptionNotifier {
  @override
  Future<SellerSubscription?> build() async {
    return null;
  }

  Future<void> createSubscription({
    required int planId,
    required String billingCycle,
    String? discountCode,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      final apiClient = ref.read(subscriptionApiClientProvider);
      final subscription = await apiClient.createSubscription(
        planId: planId,
        billingCycle: billingCycle,
        discountCode: discountCode,
      );
      
      state = AsyncValue.data(subscription);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Provider للحصول على اشتراك المستخدم
@riverpod
Future<SellerSubscription?> userSubscription(Ref ref) async {
  final apiClient = ref.watch(subscriptionApiClientProvider);
  final subscriptions = await apiClient.getSellerSubscriptions();
  return subscriptions.isNotEmpty ? subscriptions.first : null;
}

/// Provider لإلغاء الاشتراك
@riverpod
class CancelSubscriptionNotifier extends _$CancelSubscriptionNotifier {
  @override
  Future<void> build() async {
    return;
  }

  Future<void> cancelSubscription() async {
    state = const AsyncValue.loading();
    
    try {
      final apiClient = ref.read(subscriptionApiClientProvider);
      final subscription = await ref.read(userSubscriptionProvider.future);
      if (subscription != null) {
        await apiClient.cancelSubscription(int.parse(subscription.id));
      }
      
      // Refresh user subscription
      ref.invalidate(userSubscriptionProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Provider للحصول على الخصومات النشطة
@riverpod
Future<List<Discount>> activeDiscounts(Ref ref) async {
  final apiClient = ref.watch(subscriptionApiClientProvider);
  return await apiClient.getYearlySubscriptionDiscount().then((discount) => [discount]);
}

/// Provider للتحقق من رمز الخصم
@riverpod
Future<Discount?> validateDiscountCode(
  Ref ref,
  String code,
) async {
  final apiClient = ref.watch(subscriptionApiClientProvider);
  return await apiClient.validateDiscountCode(code);
}

/// Provider لحساب الخصم
@riverpod
Future<DiscountCalculation> discountCalculation(
  Ref ref, {
  required double amount,
  String? discountCode,
}) async {
  final apiClient = ref.watch(subscriptionApiClientProvider);
  return await apiClient.calculateDiscount(
    amount: amount,
    discountCode: discountCode,
  );
}

/// Provider لتطبيق الخصم
@riverpod
class ApplyDiscountNotifier extends _$ApplyDiscountNotifier {
  @override
  Future<DiscountCalculation?> build() async {
    return null;
  }

  Future<void> applyDiscount({
    required double amount,
    String? discountCode,
    int? subscriptionId,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      final apiClient = ref.read(subscriptionApiClientProvider);
      final calculation = await apiClient.applyDiscount(
        amount: amount,
        discountCode: discountCode,
        subscriptionId: subscriptionId,
      );
      
      state = AsyncValue.data(calculation);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Provider للحصول على خصم الاشتراك السنوي
@riverpod
Future<Discount> yearlySubscriptionDiscount(Ref ref) async {
  final apiClient = ref.watch(subscriptionApiClientProvider);
  return await apiClient.getYearlySubscriptionDiscount();
}

/// Provider للحصول على خطط الاشتراك مع الخصومات المحسوبة
@riverpod
Future<List<SubscriptionPlanWithDiscounts>> subscriptionPlansWithDiscounts(
  Ref ref,
) async {
  final plans = await ref.watch(subscriptionPlansProvider.future);
  final yearlyDiscount = await ref.watch(yearlySubscriptionDiscountProvider.future);
  
  return plans.map((plan) {
    // حساب السعر السنوي مع الخصم
    final yearlyPriceWithDiscount = plan.yearlyPriceLD * (1 - yearlyDiscount.discountValue / 100);
    
    return SubscriptionPlanWithDiscounts(
      plan: plan,
      yearlyDiscount: yearlyDiscount,
      yearlyPriceWithDiscount: yearlyPriceWithDiscount,
      yearlyDiscountPercentage: yearlyDiscount.discountValue,
    );
  }).toList();
}

/// نموذج خطة الاشتراك مع الخصومات
class SubscriptionPlanWithDiscounts {
  final SubscriptionPlan plan;
  final Discount yearlyDiscount;
  final double yearlyPriceWithDiscount;
  final double yearlyDiscountPercentage;

  SubscriptionPlanWithDiscounts({
    required this.plan,
    required this.yearlyDiscount,
    required this.yearlyPriceWithDiscount,
    required this.yearlyDiscountPercentage,
  });
} 