import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/simple_analytics_model.dart';
import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/unified_auth_provider.dart';

part 'simple_analytics_provider.g.dart';

/// Simple analytics provider - simplified
@riverpod
Future<SimpleAnalyticsModel> simpleAnalytics(
  Ref ref,
  String period,
) async {
  final apiClient = ref.read(simpleApiClientProvider);
  final user = ref.read(currentUserProvider);
  if (user == null) {
    throw Exception('User not authenticated');
  }

  try {
    // Fetch analytics data from Go backend
    final response = await apiClient.getApi(
      '/analytics/seller/${user.id}',
      queryParameters: {'period': period},
    );

    return SimpleAnalyticsModel.fromJson(response.data);
  } catch (e) {
    throw Exception('فشل في تحميل بيانات التحليل: $e');
  }
}

/// مزود فترات التحليل المتاحة
final analyticsPeriodsProvider = Provider<List<AnalyticsPeriod>>((ref) {
  return [
    const AnalyticsPeriod(key: 'week', label: 'الأسبوع الماضي', icon: 'week'),
    const AnalyticsPeriod(key: 'month', label: 'الشهر الماضي', icon: 'month'),
    const AnalyticsPeriod(
      key: 'quarter',
      label: 'الربع الأخير',
      icon: 'quarter',
    ),
    const AnalyticsPeriod(key: 'year', label: 'السنة الماضية', icon: 'year'),
  ];
});

/// نموذج فترة التحليل
class AnalyticsPeriod {
  const AnalyticsPeriod({
    required this.key,
    required this.label,
    required this.icon,
  });

  final String key;
  final String label;
  final String icon;
}
