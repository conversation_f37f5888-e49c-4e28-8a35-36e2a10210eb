import 'dart:io';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/repositories/product_repository.dart';
import '../../../core/services/storage_service.dart';
import '../../products/models/product_model.dart';
import '../../products/models/product_filter_model.dart';
import '../../products/services/product_api_service.dart';

part 'seller_products_provider.g.dart';

final _logger = Logger('SellerProductsProvider');

@riverpod
class SellerProducts extends _$SellerProducts {
  @override
  Future<List<ProductModel>> build() async {
    _logger.info('Building SellerProducts provider');

    final currentUser = ref.watch(currentUserProvider);
    
    if (currentUser == null) {
      _logger.severe('User not authenticated');
      return []; // Return empty list for unauthenticated user
    }

    final userId = currentUser.id;
    if (userId.isEmpty) {
      _logger.severe('Invalid user ID: empty. User: $currentUser');
      throw Exception('Invalid user ID');
    }

    _logger.info('Fetching products for seller ID: $userId');
    try {
      final products = await ref
          .read(productApiServiceProvider)
          .getProductsByUserId(userId);
      return products;
    } catch (e, stackTrace) {
      _logger.severe('Error fetching seller products', e, stackTrace);
      rethrow;
    }
  }

  /// Add a new product
  Future<String> addProduct(ProductModel product, List<File> images) async {
    _logger.info('Adding new product: ${product.name}');
    try {
      state = const AsyncValue.loading();

      final currentUser = ref.read(currentUserProvider);
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // First create the product record to get an ID
      final createdProduct = await ref
          .read(productApiServiceProvider)
          .createProduct(currentUser.id, product.copyWith(sellerId: currentUser.id).toJson());
      final productId = createdProduct.id;
      _logger.info('Product created with ID: $productId');

      if (productId.isEmpty) {
        throw Exception('Failed to get a valid product ID');
      }

      // Then upload the images if any
      var imageUrls = <String>[];
      if (images.isNotEmpty) {
        try {
          // Verify the bucket exists and is accessible
          final storageService = ref.read(storageServiceProvider);
          final bucketExists = await storageService
              .checkProductImagesBucketExists();

          if (!bucketExists) {
            _logger.warning(
              'Product images bucket does not exist, attempting to create it',
            );
            // The service will try to create the bucket automatically
          }

          _logger.info(
            'Uploading ${images.length} images for product: $productId',
          );
          imageUrls = await storageService.uploadMultipleProductImages(
            images,
            productId,
          );

          // Update the product with the image URLs
          if (imageUrls.isNotEmpty) {
            await ref
                .read(productApiServiceProvider)
                .updateProduct(productId, {'images': imageUrls});
            _logger.info('Updated product with ${imageUrls.length} image URLs');
          }
        } catch (e) {
          _logger.severe('Error uploading product images: $e');
          // Continue with product creation even if image upload fails
          // Consider showing a warning to the user that images failed to upload
        }
      }

      // Refresh the product list
      ref.invalidateSelf();

      // ref.invalidateSelf() will trigger the build method to refresh the list.
      // The explicit state update here was redundant and could use the
      // wrong ID. This ensures the product list is refreshed with the latest
      // data.
      return productId;
    } catch (e) {
      _logger.severe('Error adding product: $e');
      state = AsyncValue.error(e, StackTrace.current);
      throw Exception('Failed to add product: $e');
    }
  }

  Future<void> updateProduct(
    String productId,
    ProductModel product,
    List<File> newImages,
  ) async {
    state = const AsyncValue.loading();
    try {
      final currentUser = ref.read(currentUserProvider);
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final userId = currentUser.id;
      if (userId.isEmpty) {
        _logger.severe('Cannot update product, userId is null or empty.');
        throw Exception('User not authenticated or userId missing');
      }

      // Verify this is the seller's product
      final existingProduct = await ref
          .read(productApiServiceProvider)
          .getProductById(productId);

      if (existingProduct == null) {
        throw Exception('Product not found');
      }

      if (existingProduct.sellerId != userId) {
        _logger.severe(
          'User ID: $userId, '
          'Product seller ID: ${existingProduct.sellerId}',
        );
        throw Exception('Not authorized to update this product');
      }

      // Update product data
      _logger.info('Updating product: $productId');
      await ref.read(productApiServiceProvider).updateProduct(productId, product.toJson());

      // Handle image uploads if there are new images
      if (newImages.isNotEmpty) {
        _logger.info(
          'Uploading ${newImages.length} new images for product: $productId',
        );
        final imageUrls = await ref
            .read(storageServiceProvider)
            .uploadMultipleProductImages(newImages, productId);

        // Get existing images and add new ones
        final updatedImageUrls = [...existingProduct.images, ...imageUrls];

        // Update the product with all image URLs
        await ref
            .read(productApiServiceProvider)
            .updateProduct(productId, {'images': updatedImageUrls});
      }

      // Refresh the products list
      ref.invalidateSelf();
    } catch (e, stack) {
      _logger.severe('Error updating product: $e\n$stack');
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> deleteProduct(String productId) async {
    state = const AsyncValue.loading();
    try {
      final currentUser = ref.read(currentUserProvider);
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final userId = currentUser.id;
      if (userId.isEmpty) {
        _logger.severe('Cannot delete product, userId is null or empty.');
        throw Exception('User not authenticated or userId missing');
      }

      // Verify this is the seller's product
      final existingProduct = await ref
          .read(productApiServiceProvider)
          .getProductById(productId);

      if (existingProduct == null) {
        throw Exception('Product not found');
      }
      if (existingProduct.sellerId != userId) {
        _logger.severe(
          'User ID: $userId, '
          'Product seller ID: ${existingProduct.sellerId}',
        );
        throw Exception('Not authorized to delete this product');
      }

      // Delete image files first
      if (existingProduct.images.isNotEmpty) {
        _logger.info(
          'Deleting ${existingProduct.images.length} images for product: '
          '$productId',
        );
        for (final imageUrl in existingProduct.images) {
          await ref.read(storageServiceProvider).deleteProductImage(imageUrl);
        }
      }

      // Delete the product
      await ref.read(productApiServiceProvider).deleteProduct(productId);

      // Refresh the products list
      ref.invalidateSelf();
    } catch (e, stack) {
      _logger.severe('Error deleting product: $e\n$stack');
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }
}
