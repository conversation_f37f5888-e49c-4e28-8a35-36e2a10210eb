import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/unified_auth_provider.dart';
import '../models/subscription_model.dart';
import '../models/subscription_request_model.dart';
import '../models/seller_enums.dart';

part 'admin_subscription_provider.g.dart';

final _logger = Logger('AdminSubscriptionProvider');

/// Provider لإدارة اشتراكات البائعين من قبل المدير
@riverpod
class AdminSubscription extends _$AdminSubscription {
  @override
  Future<void> build() async {
    // This provider is for actions, no initial data needed.
  }

  /// تحديث حالة اشتراك معين
  Future<void> updateSubscriptionStatus({
    required String subscriptionId,
    required SubscriptionStatus newStatus,
    String? adminNotes,
  }) async {
    try {
      _logger.info(
        'Admin updating subscription $subscriptionId to status $newStatus',
      );

      final apiClient = ref.read(simpleApiClientProvider);
      final authSystem = ref.read(unifiedAuthProviderProvider.notifier);
    
      if (authSystem.currentUser == null) {
        throw Exception('User not authenticated');
      }

      final updateData = {
        'status': newStatus.name,
        if (adminNotes != null) 'admin_notes': adminNotes,
      };

      await apiClient.put(
        '/admin/subscriptions/$subscriptionId',
        data: updateData,
      );

      _logger.info('Successfully updated subscription $subscriptionId');
    } catch (e, st) {
      _logger.severe('Error updating subscription $subscriptionId', e, st);
      rethrow;
    }
  }

  // يمكن إضافة المزيد من الدوال الإدارية هنا مستقبلاً
  // مثل تمديد فترة تجريبية، منح خصم، إلخ.
}

/// Provider لجلب جميع طلبات الاشتراك للبائعين (للإدارة)
@riverpod
Future<List<SellerSubscriptionRequest>> allSellerSubscriptionRequests(Ref ref) async {
  try {
    _logger.info('Fetching all seller subscription requests for admin');

    final apiClient = ref.read(simpleApiClientProvider);
    final authSystem = ref.read(unifiedAuthProviderProvider.notifier);
    
    if (authSystem.currentUser == null) {
      throw Exception('User not authenticated');
    }

    final response = await apiClient.get(
      '/admin/seller/subscription-requests',
    );

    if (response.data == null || response.data is! List) {
      _logger.info('No subscription requests found');
      return [];
    }

    final List<dynamic> requestsData = response.data;
    return requestsData
        .map((item) => SellerSubscriptionRequest.fromJson(item as Map<String, dynamic>))
        .toList();
  } catch (e, st) {
    _logger.severe('Error fetching all seller subscription requests', e, st);
    rethrow;
  }
}

/// Provider لجلب الاشتراك النشط الحالي لبائع معين
@riverpod
Future<SellerSubscription?> sellerSubscription(Ref ref, String sellerId) async {
  try {
    _logger.info('Fetching current active subscription for seller: $sellerId');

    final apiClient = ref.read(simpleApiClientProvider);
    final currentUser = ref.read(currentUserProvider);
    
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final response = await apiClient.get(
      '/admin/sellers/$sellerId/subscription',
    );

    if (response.data == null) {
      _logger.info(
        'No active or frozen subscription found for seller: $sellerId',
      );
      return null;
    }

    return SellerSubscription.fromJson(response.data);
  } catch (e, st) {
    _logger.severe('Error fetching subscription for seller: $sellerId', e, st);
    return null; // Return null on error instead of rethrowing
  }
}
