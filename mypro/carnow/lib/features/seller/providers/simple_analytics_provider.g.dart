// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'simple_analytics_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$simpleAnalyticsHash() => r'e072ed796501ffc0623ebb859b20ac487441ff3a';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Simple analytics provider - simplified
///
/// Copied from [simpleAnalytics].
@ProviderFor(simpleAnalytics)
const simpleAnalyticsProvider = SimpleAnalyticsFamily();

/// Simple analytics provider - simplified
///
/// Copied from [simpleAnalytics].
class SimpleAnalyticsFamily extends Family<AsyncValue<SimpleAnalyticsModel>> {
  /// Simple analytics provider - simplified
  ///
  /// Copied from [simpleAnalytics].
  const SimpleAnalyticsFamily();

  /// Simple analytics provider - simplified
  ///
  /// Copied from [simpleAnalytics].
  SimpleAnalyticsProvider call(String period) {
    return SimpleAnalyticsProvider(period);
  }

  @override
  SimpleAnalyticsProvider getProviderOverride(
    covariant SimpleAnalyticsProvider provider,
  ) {
    return call(provider.period);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'simpleAnalyticsProvider';
}

/// Simple analytics provider - simplified
///
/// Copied from [simpleAnalytics].
class SimpleAnalyticsProvider
    extends AutoDisposeFutureProvider<SimpleAnalyticsModel> {
  /// Simple analytics provider - simplified
  ///
  /// Copied from [simpleAnalytics].
  SimpleAnalyticsProvider(String period)
    : this._internal(
        (ref) => simpleAnalytics(ref as SimpleAnalyticsRef, period),
        from: simpleAnalyticsProvider,
        name: r'simpleAnalyticsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$simpleAnalyticsHash,
        dependencies: SimpleAnalyticsFamily._dependencies,
        allTransitiveDependencies:
            SimpleAnalyticsFamily._allTransitiveDependencies,
        period: period,
      );

  SimpleAnalyticsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.period,
  }) : super.internal();

  final String period;

  @override
  Override overrideWith(
    FutureOr<SimpleAnalyticsModel> Function(SimpleAnalyticsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SimpleAnalyticsProvider._internal(
        (ref) => create(ref as SimpleAnalyticsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        period: period,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<SimpleAnalyticsModel> createElement() {
    return _SimpleAnalyticsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SimpleAnalyticsProvider && other.period == period;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, period.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SimpleAnalyticsRef on AutoDisposeFutureProviderRef<SimpleAnalyticsModel> {
  /// The parameter `period` of this provider.
  String get period;
}

class _SimpleAnalyticsProviderElement
    extends AutoDisposeFutureProviderElement<SimpleAnalyticsModel>
    with SimpleAnalyticsRef {
  _SimpleAnalyticsProviderElement(super.provider);

  @override
  String get period => (origin as SimpleAnalyticsProvider).period;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
