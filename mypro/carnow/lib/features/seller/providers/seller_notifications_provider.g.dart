// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_notifications_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$unreadNotificationsCountHash() =>
    r'0d31386629383f0c2396a5978d6228df51474ca5';

/// مزود عدد الإشعارات غير المقروءة
///
/// Copied from [unreadNotificationsCount].
@ProviderFor(unreadNotificationsCount)
final unreadNotificationsCountProvider =
    AutoDisposeFutureProvider<int>.internal(
      unreadNotificationsCount,
      name: r'unreadNotificationsCountProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$unreadNotificationsCountHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UnreadNotificationsCountRef = AutoDisposeFutureProviderRef<int>;
String _$sellerNotificationsHash() =>
    r'b21f8f43b67076673d7dadc0cb5835eda0431896';

/// مزود الإشعارات للبائع باستخدام Riverpod
///
/// Copied from [SellerNotifications].
@ProviderFor(SellerNotifications)
final sellerNotificationsProvider =
    AutoDisposeAsyncNotifierProvider<
      SellerNotifications,
      List<SellerNotification>
    >.internal(
      SellerNotifications.new,
      name: r'sellerNotificationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerNotificationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SellerNotifications =
    AutoDisposeAsyncNotifier<List<SellerNotification>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
