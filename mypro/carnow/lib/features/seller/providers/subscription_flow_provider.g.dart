// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_flow_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$isSubscriptionFormValidHash() =>
    r'abef4f6e9c6286079c4c376117174918c9fbedb1';

/// Provider for current form validation state
///
/// Copied from [isSubscriptionFormValid].
@ProviderFor(isSubscriptionFormValid)
final isSubscriptionFormValidProvider = AutoDisposeProvider<bool>.internal(
  isSubscriptionFormValid,
  name: r'isSubscriptionFormValidProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isSubscriptionFormValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsSubscriptionFormValidRef = AutoDisposeProviderRef<bool>;
String _$subscriptionProgressHash() =>
    r'd4eda6d31520438d903a3fa5832929cbbcb89adf';

/// Provider for current step progress
///
/// Copied from [subscriptionProgress].
@ProviderFor(subscriptionProgress)
final subscriptionProgressProvider = AutoDisposeProvider<double>.internal(
  subscriptionProgress,
  name: r'subscriptionProgressProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$subscriptionProgressHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionProgressRef = AutoDisposeProviderRef<double>;
String _$isSubscriptionLoadingHash() =>
    r'22fd47a76be8564c6913b1d16ed8df1e0dee553d';

/// Provider for loading state
///
/// Copied from [isSubscriptionLoading].
@ProviderFor(isSubscriptionLoading)
final isSubscriptionLoadingProvider = AutoDisposeProvider<bool>.internal(
  isSubscriptionLoading,
  name: r'isSubscriptionLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isSubscriptionLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsSubscriptionLoadingRef = AutoDisposeProviderRef<bool>;
String _$subscriptionErrorHash() => r'acaa17cdf3d20d6253a54492307a960561935a19';

/// Provider for error state
///
/// Copied from [subscriptionError].
@ProviderFor(subscriptionError)
final subscriptionErrorProvider = AutoDisposeProvider<String?>.internal(
  subscriptionError,
  name: r'subscriptionErrorProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$subscriptionErrorHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionErrorRef = AutoDisposeProviderRef<String?>;
String _$subscriptionErrorArHash() =>
    r'efdad2ff0d3d2b6c67257a1f31f3fa85028899a0';

/// Provider for Arabic error state
///
/// Copied from [subscriptionErrorAr].
@ProviderFor(subscriptionErrorAr)
final subscriptionErrorArProvider = AutoDisposeProvider<String?>.internal(
  subscriptionErrorAr,
  name: r'subscriptionErrorArProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$subscriptionErrorArHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionErrorArRef = AutoDisposeProviderRef<String?>;
String _$subscriptionSuccessHash() =>
    r'2d9304386528ec9ac68aee7a24c0d2604d7a3b2a';

/// Provider for success state
///
/// Copied from [subscriptionSuccess].
@ProviderFor(subscriptionSuccess)
final subscriptionSuccessProvider = AutoDisposeProvider<String?>.internal(
  subscriptionSuccess,
  name: r'subscriptionSuccessProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$subscriptionSuccessHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionSuccessRef = AutoDisposeProviderRef<String?>;
String _$subscriptionSuccessArHash() =>
    r'503347049c796cfcd9a02d7e0c506d3f2ee387e6';

/// Provider for Arabic success state
///
/// Copied from [subscriptionSuccessAr].
@ProviderFor(subscriptionSuccessAr)
final subscriptionSuccessArProvider = AutoDisposeProvider<String?>.internal(
  subscriptionSuccessAr,
  name: r'subscriptionSuccessArProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$subscriptionSuccessArHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionSuccessArRef = AutoDisposeProviderRef<String?>;
String _$subscriptionFlowProviderHash() =>
    r'0de684e9e9e7ac473485beb884f73004bad863e1';

/// Main subscription flow provider with comprehensive state management
///
/// Copied from [SubscriptionFlowProvider].
@ProviderFor(SubscriptionFlowProvider)
final subscriptionFlowProviderProvider =
    AutoDisposeNotifierProvider<
      SubscriptionFlowProvider,
      SubscriptionFlowState
    >.internal(
      SubscriptionFlowProvider.new,
      name: r'subscriptionFlowProviderProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionFlowProviderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SubscriptionFlowProvider = AutoDisposeNotifier<SubscriptionFlowState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
