import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/unified_auth_provider.dart';
import '../models/seller_notification_model.dart';

part 'seller_notifications_provider.g.dart';

/// مزود الإشعارات للبائع باستخدام Riverpod
@riverpod
class SellerNotifications extends _$SellerNotifications {
  Timer? _refreshTimer;

  @override
  Future<List<SellerNotification>> build() async {
    final user = ref.read(currentUserProvider);
    
    if (user == null) {
      return [];
    }

    // تحديث الإشعارات كل 5 دقائق
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      ref.invalidateSelf();
    });

    // إلغاء المؤقت عند التخلص من المزود
    ref.onDispose(() {
      _refreshTimer?.cancel();
    });

    return _loadNotifications(user.id);
  }

  Future<List<SellerNotification>> _loadNotifications(String userId) async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);
      
      final response = await apiClient.get(
        '/api/seller/notifications',
        queryParameters: {'seller_id': userId},
      );

      final notifications = (response.data['notifications'] as List)
          .map((data) => SellerNotification.fromJson(data))
          .toList();

      developer.log('تم استرداد ${notifications.length} إشعارات');
      return notifications;
    } catch (e) {
      developer.log('خطأ في تحميل الإشعارات: $e', error: e);
      return [];
    }
  }

  Future<void> markAsRead(String notificationId) async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);
      
      await apiClient.patch(
        '/api/seller/notifications/$notificationId/read',
      );

      // تحديث الحالة المحلية
      ref.invalidateSelf();
    } catch (e) {
      developer.log('خطأ في وضع علامة مقروءة على الإشعار: $e', error: e);
    }
  }

  Future<void> markAllAsRead() async {
    try {
      final user = ref.read(currentUserProvider);
      
      if (user == null) return;

      final apiClient = ref.read(simpleApiClientProvider);
      
      await apiClient.patch(
        '/api/seller/notifications/read-all',
        data: {'seller_id': user.id},
      );

      // تحديث الحالة المحلية
      ref.invalidateSelf();
    } catch (e) {
      developer.log('خطأ في وضع علامة مقروءة على كل الإشعارات: $e', error: e);
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);
      
      await apiClient.delete(
        '/api/seller/notifications/$notificationId',
      );

      // تحديث الحالة المحلية
      ref.invalidateSelf();
    } catch (e) {
      developer.log('خطأ في حذف الإشعار: $e', error: e);
    }
  }
}

/// مزود عدد الإشعارات غير المقروءة
@riverpod
Future<int> unreadNotificationsCount(Ref ref) async {
  final notifications = await ref.watch(sellerNotificationsProvider.future);
  return notifications.where((notification) => !notification.isRead).length;
}
