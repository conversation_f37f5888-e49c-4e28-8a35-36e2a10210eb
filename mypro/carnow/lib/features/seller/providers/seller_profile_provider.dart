import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:riverpod/riverpod.dart';

import '../../../core/networking/simple_api_client.dart';
import '../models/seller_profile_model.dart';

part 'seller_profile_provider.g.dart';

/// مزود لملف البائع الشخصي
/// يتبع Forever Plan: Flutter → Go API → Supabase
@riverpod
Future<SellerProfile?> sellerProfile(Ref ref, String sellerId) async {
  try {
    final apiClient = ref.watch(simpleApiClientProvider);
    final response = await apiClient.get('/api/v1/sellers/$sellerId/profile');

    if (response.isSuccess && response.data != null) {
      return SellerProfile.fromJson(response.data);
    }

    return null;
  } catch (e) {
    // التعامل مع الأخطاء
    return null;
  }
}
