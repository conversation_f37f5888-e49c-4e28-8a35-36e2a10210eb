import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:go_router/go_router.dart';

import '../providers/seller_profile_provider.dart';

/// A convenience helper for showing the seller profile bottom-sheet.
Future<void> showSellerProfileBottomSheet(
  BuildContext context, {
  required int sellerUserId,
}) async {
  return showModalBottomSheet<void>(
    context: context,
    isScrollControlled: true,
    useSafeArea: true,
    backgroundColor: Colors.transparent,
    builder: (_) => SellerProfileBottomSheet(sellerUserId: sellerUserId),
  );
}

/// Bottom-sheet that displays seller details similarly to eBay.
class SellerProfileBottomSheet extends HookConsumerWidget {
  const SellerProfileBottomSheet({super.key, required this.sellerUserId});

  final int sellerUserId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Convert sellerUserId to String because sellerProfileProvider expects a String ID
    final sellerAsync = ref.watch(
      sellerProfileProvider(sellerUserId.toString()),
    );

    return DraggableScrollableSheet(
      expand: false,
      initialChildSize: .8,
      minChildSize: .4,
      builder: (context, controller) {
        return DecoratedBox(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: ListView(
            controller: controller,
            padding: const EdgeInsets.symmetric(horizontal: 24),
            children: [
              const SizedBox(height: 12),
              const _SheetHandle(),
              const SizedBox(height: 16),
              Text(
                'معلومات البائع',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 24),
              sellerAsync.when(
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (e, st) => Padding(
                  padding: const EdgeInsets.all(16),
                  child: SelectableText.rich(
                    TextSpan(
                      text: 'خطأ في تحميل بيانات البائع: $e',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ),
                data: (seller) {
                  if (seller == null) {
                    return const _SellerHeader(
                      name: 'متجر غير مُسمّى',
                      ratingText: '100% رضا العملاء',
                      itemsCount: 0,
                      joinedDateString: '2000',
                      sellerUserId: 0,
                    );
                  }
                  
                  return _SellerHeader(
                    name: seller.storeName,
                    avatarUrl: seller.storeLogoUrl,
                    ratingText: '100% رضا العملاء',
                    itemsCount: 0, // Will be replaced with real count when API is ready
                    joinedDateString: '2000',
                    sellerUserId: sellerUserId,
                  );
                },
              ),
              const SizedBox(height: 32),
              FilledButton(
                onPressed: () => context.push('/seller/$sellerUserId/items'),
                child: const Text('منتجات البائع'),
              ),
              const SizedBox(height: 12),
              OutlinedButton(
                onPressed: () => context.push('/chat/$sellerUserId'),
                child: const Text('مُراسلة البائع'),
              ),
              const SizedBox(height: 32),
              const _DetailedRatingsSection(),
              const SizedBox(height: 32),
              Text(
                'تقييمات البائع (0)', // Will be replaced with dynamic count when API is ready
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 12),
              _FilterDropdown(onChanged: (v) {}),
              const SizedBox(height: 16),
              const _FeedbackSection(),
              const SizedBox(height: 24),
              FilledButton.tonal(
                onPressed: () => context.push('/seller/$sellerUserId/feedback'),
                style: FilledButton.styleFrom(
                  minimumSize: const Size.fromHeight(48),
                ),
                child: const Text('عرض جميع التقييمات'),
              ),
              const SizedBox(height: 32),
            ],
          ),
        );
      },
    );
  }
}

/// Small grey bar at the top of modal sheet.
class _SheetHandle extends StatelessWidget {
  const _SheetHandle();

  @override
  Widget build(BuildContext context) => Align(
    child: Container(
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: Colors.grey.shade600,
        borderRadius: BorderRadius.circular(2),
      ),
    ),
  );
}

class _SellerHeader extends StatefulWidget {
  const _SellerHeader({
    required this.name,
    this.avatarUrl,
    required this.ratingText,
    required this.itemsCount,
    required this.joinedDateString,
    required this.sellerUserId,
  });

  final String name;
  final String? avatarUrl;
  final String ratingText;
  final int itemsCount;
  final String joinedDateString;
  final int sellerUserId;

  @override
  State<_SellerHeader> createState() => _SellerHeaderState();
}

class _SellerHeaderState extends State<_SellerHeader> {
  bool _isFavorite = false;

  void _toggleFavorite() {
    setState(() {
      _isFavorite = !_isFavorite;
    });
    // TODO: Implement API call to update favorite status
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CircleAvatar(
          radius: 28,
          backgroundImage: widget.avatarUrl != null
              ? CachedNetworkImageProvider(widget.avatarUrl!)
              : const AssetImage('assets/images/placeholder.png')
                    as ImageProvider,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.name,
                style: Theme.of(context).textTheme.titleMedium,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(widget.ratingText, style: Theme.of(context).textTheme.labelMedium),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.calendar_today_outlined, size: 14),
                  const SizedBox(width: 4),
                  Text(
                    'منذ ${widget.joinedDateString}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: _toggleFavorite,
          icon: Icon(
            _isFavorite ? Icons.favorite : Icons.favorite_border,
            color: _isFavorite ? Colors.red : null,
          ),
        ),
      ],
    );
  }
}

class _FeedbackSection extends StatelessWidget {
  const _FeedbackSection();

  @override
  Widget build(BuildContext context) {
    // TODO: Replace with real feedback list using provider when API is ready
    final demoComments = List.generate(
      5,
      (index) => (
        buyer: '9***$index',
        comment: 'تجربة شراء رائعة وسرعة في التوصيل',
        product: 'قطعة غيار رقم $index',
      ),
    );

    return SizedBox(
      height: 140,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: demoComments.length,
        separatorBuilder: (_, index) => const SizedBox(width: 12),
        itemBuilder: (context, index) {
          final c = demoComments[index];
          return Container(
            width: 240,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).dividerColor),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.add, color: Colors.green.shade400, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      c.buyer,
                      style: Theme.of(context).textTheme.labelSmall,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  c.comment,
                  style: Theme.of(context).textTheme.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const Spacer(),
                Text(
                  c.product,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

/// Section that visualises the seller's detailed ratings (accuracy, shipping cost, speed, communication).
class _DetailedRatingsSection extends StatelessWidget {
  const _DetailedRatingsSection();

  @override
  Widget build(BuildContext context) {
    // Demo static values; later bind to provider.
    const ratings = [
      ('دقة الوصف', 5.0),
      ('تكلفة الشحن', 4.9),
      ('سرعة الشحن', 4.8),
      ('التواصل', 4.9),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تقييمات تفصيلية للبائع',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 4),
        Text(
          'متوسط آخر 12 شهرًا',
          style: Theme.of(context).textTheme.bodySmall,
        ),
        const SizedBox(height: 16),
        ...ratings.map((e) => _RatingRow(label: e.$1, value: e.$2)),
      ],
    );
  }
}

class _RatingRow extends StatelessWidget {
  const _RatingRow({required this.label, required this.value});

  final String label;
  final double value; // out of 5

  @override
  Widget build(BuildContext context) {
    final progress = value / 5;
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Expanded(
            flex: 3,
            child: LinearProgressIndicator(
              value: progress,
              minHeight: 6,
              borderRadius: BorderRadius.circular(3),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            value.toStringAsFixed(1),
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }
}

class _FilterDropdown extends StatelessWidget {
  const _FilterDropdown({required this.onChanged});

  final Function(dynamic) onChanged;

  @override
  Widget build(BuildContext context) {
    // TODO: Implement dropdown logic when API is ready
    return DropdownButton<String>(
      value: 'all',
      items: const [
        DropdownMenuItem(value: 'all', child: Text('جميع التقييمات')),
        DropdownMenuItem(value: 'positive', child: Text('إيجابية')),
        DropdownMenuItem(value: 'negative', child: Text('سلبية')),
        DropdownMenuItem(value: 'neutral', child: Text('محايدة')),
      ],
      onChanged: onChanged,
    );
  }
}
