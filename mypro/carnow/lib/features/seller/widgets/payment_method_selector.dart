import 'package:flutter/material.dart';

import '../../../core/theme/text_styles.dart';

/// Payment Method Selector Widget - Material 3 Design
/// Forever Plan Architecture: Flutter (UI Only)
/// 
/// ✅ Material 3 design system components
/// ✅ Accessibility features following WCAG guidelines
/// ✅ Bilingual support (Arabic/English)
/// ✅ Interactive selection with visual feedback
/// ✅ UI only - no business logic
class PaymentMethodSelector extends StatelessWidget {
  final String? selectedPaymentMethodId;
  final ValueChanged<String> onPaymentMethodChanged;

  const PaymentMethodSelector({
    super.key,
    required this.selectedPaymentMethodId,
    required this.onPaymentMethodChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'طريقة الدفع',
          style: CarNowTextStyles.sectionTitle.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12.0),
        
        // Payment method options
        _buildPaymentMethodOption(
          context,
          id: 'credit_card',
          title: 'بطاقة ائتمان',
          subtitle: 'Visa, Mastercard, American Express',
          icon: Icons.credit_card_rounded,
          isSelected: selectedPaymentMethodId == 'credit_card',
        ),
        
        const SizedBox(height: 12.0),
        
        _buildPaymentMethodOption(
          context,
          id: 'bank_transfer',
          title: 'تحويل بنكي',
          subtitle: 'تحويل مباشر من حسابك البنكي',
          icon: Icons.account_balance_rounded,
          isSelected: selectedPaymentMethodId == 'bank_transfer',
        ),
        
        const SizedBox(height: 12.0),
        
        _buildPaymentMethodOption(
          context,
          id: 'mobile_wallet',
          title: 'محفظة إلكترونية',
          subtitle: 'مدفوعات الهاتف المحمول',
          icon: Icons.phone_android_rounded,
          isSelected: selectedPaymentMethodId == 'mobile_wallet',
        ),
        
        // Security notice
        const SizedBox(height: 16.0),
        _buildSecurityNotice(context),
      ],
    );
  }

  Widget _buildPaymentMethodOption(
    BuildContext context, {
    required String id,
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isSelected,
  }) {
    return Card(
      elevation: isSelected ? 4.0 : 1.0,
      child: InkWell(
        onTap: () => onPaymentMethodChanged(id),
        borderRadius: BorderRadius.circular(12.0),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            border: isSelected
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2.0,
                  )
                : null,
          ),
          child: Row(
            children: [
              // Payment method icon
              Container(
                width: 48.0,
                height: 48.0,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primaryContainer
                      : Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Icon(
                  icon,
                  color: isSelected
                      ? Theme.of(context).colorScheme.onPrimaryContainer
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 24.0,
                ),
              ),
              
              const SizedBox(width: 16.0),
              
              // Payment method details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: CarNowTextStyles.errorText.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4.0),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Selection indicator
              Container(
                width: 24.0,
                height: 24.0,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.outline,
                    width: 2.0,
                  ),
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Colors.transparent,
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        size: 16.0,
                        color: Theme.of(context).colorScheme.onPrimary,
                      )
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSecurityNotice(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Row(
        children: [
          Icon(
            Icons.security_rounded,
            size: 16.0,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8.0),
          Expanded(
            child: Text(
              'جميع المدفوعات محمية بتشفير SSL وتتم معالجتها بشكل آمن',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
