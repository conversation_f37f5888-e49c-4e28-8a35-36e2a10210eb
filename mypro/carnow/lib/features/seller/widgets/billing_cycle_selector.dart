import 'package:flutter/material.dart';

import '../../../core/theme/text_styles.dart';
import '../models/subscription_model.dart';
import '../models/seller_enums.dart';

/// Billing Cycle Selector Widget - Material 3 Design
/// Forever Plan Architecture: Flutter (UI Only)
/// 
/// ✅ Material 3 design system components
/// ✅ Accessibility features following WCAG guidelines
/// ✅ Bilingual support (Arabic/English)
/// ✅ Interactive selection with visual feedback
/// ✅ UI only - no business logic
class BillingCycleSelector extends StatelessWidget {
  final BillingCycle? selectedCycle;
  final ValueChanged<BillingCycle> onCycleChanged;
  final SubscriptionPlan plan;

  const BillingCycleSelector({
    super.key,
    required this.selectedCycle,
    required this.onCycleChanged,
    required this.plan,
  });

  @override
  Widget build(BuildContext context) {
    // Don't show selector for free plans
    if (plan.tier == SubscriptionTier.starter) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'دورة الفوترة',
          style: CarNowTextStyles.sectionTitle.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12.0),
        
        // Billing cycle options
        Row(
          children: [
            Expanded(
              child: _buildCycleOption(
                context,
                cycle: BillingCycle.monthly,
                title: 'شهرية',
                subtitle: '${plan.monthlyPriceLD} د.ل / شهر',
                isSelected: selectedCycle == BillingCycle.monthly,
              ),
            ),
            const SizedBox(width: 12.0),
            Expanded(
              child: _buildCycleOption(
                context,
                cycle: BillingCycle.yearly,
                title: 'سنوية',
                subtitle: '${plan.yearlyPriceLD} د.ل / سنة',
                isSelected: selectedCycle == BillingCycle.yearly,
                badge: _calculateYearlyDiscount() > 0 ? 'وفر ${_calculateYearlyDiscount()}%' : null,
              ),
            ),
          ],
        ),
        
        // Additional information
        if (selectedCycle != null) ...[
          const SizedBox(height: 12.0),
          _buildAdditionalInfo(context),
        ],
      ],
    );
  }

  Widget _buildCycleOption(
    BuildContext context, {
    required BillingCycle cycle,
    required String title,
    required String subtitle,
    required bool isSelected,
    String? badge,
  }) {
    return Card(
      elevation: isSelected ? 4.0 : 1.0,
      child: InkWell(
        onTap: () => onCycleChanged(cycle),
        borderRadius: BorderRadius.circular(12.0),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            border: isSelected
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2.0,
                  )
                : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with selection indicator
              Row(
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: CarNowTextStyles.categoryTitle.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  
                  // Selection indicator
                  Container(
                    width: 20.0,
                    height: 20.0,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline,
                        width: 2.0,
                      ),
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Colors.transparent,
                    ),
                    child: isSelected
                        ? Icon(
                            Icons.check,
                            size: 12.0,
                            color: Theme.of(context).colorScheme.onPrimary,
                          )
                        : null,
                  ),
                ],
              ),
              
              const SizedBox(height: 8.0),
              
              // Price
              Text(
                subtitle,
                style: CarNowTextStyles.generalMessage.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              
              // Discount badge
              if (badge != null) ...[
                const SizedBox(height: 8.0),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8.0,
                    vertical: 4.0,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.errorContainer,
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Text(
                    badge,
                    style: CarNowTextStyles.errorText.copyWith(
                      color: Theme.of(context).colorScheme.onErrorContainer,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdditionalInfo(BuildContext context) {
    final isYearly = selectedCycle == BillingCycle.yearly;
    
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline_rounded,
            size: 16.0,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 8.0),
          Expanded(
            child: Text(
              isYearly
                  ? 'سيتم خصم المبلغ كاملاً مقدماً. يمكنك الإلغاء في أي وقت.'
                  : 'سيتم تجديد الاشتراك تلقائياً كل شهر. يمكنك الإلغاء في أي وقت.',
              style: CarNowTextStyles.hintText.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  int _calculateYearlyDiscount() {
    if (plan.tier == SubscriptionTier.starter) return 0;
    
    final monthlyTotal = plan.monthlyPriceLD * 12;
    final yearlyPrice = plan.yearlyPriceLD;
    
    if (yearlyPrice >= monthlyTotal) return 0;
    
    final discount = ((monthlyTotal - yearlyPrice) / monthlyTotal * 100).round();
    return discount;
  }
}
