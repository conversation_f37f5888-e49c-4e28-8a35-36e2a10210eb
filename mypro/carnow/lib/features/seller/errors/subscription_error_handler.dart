import 'package:flutter/material.dart';
import 'package:logging/logging.dart';

import '../../../core/errors/app_error.dart';
import '../navigation/subscription_navigation_manager.dart';

/// Subscription Error Types - Union Pattern
abstract class SubscriptionError {
  final String message;
  final String messageAr;
  final Object? originalError;
  final DateTime timestamp;

  SubscriptionError({
    required this.message,
    required this.messageAr,
    this.originalError,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  @override
  String toString() {
    return 'SubscriptionError: $messageAr ($message)';
  }
}

class SubscriptionNetworkError extends SubscriptionError {
  final int? statusCode;
  final String? endpoint;

  SubscriptionNetworkError({
    required String message,
    required String messageAr,
    this.statusCode,
    this.endpoint,
    Object? originalError,
    DateTime? timestamp,
  }) : super(
          message: message,
          messageAr: messageAr,
          originalError: originalError,
          timestamp: timestamp,
        );
}

class SubscriptionDatabaseError extends SubscriptionError {
  final String? operation;

  SubscriptionDatabaseError({
    required String message,
    required String messageAr,
    this.operation,
    Object? originalError,
    DateTime? timestamp,
  }) : super(
          message: message,
          messageAr: messageAr,
          originalError: originalError,
          timestamp: timestamp,
        );
}

class SubscriptionValidationError extends SubscriptionError {
  final String? field;
  final String? value;

  SubscriptionValidationError({
    required String message,
    required String messageAr,
    this.field,
    this.value,
    Object? originalError,
    DateTime? timestamp,
  }) : super(
          message: message,
          messageAr: messageAr,
          originalError: originalError,
          timestamp: timestamp,
        );
}

class SubscriptionAuthenticationError extends SubscriptionError {
  SubscriptionAuthenticationError({
    required String message,
    required String messageAr,
    Object? originalError,
    DateTime? timestamp,
  }) : super(
          message: message,
          messageAr: messageAr,
          originalError: originalError,
          timestamp: timestamp,
        );
}

class SubscriptionPaymentError extends SubscriptionError {
  final String? paymentMethod;
  final String? errorCode;

  SubscriptionPaymentError({
    required String message,
    required String messageAr,
    this.paymentMethod,
    this.errorCode,
    Object? originalError,
    DateTime? timestamp,
  }) : super(
          message: message,
          messageAr: messageAr,
          originalError: originalError,
          timestamp: timestamp,
        );
}

class SubscriptionPlanError extends SubscriptionError {
  final String? planId;

  SubscriptionPlanError({
    required String message,
    required String messageAr,
    this.planId,
    Object? originalError,
    DateTime? timestamp,
  }) : super(
          message: message,
          messageAr: messageAr,
          originalError: originalError,
          timestamp: timestamp,
        );
}

/// Subscription Error Handler - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// ✅ Provides user-friendly error dialogs in Arabic
/// ✅ Handles all subscription error types
/// ✅ Implements retry mechanisms where appropriate
/// ✅ Logs errors with detailed context

class SubscriptionErrorHandler {
  static final Logger _logger = Logger('SubscriptionErrorHandler');

  /// Handle subscription errors with user-friendly dialogs
  static Future<bool> handleSubscriptionError({
    required BuildContext context,
    required SubscriptionError error,
    VoidCallback? onRetry,
  }) async {
    _logger.severe(
      'Handling subscription error: ${error.message}',
      error.originalError,
    );

    // Log error with detailed context
    _logError(error);

    // Show user-friendly error dialog
    return await _showErrorDialog(
      context: context,
      error: error,
      onRetry: onRetry,
    );
  }

  /// Handle AppError from subscription service
  static Future<bool> handleAppError({
    required BuildContext context,
    required AppError error,
    VoidCallback? onRetry,
  }) async {
    _logger.severe(
      'Handling AppError: ${error.message}',
      error.originalError,
    );

    // Convert AppError to SubscriptionError
    final subscriptionError = _convertAppError(error);
    
    // Log error with detailed context
    _logError(subscriptionError);

    // Show user-friendly error dialog
    return await _showErrorDialog(
      context: context,
      error: subscriptionError,
      onRetry: onRetry,
    );
  }

  /// Convert AppError to SubscriptionError
  static SubscriptionError _convertAppError(AppError error) {
    switch (error.type) {
      case AppErrorType.network:
        return SubscriptionNetworkError(
          message: error.message,
          messageAr: error.userMessage,
          statusCode: error.data != null && error.data!['statusCode'] is int 
              ? error.data!['statusCode'] as int 
              : null,
          endpoint: error.data != null && error.data!['endpoint'] is String 
              ? error.data!['endpoint'] as String 
              : null,
          originalError: error.originalError,
          timestamp: DateTime.now(),
        );
      case AppErrorType.database:
        return SubscriptionDatabaseError(
          message: error.message,
          messageAr: error.userMessage,
          operation: error.data != null && error.data!['operation'] is String 
              ? error.data!['operation'] as String 
              : null,
          originalError: error.originalError,
          timestamp: DateTime.now(),
        );
      case AppErrorType.validation:
        return SubscriptionValidationError(
          message: error.message,
          messageAr: error.userMessage,
          field: error.data != null && error.data!['field'] is String 
              ? error.data!['field'] as String 
              : null,
          value: error.data != null && error.data!['value'] != null 
              ? error.data!['value'].toString() 
              : null,
          originalError: error.originalError,
          timestamp: DateTime.now(),
        );
      case AppErrorType.authentication:
        return SubscriptionAuthenticationError(
          message: error.message,
          messageAr: error.userMessage,
          originalError: error.originalError,
          timestamp: DateTime.now(),
        );
      default:
        return SubscriptionValidationError(
          message: error.message,
          messageAr: error.userMessage,
          originalError: error.originalError,
          timestamp: DateTime.now(),
        );
    }
  }

  /// Log errors with detailed context
  static void _logError(SubscriptionError error) {
    _logger.severe(
      'Subscription Error Details:',
      {
        'type': error.runtimeType.toString(),
        'message': error.message,
        'messageAr': error.messageAr,
        'timestamp': error.timestamp.toIso8601String(),
        'originalError': error.originalError?.toString(),
        if (error is SubscriptionNetworkError) 'statusCode': error.statusCode,
        if (error is SubscriptionNetworkError) 'endpoint': error.endpoint,
        if (error is SubscriptionDatabaseError) 'operation': error.operation,
        if (error is SubscriptionValidationError) 'field': error.field,
        if (error is SubscriptionValidationError) 'value': error.value,
        if (error is SubscriptionPaymentError) 'paymentMethod': error.paymentMethod,
        if (error is SubscriptionPaymentError) 'errorCode': error.errorCode,
        if (error is SubscriptionPlanError) 'planId': error.planId,
      },
    );
  }

  /// Show user-friendly error dialog
  static Future<bool> _showErrorDialog({
    required BuildContext context,
    required SubscriptionError error,
    VoidCallback? onRetry,
  }) async {
    if (!context.mounted) return false;

    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          title: const Text('خطأ في الاشتراك', style: TextStyle(fontWeight: FontWeight.bold)),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  error.messageAr,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                Text(
                  'الوقت: ${error.timestamp.toLocal().toString().split('.')[0]}',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
                if (error is SubscriptionNetworkError && error.statusCode != null)
                  Text(
                    'رمز الخطأ: ${error.statusCode}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
              ],
            ),
          ),
          actions: <Widget>[
            if (onRetry != null)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(true); // Return true for retry
                },
                child: const Text('إعادة المحاولة'),
              ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(false); // Return false for cancel
              },
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// Handle navigation errors specifically
  static Future<void> handleNavigationError({
    required BuildContext context,
    required SubscriptionNavigationError error,
  }) async {
    _logger.severe(
      'Handling navigation error: ${error.message}',
      error.originalError,
    );

    // Log navigation error
    _logNavigationError(error);

    // Show navigation error dialog
    if (!context.mounted) return;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ في التنقل'),
        content: Text(error.message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// Log navigation errors
  static void _logNavigationError(SubscriptionNavigationError error) {
    _logger.severe(
      'Navigation Error Details:',
      {
        'message': error.message,
        'attemptedRoute': error.attemptedRoute,
        'timestamp': DateTime.now().toIso8601String(),
        'originalError': error.originalError?.toString(),
      },
    );
  }
}
