import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../../../core/repositories/base_repository.dart';
import '../models/seller_profile_model.dart';

part 'seller_repository.g.dart';

final _logger = Logger('SellerRepository');

/// Seller Repository - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// ✅ Uses SimpleApiClient ONLY
/// ✅ ALL business logic in Go backend
/// ✅ NO direct Supabase calls
class SellerRepository extends BaseRepository {
  SellerRepository(super.ref);

  @override
  String get apiPath => '/seller';

  /// Get seller profile for current user from Go backend
  Future<SellerProfile?> getSellerProfile() async {
    try {
      _logger.info('Fetching seller profile from Go backend');

      final response = await apiClient.getApi<Map<String, dynamic>>(apiPath);

      if (!response.isSuccess || response.data == null) {
        _logger.info('No seller profile found');
        return null;
      }

      final sellerProfile = SellerProfile.fromJson(response.data!);
      _logger.info('Seller profile fetched successfully');
      return sellerProfile;
    } catch (e) {
      _logger.severe('Error fetching seller profile: $e');
      return null;
    }
  }

  /// Create or update seller profile through Go backend
  Future<SellerProfile?> upsertSellerProfile(SellerProfile profile) async {
    try {
      _logger.info('Upserting seller profile through Go backend');

      // Use POST for create, PUT for update
      final response = profile.id == null
          ? await apiClient.postApi<Map<String, dynamic>>(
              '$apiPath/request',
              data: profile.toJson(),
            )
          : await apiClient.putApi<Map<String, dynamic>>(
              apiPath,
              data: profile.toJson(),
            );

      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to upsert seller profile');
        return null;
      }

      final updatedProfile = SellerProfile.fromJson(response.data!);
      _logger.info('Seller profile upserted successfully');
      return updatedProfile;
    } catch (e) {
      _logger.severe('Error upserting seller profile: $e');
      return null;
    }
  }

  /// Upload seller documents through Go backend
  Future<bool> uploadSellerDocument({
    required String documentType,
    required String filePath,
  }) async {
    try {
      _logger.info('Uploading seller document: $documentType');

      final response = await apiClient.postApi<Map<String, dynamic>>(
        '$apiPath/documents',
        data: {
          'document_type': documentType,
          'file_path': filePath,
        },
      );

      final success = response.isSuccess;
      _logger.info('Document upload result: $success');
      return success;
    } catch (e) {
      _logger.severe('Error uploading seller document: $e');
      return false;
    }
  }

  /// Get seller documents from Go backend
  Future<List<Map<String, dynamic>>> getSellerDocuments() async {
    try {
      _logger.info('Fetching seller documents from Go backend');

      final response = await apiClient.getApi<List<dynamic>>(
        '$apiPath/documents',
      );

      if (!response.isSuccess || response.data == null) {
        _logger.info('No seller documents found');
        return [];
      }

      final documents = response.data!
          .cast<Map<String, dynamic>>()
          .toList();
      
      _logger.info('Found ${documents.length} seller documents');
      return documents;
    } catch (e) {
      _logger.severe('Error fetching seller documents: $e');
      return [];
    }
  }
}

/// Provider for seller repository - Forever Plan compliant
@riverpod
SellerRepository sellerRepository(Ref ref) => SellerRepository(ref);
