// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_api_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sellerApiServiceHash() => r'3a1228c2530e445bbc78636de5e65da4ca77518f';

/// Provider for SellerApiService
///
/// Copied from [sellerApiService].
@ProviderFor(sellerApiService)
final sellerApiServiceProvider = AutoDisposeProvider<SellerApiService>.internal(
  sellerApiService,
  name: r'sellerApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sellerApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SellerApiServiceRef = AutoDisposeProviderRef<SellerApiService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
