// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$subscriptionServiceHash() =>
    r'bab3badc46031a5d948333735bbd08f33314620d';

/// Provider for SubscriptionService
///
/// Copied from [subscriptionService].
@ProviderFor(subscriptionService)
final subscriptionServiceProvider =
    AutoDisposeProvider<SubscriptionService>.internal(
      subscriptionService,
      name: r'subscriptionServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionServiceRef = AutoDisposeProviderRef<SubscriptionService>;
String _$subscriptionServiceImplHash() =>
    r'0198435918cbc4d2bfd212bd33b863aea6854f4d';

/// Concrete implementation of SubscriptionService using core models exclusively
/// Uses SimpleApiClient for all backend communication
/// Implements retry logic with exponential backoff
/// Provides comprehensive logging for all operations
///
/// Copied from [SubscriptionServiceImpl].
@ProviderFor(SubscriptionServiceImpl)
final subscriptionServiceImplProvider =
    NotifierProvider<SubscriptionServiceImpl, SubscriptionService>.internal(
      SubscriptionServiceImpl.new,
      name: r'subscriptionServiceImplProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionServiceImplHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SubscriptionServiceImpl = Notifier<SubscriptionService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
