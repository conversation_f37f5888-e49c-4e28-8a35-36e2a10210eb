// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notifications_api_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$notificationsApiServiceHash() =>
    r'b9e6393d883d307bfe04af13ce4acf72ac46a774';

/// See also [notificationsApiService].
@ProviderFor(notificationsApiService)
final notificationsApiServiceProvider =
    AutoDisposeProvider<NotificationsApiService>.internal(
      notificationsApiService,
      name: r'notificationsApiServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationsApiServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NotificationsApiServiceRef =
    AutoDisposeProviderRef<NotificationsApiService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
