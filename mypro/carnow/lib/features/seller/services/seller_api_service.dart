import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/config/backend_config.dart';
import '../../../core/networking/simple_api_client.dart';
import '../models/seller_application_model.dart';
import '../models/seller_store_model.dart';

part 'seller_api_service.g.dart';

/// Service for managing seller operations via Go backend API
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
class SellerApiService {
  final SimpleApiClient _apiClient;

  SellerApiService(this._apiClient);

  /// Get current user's seller application
  Future<SellerApplicationModel?> getCurrentUserApplication() async {
    try {
      final response = await _apiClient.getApi(
        BackendConfig.sellerApplicationsEndpoint,
      );
      
      if (response.data == null) return null;
      
      return SellerApplicationModel.fromJson(response.data);
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        return null; // No application found
      }
      rethrow;
    }
  }

  /// Submit a new seller application
  Future<SellerApplicationModel> submitApplication(
    Map<String, dynamic> applicationData,
  ) async {
    try {
      final response = await _apiClient.postApi(
        BackendConfig.sellerApplicationsEndpoint,
        data: applicationData,
      );
      
      return SellerApplicationModel.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to submit seller application: $e');
    }
  }

  /// Get seller application logs
  Future<List<Map<String, dynamic>>> getApplicationLogs(
    String applicationId,
  ) async {
    try {
      final response = await _apiClient.getApi(
        '${BackendConfig.sellerApplicationsEndpoint}/$applicationId/logs',
      );
      
      return List<Map<String, dynamic>>.from(response.data ?? []);
    } catch (e) {
      throw Exception('Failed to get application logs: $e');
    }
  }

  /// Check if user can apply as seller
  Future<bool> canApplyAsSeller() async {
    try {
      final response = await _apiClient.getApi(
        '${BackendConfig.sellerApplicationsEndpoint}/can-apply',
      );
      
      return response.data['can_apply'] ?? false;
    } catch (e) {
      throw Exception('Failed to check seller eligibility: $e');
    }
  }

  /// Get seller store by seller ID
  Future<SellerStoreModel?> getSellerStore(int sellerId) async {
    try {
      final response = await _apiClient.getApi(
        '${BackendConfig.sellersEndpoint}/$sellerId/store',
      );
      
      if (response.data == null) return null;
      
      return SellerStoreModel.fromJson(response.data);
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        return null; // No store found
      }
      rethrow;
    }
  }

  /// Create a new seller store
  Future<SellerStoreModel> createSellerStore(
    SellerStoreModel store,
  ) async {
    try {
      final response = await _apiClient.postApi(
        '${BackendConfig.sellersEndpoint}/store',
        data: store.toJson(),
      );
      
      return SellerStoreModel.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to create seller store: $e');
    }
  }

  /// Update seller store
  Future<SellerStoreModel> updateSellerStore(
    int sellerId,
    SellerStoreModel store,
  ) async {
    try {
      final response = await _apiClient.putApi(
        '${BackendConfig.sellersEndpoint}/$sellerId/store',
        data: store.toJson(),
      );
      
      return SellerStoreModel.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to update seller store: $e');
    }
  }
}

/// Provider for SellerApiService
@riverpod
SellerApiService sellerApiService(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return SellerApiService(apiClient);
}