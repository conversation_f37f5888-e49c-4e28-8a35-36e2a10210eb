import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/config/backend_config.dart';
import '../../../core/networking/simple_api_client.dart';
import '../models/seller_notification_model.dart';

part 'notifications_api_service.g.dart';

/// API service for handling seller notifications
class NotificationsApiService {
  NotificationsApiService(this._apiClient);

  final SimpleApiClient _apiClient;

  /// Get notifications for a seller
  Future<List<SellerNotification>> getNotifications(String sellerId) async {
    try {
      final response = await _apiClient.get(
        CarnowBackendConfig.buildUrlSync('/sellers/$sellerId/notifications'),
      );
      
      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final List<dynamic> data = responseData['data'] ?? [];
        return data.map((json) => SellerNotification.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load notifications: ${response.error}');
      }
    } catch (e) {
      throw Exception('Error fetching notifications: $e');
    }
  }

  /// Mark a notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      final response = await _apiClient.patch(
        CarnowBackendConfig.buildUrlSync('/notifications/$notificationId/read'),
      );
      
      if (!response.isSuccess) {
        throw Exception('Failed to mark notification as read: ${response.error}');
      }
    } catch (e) {
      throw Exception('Error marking notification as read: $e');
    }
  }

  /// Mark all notifications as read for a seller
  Future<void> markAllAsRead(String sellerId) async {
    try {
      final response = await _apiClient.patch(
        CarnowBackendConfig.buildUrlSync('/sellers/$sellerId/notifications/read-all'),
      );
      
      if (!response.isSuccess) {
        throw Exception('Failed to mark all notifications as read: ${response.error}');
      }
    } catch (e) {
      throw Exception('Error marking all notifications as read: $e');
    }
  }

  /// Delete a notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      final response = await _apiClient.delete(
        CarnowBackendConfig.buildUrlSync('/notifications/$notificationId'),
      );
      
      if (!response.isSuccess) {
        throw Exception('Failed to delete notification: ${response.error}');
      }
    } catch (e) {
      throw Exception('Error deleting notification: $e');
    }
  }

  /// Get count of unread notifications for a seller
  Future<int> getUnreadCount(String sellerId) async {
    try {
      final response = await _apiClient.get(
        CarnowBackendConfig.buildUrlSync('/sellers/$sellerId/notifications/unread-count'),
      );
      
      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        return responseData['count'] ?? 0;
      } else {
        throw Exception('Failed to get unread count: ${response.error}');
      }
    } catch (e) {
      throw Exception('Error getting unread count: $e');
    }
  }
}

@riverpod
NotificationsApiService notificationsApiService(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return NotificationsApiService(apiClient);
}