import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/config/backend_config.dart';
import '../../../core/providers/dio_provider.dart';

part 'subscription_analytics_api_service.g.dart';

/// API service for subscription analytics
class SubscriptionAnalyticsApiService {
  SubscriptionAnalyticsApiService(this._dio);

  final Dio _dio;

  /// Track subscription plan view event
  Future<void> trackPlanViewEvent({
    String? currentPlan,
    String? billingCycle,
    Map<String, dynamic>? context,
  }) async {
    await _dio.post(
      BackendConfig.buildUrl('/analytics/subscription/plan-view'),
      data: {
        'current_plan': currentPlan,
        'billing_cycle': billingCycle,
        'context': context,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track plan selection/change event
  Future<void> trackPlanSelectionEvent({
    required String selectedPlan,
    String? previousPlan,
    required String billingCycle,
    required double price,
    Map<String, dynamic>? context,
  }) async {
    await _dio.post(
      BackendConfig.buildUrl('/analytics/subscription/plan-selection'),
      data: {
        'selected_plan': selectedPlan,
        'previous_plan': previousPlan,
        'billing_cycle': billingCycle,
        'price': price,
        'context': context,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track subscription upgrade event
  Future<void> trackSubscriptionUpgradeEvent({
    required String fromPlan,
    required String toPlan,
    required double priceDifference,
    Map<String, dynamic>? context,
  }) async {
    await _dio.post(
      BackendConfig.buildUrl('/analytics/subscription/upgrade'),
      data: {
        'from_plan': fromPlan,
        'to_plan': toPlan,
        'price_difference': priceDifference,
        'context': context,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track subscription cancellation event
  Future<void> trackSubscriptionCancellationEvent({
    required String plan,
    required String reason,
    Map<String, dynamic>? context,
  }) async {
    await _dio.post(
      BackendConfig.buildUrl('/analytics/subscription/cancellation'),
      data: {
        'plan': plan,
        'reason': reason,
        'context': context,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track billing event
  Future<void> trackBillingEvent({
    required String eventType,
    required double amount,
    required String currency,
    String? paymentMethod,
    Map<String, dynamic>? context,
  }) async {
    await _dio.post(
      BackendConfig.buildUrl('/analytics/subscription/billing'),
      data: {
        'event_type': eventType,
        'amount': amount,
        'currency': currency,
        'payment_method': paymentMethod,
        'context': context,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Get subscription analytics summary
  Future<Map<String, dynamic>> getSubscriptionAnalyticsSummary() async {
    final response = await _dio.get(
      BackendConfig.buildUrl('/analytics/subscription/summary'),
    );
    return response.data as Map<String, dynamic>;
  }
}

@riverpod
SubscriptionAnalyticsApiService subscriptionAnalyticsApiService(
  SubscriptionAnalyticsApiServiceRef ref,
) {
  final dio = ref.watch(dioProvider);
  return SubscriptionAnalyticsApiService(dio);
}