import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/config/backend_config.dart';
import '../../../core/networking/simple_api_client.dart';
import '../models/seller_enums.dart';

part 'inventory_api_service.g.dart';

/// API service for handling product inventory integration
class InventoryApiService {
  InventoryApiService(this._apiClient);

  final SimpleApiClient _apiClient;

  /// Link a new product to inventory automatically
  Future<void> linkProductToInventory({
    required String sellerId,
    required String productId,
    required String productName,
    required int initialStock,
    required int minStockLevel,
    required int reorderPoint,
    required double costPrice,
    required double sellingPrice,
  }) async {
    try {
      final data = {
        'product_id': productId,
        'product_name': productName,
        'current_stock': initialStock,
        'reserved_stock': 0,
        'min_stock_level': minStockLevel,
        'reorder_point': reorderPoint,
        'cost_price': costPrice,
        'selling_price': sellingPrice,
        'status': InventoryStatus.active.name,
      };

      final response = await _apiClient.post(
        CarnowBackendConfig.buildUrl('/sellers/$sellerId/inventory'),
        data: data,
      );
      
      if (!response.isSuccess) {
        throw Exception('Failed to link product to inventory: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to link product to inventory: $e');
    }
  }

  /// Process a sale and update inventory
  Future<void> processSale({
    required String sellerId,
    required String productId,
    required int quantitySold,
    required String orderId,
    String? notes,
  }) async {
    try {
      final data = {
        'product_id': productId,
        'quantity_sold': quantitySold,
        'order_id': orderId,
        'notes': notes ?? 'Sale transaction',
      };

      final response = await _apiClient.post(
        CarnowBackendConfig.buildUrl('/sellers/$sellerId/inventory/process-sale'),
        data: data,
      );
      
      if (!response.isSuccess) {
        throw Exception('Failed to process sale: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to process sale: $e');
    }
  }

  /// Restock inventory
  Future<void> restockInventory({
    required String sellerId,
    required String productId,
    required int quantity,
    required double unitCost,
    String? supplier,
    String? notes,
  }) async {
    try {
      final data = {
        'product_id': productId,
        'quantity': quantity,
        'unit_cost': unitCost,
        'supplier': supplier ?? 'Unknown',
        'notes': notes ?? 'Restock transaction',
      };

      final response = await _apiClient.post(
        CarnowBackendConfig.buildUrl('/sellers/$sellerId/inventory/restock'),
        data: data,
      );
      
      if (!response.isSuccess) {
        throw Exception('Failed to restock inventory: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to restock inventory: $e');
    }
  }

  /// Get inventory status for all products
  Future<List<Map<String, dynamic>>> getInventoryStatus(String sellerId) async {
    try {
      final response = await _apiClient.get(
        CarnowBackendConfig.buildUrl('/sellers/$sellerId/inventory'),
      );
      
      if (response.isSuccess && response.data != null) {
        return List<Map<String, dynamic>>.from(response.data);
      } else {
        throw Exception('Failed to get inventory status: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to get inventory status: $e');
    }
  }

  /// Get inventory details for specific product
  Future<Map<String, dynamic>> getProductInventory({
    required String sellerId,
    required String productId,
  }) async {
    try {
      final response = await _apiClient.get(
        CarnowBackendConfig.buildUrl('/sellers/$sellerId/inventory/$productId'),
      );
      
      if (response.isSuccess && response.data != null) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('Failed to get product inventory: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to get product inventory: $e');
    }
  }

  /// Update inventory settings
  Future<void> updateInventorySettings({
    required String sellerId,
    required String productId,
    int? minStockLevel,
    int? reorderPoint,
    double? costPrice,
    double? sellingPrice,
    InventoryStatus? status,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      if (minStockLevel != null) data['min_stock_level'] = minStockLevel;
      if (reorderPoint != null) data['reorder_point'] = reorderPoint;
      if (costPrice != null) data['cost_price'] = costPrice;
      if (sellingPrice != null) data['selling_price'] = sellingPrice;
      if (status != null) data['status'] = status.name;

      final response = await _apiClient.put(
        CarnowBackendConfig.buildUrl('/sellers/$sellerId/inventory/$productId'),
        data: data,
      );
      
      if (!response.isSuccess) {
        throw Exception('Failed to update inventory settings: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to update inventory settings: $e');
    }
  }

  /// Get low stock alerts
  Future<List<Map<String, dynamic>>> getLowStockAlerts(String sellerId) async {
    try {
      final response = await _apiClient.get(
        CarnowBackendConfig.buildUrl('/sellers/$sellerId/inventory/low-stock'),
      );
      
      if (response.isSuccess && response.data != null) {
        return List<Map<String, dynamic>>.from(response.data);
      } else {
        throw Exception('Failed to get low stock alerts: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to get low stock alerts: $e');
    }
  }

  /// Manual stock adjustment
  Future<void> adjustStock({
    required String sellerId,
    required String productId,
    required int adjustment,
    required String reason,
  }) async {
    try {
      final data = {
        'adjustment': adjustment,
        'reason': reason,
      };

      final response = await _apiClient.post(
        CarnowBackendConfig.buildUrl('/sellers/$sellerId/inventory/$productId/adjust'),
        data: data,
      );
      
      if (!response.isSuccess) {
        throw Exception('Failed to adjust stock: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to adjust stock: $e');
    }
  }

  /// Get inventory transaction history
  Future<List<Map<String, dynamic>>> getInventoryHistory({
    required String sellerId,
    String? productId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (productId != null) queryParams['product_id'] = productId;
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String();
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String();

      final response = await _apiClient.get(
        CarnowBackendConfig.buildUrl('/sellers/$sellerId/inventory/history'),
        queryParameters: queryParams,
      );
      
      if (response.isSuccess && response.data != null) {
        return List<Map<String, dynamic>>.from(response.data);
      } else {
        throw Exception('Failed to get inventory history: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to get inventory history: $e');
    }
  }

  /// Reserve stock for pending orders
  Future<void> reserveStock({
    required String sellerId,
    required String productId,
    required int quantity,
    required String orderId,
  }) async {
    try {
      final data = {
        'quantity': quantity,
        'order_id': orderId,
      };

      final response = await _apiClient.post(
        CarnowBackendConfig.buildUrl('/sellers/$sellerId/inventory/$productId/reserve'),
        data: data,
      );
      
      if (!response.isSuccess) {
        throw Exception('Failed to reserve stock: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to reserve stock: $e');
    }
  }

  /// Release reserved stock
  Future<void> releaseReservedStock({
    required String sellerId,
    required String productId,
    required int quantity,
    required String orderId,
  }) async {
    try {
      final data = {
        'quantity': quantity,
        'order_id': orderId,
      };

      final response = await _apiClient.post(
        CarnowBackendConfig.buildUrl('/sellers/$sellerId/inventory/$productId/release'),
        data: data,
      );
      
      if (!response.isSuccess) {
        throw Exception('Failed to release reserved stock: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to release reserved stock: $e');
    }
  }
}

@riverpod
InventoryApiService inventoryApiService(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return InventoryApiService(apiClient);
}