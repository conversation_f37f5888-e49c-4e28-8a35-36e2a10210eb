import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/config/backend_config.dart';
import '../../../core/networking/simple_api_client.dart';
import '../models/sales_analytics_models.dart';

part 'sales_analytics_api_service.g.dart';

/// Service for handling sales analytics API calls to the Go backend
class SalesAnalyticsApiService {
  final SimpleApiClient _apiClient;

  SalesAnalyticsApiService(this._apiClient);

  /// Get sales analytics data
  Future<List<SalesAnalyticsPoint>> getSalesAnalytics({
    required DateTime startDate,
    required DateTime endDate,
    required String grouping,
  }) async {
    try {
      final response = await _apiClient.get(
        BackendConfig.buildUrl('/sellers/analytics/sales'),
        queryParameters: {
          'start_date': startDate.toIso8601String(),
          'end_date': endDate.toIso8601String(),
          'grouping': grouping,
        },
      );

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final data = responseData['data'] as List<dynamic>? ?? [];
        return data
            .map((item) => SalesAnalyticsPoint.fromJson(item as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Failed to fetch sales analytics: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to fetch sales analytics: $e');
    }
  }

  /// Get product performance data
  Future<List<ProductPerformance>> getProductPerformance({
    required DateTime startDate,
    required DateTime endDate,
    int limit = 10,
  }) async {
    try {
      final response = await _apiClient.get(
        BackendConfig.buildUrl('/sellers/analytics/products'),
        queryParameters: {
          'start_date': startDate.toIso8601String(),
          'end_date': endDate.toIso8601String(),
          'limit': limit,
        },
      );

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final data = responseData['data'] as List<dynamic>? ?? [];
        return data
            .map((item) => ProductPerformance.fromJson(item as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Failed to fetch product performance: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to fetch product performance: $e');
    }
  }

  /// Get customer insights data
  Future<List<CustomerInsight>> getCustomerInsights({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final response = await _apiClient.get(
        BackendConfig.buildUrl('/sellers/analytics/customers'),
        queryParameters: {
          'start_date': startDate.toIso8601String(),
          'end_date': endDate.toIso8601String(),
        },
      );

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final data = responseData['data'] as List<dynamic>? ?? [];
        return data
            .map((item) => CustomerInsight.fromJson(item as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Failed to fetch customer insights: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to fetch customer insights: $e');
    }
  }
}

@riverpod
SalesAnalyticsApiService salesAnalyticsApiService(Ref ref) {
  final apiClient = ref.read(simpleApiClientProvider);
  return SalesAnalyticsApiService(apiClient);
}