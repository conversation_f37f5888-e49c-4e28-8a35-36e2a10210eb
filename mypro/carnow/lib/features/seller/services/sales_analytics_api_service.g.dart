// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sales_analytics_api_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$salesAnalyticsApiServiceHash() =>
    r'a043a140f01f22b473e2065975e68a3ff7224927';

/// See also [salesAnalyticsApiService].
@ProviderFor(salesAnalyticsApiService)
final salesAnalyticsApiServiceProvider =
    AutoDisposeProvider<SalesAnalyticsApiService>.internal(
      salesAnalyticsApiService,
      name: r'salesAnalyticsApiServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$salesAnalyticsApiServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SalesAnalyticsApiServiceRef =
    AutoDisposeProviderRef<SalesAnalyticsApiService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
