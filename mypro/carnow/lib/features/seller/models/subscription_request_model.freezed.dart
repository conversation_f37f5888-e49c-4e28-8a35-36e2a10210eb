// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'subscription_request_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SellerSubscriptionRequest {

 String get id; String get sellerId; String get planId; String get requestedTier; String get billingCycle; String get status; DateTime get requestDate; DateTime? get approvedDate; DateTime? get rejectedDate; String? get adminId; String? get adminNotes; String? get rejectionReason; double get requestedPriceLD; String? get paymentMethodId; Map<String, dynamic> get sellerInfo; Map<String, dynamic> get businessDocuments; bool get requiresDocumentVerification; int? get priority;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of SellerSubscriptionRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerSubscriptionRequestCopyWith<SellerSubscriptionRequest> get copyWith => _$SellerSubscriptionRequestCopyWithImpl<SellerSubscriptionRequest>(this as SellerSubscriptionRequest, _$identity);

  /// Serializes this SellerSubscriptionRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerSubscriptionRequest&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.planId, planId) || other.planId == planId)&&(identical(other.requestedTier, requestedTier) || other.requestedTier == requestedTier)&&(identical(other.billingCycle, billingCycle) || other.billingCycle == billingCycle)&&(identical(other.status, status) || other.status == status)&&(identical(other.requestDate, requestDate) || other.requestDate == requestDate)&&(identical(other.approvedDate, approvedDate) || other.approvedDate == approvedDate)&&(identical(other.rejectedDate, rejectedDate) || other.rejectedDate == rejectedDate)&&(identical(other.adminId, adminId) || other.adminId == adminId)&&(identical(other.adminNotes, adminNotes) || other.adminNotes == adminNotes)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.requestedPriceLD, requestedPriceLD) || other.requestedPriceLD == requestedPriceLD)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&const DeepCollectionEquality().equals(other.sellerInfo, sellerInfo)&&const DeepCollectionEquality().equals(other.businessDocuments, businessDocuments)&&(identical(other.requiresDocumentVerification, requiresDocumentVerification) || other.requiresDocumentVerification == requiresDocumentVerification)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,sellerId,planId,requestedTier,billingCycle,status,requestDate,approvedDate,rejectedDate,adminId,adminNotes,rejectionReason,requestedPriceLD,paymentMethodId,const DeepCollectionEquality().hash(sellerInfo),const DeepCollectionEquality().hash(businessDocuments),requiresDocumentVerification,priority,createdAt,updatedAt]);

@override
String toString() {
  return 'SellerSubscriptionRequest(id: $id, sellerId: $sellerId, planId: $planId, requestedTier: $requestedTier, billingCycle: $billingCycle, status: $status, requestDate: $requestDate, approvedDate: $approvedDate, rejectedDate: $rejectedDate, adminId: $adminId, adminNotes: $adminNotes, rejectionReason: $rejectionReason, requestedPriceLD: $requestedPriceLD, paymentMethodId: $paymentMethodId, sellerInfo: $sellerInfo, businessDocuments: $businessDocuments, requiresDocumentVerification: $requiresDocumentVerification, priority: $priority, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SellerSubscriptionRequestCopyWith<$Res>  {
  factory $SellerSubscriptionRequestCopyWith(SellerSubscriptionRequest value, $Res Function(SellerSubscriptionRequest) _then) = _$SellerSubscriptionRequestCopyWithImpl;
@useResult
$Res call({
 String id, String sellerId, String planId, String requestedTier, String billingCycle, String status, DateTime requestDate, DateTime? approvedDate, DateTime? rejectedDate, String? adminId, String? adminNotes, String? rejectionReason, double requestedPriceLD, String? paymentMethodId, Map<String, dynamic> sellerInfo, Map<String, dynamic> businessDocuments, bool requiresDocumentVerification, int? priority,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$SellerSubscriptionRequestCopyWithImpl<$Res>
    implements $SellerSubscriptionRequestCopyWith<$Res> {
  _$SellerSubscriptionRequestCopyWithImpl(this._self, this._then);

  final SellerSubscriptionRequest _self;
  final $Res Function(SellerSubscriptionRequest) _then;

/// Create a copy of SellerSubscriptionRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sellerId = null,Object? planId = null,Object? requestedTier = null,Object? billingCycle = null,Object? status = null,Object? requestDate = null,Object? approvedDate = freezed,Object? rejectedDate = freezed,Object? adminId = freezed,Object? adminNotes = freezed,Object? rejectionReason = freezed,Object? requestedPriceLD = null,Object? paymentMethodId = freezed,Object? sellerInfo = null,Object? businessDocuments = null,Object? requiresDocumentVerification = null,Object? priority = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,planId: null == planId ? _self.planId : planId // ignore: cast_nullable_to_non_nullable
as String,requestedTier: null == requestedTier ? _self.requestedTier : requestedTier // ignore: cast_nullable_to_non_nullable
as String,billingCycle: null == billingCycle ? _self.billingCycle : billingCycle // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,requestDate: null == requestDate ? _self.requestDate : requestDate // ignore: cast_nullable_to_non_nullable
as DateTime,approvedDate: freezed == approvedDate ? _self.approvedDate : approvedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,rejectedDate: freezed == rejectedDate ? _self.rejectedDate : rejectedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,adminId: freezed == adminId ? _self.adminId : adminId // ignore: cast_nullable_to_non_nullable
as String?,adminNotes: freezed == adminNotes ? _self.adminNotes : adminNotes // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,requestedPriceLD: null == requestedPriceLD ? _self.requestedPriceLD : requestedPriceLD // ignore: cast_nullable_to_non_nullable
as double,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,sellerInfo: null == sellerInfo ? _self.sellerInfo : sellerInfo // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,businessDocuments: null == businessDocuments ? _self.businessDocuments : businessDocuments // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,requiresDocumentVerification: null == requiresDocumentVerification ? _self.requiresDocumentVerification : requiresDocumentVerification // ignore: cast_nullable_to_non_nullable
as bool,priority: freezed == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as int?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerSubscriptionRequest].
extension SellerSubscriptionRequestPatterns on SellerSubscriptionRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerSubscriptionRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerSubscriptionRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerSubscriptionRequest value)  $default,){
final _that = this;
switch (_that) {
case _SellerSubscriptionRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerSubscriptionRequest value)?  $default,){
final _that = this;
switch (_that) {
case _SellerSubscriptionRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String sellerId,  String planId,  String requestedTier,  String billingCycle,  String status,  DateTime requestDate,  DateTime? approvedDate,  DateTime? rejectedDate,  String? adminId,  String? adminNotes,  String? rejectionReason,  double requestedPriceLD,  String? paymentMethodId,  Map<String, dynamic> sellerInfo,  Map<String, dynamic> businessDocuments,  bool requiresDocumentVerification,  int? priority, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerSubscriptionRequest() when $default != null:
return $default(_that.id,_that.sellerId,_that.planId,_that.requestedTier,_that.billingCycle,_that.status,_that.requestDate,_that.approvedDate,_that.rejectedDate,_that.adminId,_that.adminNotes,_that.rejectionReason,_that.requestedPriceLD,_that.paymentMethodId,_that.sellerInfo,_that.businessDocuments,_that.requiresDocumentVerification,_that.priority,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String sellerId,  String planId,  String requestedTier,  String billingCycle,  String status,  DateTime requestDate,  DateTime? approvedDate,  DateTime? rejectedDate,  String? adminId,  String? adminNotes,  String? rejectionReason,  double requestedPriceLD,  String? paymentMethodId,  Map<String, dynamic> sellerInfo,  Map<String, dynamic> businessDocuments,  bool requiresDocumentVerification,  int? priority, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _SellerSubscriptionRequest():
return $default(_that.id,_that.sellerId,_that.planId,_that.requestedTier,_that.billingCycle,_that.status,_that.requestDate,_that.approvedDate,_that.rejectedDate,_that.adminId,_that.adminNotes,_that.rejectionReason,_that.requestedPriceLD,_that.paymentMethodId,_that.sellerInfo,_that.businessDocuments,_that.requiresDocumentVerification,_that.priority,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String sellerId,  String planId,  String requestedTier,  String billingCycle,  String status,  DateTime requestDate,  DateTime? approvedDate,  DateTime? rejectedDate,  String? adminId,  String? adminNotes,  String? rejectionReason,  double requestedPriceLD,  String? paymentMethodId,  Map<String, dynamic> sellerInfo,  Map<String, dynamic> businessDocuments,  bool requiresDocumentVerification,  int? priority, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _SellerSubscriptionRequest() when $default != null:
return $default(_that.id,_that.sellerId,_that.planId,_that.requestedTier,_that.billingCycle,_that.status,_that.requestDate,_that.approvedDate,_that.rejectedDate,_that.adminId,_that.adminNotes,_that.rejectionReason,_that.requestedPriceLD,_that.paymentMethodId,_that.sellerInfo,_that.businessDocuments,_that.requiresDocumentVerification,_that.priority,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _SellerSubscriptionRequest implements SellerSubscriptionRequest {
  const _SellerSubscriptionRequest({required this.id, required this.sellerId, required this.planId, required this.requestedTier, required this.billingCycle, required this.status, required this.requestDate, this.approvedDate, this.rejectedDate, this.adminId, this.adminNotes, this.rejectionReason, required this.requestedPriceLD, this.paymentMethodId, final  Map<String, dynamic> sellerInfo = const <String, dynamic>{}, final  Map<String, dynamic> businessDocuments = const <String, dynamic>{}, this.requiresDocumentVerification = false, this.priority, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _sellerInfo = sellerInfo,_businessDocuments = businessDocuments;
  factory _SellerSubscriptionRequest.fromJson(Map<String, dynamic> json) => _$SellerSubscriptionRequestFromJson(json);

@override final  String id;
@override final  String sellerId;
@override final  String planId;
@override final  String requestedTier;
@override final  String billingCycle;
@override final  String status;
@override final  DateTime requestDate;
@override final  DateTime? approvedDate;
@override final  DateTime? rejectedDate;
@override final  String? adminId;
@override final  String? adminNotes;
@override final  String? rejectionReason;
@override final  double requestedPriceLD;
@override final  String? paymentMethodId;
 final  Map<String, dynamic> _sellerInfo;
@override@JsonKey() Map<String, dynamic> get sellerInfo {
  if (_sellerInfo is EqualUnmodifiableMapView) return _sellerInfo;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_sellerInfo);
}

 final  Map<String, dynamic> _businessDocuments;
@override@JsonKey() Map<String, dynamic> get businessDocuments {
  if (_businessDocuments is EqualUnmodifiableMapView) return _businessDocuments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_businessDocuments);
}

@override@JsonKey() final  bool requiresDocumentVerification;
@override final  int? priority;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of SellerSubscriptionRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerSubscriptionRequestCopyWith<_SellerSubscriptionRequest> get copyWith => __$SellerSubscriptionRequestCopyWithImpl<_SellerSubscriptionRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerSubscriptionRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerSubscriptionRequest&&(identical(other.id, id) || other.id == id)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.planId, planId) || other.planId == planId)&&(identical(other.requestedTier, requestedTier) || other.requestedTier == requestedTier)&&(identical(other.billingCycle, billingCycle) || other.billingCycle == billingCycle)&&(identical(other.status, status) || other.status == status)&&(identical(other.requestDate, requestDate) || other.requestDate == requestDate)&&(identical(other.approvedDate, approvedDate) || other.approvedDate == approvedDate)&&(identical(other.rejectedDate, rejectedDate) || other.rejectedDate == rejectedDate)&&(identical(other.adminId, adminId) || other.adminId == adminId)&&(identical(other.adminNotes, adminNotes) || other.adminNotes == adminNotes)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.requestedPriceLD, requestedPriceLD) || other.requestedPriceLD == requestedPriceLD)&&(identical(other.paymentMethodId, paymentMethodId) || other.paymentMethodId == paymentMethodId)&&const DeepCollectionEquality().equals(other._sellerInfo, _sellerInfo)&&const DeepCollectionEquality().equals(other._businessDocuments, _businessDocuments)&&(identical(other.requiresDocumentVerification, requiresDocumentVerification) || other.requiresDocumentVerification == requiresDocumentVerification)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,sellerId,planId,requestedTier,billingCycle,status,requestDate,approvedDate,rejectedDate,adminId,adminNotes,rejectionReason,requestedPriceLD,paymentMethodId,const DeepCollectionEquality().hash(_sellerInfo),const DeepCollectionEquality().hash(_businessDocuments),requiresDocumentVerification,priority,createdAt,updatedAt]);

@override
String toString() {
  return 'SellerSubscriptionRequest(id: $id, sellerId: $sellerId, planId: $planId, requestedTier: $requestedTier, billingCycle: $billingCycle, status: $status, requestDate: $requestDate, approvedDate: $approvedDate, rejectedDate: $rejectedDate, adminId: $adminId, adminNotes: $adminNotes, rejectionReason: $rejectionReason, requestedPriceLD: $requestedPriceLD, paymentMethodId: $paymentMethodId, sellerInfo: $sellerInfo, businessDocuments: $businessDocuments, requiresDocumentVerification: $requiresDocumentVerification, priority: $priority, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SellerSubscriptionRequestCopyWith<$Res> implements $SellerSubscriptionRequestCopyWith<$Res> {
  factory _$SellerSubscriptionRequestCopyWith(_SellerSubscriptionRequest value, $Res Function(_SellerSubscriptionRequest) _then) = __$SellerSubscriptionRequestCopyWithImpl;
@override @useResult
$Res call({
 String id, String sellerId, String planId, String requestedTier, String billingCycle, String status, DateTime requestDate, DateTime? approvedDate, DateTime? rejectedDate, String? adminId, String? adminNotes, String? rejectionReason, double requestedPriceLD, String? paymentMethodId, Map<String, dynamic> sellerInfo, Map<String, dynamic> businessDocuments, bool requiresDocumentVerification, int? priority,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$SellerSubscriptionRequestCopyWithImpl<$Res>
    implements _$SellerSubscriptionRequestCopyWith<$Res> {
  __$SellerSubscriptionRequestCopyWithImpl(this._self, this._then);

  final _SellerSubscriptionRequest _self;
  final $Res Function(_SellerSubscriptionRequest) _then;

/// Create a copy of SellerSubscriptionRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sellerId = null,Object? planId = null,Object? requestedTier = null,Object? billingCycle = null,Object? status = null,Object? requestDate = null,Object? approvedDate = freezed,Object? rejectedDate = freezed,Object? adminId = freezed,Object? adminNotes = freezed,Object? rejectionReason = freezed,Object? requestedPriceLD = null,Object? paymentMethodId = freezed,Object? sellerInfo = null,Object? businessDocuments = null,Object? requiresDocumentVerification = null,Object? priority = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_SellerSubscriptionRequest(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,planId: null == planId ? _self.planId : planId // ignore: cast_nullable_to_non_nullable
as String,requestedTier: null == requestedTier ? _self.requestedTier : requestedTier // ignore: cast_nullable_to_non_nullable
as String,billingCycle: null == billingCycle ? _self.billingCycle : billingCycle // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,requestDate: null == requestDate ? _self.requestDate : requestDate // ignore: cast_nullable_to_non_nullable
as DateTime,approvedDate: freezed == approvedDate ? _self.approvedDate : approvedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,rejectedDate: freezed == rejectedDate ? _self.rejectedDate : rejectedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,adminId: freezed == adminId ? _self.adminId : adminId // ignore: cast_nullable_to_non_nullable
as String?,adminNotes: freezed == adminNotes ? _self.adminNotes : adminNotes // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,requestedPriceLD: null == requestedPriceLD ? _self.requestedPriceLD : requestedPriceLD // ignore: cast_nullable_to_non_nullable
as double,paymentMethodId: freezed == paymentMethodId ? _self.paymentMethodId : paymentMethodId // ignore: cast_nullable_to_non_nullable
as String?,sellerInfo: null == sellerInfo ? _self._sellerInfo : sellerInfo // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,businessDocuments: null == businessDocuments ? _self._businessDocuments : businessDocuments // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,requiresDocumentVerification: null == requiresDocumentVerification ? _self.requiresDocumentVerification : requiresDocumentVerification // ignore: cast_nullable_to_non_nullable
as bool,priority: freezed == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as int?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$SellerSubscriptionRequestReview {

 String get id; String get requestId; String get adminId; DateTime get reviewDate; String get decision; String? get notes; String? get rejectionReason; Map<String, dynamic> get reviewData;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of SellerSubscriptionRequestReview
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerSubscriptionRequestReviewCopyWith<SellerSubscriptionRequestReview> get copyWith => _$SellerSubscriptionRequestReviewCopyWithImpl<SellerSubscriptionRequestReview>(this as SellerSubscriptionRequestReview, _$identity);

  /// Serializes this SellerSubscriptionRequestReview to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerSubscriptionRequestReview&&(identical(other.id, id) || other.id == id)&&(identical(other.requestId, requestId) || other.requestId == requestId)&&(identical(other.adminId, adminId) || other.adminId == adminId)&&(identical(other.reviewDate, reviewDate) || other.reviewDate == reviewDate)&&(identical(other.decision, decision) || other.decision == decision)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&const DeepCollectionEquality().equals(other.reviewData, reviewData)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,requestId,adminId,reviewDate,decision,notes,rejectionReason,const DeepCollectionEquality().hash(reviewData),createdAt,updatedAt);

@override
String toString() {
  return 'SellerSubscriptionRequestReview(id: $id, requestId: $requestId, adminId: $adminId, reviewDate: $reviewDate, decision: $decision, notes: $notes, rejectionReason: $rejectionReason, reviewData: $reviewData, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SellerSubscriptionRequestReviewCopyWith<$Res>  {
  factory $SellerSubscriptionRequestReviewCopyWith(SellerSubscriptionRequestReview value, $Res Function(SellerSubscriptionRequestReview) _then) = _$SellerSubscriptionRequestReviewCopyWithImpl;
@useResult
$Res call({
 String id, String requestId, String adminId, DateTime reviewDate, String decision, String? notes, String? rejectionReason, Map<String, dynamic> reviewData,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$SellerSubscriptionRequestReviewCopyWithImpl<$Res>
    implements $SellerSubscriptionRequestReviewCopyWith<$Res> {
  _$SellerSubscriptionRequestReviewCopyWithImpl(this._self, this._then);

  final SellerSubscriptionRequestReview _self;
  final $Res Function(SellerSubscriptionRequestReview) _then;

/// Create a copy of SellerSubscriptionRequestReview
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? requestId = null,Object? adminId = null,Object? reviewDate = null,Object? decision = null,Object? notes = freezed,Object? rejectionReason = freezed,Object? reviewData = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,requestId: null == requestId ? _self.requestId : requestId // ignore: cast_nullable_to_non_nullable
as String,adminId: null == adminId ? _self.adminId : adminId // ignore: cast_nullable_to_non_nullable
as String,reviewDate: null == reviewDate ? _self.reviewDate : reviewDate // ignore: cast_nullable_to_non_nullable
as DateTime,decision: null == decision ? _self.decision : decision // ignore: cast_nullable_to_non_nullable
as String,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,reviewData: null == reviewData ? _self.reviewData : reviewData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerSubscriptionRequestReview].
extension SellerSubscriptionRequestReviewPatterns on SellerSubscriptionRequestReview {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerSubscriptionRequestReview value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerSubscriptionRequestReview() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerSubscriptionRequestReview value)  $default,){
final _that = this;
switch (_that) {
case _SellerSubscriptionRequestReview():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerSubscriptionRequestReview value)?  $default,){
final _that = this;
switch (_that) {
case _SellerSubscriptionRequestReview() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String requestId,  String adminId,  DateTime reviewDate,  String decision,  String? notes,  String? rejectionReason,  Map<String, dynamic> reviewData, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerSubscriptionRequestReview() when $default != null:
return $default(_that.id,_that.requestId,_that.adminId,_that.reviewDate,_that.decision,_that.notes,_that.rejectionReason,_that.reviewData,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String requestId,  String adminId,  DateTime reviewDate,  String decision,  String? notes,  String? rejectionReason,  Map<String, dynamic> reviewData, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _SellerSubscriptionRequestReview():
return $default(_that.id,_that.requestId,_that.adminId,_that.reviewDate,_that.decision,_that.notes,_that.rejectionReason,_that.reviewData,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String requestId,  String adminId,  DateTime reviewDate,  String decision,  String? notes,  String? rejectionReason,  Map<String, dynamic> reviewData, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _SellerSubscriptionRequestReview() when $default != null:
return $default(_that.id,_that.requestId,_that.adminId,_that.reviewDate,_that.decision,_that.notes,_that.rejectionReason,_that.reviewData,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _SellerSubscriptionRequestReview implements SellerSubscriptionRequestReview {
  const _SellerSubscriptionRequestReview({required this.id, required this.requestId, required this.adminId, required this.reviewDate, required this.decision, this.notes, this.rejectionReason, final  Map<String, dynamic> reviewData = const <String, dynamic>{}, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt}): _reviewData = reviewData;
  factory _SellerSubscriptionRequestReview.fromJson(Map<String, dynamic> json) => _$SellerSubscriptionRequestReviewFromJson(json);

@override final  String id;
@override final  String requestId;
@override final  String adminId;
@override final  DateTime reviewDate;
@override final  String decision;
@override final  String? notes;
@override final  String? rejectionReason;
 final  Map<String, dynamic> _reviewData;
@override@JsonKey() Map<String, dynamic> get reviewData {
  if (_reviewData is EqualUnmodifiableMapView) return _reviewData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_reviewData);
}

@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of SellerSubscriptionRequestReview
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerSubscriptionRequestReviewCopyWith<_SellerSubscriptionRequestReview> get copyWith => __$SellerSubscriptionRequestReviewCopyWithImpl<_SellerSubscriptionRequestReview>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerSubscriptionRequestReviewToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerSubscriptionRequestReview&&(identical(other.id, id) || other.id == id)&&(identical(other.requestId, requestId) || other.requestId == requestId)&&(identical(other.adminId, adminId) || other.adminId == adminId)&&(identical(other.reviewDate, reviewDate) || other.reviewDate == reviewDate)&&(identical(other.decision, decision) || other.decision == decision)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&const DeepCollectionEquality().equals(other._reviewData, _reviewData)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,requestId,adminId,reviewDate,decision,notes,rejectionReason,const DeepCollectionEquality().hash(_reviewData),createdAt,updatedAt);

@override
String toString() {
  return 'SellerSubscriptionRequestReview(id: $id, requestId: $requestId, adminId: $adminId, reviewDate: $reviewDate, decision: $decision, notes: $notes, rejectionReason: $rejectionReason, reviewData: $reviewData, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SellerSubscriptionRequestReviewCopyWith<$Res> implements $SellerSubscriptionRequestReviewCopyWith<$Res> {
  factory _$SellerSubscriptionRequestReviewCopyWith(_SellerSubscriptionRequestReview value, $Res Function(_SellerSubscriptionRequestReview) _then) = __$SellerSubscriptionRequestReviewCopyWithImpl;
@override @useResult
$Res call({
 String id, String requestId, String adminId, DateTime reviewDate, String decision, String? notes, String? rejectionReason, Map<String, dynamic> reviewData,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$SellerSubscriptionRequestReviewCopyWithImpl<$Res>
    implements _$SellerSubscriptionRequestReviewCopyWith<$Res> {
  __$SellerSubscriptionRequestReviewCopyWithImpl(this._self, this._then);

  final _SellerSubscriptionRequestReview _self;
  final $Res Function(_SellerSubscriptionRequestReview) _then;

/// Create a copy of SellerSubscriptionRequestReview
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? requestId = null,Object? adminId = null,Object? reviewDate = null,Object? decision = null,Object? notes = freezed,Object? rejectionReason = freezed,Object? reviewData = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_SellerSubscriptionRequestReview(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,requestId: null == requestId ? _self.requestId : requestId // ignore: cast_nullable_to_non_nullable
as String,adminId: null == adminId ? _self.adminId : adminId // ignore: cast_nullable_to_non_nullable
as String,reviewDate: null == reviewDate ? _self.reviewDate : reviewDate // ignore: cast_nullable_to_non_nullable
as DateTime,decision: null == decision ? _self.decision : decision // ignore: cast_nullable_to_non_nullable
as String,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,reviewData: null == reviewData ? _self._reviewData : reviewData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
