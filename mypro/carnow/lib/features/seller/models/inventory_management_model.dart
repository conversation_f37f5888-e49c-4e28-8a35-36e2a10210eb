import 'package:freezed_annotation/freezed_annotation.dart';
import 'seller_enums.dart';

part 'inventory_management_model.freezed.dart';
part 'inventory_management_model.g.dart';

/// يمثل عنصرًا واحدًا في مخزون البائع.
///
/// يحتوي على جميع التفاصيل المتعلقة بمنتج معين في المخزون،
/// مثل الكميات، الأسعار، والموقع.
@freezed
abstract class SellerInventoryItem with _$SellerInventoryItem {
  const factory SellerInventoryItem({
    /// المعرف الفريد لعنصر المخزون.
    required String id,

    /// معرّف البائع.
    required String sellerId,

    /// معرّف المنتج.
    required String productId,

    /// وحدة حفظ المخزون (SKU).
    required String sku,

    /// اسم المنتج.
    required String name,

    /// الكمية الحالية في المخزون.
    @Default(0) int currentStock,

    /// الكمية المحجوزة للطلبات.
    @Default(0) int reservedStock,

    /// الكمية المتاحة للبيع (الحالية - المحجوزة).
    @Default(0) int availableStock,

    /// أدنى مستوى للمخزون قبل التنبيه.
    @Default(0) int minStockLevel,

    /// أقصى مستوى للمخزون يمكن تخزينه.
    @Default(0) int maxStockLevel,

    /// نقطة إعادة الطلب (عند أي مستوى يتم إعادة الطلب).
    @Default(0) int reorderPoint,

    /// الكمية التي يتم إعادة طلبها.
    @Default(0) int reorderQuantity,

    /// سعر تكلفة المنتج.
    double? costPrice,

    /// سعر بيع المنتج.
    double? sellingPrice,

    /// موقع المنتج في المستودع.
    String? location,

    /// المورد الذي تم شراء المنتج منه.
    String? supplier,

    /// تاريخ آخر عملية إعادة تخزين.
    DateTime? lastRestocked,

    /// تاريخ انتهاء صلاحية المنتج.
    DateTime? expiryDate,

    /// حالة المخزون الحالية.
    @Default(InventoryStatus.inStock) InventoryStatus status,

    /// قائمة بحركات المخزون لهذا العنصر.
    List<StockMovement>? movements,

    /// بيانات وصفية إضافية.
    Map<String, dynamic>? metadata,

    /// تاريخ إنشاء سجل المخزون.
    DateTime? createdAt,

    /// تاريخ آخر تحديث لسجل المخزون.
    DateTime? updatedAt,
  }) = _SellerInventoryItem;

  factory SellerInventoryItem.fromJson(Map<String, dynamic> json) =>
      _$SellerInventoryItemFromJson(json);
}

/// يمثل حركة واحدة للمخزون، مثل عملية بيع أو شراء.
///
/// يستخدم لتسجيل كل التغييرات التي تطرأ على كمية المخزون.
@freezed
abstract class StockMovement with _$StockMovement {
  const factory StockMovement({
    /// المعرف الفريد للحركة.
    required String id,

    /// معرّف عنصر المخزون المرتبط.
    required String inventoryItemId,

    /// نوع الحركة (شراء، بيع، إرجاع، ...).
    required MovementType type,

    /// الكمية التي تم تحريكها.
    required int quantity,

    /// رصيد المخزون قبل الحركة.
    int? previousStock,

    /// رصيد المخزون بعد الحركة.
    int? newStock,

    /// سبب الحركة (إذا كان تعديلاً).
    String? reason,

    /// مرجع للحركة (مثل رقم الفاتورة أو الطلب).
    String? reference,

    /// ملاحظات إضافية.
    String? notes,

    /// المستخدم الذي قام بالحركة.
    String? performedBy,

    /// تاريخ إنشاء الحركة.
    DateTime? createdAt,
  }) = _StockMovement;

  factory StockMovement.fromJson(Map<String, dynamic> json) =>
      _$StockMovementFromJson(json);
}

/// يمثل تقريرًا شاملاً عن حالة المخزون للبائع.
///
/// يجمع هذا التقرير إحصائيات متنوعة مثل إجمالي عدد العناصر،
/// وقيمتها، وتوزيعها حسب الفئات والحالات، بالإضافة إلى التنبيهات الهامة.
@freezed
abstract class InventoryReport with _$InventoryReport {
  const factory InventoryReport({
    /// معرّف البائع الذي يخصه هذا التقرير.
    required String sellerId,

    /// إجمالي عدد أنواع المنتجات المختلفة في المخزون.
    @Default(0) int totalItems,

    /// عدد المنتجات المتوفرة حاليًا.
    @Default(0) int inStockItems,

    /// عدد المنتجات التي وصل مخزونها إلى مستوى منخفض.
    @Default(0) int lowStockItems,

    /// عدد المنتجات التي نفدت من المخزون.
    @Default(0) int outOfStockItems,

    /// عدد المنتجات التي تقترب من تاريخ انتهاء صلاحيتها.
    @Default(0) int expiringSoonItems,

    /// القيمة الإجمالية لجميع المنتجات في المخزون.
    @Default(0) double totalValue,

    /// متوسط معدل دوران المخزون.
    @Default(0) double averageTurnover,

    /// قائمة بالتنبيهات المتعلقة بالمخزون.
    List<InventoryAlert>? alerts,

    /// توزيع عدد المنتجات حسب الفئة.
    Map<String, int>? categoryBreakdown,

    /// توزيع عدد المنتجات حسب الحالة.
    Map<String, int>? statusBreakdown,

    /// تاريخ إنشاء التقرير.
    DateTime? generatedAt,
  }) = _InventoryReport;

  factory InventoryReport.fromJson(Map<String, dynamic> json) =>
      _$InventoryReportFromJson(json);
}

/// نموذج تنبيه المخزون
@freezed
abstract class InventoryAlert with _$InventoryAlert {
  const factory InventoryAlert({
    required String id,
    required String sellerId,
    required String inventoryId,
    required String alertType,
    required String title,
    required String message,
    @Default(AlertPriority.medium) AlertPriority priority,
    @Default(false) bool isRead,
    DateTime? readAt,
    DateTime? createdAt,
    SellerInventoryItem? inventoryItem,
  }) = _InventoryAlert;

  factory InventoryAlert.fromJson(Map<String, dynamic> json) =>
      _$InventoryAlertFromJson(json);
}

/// نموذج إحصائيات المخزون
@freezed
abstract class InventoryStats with _$InventoryStats {
  const factory InventoryStats({
    @Default(0) int totalItems,
    @Default(0) int lowStockItems,
    @Default(0) int outOfStockItems,
    @Default(0) int activeItems,
    @Default(0) double totalValue,
  }) = _InventoryStats;

  factory InventoryStats.fromJson(Map<String, dynamic> json) =>
      _$InventoryStatsFromJson(json);
}

/// حالات المخزون
enum InventoryStatus {
  @JsonValue('in_stock')
  inStock,

  @JsonValue('low_stock')
  lowStock,

  @JsonValue('out_of_stock')
  outOfStock,

  @JsonValue('discontinued')
  discontinued,

  @JsonValue('reserved')
  reserved,

  @JsonValue('damaged')
  damaged,

  @JsonValue('expired')
  expired,
}

/// أنواع حركة المخزون
enum MovementType {
  @JsonValue('purchase')
  purchase,

  @JsonValue('sale')
  sale,

  @JsonValue('return')
  return_,

  @JsonValue('adjustment')
  adjustment,

  @JsonValue('transfer')
  transfer,

  @JsonValue('damage')
  damage,

  @JsonValue('expiry')
  expiry,

  @JsonValue('reservation')
  reservation,

  @JsonValue('release')
  release,
}

/// أنواع التنبيهات
enum AlertType {
  @JsonValue('low_stock')
  lowStock,

  @JsonValue('out_of_stock')
  outOfStock,

  @JsonValue('expiry_warning')
  expiryWarning,

  @JsonValue('reorder_point')
  reorderPoint,

  @JsonValue('overstock')
  overstock,

  @JsonValue('negative_stock')
  negativeStock,
}

/// درجات خطورة التنبيه
enum AlertSeverity {
  @JsonValue('low')
  low,

  @JsonValue('medium')
  medium,

  @JsonValue('high')
  high,

  @JsonValue('critical')
  critical,
}

/// امتدادات مساعدة
extension InventoryStatusExtension on InventoryStatus {
  String get displayName {
    switch (this) {
      case InventoryStatus.inStock:
        return 'متوفر';
      case InventoryStatus.lowStock:
        return 'مخزون منخفض';
      case InventoryStatus.outOfStock:
        return 'نفد المخزون';
      case InventoryStatus.discontinued:
        return 'متوقف';
      case InventoryStatus.reserved:
        return 'محجوز';
      case InventoryStatus.damaged:
        return 'تالف';
      case InventoryStatus.expired:
        return 'منتهي الصلاحية';
    }
  }

  String get color {
    switch (this) {
      case InventoryStatus.inStock:
        return '#4CAF50'; // Green
      case InventoryStatus.lowStock:
        return '#FF9800'; // Orange
      case InventoryStatus.outOfStock:
        return '#F44336'; // Red
      case InventoryStatus.discontinued:
        return '#9E9E9E'; // Grey
      case InventoryStatus.reserved:
        return '#2196F3'; // Blue
      case InventoryStatus.damaged:
        return '#E91E63'; // Pink
      case InventoryStatus.expired:
        return '#795548'; // Brown
    }
  }
}

extension MovementTypeExtension on MovementType {
  String get displayName {
    switch (this) {
      case MovementType.purchase:
        return 'شراء';
      case MovementType.sale:
        return 'بيع';
      case MovementType.return_:
        return 'إرجاع';
      case MovementType.adjustment:
        return 'تعديل';
      case MovementType.transfer:
        return 'نقل';
      case MovementType.damage:
        return 'تلف';
      case MovementType.expiry:
        return 'انتهاء صلاحية';
      case MovementType.reservation:
        return 'حجز';
      case MovementType.release:
        return 'إلغاء حجز';
    }
  }

  bool get isInbound => [
    MovementType.purchase,
    MovementType.return_,
    MovementType.adjustment,
  ].contains(this);
  bool get isOutbound => [
    MovementType.sale,
    MovementType.damage,
    MovementType.expiry,
    MovementType.transfer,
  ].contains(this);
}

extension AlertSeverityExtension on AlertSeverity {
  String get displayName {
    switch (this) {
      case AlertSeverity.low:
        return 'منخفض';
      case AlertSeverity.medium:
        return 'متوسط';
      case AlertSeverity.high:
        return 'عالي';
      case AlertSeverity.critical:
        return 'حرج';
    }
  }

  String get color {
    switch (this) {
      case AlertSeverity.low:
        return '#4CAF50';
      case AlertSeverity.medium:
        return '#FF9800';
      case AlertSeverity.high:
        return '#FF5722';
      case AlertSeverity.critical:
        return '#F44336';
    }
  }
}

/// مساعدات حسابية للمخزون
extension SellerInventoryItemExtensions on SellerInventoryItem {
  /// حساب المخزون المتاح (المخزون الحالي - المحجوز)
  int get calculatedAvailableStock => currentStock - reservedStock;

  /// هل المخزون منخفض؟
  bool get isLowStock => currentStock <= minStockLevel && currentStock > 0;

  /// هل نفد المخزون؟
  bool get isOutOfStock => currentStock <= 0;

  /// هل وصل لنقطة إعادة الطلب؟
  bool get needsReorder => currentStock <= reorderPoint;

  /// حساب معدل دوران المخزون (يحتاج بيانات إضافية)
  double calculateTurnoverRate(int soldQuantity, int days) {
    if (currentStock == 0 || days == 0) return 0;
    return soldQuantity / (currentStock / days);
  }

  /// حساب قيمة المخزون
  double get inventoryValue {
    if (costPrice == null) return 0;
    return currentStock * costPrice!;
  }

  /// حساب الربح المحتمل
  double get potentialProfit {
    if (costPrice == null || sellingPrice == null) return 0;
    return currentStock * (sellingPrice! - costPrice!);
  }
}

/// يمثل هذا النموذج حالة المخزون لمنتج معين.
///
/// يستخدم لتتبع كمية المنتج المتاحة، والمحجوزة، والمباعة.
@freezed
abstract class InventoryManagementModel with _$InventoryManagementModel {
  const factory InventoryManagementModel({
    /// المعرف الفريد للمنتج.
    String? productId,

    /// اسم المنتج.
    String? productName,

    /// إجمالي الكمية المتاحة حاليًا في المخزون.
    int? inStock,

    /// الكمية المحجوزة للطلبات التي لم تكتمل بعد.
    int? reserved,

    /// إجمالي الكمية التي تم بيعها.
    int? sold,

    /// تاريخ آخر تحديث لمعلومات المخزون.
    DateTime? lastUpdated,

    /// قائمة بالموردين الذين يوفرون هذا المنتج.
    List<String>? suppliers,

    /// الموقع الفعلي للمنتج في المستودع (مثل: "الرف A، القسم 3").
    String? warehouseLocation,

    /// الحد الأدنى لكمية المخزون قبل إعادة الطلب.
    int? reorderLevel,

    /// الكمية المثلى لإعادة الطلب.
    int? reorderQuantity,

    /// تكلفة الوحدة الواحدة من المنتج.
    double? costPerUnit,

    /// ملاحظات إضافية حول حالة المخزون.
    String? notes,
  }) = _InventoryManagementModel;

  factory InventoryManagementModel.fromJson(Map<String, dynamic> json) =>
      _$InventoryManagementModelFromJson(json);
}
