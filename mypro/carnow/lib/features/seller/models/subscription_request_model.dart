import 'package:freezed_annotation/freezed_annotation.dart';

part 'subscription_request_model.freezed.dart';
part 'subscription_request_model.g.dart';

/// نموذج طلب اشتراك البائع
@freezed
abstract class SellerSubscriptionRequest with _$SellerSubscriptionRequest {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory SellerSubscriptionRequest({
    required String id,
    required String sellerId,
    required String planId,
    required String requestedTier,
    required String billingCycle,
    required String status,
    required DateTime requestDate,
    DateTime? approvedDate,
    DateTime? rejectedDate,
    String? adminId,
    String? adminNotes,
    String? rejectionReason,
    required double requestedPriceLD,
    String? paymentMethodId,
    @Default(<String, dynamic>{})
    Map<String, dynamic> sellerInfo,
    @Default(<String, dynamic>{})
    Map<String, dynamic> businessDocuments,
    @Default(false) bool requiresDocumentVerification,
    int? priority,
    @JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,
    @JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt,
  }) = _SellerSubscriptionRequest;

  factory SellerSubscriptionRequest.fromJson(Map<String, dynamic> json) =>
      _$SellerSubscriptionRequestFromJson(json);
}

/// نموذج مراجعة طلب اشتراك البائع
@freezed
abstract class SellerSubscriptionRequestReview with _$SellerSubscriptionRequestReview {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory SellerSubscriptionRequestReview({
    required String id,
    required String requestId,
    required String adminId,
    required DateTime reviewDate,
    required String decision,
    String? notes,
    String? rejectionReason,
    @Default(<String, dynamic>{})
    Map<String, dynamic> reviewData,
    @JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,
    @JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt,
  }) = _SellerSubscriptionRequestReview;

  factory SellerSubscriptionRequestReview.fromJson(Map<String, dynamic> json) =>
      _$SellerSubscriptionRequestReviewFromJson(json);
}
