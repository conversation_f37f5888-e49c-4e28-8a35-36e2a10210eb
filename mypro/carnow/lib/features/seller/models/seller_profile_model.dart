import 'package:freezed_annotation/freezed_annotation.dart';

part 'seller_profile_model.freezed.dart';
part 'seller_profile_model.g.dart';

/// حالات طلب الاشتراك
enum SubscriptionRequestStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('under_review')
  underReview,
  @JsonValue('approved')
  approved,
  @JsonValue('rejected')
  rejected,
  @JsonValue('cancelled')
  cancelled,
  @JsonValue('expired')
  expired,
}

/// معلومات البائع التفصيلية
@freezed
abstract class SellerProfile with _$SellerProfile {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory SellerProfile({
    required String id,
    required String userId,
    required String fullName,
    required String storeName,
    required String email,
    required String phone,
    String? whatsappNumber,
    required String city,
    required String address,
    String? businessDescription,
    String? businessType,
    String? businessLicense,
    String? taxNumber,
    List<String>? businessDocuments,
    String? profileImageUrl,
    String? storeLogoUrl,
    @Default(false) bool isVerified,
    @Default(false) bool isActive,
    double? rating,
    int? totalSales,
    int? totalProducts,
    @JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,
    @JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt,
  }) = _SellerProfile;

  factory SellerProfile.fromJson(Map<String, dynamic> json) =>
      _$SellerProfileFromJson(json);
}

/// نموذج طلب عضوية محسن يحتوي على معلومات البائع
@freezed
abstract class EnhancedSubscriptionRequest with _$EnhancedSubscriptionRequest {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory EnhancedSubscriptionRequest({
    required String id,
    required String sellerId,
    required String planId,
    required String requestedTierName,
    required String billingCycleName,
    required SubscriptionRequestStatus status,
    required String statusName,
    required DateTime requestDate,
    DateTime? approvedDate,
    DateTime? rejectedDate,
    String? adminId,
    String? adminNotes,
    String? rejectionReason,
    required double requestedPriceLD,
    String? paymentMethodId,
    int? priority,

    // معلومات البائع التفصيلية
    required String sellerFullName,
    required String sellerStoreName,
    required String sellerEmail,
    required String sellerPhone,
    String? sellerWhatsapp,
    required String sellerCity,
    required String sellerAddress,
    String? sellerBusinessType,
    String? sellerBusinessDescription,
    String? sellerProfileImageUrl,
    String? sellerStoreLogoUrl,

    @JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,
    @JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt,
  }) = _EnhancedSubscriptionRequest;

  factory EnhancedSubscriptionRequest.fromJson(Map<String, dynamic> json) =>
      _$EnhancedSubscriptionRequestFromJson(json);
}

/// امتدادات مساعدة للطلب المحسن
extension EnhancedSubscriptionRequestExtensions on EnhancedSubscriptionRequest {
  Duration get timeInQueue => DateTime.now().difference(requestDate);

  String get timeInQueueDisplayText {
    final days = timeInQueue.inDays;
    final hours = timeInQueue.inHours % 24;
    final minutes = timeInQueue.inMinutes % 60;

    if (days > 0) {
      return '$days يوم و $hours ساعة';
    } else if (hours > 0) {
      return '$hours ساعة و $minutes دقيقة';
    } else if (minutes > 0) {
      return '$minutes دقيقة';
    } else {
      return 'أقل من دقيقة';
    }
  }

  bool get isExpired => timeInQueue.inDays > 30;
  bool get needsUrgentAttention => timeInQueue.inDays > 2;

  String get shortId => id.length > 8 ? id.substring(0, 8) : id;

  String get displayPhone {
    if (sellerWhatsapp != null && sellerWhatsapp!.isNotEmpty) {
      return '$sellerPhone (واتساب: $sellerWhatsapp)';
    }
    return sellerPhone;
  }
}
