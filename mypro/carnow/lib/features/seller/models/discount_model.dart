import 'package:freezed_annotation/freezed_annotation.dart';

part 'discount_model.freezed.dart';
part 'discount_model.g.dart';

/// نموذج الخصم
@freezed
abstract class Discount with _$Discount {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory Discount({
    required int id,
    required String name,
    required String nameAr,
    String? description,
    String? descriptionAr,
    required String discountType, // percentage, fixed_amount
    required double discountValue,
    @Default(0.0) double minAmount,
    double? maxDiscount,
    @Default([]) List<String> applicableTo,
    required DateTime startDate,
    DateTime? endDate,
    @Default(true) bool isActive,
    int? usageLimit,
    @Default(0) int usedCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Discount;

  factory Discount.fromJson(Map<String, dynamic> json) =>
      _$DiscountFromJson(json);
}

/// نموذج رمز الخصم
@freezed
abstract class DiscountCode with _$DiscountCode {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory DiscountCode({
    required int id,
    required String code,
    required int discountId,
    required Discount discount,
    @Default(true) bool isActive,
    int? usageLimit,
    @Default(0) int usedCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _DiscountCode;

  factory DiscountCode.fromJson(Map<String, dynamic> json) =>
      _$DiscountCodeFromJson(json);
}

/// نموذج استخدام الخصم
@freezed
abstract class DiscountUsage with _$DiscountUsage {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory DiscountUsage({
    required int id,
    required int discountId,
    int? discountCodeId,
    required String userId,
    int? subscriptionId,
    required double amountBeforeDiscount,
    required double discountAmount,
    required double amountAfterDiscount,
    required DateTime usedAt,
  }) = _DiscountUsage;

  factory DiscountUsage.fromJson(Map<String, dynamic> json) =>
      _$DiscountUsageFromJson(json);
}

/// نموذج حساب الخصم
@freezed
abstract class DiscountCalculation with _$DiscountCalculation {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory DiscountCalculation({
    required double originalAmount,
    required double discountAmount,
    required double finalAmount,
    required double discountPercentage,
    Discount? appliedDiscount,
    DiscountCode? appliedCode,
  }) = _DiscountCalculation;

  factory DiscountCalculation.fromJson(Map<String, dynamic> json) =>
      _$DiscountCalculationFromJson(json);
} 