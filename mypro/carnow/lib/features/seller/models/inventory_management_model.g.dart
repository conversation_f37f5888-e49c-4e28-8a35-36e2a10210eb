// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'inventory_management_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SellerInventoryItem _$SellerInventoryItemFromJson(Map<String, dynamic> json) =>
    _SellerInventoryItem(
      id: json['id'] as String,
      sellerId: json['sellerId'] as String,
      productId: json['productId'] as String,
      sku: json['sku'] as String,
      name: json['name'] as String,
      currentStock: (json['currentStock'] as num?)?.toInt() ?? 0,
      reservedStock: (json['reservedStock'] as num?)?.toInt() ?? 0,
      availableStock: (json['availableStock'] as num?)?.toInt() ?? 0,
      minStockLevel: (json['minStockLevel'] as num?)?.toInt() ?? 0,
      maxStockLevel: (json['maxStockLevel'] as num?)?.toInt() ?? 0,
      reorderPoint: (json['reorderPoint'] as num?)?.toInt() ?? 0,
      reorderQuantity: (json['reorderQuantity'] as num?)?.toInt() ?? 0,
      costPrice: (json['costPrice'] as num?)?.toDouble(),
      sellingPrice: (json['sellingPrice'] as num?)?.toDouble(),
      location: json['location'] as String?,
      supplier: json['supplier'] as String?,
      lastRestocked: json['lastRestocked'] == null
          ? null
          : DateTime.parse(json['lastRestocked'] as String),
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
      status:
          $enumDecodeNullable(_$InventoryStatusEnumMap, json['status']) ??
          InventoryStatus.inStock,
      movements: (json['movements'] as List<dynamic>?)
          ?.map((e) => StockMovement.fromJson(e as Map<String, dynamic>))
          .toList(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$SellerInventoryItemToJson(
  _SellerInventoryItem instance,
) => <String, dynamic>{
  'id': instance.id,
  'sellerId': instance.sellerId,
  'productId': instance.productId,
  'sku': instance.sku,
  'name': instance.name,
  'currentStock': instance.currentStock,
  'reservedStock': instance.reservedStock,
  'availableStock': instance.availableStock,
  'minStockLevel': instance.minStockLevel,
  'maxStockLevel': instance.maxStockLevel,
  'reorderPoint': instance.reorderPoint,
  'reorderQuantity': instance.reorderQuantity,
  'costPrice': instance.costPrice,
  'sellingPrice': instance.sellingPrice,
  'location': instance.location,
  'supplier': instance.supplier,
  'lastRestocked': instance.lastRestocked?.toIso8601String(),
  'expiryDate': instance.expiryDate?.toIso8601String(),
  'status': _$InventoryStatusEnumMap[instance.status]!,
  'movements': instance.movements,
  'metadata': instance.metadata,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

const _$InventoryStatusEnumMap = {
  InventoryStatus.inStock: 'in_stock',
  InventoryStatus.lowStock: 'low_stock',
  InventoryStatus.outOfStock: 'out_of_stock',
  InventoryStatus.discontinued: 'discontinued',
  InventoryStatus.reserved: 'reserved',
  InventoryStatus.damaged: 'damaged',
  InventoryStatus.expired: 'expired',
};

_StockMovement _$StockMovementFromJson(Map<String, dynamic> json) =>
    _StockMovement(
      id: json['id'] as String,
      inventoryItemId: json['inventoryItemId'] as String,
      type: $enumDecode(_$MovementTypeEnumMap, json['type']),
      quantity: (json['quantity'] as num).toInt(),
      previousStock: (json['previousStock'] as num?)?.toInt(),
      newStock: (json['newStock'] as num?)?.toInt(),
      reason: json['reason'] as String?,
      reference: json['reference'] as String?,
      notes: json['notes'] as String?,
      performedBy: json['performedBy'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$StockMovementToJson(_StockMovement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'inventoryItemId': instance.inventoryItemId,
      'type': _$MovementTypeEnumMap[instance.type]!,
      'quantity': instance.quantity,
      'previousStock': instance.previousStock,
      'newStock': instance.newStock,
      'reason': instance.reason,
      'reference': instance.reference,
      'notes': instance.notes,
      'performedBy': instance.performedBy,
      'createdAt': instance.createdAt?.toIso8601String(),
    };

const _$MovementTypeEnumMap = {
  MovementType.purchase: 'purchase',
  MovementType.sale: 'sale',
  MovementType.return_: 'return',
  MovementType.adjustment: 'adjustment',
  MovementType.transfer: 'transfer',
  MovementType.damage: 'damage',
  MovementType.expiry: 'expiry',
  MovementType.reservation: 'reservation',
  MovementType.release: 'release',
};

_InventoryReport _$InventoryReportFromJson(Map<String, dynamic> json) =>
    _InventoryReport(
      sellerId: json['sellerId'] as String,
      totalItems: (json['totalItems'] as num?)?.toInt() ?? 0,
      inStockItems: (json['inStockItems'] as num?)?.toInt() ?? 0,
      lowStockItems: (json['lowStockItems'] as num?)?.toInt() ?? 0,
      outOfStockItems: (json['outOfStockItems'] as num?)?.toInt() ?? 0,
      expiringSoonItems: (json['expiringSoonItems'] as num?)?.toInt() ?? 0,
      totalValue: (json['totalValue'] as num?)?.toDouble() ?? 0,
      averageTurnover: (json['averageTurnover'] as num?)?.toDouble() ?? 0,
      alerts: (json['alerts'] as List<dynamic>?)
          ?.map((e) => InventoryAlert.fromJson(e as Map<String, dynamic>))
          .toList(),
      categoryBreakdown: (json['categoryBreakdown'] as Map<String, dynamic>?)
          ?.map((k, e) => MapEntry(k, (e as num).toInt())),
      statusBreakdown: (json['statusBreakdown'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
      generatedAt: json['generatedAt'] == null
          ? null
          : DateTime.parse(json['generatedAt'] as String),
    );

Map<String, dynamic> _$InventoryReportToJson(_InventoryReport instance) =>
    <String, dynamic>{
      'sellerId': instance.sellerId,
      'totalItems': instance.totalItems,
      'inStockItems': instance.inStockItems,
      'lowStockItems': instance.lowStockItems,
      'outOfStockItems': instance.outOfStockItems,
      'expiringSoonItems': instance.expiringSoonItems,
      'totalValue': instance.totalValue,
      'averageTurnover': instance.averageTurnover,
      'alerts': instance.alerts,
      'categoryBreakdown': instance.categoryBreakdown,
      'statusBreakdown': instance.statusBreakdown,
      'generatedAt': instance.generatedAt?.toIso8601String(),
    };

_InventoryAlert _$InventoryAlertFromJson(Map<String, dynamic> json) =>
    _InventoryAlert(
      id: json['id'] as String,
      sellerId: json['sellerId'] as String,
      inventoryId: json['inventoryId'] as String,
      alertType: json['alertType'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      priority:
          $enumDecodeNullable(_$AlertPriorityEnumMap, json['priority']) ??
          AlertPriority.medium,
      isRead: json['isRead'] as bool? ?? false,
      readAt: json['readAt'] == null
          ? null
          : DateTime.parse(json['readAt'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      inventoryItem: json['inventoryItem'] == null
          ? null
          : SellerInventoryItem.fromJson(
              json['inventoryItem'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$InventoryAlertToJson(_InventoryAlert instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sellerId': instance.sellerId,
      'inventoryId': instance.inventoryId,
      'alertType': instance.alertType,
      'title': instance.title,
      'message': instance.message,
      'priority': _$AlertPriorityEnumMap[instance.priority]!,
      'isRead': instance.isRead,
      'readAt': instance.readAt?.toIso8601String(),
      'createdAt': instance.createdAt?.toIso8601String(),
      'inventoryItem': instance.inventoryItem,
    };

const _$AlertPriorityEnumMap = {
  AlertPriority.low: 'low',
  AlertPriority.medium: 'medium',
  AlertPriority.high: 'high',
  AlertPriority.urgent: 'urgent',
  AlertPriority.critical: 'critical',
};

_InventoryStats _$InventoryStatsFromJson(Map<String, dynamic> json) =>
    _InventoryStats(
      totalItems: (json['totalItems'] as num?)?.toInt() ?? 0,
      lowStockItems: (json['lowStockItems'] as num?)?.toInt() ?? 0,
      outOfStockItems: (json['outOfStockItems'] as num?)?.toInt() ?? 0,
      activeItems: (json['activeItems'] as num?)?.toInt() ?? 0,
      totalValue: (json['totalValue'] as num?)?.toDouble() ?? 0,
    );

Map<String, dynamic> _$InventoryStatsToJson(_InventoryStats instance) =>
    <String, dynamic>{
      'totalItems': instance.totalItems,
      'lowStockItems': instance.lowStockItems,
      'outOfStockItems': instance.outOfStockItems,
      'activeItems': instance.activeItems,
      'totalValue': instance.totalValue,
    };

_InventoryManagementModel _$InventoryManagementModelFromJson(
  Map<String, dynamic> json,
) => _InventoryManagementModel(
  productId: json['productId'] as String?,
  productName: json['productName'] as String?,
  inStock: (json['inStock'] as num?)?.toInt(),
  reserved: (json['reserved'] as num?)?.toInt(),
  sold: (json['sold'] as num?)?.toInt(),
  lastUpdated: json['lastUpdated'] == null
      ? null
      : DateTime.parse(json['lastUpdated'] as String),
  suppliers: (json['suppliers'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  warehouseLocation: json['warehouseLocation'] as String?,
  reorderLevel: (json['reorderLevel'] as num?)?.toInt(),
  reorderQuantity: (json['reorderQuantity'] as num?)?.toInt(),
  costPerUnit: (json['costPerUnit'] as num?)?.toDouble(),
  notes: json['notes'] as String?,
);

Map<String, dynamic> _$InventoryManagementModelToJson(
  _InventoryManagementModel instance,
) => <String, dynamic>{
  'productId': instance.productId,
  'productName': instance.productName,
  'inStock': instance.inStock,
  'reserved': instance.reserved,
  'sold': instance.sold,
  'lastUpdated': instance.lastUpdated?.toIso8601String(),
  'suppliers': instance.suppliers,
  'warehouseLocation': instance.warehouseLocation,
  'reorderLevel': instance.reorderLevel,
  'reorderQuantity': instance.reorderQuantity,
  'costPerUnit': instance.costPerUnit,
  'notes': instance.notes,
};
