// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'discount_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Discount {

 int get id; String get name; String get nameAr; String? get description; String? get descriptionAr; String get discountType;// percentage, fixed_amount
 double get discountValue; double get minAmount; double? get maxDiscount; List<String> get applicableTo; DateTime get startDate; DateTime? get endDate; bool get isActive; int? get usageLimit; int get usedCount; DateTime? get createdAt; DateTime? get updatedAt;
/// Create a copy of Discount
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DiscountCopyWith<Discount> get copyWith => _$DiscountCopyWithImpl<Discount>(this as Discount, _$identity);

  /// Serializes this Discount to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Discount&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.discountType, discountType) || other.discountType == discountType)&&(identical(other.discountValue, discountValue) || other.discountValue == discountValue)&&(identical(other.minAmount, minAmount) || other.minAmount == minAmount)&&(identical(other.maxDiscount, maxDiscount) || other.maxDiscount == maxDiscount)&&const DeepCollectionEquality().equals(other.applicableTo, applicableTo)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.usageLimit, usageLimit) || other.usageLimit == usageLimit)&&(identical(other.usedCount, usedCount) || other.usedCount == usedCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,description,descriptionAr,discountType,discountValue,minAmount,maxDiscount,const DeepCollectionEquality().hash(applicableTo),startDate,endDate,isActive,usageLimit,usedCount,createdAt,updatedAt);

@override
String toString() {
  return 'Discount(id: $id, name: $name, nameAr: $nameAr, description: $description, descriptionAr: $descriptionAr, discountType: $discountType, discountValue: $discountValue, minAmount: $minAmount, maxDiscount: $maxDiscount, applicableTo: $applicableTo, startDate: $startDate, endDate: $endDate, isActive: $isActive, usageLimit: $usageLimit, usedCount: $usedCount, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $DiscountCopyWith<$Res>  {
  factory $DiscountCopyWith(Discount value, $Res Function(Discount) _then) = _$DiscountCopyWithImpl;
@useResult
$Res call({
 int id, String name, String nameAr, String? description, String? descriptionAr, String discountType, double discountValue, double minAmount, double? maxDiscount, List<String> applicableTo, DateTime startDate, DateTime? endDate, bool isActive, int? usageLimit, int usedCount, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class _$DiscountCopyWithImpl<$Res>
    implements $DiscountCopyWith<$Res> {
  _$DiscountCopyWithImpl(this._self, this._then);

  final Discount _self;
  final $Res Function(Discount) _then;

/// Create a copy of Discount
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? nameAr = null,Object? description = freezed,Object? descriptionAr = freezed,Object? discountType = null,Object? discountValue = null,Object? minAmount = null,Object? maxDiscount = freezed,Object? applicableTo = null,Object? startDate = null,Object? endDate = freezed,Object? isActive = null,Object? usageLimit = freezed,Object? usedCount = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: null == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,descriptionAr: freezed == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String?,discountType: null == discountType ? _self.discountType : discountType // ignore: cast_nullable_to_non_nullable
as String,discountValue: null == discountValue ? _self.discountValue : discountValue // ignore: cast_nullable_to_non_nullable
as double,minAmount: null == minAmount ? _self.minAmount : minAmount // ignore: cast_nullable_to_non_nullable
as double,maxDiscount: freezed == maxDiscount ? _self.maxDiscount : maxDiscount // ignore: cast_nullable_to_non_nullable
as double?,applicableTo: null == applicableTo ? _self.applicableTo : applicableTo // ignore: cast_nullable_to_non_nullable
as List<String>,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,usageLimit: freezed == usageLimit ? _self.usageLimit : usageLimit // ignore: cast_nullable_to_non_nullable
as int?,usedCount: null == usedCount ? _self.usedCount : usedCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [Discount].
extension DiscountPatterns on Discount {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Discount value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Discount() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Discount value)  $default,){
final _that = this;
switch (_that) {
case _Discount():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Discount value)?  $default,){
final _that = this;
switch (_that) {
case _Discount() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String name,  String nameAr,  String? description,  String? descriptionAr,  String discountType,  double discountValue,  double minAmount,  double? maxDiscount,  List<String> applicableTo,  DateTime startDate,  DateTime? endDate,  bool isActive,  int? usageLimit,  int usedCount,  DateTime? createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Discount() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.discountType,_that.discountValue,_that.minAmount,_that.maxDiscount,_that.applicableTo,_that.startDate,_that.endDate,_that.isActive,_that.usageLimit,_that.usedCount,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String name,  String nameAr,  String? description,  String? descriptionAr,  String discountType,  double discountValue,  double minAmount,  double? maxDiscount,  List<String> applicableTo,  DateTime startDate,  DateTime? endDate,  bool isActive,  int? usageLimit,  int usedCount,  DateTime? createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _Discount():
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.discountType,_that.discountValue,_that.minAmount,_that.maxDiscount,_that.applicableTo,_that.startDate,_that.endDate,_that.isActive,_that.usageLimit,_that.usedCount,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String name,  String nameAr,  String? description,  String? descriptionAr,  String discountType,  double discountValue,  double minAmount,  double? maxDiscount,  List<String> applicableTo,  DateTime startDate,  DateTime? endDate,  bool isActive,  int? usageLimit,  int usedCount,  DateTime? createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _Discount() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.description,_that.descriptionAr,_that.discountType,_that.discountValue,_that.minAmount,_that.maxDiscount,_that.applicableTo,_that.startDate,_that.endDate,_that.isActive,_that.usageLimit,_that.usedCount,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _Discount implements Discount {
  const _Discount({required this.id, required this.name, required this.nameAr, this.description, this.descriptionAr, required this.discountType, required this.discountValue, this.minAmount = 0.0, this.maxDiscount, final  List<String> applicableTo = const [], required this.startDate, this.endDate, this.isActive = true, this.usageLimit, this.usedCount = 0, this.createdAt, this.updatedAt}): _applicableTo = applicableTo;
  factory _Discount.fromJson(Map<String, dynamic> json) => _$DiscountFromJson(json);

@override final  int id;
@override final  String name;
@override final  String nameAr;
@override final  String? description;
@override final  String? descriptionAr;
@override final  String discountType;
// percentage, fixed_amount
@override final  double discountValue;
@override@JsonKey() final  double minAmount;
@override final  double? maxDiscount;
 final  List<String> _applicableTo;
@override@JsonKey() List<String> get applicableTo {
  if (_applicableTo is EqualUnmodifiableListView) return _applicableTo;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_applicableTo);
}

@override final  DateTime startDate;
@override final  DateTime? endDate;
@override@JsonKey() final  bool isActive;
@override final  int? usageLimit;
@override@JsonKey() final  int usedCount;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of Discount
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DiscountCopyWith<_Discount> get copyWith => __$DiscountCopyWithImpl<_Discount>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DiscountToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Discount&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.discountType, discountType) || other.discountType == discountType)&&(identical(other.discountValue, discountValue) || other.discountValue == discountValue)&&(identical(other.minAmount, minAmount) || other.minAmount == minAmount)&&(identical(other.maxDiscount, maxDiscount) || other.maxDiscount == maxDiscount)&&const DeepCollectionEquality().equals(other._applicableTo, _applicableTo)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.usageLimit, usageLimit) || other.usageLimit == usageLimit)&&(identical(other.usedCount, usedCount) || other.usedCount == usedCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,nameAr,description,descriptionAr,discountType,discountValue,minAmount,maxDiscount,const DeepCollectionEquality().hash(_applicableTo),startDate,endDate,isActive,usageLimit,usedCount,createdAt,updatedAt);

@override
String toString() {
  return 'Discount(id: $id, name: $name, nameAr: $nameAr, description: $description, descriptionAr: $descriptionAr, discountType: $discountType, discountValue: $discountValue, minAmount: $minAmount, maxDiscount: $maxDiscount, applicableTo: $applicableTo, startDate: $startDate, endDate: $endDate, isActive: $isActive, usageLimit: $usageLimit, usedCount: $usedCount, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$DiscountCopyWith<$Res> implements $DiscountCopyWith<$Res> {
  factory _$DiscountCopyWith(_Discount value, $Res Function(_Discount) _then) = __$DiscountCopyWithImpl;
@override @useResult
$Res call({
 int id, String name, String nameAr, String? description, String? descriptionAr, String discountType, double discountValue, double minAmount, double? maxDiscount, List<String> applicableTo, DateTime startDate, DateTime? endDate, bool isActive, int? usageLimit, int usedCount, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class __$DiscountCopyWithImpl<$Res>
    implements _$DiscountCopyWith<$Res> {
  __$DiscountCopyWithImpl(this._self, this._then);

  final _Discount _self;
  final $Res Function(_Discount) _then;

/// Create a copy of Discount
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? nameAr = null,Object? description = freezed,Object? descriptionAr = freezed,Object? discountType = null,Object? discountValue = null,Object? minAmount = null,Object? maxDiscount = freezed,Object? applicableTo = null,Object? startDate = null,Object? endDate = freezed,Object? isActive = null,Object? usageLimit = freezed,Object? usedCount = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_Discount(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: null == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,descriptionAr: freezed == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String?,discountType: null == discountType ? _self.discountType : discountType // ignore: cast_nullable_to_non_nullable
as String,discountValue: null == discountValue ? _self.discountValue : discountValue // ignore: cast_nullable_to_non_nullable
as double,minAmount: null == minAmount ? _self.minAmount : minAmount // ignore: cast_nullable_to_non_nullable
as double,maxDiscount: freezed == maxDiscount ? _self.maxDiscount : maxDiscount // ignore: cast_nullable_to_non_nullable
as double?,applicableTo: null == applicableTo ? _self._applicableTo : applicableTo // ignore: cast_nullable_to_non_nullable
as List<String>,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,usageLimit: freezed == usageLimit ? _self.usageLimit : usageLimit // ignore: cast_nullable_to_non_nullable
as int?,usedCount: null == usedCount ? _self.usedCount : usedCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$DiscountCode {

 int get id; String get code; int get discountId; Discount get discount; bool get isActive; int? get usageLimit; int get usedCount; DateTime? get createdAt; DateTime? get updatedAt;
/// Create a copy of DiscountCode
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DiscountCodeCopyWith<DiscountCode> get copyWith => _$DiscountCodeCopyWithImpl<DiscountCode>(this as DiscountCode, _$identity);

  /// Serializes this DiscountCode to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DiscountCode&&(identical(other.id, id) || other.id == id)&&(identical(other.code, code) || other.code == code)&&(identical(other.discountId, discountId) || other.discountId == discountId)&&(identical(other.discount, discount) || other.discount == discount)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.usageLimit, usageLimit) || other.usageLimit == usageLimit)&&(identical(other.usedCount, usedCount) || other.usedCount == usedCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,code,discountId,discount,isActive,usageLimit,usedCount,createdAt,updatedAt);

@override
String toString() {
  return 'DiscountCode(id: $id, code: $code, discountId: $discountId, discount: $discount, isActive: $isActive, usageLimit: $usageLimit, usedCount: $usedCount, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $DiscountCodeCopyWith<$Res>  {
  factory $DiscountCodeCopyWith(DiscountCode value, $Res Function(DiscountCode) _then) = _$DiscountCodeCopyWithImpl;
@useResult
$Res call({
 int id, String code, int discountId, Discount discount, bool isActive, int? usageLimit, int usedCount, DateTime? createdAt, DateTime? updatedAt
});


$DiscountCopyWith<$Res> get discount;

}
/// @nodoc
class _$DiscountCodeCopyWithImpl<$Res>
    implements $DiscountCodeCopyWith<$Res> {
  _$DiscountCodeCopyWithImpl(this._self, this._then);

  final DiscountCode _self;
  final $Res Function(DiscountCode) _then;

/// Create a copy of DiscountCode
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? code = null,Object? discountId = null,Object? discount = null,Object? isActive = null,Object? usageLimit = freezed,Object? usedCount = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,discountId: null == discountId ? _self.discountId : discountId // ignore: cast_nullable_to_non_nullable
as int,discount: null == discount ? _self.discount : discount // ignore: cast_nullable_to_non_nullable
as Discount,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,usageLimit: freezed == usageLimit ? _self.usageLimit : usageLimit // ignore: cast_nullable_to_non_nullable
as int?,usedCount: null == usedCount ? _self.usedCount : usedCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}
/// Create a copy of DiscountCode
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DiscountCopyWith<$Res> get discount {
  
  return $DiscountCopyWith<$Res>(_self.discount, (value) {
    return _then(_self.copyWith(discount: value));
  });
}
}


/// Adds pattern-matching-related methods to [DiscountCode].
extension DiscountCodePatterns on DiscountCode {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DiscountCode value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DiscountCode() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DiscountCode value)  $default,){
final _that = this;
switch (_that) {
case _DiscountCode():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DiscountCode value)?  $default,){
final _that = this;
switch (_that) {
case _DiscountCode() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String code,  int discountId,  Discount discount,  bool isActive,  int? usageLimit,  int usedCount,  DateTime? createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DiscountCode() when $default != null:
return $default(_that.id,_that.code,_that.discountId,_that.discount,_that.isActive,_that.usageLimit,_that.usedCount,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String code,  int discountId,  Discount discount,  bool isActive,  int? usageLimit,  int usedCount,  DateTime? createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _DiscountCode():
return $default(_that.id,_that.code,_that.discountId,_that.discount,_that.isActive,_that.usageLimit,_that.usedCount,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String code,  int discountId,  Discount discount,  bool isActive,  int? usageLimit,  int usedCount,  DateTime? createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _DiscountCode() when $default != null:
return $default(_that.id,_that.code,_that.discountId,_that.discount,_that.isActive,_that.usageLimit,_that.usedCount,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _DiscountCode implements DiscountCode {
  const _DiscountCode({required this.id, required this.code, required this.discountId, required this.discount, this.isActive = true, this.usageLimit, this.usedCount = 0, this.createdAt, this.updatedAt});
  factory _DiscountCode.fromJson(Map<String, dynamic> json) => _$DiscountCodeFromJson(json);

@override final  int id;
@override final  String code;
@override final  int discountId;
@override final  Discount discount;
@override@JsonKey() final  bool isActive;
@override final  int? usageLimit;
@override@JsonKey() final  int usedCount;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of DiscountCode
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DiscountCodeCopyWith<_DiscountCode> get copyWith => __$DiscountCodeCopyWithImpl<_DiscountCode>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DiscountCodeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DiscountCode&&(identical(other.id, id) || other.id == id)&&(identical(other.code, code) || other.code == code)&&(identical(other.discountId, discountId) || other.discountId == discountId)&&(identical(other.discount, discount) || other.discount == discount)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.usageLimit, usageLimit) || other.usageLimit == usageLimit)&&(identical(other.usedCount, usedCount) || other.usedCount == usedCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,code,discountId,discount,isActive,usageLimit,usedCount,createdAt,updatedAt);

@override
String toString() {
  return 'DiscountCode(id: $id, code: $code, discountId: $discountId, discount: $discount, isActive: $isActive, usageLimit: $usageLimit, usedCount: $usedCount, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$DiscountCodeCopyWith<$Res> implements $DiscountCodeCopyWith<$Res> {
  factory _$DiscountCodeCopyWith(_DiscountCode value, $Res Function(_DiscountCode) _then) = __$DiscountCodeCopyWithImpl;
@override @useResult
$Res call({
 int id, String code, int discountId, Discount discount, bool isActive, int? usageLimit, int usedCount, DateTime? createdAt, DateTime? updatedAt
});


@override $DiscountCopyWith<$Res> get discount;

}
/// @nodoc
class __$DiscountCodeCopyWithImpl<$Res>
    implements _$DiscountCodeCopyWith<$Res> {
  __$DiscountCodeCopyWithImpl(this._self, this._then);

  final _DiscountCode _self;
  final $Res Function(_DiscountCode) _then;

/// Create a copy of DiscountCode
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? code = null,Object? discountId = null,Object? discount = null,Object? isActive = null,Object? usageLimit = freezed,Object? usedCount = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_DiscountCode(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,discountId: null == discountId ? _self.discountId : discountId // ignore: cast_nullable_to_non_nullable
as int,discount: null == discount ? _self.discount : discount // ignore: cast_nullable_to_non_nullable
as Discount,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,usageLimit: freezed == usageLimit ? _self.usageLimit : usageLimit // ignore: cast_nullable_to_non_nullable
as int?,usedCount: null == usedCount ? _self.usedCount : usedCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

/// Create a copy of DiscountCode
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DiscountCopyWith<$Res> get discount {
  
  return $DiscountCopyWith<$Res>(_self.discount, (value) {
    return _then(_self.copyWith(discount: value));
  });
}
}


/// @nodoc
mixin _$DiscountUsage {

 int get id; int get discountId; int? get discountCodeId; String get userId; int? get subscriptionId; double get amountBeforeDiscount; double get discountAmount; double get amountAfterDiscount; DateTime get usedAt;
/// Create a copy of DiscountUsage
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DiscountUsageCopyWith<DiscountUsage> get copyWith => _$DiscountUsageCopyWithImpl<DiscountUsage>(this as DiscountUsage, _$identity);

  /// Serializes this DiscountUsage to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DiscountUsage&&(identical(other.id, id) || other.id == id)&&(identical(other.discountId, discountId) || other.discountId == discountId)&&(identical(other.discountCodeId, discountCodeId) || other.discountCodeId == discountCodeId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId)&&(identical(other.amountBeforeDiscount, amountBeforeDiscount) || other.amountBeforeDiscount == amountBeforeDiscount)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.amountAfterDiscount, amountAfterDiscount) || other.amountAfterDiscount == amountAfterDiscount)&&(identical(other.usedAt, usedAt) || other.usedAt == usedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,discountId,discountCodeId,userId,subscriptionId,amountBeforeDiscount,discountAmount,amountAfterDiscount,usedAt);

@override
String toString() {
  return 'DiscountUsage(id: $id, discountId: $discountId, discountCodeId: $discountCodeId, userId: $userId, subscriptionId: $subscriptionId, amountBeforeDiscount: $amountBeforeDiscount, discountAmount: $discountAmount, amountAfterDiscount: $amountAfterDiscount, usedAt: $usedAt)';
}


}

/// @nodoc
abstract mixin class $DiscountUsageCopyWith<$Res>  {
  factory $DiscountUsageCopyWith(DiscountUsage value, $Res Function(DiscountUsage) _then) = _$DiscountUsageCopyWithImpl;
@useResult
$Res call({
 int id, int discountId, int? discountCodeId, String userId, int? subscriptionId, double amountBeforeDiscount, double discountAmount, double amountAfterDiscount, DateTime usedAt
});




}
/// @nodoc
class _$DiscountUsageCopyWithImpl<$Res>
    implements $DiscountUsageCopyWith<$Res> {
  _$DiscountUsageCopyWithImpl(this._self, this._then);

  final DiscountUsage _self;
  final $Res Function(DiscountUsage) _then;

/// Create a copy of DiscountUsage
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? discountId = null,Object? discountCodeId = freezed,Object? userId = null,Object? subscriptionId = freezed,Object? amountBeforeDiscount = null,Object? discountAmount = null,Object? amountAfterDiscount = null,Object? usedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,discountId: null == discountId ? _self.discountId : discountId // ignore: cast_nullable_to_non_nullable
as int,discountCodeId: freezed == discountCodeId ? _self.discountCodeId : discountCodeId // ignore: cast_nullable_to_non_nullable
as int?,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,subscriptionId: freezed == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as int?,amountBeforeDiscount: null == amountBeforeDiscount ? _self.amountBeforeDiscount : amountBeforeDiscount // ignore: cast_nullable_to_non_nullable
as double,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as double,amountAfterDiscount: null == amountAfterDiscount ? _self.amountAfterDiscount : amountAfterDiscount // ignore: cast_nullable_to_non_nullable
as double,usedAt: null == usedAt ? _self.usedAt : usedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [DiscountUsage].
extension DiscountUsagePatterns on DiscountUsage {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DiscountUsage value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DiscountUsage() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DiscountUsage value)  $default,){
final _that = this;
switch (_that) {
case _DiscountUsage():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DiscountUsage value)?  $default,){
final _that = this;
switch (_that) {
case _DiscountUsage() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  int discountId,  int? discountCodeId,  String userId,  int? subscriptionId,  double amountBeforeDiscount,  double discountAmount,  double amountAfterDiscount,  DateTime usedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DiscountUsage() when $default != null:
return $default(_that.id,_that.discountId,_that.discountCodeId,_that.userId,_that.subscriptionId,_that.amountBeforeDiscount,_that.discountAmount,_that.amountAfterDiscount,_that.usedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  int discountId,  int? discountCodeId,  String userId,  int? subscriptionId,  double amountBeforeDiscount,  double discountAmount,  double amountAfterDiscount,  DateTime usedAt)  $default,) {final _that = this;
switch (_that) {
case _DiscountUsage():
return $default(_that.id,_that.discountId,_that.discountCodeId,_that.userId,_that.subscriptionId,_that.amountBeforeDiscount,_that.discountAmount,_that.amountAfterDiscount,_that.usedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  int discountId,  int? discountCodeId,  String userId,  int? subscriptionId,  double amountBeforeDiscount,  double discountAmount,  double amountAfterDiscount,  DateTime usedAt)?  $default,) {final _that = this;
switch (_that) {
case _DiscountUsage() when $default != null:
return $default(_that.id,_that.discountId,_that.discountCodeId,_that.userId,_that.subscriptionId,_that.amountBeforeDiscount,_that.discountAmount,_that.amountAfterDiscount,_that.usedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _DiscountUsage implements DiscountUsage {
  const _DiscountUsage({required this.id, required this.discountId, this.discountCodeId, required this.userId, this.subscriptionId, required this.amountBeforeDiscount, required this.discountAmount, required this.amountAfterDiscount, required this.usedAt});
  factory _DiscountUsage.fromJson(Map<String, dynamic> json) => _$DiscountUsageFromJson(json);

@override final  int id;
@override final  int discountId;
@override final  int? discountCodeId;
@override final  String userId;
@override final  int? subscriptionId;
@override final  double amountBeforeDiscount;
@override final  double discountAmount;
@override final  double amountAfterDiscount;
@override final  DateTime usedAt;

/// Create a copy of DiscountUsage
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DiscountUsageCopyWith<_DiscountUsage> get copyWith => __$DiscountUsageCopyWithImpl<_DiscountUsage>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DiscountUsageToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DiscountUsage&&(identical(other.id, id) || other.id == id)&&(identical(other.discountId, discountId) || other.discountId == discountId)&&(identical(other.discountCodeId, discountCodeId) || other.discountCodeId == discountCodeId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.subscriptionId, subscriptionId) || other.subscriptionId == subscriptionId)&&(identical(other.amountBeforeDiscount, amountBeforeDiscount) || other.amountBeforeDiscount == amountBeforeDiscount)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.amountAfterDiscount, amountAfterDiscount) || other.amountAfterDiscount == amountAfterDiscount)&&(identical(other.usedAt, usedAt) || other.usedAt == usedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,discountId,discountCodeId,userId,subscriptionId,amountBeforeDiscount,discountAmount,amountAfterDiscount,usedAt);

@override
String toString() {
  return 'DiscountUsage(id: $id, discountId: $discountId, discountCodeId: $discountCodeId, userId: $userId, subscriptionId: $subscriptionId, amountBeforeDiscount: $amountBeforeDiscount, discountAmount: $discountAmount, amountAfterDiscount: $amountAfterDiscount, usedAt: $usedAt)';
}


}

/// @nodoc
abstract mixin class _$DiscountUsageCopyWith<$Res> implements $DiscountUsageCopyWith<$Res> {
  factory _$DiscountUsageCopyWith(_DiscountUsage value, $Res Function(_DiscountUsage) _then) = __$DiscountUsageCopyWithImpl;
@override @useResult
$Res call({
 int id, int discountId, int? discountCodeId, String userId, int? subscriptionId, double amountBeforeDiscount, double discountAmount, double amountAfterDiscount, DateTime usedAt
});




}
/// @nodoc
class __$DiscountUsageCopyWithImpl<$Res>
    implements _$DiscountUsageCopyWith<$Res> {
  __$DiscountUsageCopyWithImpl(this._self, this._then);

  final _DiscountUsage _self;
  final $Res Function(_DiscountUsage) _then;

/// Create a copy of DiscountUsage
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? discountId = null,Object? discountCodeId = freezed,Object? userId = null,Object? subscriptionId = freezed,Object? amountBeforeDiscount = null,Object? discountAmount = null,Object? amountAfterDiscount = null,Object? usedAt = null,}) {
  return _then(_DiscountUsage(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,discountId: null == discountId ? _self.discountId : discountId // ignore: cast_nullable_to_non_nullable
as int,discountCodeId: freezed == discountCodeId ? _self.discountCodeId : discountCodeId // ignore: cast_nullable_to_non_nullable
as int?,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,subscriptionId: freezed == subscriptionId ? _self.subscriptionId : subscriptionId // ignore: cast_nullable_to_non_nullable
as int?,amountBeforeDiscount: null == amountBeforeDiscount ? _self.amountBeforeDiscount : amountBeforeDiscount // ignore: cast_nullable_to_non_nullable
as double,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as double,amountAfterDiscount: null == amountAfterDiscount ? _self.amountAfterDiscount : amountAfterDiscount // ignore: cast_nullable_to_non_nullable
as double,usedAt: null == usedAt ? _self.usedAt : usedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$DiscountCalculation {

 double get originalAmount; double get discountAmount; double get finalAmount; double get discountPercentage; Discount? get appliedDiscount; DiscountCode? get appliedCode;
/// Create a copy of DiscountCalculation
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DiscountCalculationCopyWith<DiscountCalculation> get copyWith => _$DiscountCalculationCopyWithImpl<DiscountCalculation>(this as DiscountCalculation, _$identity);

  /// Serializes this DiscountCalculation to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DiscountCalculation&&(identical(other.originalAmount, originalAmount) || other.originalAmount == originalAmount)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.finalAmount, finalAmount) || other.finalAmount == finalAmount)&&(identical(other.discountPercentage, discountPercentage) || other.discountPercentage == discountPercentage)&&(identical(other.appliedDiscount, appliedDiscount) || other.appliedDiscount == appliedDiscount)&&(identical(other.appliedCode, appliedCode) || other.appliedCode == appliedCode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,originalAmount,discountAmount,finalAmount,discountPercentage,appliedDiscount,appliedCode);

@override
String toString() {
  return 'DiscountCalculation(originalAmount: $originalAmount, discountAmount: $discountAmount, finalAmount: $finalAmount, discountPercentage: $discountPercentage, appliedDiscount: $appliedDiscount, appliedCode: $appliedCode)';
}


}

/// @nodoc
abstract mixin class $DiscountCalculationCopyWith<$Res>  {
  factory $DiscountCalculationCopyWith(DiscountCalculation value, $Res Function(DiscountCalculation) _then) = _$DiscountCalculationCopyWithImpl;
@useResult
$Res call({
 double originalAmount, double discountAmount, double finalAmount, double discountPercentage, Discount? appliedDiscount, DiscountCode? appliedCode
});


$DiscountCopyWith<$Res>? get appliedDiscount;$DiscountCodeCopyWith<$Res>? get appliedCode;

}
/// @nodoc
class _$DiscountCalculationCopyWithImpl<$Res>
    implements $DiscountCalculationCopyWith<$Res> {
  _$DiscountCalculationCopyWithImpl(this._self, this._then);

  final DiscountCalculation _self;
  final $Res Function(DiscountCalculation) _then;

/// Create a copy of DiscountCalculation
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? originalAmount = null,Object? discountAmount = null,Object? finalAmount = null,Object? discountPercentage = null,Object? appliedDiscount = freezed,Object? appliedCode = freezed,}) {
  return _then(_self.copyWith(
originalAmount: null == originalAmount ? _self.originalAmount : originalAmount // ignore: cast_nullable_to_non_nullable
as double,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as double,finalAmount: null == finalAmount ? _self.finalAmount : finalAmount // ignore: cast_nullable_to_non_nullable
as double,discountPercentage: null == discountPercentage ? _self.discountPercentage : discountPercentage // ignore: cast_nullable_to_non_nullable
as double,appliedDiscount: freezed == appliedDiscount ? _self.appliedDiscount : appliedDiscount // ignore: cast_nullable_to_non_nullable
as Discount?,appliedCode: freezed == appliedCode ? _self.appliedCode : appliedCode // ignore: cast_nullable_to_non_nullable
as DiscountCode?,
  ));
}
/// Create a copy of DiscountCalculation
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DiscountCopyWith<$Res>? get appliedDiscount {
    if (_self.appliedDiscount == null) {
    return null;
  }

  return $DiscountCopyWith<$Res>(_self.appliedDiscount!, (value) {
    return _then(_self.copyWith(appliedDiscount: value));
  });
}/// Create a copy of DiscountCalculation
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DiscountCodeCopyWith<$Res>? get appliedCode {
    if (_self.appliedCode == null) {
    return null;
  }

  return $DiscountCodeCopyWith<$Res>(_self.appliedCode!, (value) {
    return _then(_self.copyWith(appliedCode: value));
  });
}
}


/// Adds pattern-matching-related methods to [DiscountCalculation].
extension DiscountCalculationPatterns on DiscountCalculation {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DiscountCalculation value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DiscountCalculation() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DiscountCalculation value)  $default,){
final _that = this;
switch (_that) {
case _DiscountCalculation():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DiscountCalculation value)?  $default,){
final _that = this;
switch (_that) {
case _DiscountCalculation() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double originalAmount,  double discountAmount,  double finalAmount,  double discountPercentage,  Discount? appliedDiscount,  DiscountCode? appliedCode)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DiscountCalculation() when $default != null:
return $default(_that.originalAmount,_that.discountAmount,_that.finalAmount,_that.discountPercentage,_that.appliedDiscount,_that.appliedCode);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double originalAmount,  double discountAmount,  double finalAmount,  double discountPercentage,  Discount? appliedDiscount,  DiscountCode? appliedCode)  $default,) {final _that = this;
switch (_that) {
case _DiscountCalculation():
return $default(_that.originalAmount,_that.discountAmount,_that.finalAmount,_that.discountPercentage,_that.appliedDiscount,_that.appliedCode);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double originalAmount,  double discountAmount,  double finalAmount,  double discountPercentage,  Discount? appliedDiscount,  DiscountCode? appliedCode)?  $default,) {final _that = this;
switch (_that) {
case _DiscountCalculation() when $default != null:
return $default(_that.originalAmount,_that.discountAmount,_that.finalAmount,_that.discountPercentage,_that.appliedDiscount,_that.appliedCode);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _DiscountCalculation implements DiscountCalculation {
  const _DiscountCalculation({required this.originalAmount, required this.discountAmount, required this.finalAmount, required this.discountPercentage, this.appliedDiscount, this.appliedCode});
  factory _DiscountCalculation.fromJson(Map<String, dynamic> json) => _$DiscountCalculationFromJson(json);

@override final  double originalAmount;
@override final  double discountAmount;
@override final  double finalAmount;
@override final  double discountPercentage;
@override final  Discount? appliedDiscount;
@override final  DiscountCode? appliedCode;

/// Create a copy of DiscountCalculation
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DiscountCalculationCopyWith<_DiscountCalculation> get copyWith => __$DiscountCalculationCopyWithImpl<_DiscountCalculation>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DiscountCalculationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DiscountCalculation&&(identical(other.originalAmount, originalAmount) || other.originalAmount == originalAmount)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.finalAmount, finalAmount) || other.finalAmount == finalAmount)&&(identical(other.discountPercentage, discountPercentage) || other.discountPercentage == discountPercentage)&&(identical(other.appliedDiscount, appliedDiscount) || other.appliedDiscount == appliedDiscount)&&(identical(other.appliedCode, appliedCode) || other.appliedCode == appliedCode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,originalAmount,discountAmount,finalAmount,discountPercentage,appliedDiscount,appliedCode);

@override
String toString() {
  return 'DiscountCalculation(originalAmount: $originalAmount, discountAmount: $discountAmount, finalAmount: $finalAmount, discountPercentage: $discountPercentage, appliedDiscount: $appliedDiscount, appliedCode: $appliedCode)';
}


}

/// @nodoc
abstract mixin class _$DiscountCalculationCopyWith<$Res> implements $DiscountCalculationCopyWith<$Res> {
  factory _$DiscountCalculationCopyWith(_DiscountCalculation value, $Res Function(_DiscountCalculation) _then) = __$DiscountCalculationCopyWithImpl;
@override @useResult
$Res call({
 double originalAmount, double discountAmount, double finalAmount, double discountPercentage, Discount? appliedDiscount, DiscountCode? appliedCode
});


@override $DiscountCopyWith<$Res>? get appliedDiscount;@override $DiscountCodeCopyWith<$Res>? get appliedCode;

}
/// @nodoc
class __$DiscountCalculationCopyWithImpl<$Res>
    implements _$DiscountCalculationCopyWith<$Res> {
  __$DiscountCalculationCopyWithImpl(this._self, this._then);

  final _DiscountCalculation _self;
  final $Res Function(_DiscountCalculation) _then;

/// Create a copy of DiscountCalculation
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? originalAmount = null,Object? discountAmount = null,Object? finalAmount = null,Object? discountPercentage = null,Object? appliedDiscount = freezed,Object? appliedCode = freezed,}) {
  return _then(_DiscountCalculation(
originalAmount: null == originalAmount ? _self.originalAmount : originalAmount // ignore: cast_nullable_to_non_nullable
as double,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as double,finalAmount: null == finalAmount ? _self.finalAmount : finalAmount // ignore: cast_nullable_to_non_nullable
as double,discountPercentage: null == discountPercentage ? _self.discountPercentage : discountPercentage // ignore: cast_nullable_to_non_nullable
as double,appliedDiscount: freezed == appliedDiscount ? _self.appliedDiscount : appliedDiscount // ignore: cast_nullable_to_non_nullable
as Discount?,appliedCode: freezed == appliedCode ? _self.appliedCode : appliedCode // ignore: cast_nullable_to_non_nullable
as DiscountCode?,
  ));
}

/// Create a copy of DiscountCalculation
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DiscountCopyWith<$Res>? get appliedDiscount {
    if (_self.appliedDiscount == null) {
    return null;
  }

  return $DiscountCopyWith<$Res>(_self.appliedDiscount!, (value) {
    return _then(_self.copyWith(appliedDiscount: value));
  });
}/// Create a copy of DiscountCalculation
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DiscountCodeCopyWith<$Res>? get appliedCode {
    if (_self.appliedCode == null) {
    return null;
  }

  return $DiscountCodeCopyWith<$Res>(_self.appliedCode!, (value) {
    return _then(_self.copyWith(appliedCode: value));
  });
}
}

// dart format on
