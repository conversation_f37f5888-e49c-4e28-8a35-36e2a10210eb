// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'discount_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Discount _$DiscountFromJson(Map<String, dynamic> json) => _Discount(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  nameAr: json['name_ar'] as String,
  description: json['description'] as String?,
  descriptionAr: json['description_ar'] as String?,
  discountType: json['discount_type'] as String,
  discountValue: (json['discount_value'] as num).toDouble(),
  minAmount: (json['min_amount'] as num?)?.toDouble() ?? 0.0,
  maxDiscount: (json['max_discount'] as num?)?.toDouble(),
  applicableTo:
      (json['applicable_to'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  startDate: DateTime.parse(json['start_date'] as String),
  endDate: json['end_date'] == null
      ? null
      : DateTime.parse(json['end_date'] as String),
  isActive: json['is_active'] as bool? ?? true,
  usageLimit: (json['usage_limit'] as num?)?.toInt(),
  usedCount: (json['used_count'] as num?)?.toInt() ?? 0,
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$DiscountToJson(_Discount instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'name_ar': instance.nameAr,
  'description': instance.description,
  'description_ar': instance.descriptionAr,
  'discount_type': instance.discountType,
  'discount_value': instance.discountValue,
  'min_amount': instance.minAmount,
  'max_discount': instance.maxDiscount,
  'applicable_to': instance.applicableTo,
  'start_date': instance.startDate.toIso8601String(),
  'end_date': instance.endDate?.toIso8601String(),
  'is_active': instance.isActive,
  'usage_limit': instance.usageLimit,
  'used_count': instance.usedCount,
  'created_at': instance.createdAt?.toIso8601String(),
  'updated_at': instance.updatedAt?.toIso8601String(),
};

_DiscountCode _$DiscountCodeFromJson(Map<String, dynamic> json) =>
    _DiscountCode(
      id: (json['id'] as num).toInt(),
      code: json['code'] as String,
      discountId: (json['discount_id'] as num).toInt(),
      discount: Discount.fromJson(json['discount'] as Map<String, dynamic>),
      isActive: json['is_active'] as bool? ?? true,
      usageLimit: (json['usage_limit'] as num?)?.toInt(),
      usedCount: (json['used_count'] as num?)?.toInt() ?? 0,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$DiscountCodeToJson(_DiscountCode instance) =>
    <String, dynamic>{
      'id': instance.id,
      'code': instance.code,
      'discount_id': instance.discountId,
      'discount': instance.discount,
      'is_active': instance.isActive,
      'usage_limit': instance.usageLimit,
      'used_count': instance.usedCount,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };

_DiscountUsage _$DiscountUsageFromJson(Map<String, dynamic> json) =>
    _DiscountUsage(
      id: (json['id'] as num).toInt(),
      discountId: (json['discount_id'] as num).toInt(),
      discountCodeId: (json['discount_code_id'] as num?)?.toInt(),
      userId: json['user_id'] as String,
      subscriptionId: (json['subscription_id'] as num?)?.toInt(),
      amountBeforeDiscount: (json['amount_before_discount'] as num).toDouble(),
      discountAmount: (json['discount_amount'] as num).toDouble(),
      amountAfterDiscount: (json['amount_after_discount'] as num).toDouble(),
      usedAt: DateTime.parse(json['used_at'] as String),
    );

Map<String, dynamic> _$DiscountUsageToJson(_DiscountUsage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'discount_id': instance.discountId,
      'discount_code_id': instance.discountCodeId,
      'user_id': instance.userId,
      'subscription_id': instance.subscriptionId,
      'amount_before_discount': instance.amountBeforeDiscount,
      'discount_amount': instance.discountAmount,
      'amount_after_discount': instance.amountAfterDiscount,
      'used_at': instance.usedAt.toIso8601String(),
    };

_DiscountCalculation _$DiscountCalculationFromJson(Map<String, dynamic> json) =>
    _DiscountCalculation(
      originalAmount: (json['original_amount'] as num).toDouble(),
      discountAmount: (json['discount_amount'] as num).toDouble(),
      finalAmount: (json['final_amount'] as num).toDouble(),
      discountPercentage: (json['discount_percentage'] as num).toDouble(),
      appliedDiscount: json['applied_discount'] == null
          ? null
          : Discount.fromJson(json['applied_discount'] as Map<String, dynamic>),
      appliedCode: json['applied_code'] == null
          ? null
          : DiscountCode.fromJson(json['applied_code'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DiscountCalculationToJson(
  _DiscountCalculation instance,
) => <String, dynamic>{
  'original_amount': instance.originalAmount,
  'discount_amount': instance.discountAmount,
  'final_amount': instance.finalAmount,
  'discount_percentage': instance.discountPercentage,
  'applied_discount': instance.appliedDiscount,
  'applied_code': instance.appliedCode,
};
