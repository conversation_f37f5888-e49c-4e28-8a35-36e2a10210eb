import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:carnow/features/products/models/product_model.dart';
import '../../../core/models/enums.dart';
import '../../../core/utils/unified_logger.dart';
import '../../../core/auth/unified_auth_provider.dart';
import '../models/product_comparison.dart';

part 'comparison_provider.g.dart';

@riverpod
class ComparisonManager extends _$ComparisonManager {
  @override
  Future<List<ProductComparison>> build() async {
    return [];
  }

  /// إضافة منتج للمقارنة
  Future<bool> addProductToComparison(ProductModel product) async {
    try {
      UnifiedLogger.info(
        'Adding product to comparison: ${product.id}',
        tag: 'ComparisonManager',
      );

      final currentComparisons = await future;
      final activeComparison = _getOrCreateActiveComparison(currentComparisons);

      // التحقق من عدم وجود المنتج مسبقاً
      if (activeComparison.products?.any((p) => p.id == product.id) == true) {
        UnifiedLogger.warning(
          'Product already in comparison: ${product.id}',
          tag: 'ComparisonManager',
        );
        return false;
      }

      // التحقق من الحد الأقصى للمقارنة (4 منتجات)
      if ((activeComparison.products?.length ?? 0) >= 4) {
        UnifiedLogger.warning(
          'Maximum comparison limit reached',
          tag: 'ComparisonManager',
        );
        return false;
      }

      final updatedProducts = [...(activeComparison.products ?? []), product];
      final updatedComparison = activeComparison.copyWith(
        products: updatedProducts,
        updatedAt: DateTime.now(),
      );

      await _updateComparison(updatedComparison);
      UnifiedLogger.info(
        'Product added to comparison successfully',
        tag: 'ComparisonManager',
      );
      return true;
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to add product to comparison',
        tag: 'ComparisonManager',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// إزالة منتج من المقارنة
  Future<bool> removeProductFromComparison(String productId) async {
    try {
      UnifiedLogger.info(
        'Removing product from comparison: $productId',
        tag: 'ComparisonManager',
      );

      final currentComparisons = await future;
      final activeComparison = _getActiveComparison(currentComparisons);

      if (activeComparison == null) {
        return false;
      }

      final updatedProducts =
          activeComparison.products?.where((p) => p.id != productId).toList() ??
          [];

      final updatedComparison = activeComparison.copyWith(
        products: updatedProducts,
        updatedAt: DateTime.now(),
      );

      await _updateComparison(updatedComparison);
      UnifiedLogger.info(
        'Product removed from comparison successfully',
        tag: 'ComparisonManager',
      );
      return true;
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to remove product from comparison',
        tag: 'ComparisonManager',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// إجراء المقارنة الذكية
  Future<Map<String, dynamic>?> performSmartComparison(
    List<ProductModel> products,
  ) async {
    try {
      UnifiedLogger.info(
        'Performing smart comparison for ${products.length} products',
        tag: 'ComparisonManager',
      );

      if (products.length < 2) {
        UnifiedLogger.warning(
          'Need at least 2 products for comparison',
          tag: 'ComparisonManager',
        );
        return null;
      }

      final comparison = {
        'products': products.map((p) => p.toJson()).toList(),
        'criteria': _generateComparisonCriteria(products),
        'scores': _calculateComparisonScores(products),
        'insights': _generateInsights(products),
        'recommendation': _generateRecommendation(products),
        'createdAt': DateTime.now().toIso8601String(),
      };

      UnifiedLogger.info(
        'Smart comparison completed successfully',
        tag: 'ComparisonManager',
      );
      return comparison;
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to perform smart comparison',
        tag: 'ComparisonManager',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// حفظ المقارنة
  Future<ProductComparison?> saveComparison({
    required List<ProductModel> products,
    required String title,
    String? description,
    bool isPublic = false,
  }) async {
    try {
      UnifiedLogger.info('Saving comparison: $title', tag: 'ComparisonManager');

      final comparison = ProductComparison(
        id: _generateComparisonId(),
        userId: _getCurrentUserId(),
        products: products,
        criteria: _generateComparisonCriteria(products),
        scores: _calculateDetailedScores(products),
        title: title,
        description: description,
        type: ComparisonType.saved,
        isPublic: isPublic,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final currentComparisons = await future;
      final updatedComparisons = [...currentComparisons, comparison];
      state = AsyncValue.data(updatedComparisons);

      UnifiedLogger.info(
        'Comparison saved successfully: ${comparison.id}',
        tag: 'ComparisonManager',
      );
      return comparison;
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to save comparison',
        tag: 'ComparisonManager',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// مشاركة المقارنة
  Future<String?> shareComparison(String comparisonId) async {
    try {
      UnifiedLogger.info(
        'Sharing comparison: $comparisonId',
        tag: 'ComparisonManager',
      );

      // إنشاء رابط المشاركة
      final shareUrl = 'https://carnow.app/compare/$comparisonId';

      UnifiedLogger.info(
        'Comparison share URL generated: $shareUrl',
        tag: 'ComparisonManager',
      );
      return shareUrl;
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to share comparison',
        tag: 'ComparisonManager',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  // Helper Methods
  ProductComparison _getOrCreateActiveComparison(
    List<ProductComparison> comparisons,
  ) {
    final activeComparison = _getActiveComparison(comparisons);
    if (activeComparison != null) {
      return activeComparison;
    }

    return ProductComparison(
      id: _generateComparisonId(),
      userId: _getCurrentUserId(),
      products: [],
      criteria: [],
      scores: {},
      type: ComparisonType.active,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  ProductComparison? _getActiveComparison(List<ProductComparison> comparisons) {
    try {
      return comparisons.firstWhere((c) => c.type == ComparisonType.active);
    } catch (e) {
      return null;
    }
  }

  Future<void> _updateComparison(ProductComparison comparison) async {
    final currentComparisons = await future;
    final updatedComparisons = currentComparisons
        .map((c) => c.id == comparison.id ? comparison : c)
        .toList();

    state = AsyncValue.data(updatedComparisons);
  }

  List<ComparisonCriteria> _generateComparisonCriteria(
    List<ProductModel> products,
  ) {
    return [
      const ComparisonCriteria(
        id: 'price',
        name: 'السعر',
        type: ComparisonCriteriaType.price,
        weight: 1,
        isHigherBetter: false,
        description: 'سعر المنتج',
        unit: 'LYD',
      ),
      const ComparisonCriteria(
        id: 'condition',
        name: 'الحالة',
        type: ComparisonCriteriaType.categorical,
        weight: 1.2,
        isHigherBetter: true,
        description: 'حالة المنتج',
      ),
      const ComparisonCriteria(
        id: 'availability',
        name: 'التوفر',
        type: ComparisonCriteriaType.boolean,
        weight: 0.8,
        isHigherBetter: true,
        description: 'توفر المنتج',
      ),
    ];
  }

  Map<String, ComparisonScore> _calculateComparisonScores(
    List<ProductModel> products,
  ) {
    final scores = <String, ComparisonScore>{};

    for (final product in products) {
      final criteriaScores = <String, double>{
        'price': _scorePricing(product, products),
        'condition': _scoreCondition(product.condition),
        'availability': product.isInStock ? 1.0 : 0.0,
      };

      final totalScore =
          criteriaScores.values.reduce((sum, score) => sum + score) /
          criteriaScores.length;

      scores[product.id] = ComparisonScore(
        productId: product.id,
        totalScore: totalScore,
        criteriaScores: criteriaScores,
        normalizedValues: {
          'price': product.price,
          'condition': product.condition.index.toDouble(),
          'availability': product.isInStock ? 1 : 0,
        },
        isHigherBetter: true,
      );
    }

    return scores;
  }

  Map<String, ComparisonScore> _calculateDetailedScores(
    List<ProductModel> products,
  ) {
    final scores = <String, ComparisonScore>{};

    for (final product in products) {
      final criteriaScores = <String, double>{
        'price': _scorePricing(product, products),
        'condition': _scoreCondition(product.condition),
        'availability': product.isInStock ? 1 : 0,
        'features': _scoreFeatures(product),
      };

      final totalScore =
          criteriaScores.values.reduce((sum, score) => sum + score) /
          criteriaScores.length;

      scores[product.id] = ComparisonScore(
        productId: product.id,
        totalScore: totalScore,
        criteriaScores: criteriaScores,
        normalizedValues: {
          'price': product.price,
          'condition': product.condition.index.toDouble(),
          'availability': product.isInStock ? 1.0 : 0.0,
          'features': product.specifications?.length.toDouble() ?? 0.0,
        },
        isHigherBetter: true,
        recommendation: _generateProductRecommendation(product, totalScore),
        pros: _generatePros(product),
        cons: _generateCons(product),
      );
    }

    return scores;
  }

  double _scorePricing(ProductModel product, List<ProductModel> allProducts) {
    final prices = allProducts.map((p) => p.price).toList();
    final minPrice = prices.reduce((a, b) => a < b ? a : b);
    final maxPrice = prices.reduce((a, b) => a > b ? a : b);

    if (maxPrice == minPrice) return 1;

    // كلما قل السعر، كلما زادت النقاط
    return 1.0 - ((product.price - minPrice) / (maxPrice - minPrice));
  }

  double _scoreCondition(ProductCondition condition) {
    switch (condition) {
      case ProductCondition.new_:
        return 1;
      case ProductCondition.likeNew:
        return 0.9;
      case ProductCondition.good:
        return 0.8;
      case ProductCondition.used:
        return 0.7;
      case ProductCondition.fair:
        return 0.5;
      case ProductCondition.poor:
        return 0.3;
      case ProductCondition.forParts:
        return 0.1;
      case ProductCondition.refurbished:
        return 0.8;
    }
  }

  double _scoreFeatures(ProductModel product) {
    final specCount = product.specifications?.length ?? 0;
    return (specCount / 10).clamp(0, 1); // تطبيع بين 0 و 1
  }

  String _generateProductRecommendation(ProductModel product, double score) {
    if (score >= 0.8) {
      return 'منتج ممتاز، ننصح بشرائه';
    } else if (score >= 0.6) {
      return 'منتج جيد، خيار مناسب';
    } else if (score >= 0.4) {
      return 'منتج متوسط، يحتاج مراجعة';
    } else {
      return 'منتج ضعيف، لا ننصح به';
    }
  }

  List<String> _generatePros(ProductModel product) {
    final pros = <String>[];

    if (product.isInStock) pros.add('متوفر');
    if (product.isFeatured) pros.add('منتج مميز');
    if (product.condition == ProductCondition.new_) pros.add('جديد');
    if (product.originalPrice != null &&
        product.originalPrice! > product.price) {
      pros.add(
        'خصم ${((product.originalPrice! - product.price) / product.originalPrice! * 100).toStringAsFixed(0)}%',
      );
    }

    return pros;
  }

  List<String> _generateCons(ProductModel product) {
    final cons = <String>[];

    if (!product.isInStock) cons.add('غير متوفر');
    if (product.condition == ProductCondition.poor) cons.add('حالة سيئة');
    if (product.stockQuantity == 0) cons.add('نفدت الكمية');

    return cons;
  }

  Map<String, dynamic> _generateInsights(List<ProductModel> products) {
    return {
      'totalProducts': products.length,
      'priceRange': {
        'min': products
            .map((p) => p.price)
            .reduce((min, price) => price < min ? price : min),
        'max': products
            .map((p) => p.price)
            .reduce((max, price) => price > max ? price : max),
      },
      'conditions': products.map((p) => p.condition.name).toSet().toList(),
      'brands': products.map((p) => p.brand ?? 'غير محدد').toSet().toList(),
    };
  }

  String _generateRecommendation(List<ProductModel> products) {
    final scores = _calculateComparisonScores(products);
    final sortedScores = scores.entries.toList()
      ..sort((a, b) => b.value.totalScore.compareTo(a.value.totalScore));

    final winnerId = sortedScores.first.key;
    final winner = products.firstWhere((p) => p.id == winnerId);

    return 'ننصح بـ ${winner.name} بناءً على المقارنة الشاملة';
  }

  String _generateComparisonId() {
    return 'comp_${DateTime.now().millisecondsSinceEpoch}';
  }

  String _getCurrentUserId() {
    // Get current user ID from authentication system
    final currentUser = ref.read(currentUserProvider);
    return currentUser?.id ?? 'guest_user';
  }
}

// Providers للوصول السريع
@riverpod
Future<ProductComparison?> activeComparison(Ref ref) async {
  final comparisons = await ref.watch(comparisonManagerProvider.future);
  try {
    return comparisons.firstWhere((c) => c.type == ComparisonType.active);
  } catch (e) {
    return null;
  }
}

@riverpod
Future<List<ProductComparison>> savedComparisons(Ref ref) async {
  final comparisons = await ref.watch(comparisonManagerProvider.future);
  return comparisons.where((c) => c.type == ComparisonType.saved).toList();
}
