// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in carnow/test/features/admin_tools/vehicles/add_car_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:carnow/core/errors/result.dart' as _i7;
import 'package:carnow/core/networking/simple_api_client.dart' as _i3;
import 'package:carnow/features/admin_tools/vehicles/models/vehicle_generation.dart'
    as _i12;
import 'package:carnow/features/admin_tools/vehicles/models/vehicle_make.dart'
    as _i8;
import 'package:carnow/features/admin_tools/vehicles/models/vehicle_model.dart'
    as _i10;
import 'package:carnow/features/admin_tools/vehicles/models/vehicle_trim.dart'
    as _i14;
import 'package:carnow/features/admin_tools/vehicles/repositories/vehicle_generation_repository.dart'
    as _i11;
import 'package:carnow/features/admin_tools/vehicles/repositories/vehicle_make_repository.dart'
    as _i4;
import 'package:carnow/features/admin_tools/vehicles/repositories/vehicle_model_repository.dart'
    as _i9;
import 'package:carnow/features/admin_tools/vehicles/repositories/vehicle_trim_repository.dart'
    as _i13;
import 'package:flutter_riverpod/flutter_riverpod.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeRef_0<State extends Object?> extends _i1.SmartFake
    implements _i2.Ref<State> {
  _FakeRef_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSimpleApiClient_1 extends _i1.SmartFake
    implements _i3.SimpleApiClient {
  _FakeSimpleApiClient_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [VehicleMakeRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockVehicleMakeRepository extends _i1.Mock
    implements _i4.VehicleMakeRepository {
  MockVehicleMakeRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get apiPath =>
      (super.noSuchMethod(
            Invocation.getter(#apiPath),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.getter(#apiPath),
            ),
          )
          as String);

  @override
  _i2.Ref<Object?> get ref =>
      (super.noSuchMethod(
            Invocation.getter(#ref),
            returnValue: _FakeRef_0<Object?>(this, Invocation.getter(#ref)),
          )
          as _i2.Ref<Object?>);

  @override
  _i3.SimpleApiClient get apiClient =>
      (super.noSuchMethod(
            Invocation.getter(#apiClient),
            returnValue: _FakeSimpleApiClient_1(
              this,
              Invocation.getter(#apiClient),
            ),
          )
          as _i3.SimpleApiClient);

  @override
  _i6.Future<_i7.Result<List<_i8.VehicleMake>>> getMakes() =>
      (super.noSuchMethod(
            Invocation.method(#getMakes, []),
            returnValue: _i6.Future<_i7.Result<List<_i8.VehicleMake>>>.value(
              _i5.dummyValue<_i7.Result<List<_i8.VehicleMake>>>(
                this,
                Invocation.method(#getMakes, []),
              ),
            ),
          )
          as _i6.Future<_i7.Result<List<_i8.VehicleMake>>>);

  @override
  _i6.Future<_i7.Result<_i8.VehicleMake>> createMake(_i8.VehicleMake? make) =>
      (super.noSuchMethod(
            Invocation.method(#createMake, [make]),
            returnValue: _i6.Future<_i7.Result<_i8.VehicleMake>>.value(
              _i5.dummyValue<_i7.Result<_i8.VehicleMake>>(
                this,
                Invocation.method(#createMake, [make]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<_i8.VehicleMake>>);

  @override
  _i6.Future<_i7.Result<_i8.VehicleMake>> updateMake(
    String? id,
    _i8.VehicleMake? make,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateMake, [id, make]),
            returnValue: _i6.Future<_i7.Result<_i8.VehicleMake>>.value(
              _i5.dummyValue<_i7.Result<_i8.VehicleMake>>(
                this,
                Invocation.method(#updateMake, [id, make]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<_i8.VehicleMake>>);

  @override
  _i6.Future<_i7.Result<void>> deleteMake(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteMake, [id]),
            returnValue: _i6.Future<_i7.Result<void>>.value(
              _i5.dummyValue<_i7.Result<void>>(
                this,
                Invocation.method(#deleteMake, [id]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<List<_i8.VehicleMake>>> searchMakes(String? query) =>
      (super.noSuchMethod(
            Invocation.method(#searchMakes, [query]),
            returnValue: _i6.Future<_i7.Result<List<_i8.VehicleMake>>>.value(
              _i5.dummyValue<_i7.Result<List<_i8.VehicleMake>>>(
                this,
                Invocation.method(#searchMakes, [query]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<List<_i8.VehicleMake>>>);

  @override
  _i6.Future<_i7.Result<T>> executeWithErrorHandling<T>(
    _i6.Future<T> Function()? operation, {
    String? operationName,
    bool? shouldLog = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #executeWithErrorHandling,
              [operation],
              {#operationName: operationName, #shouldLog: shouldLog},
            ),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(
                  #executeWithErrorHandling,
                  [operation],
                  {#operationName: operationName, #shouldLog: shouldLog},
                ),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<T>> getById<T>(
    String? id, {
    required T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic>? queryParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getById,
              [id],
              {#fromJson: fromJson, #queryParams: queryParams},
            ),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(
                  #getById,
                  [id],
                  {#fromJson: fromJson, #queryParams: queryParams},
                ),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<List<T>>> getAll<T>({
    required T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic>? queryParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAll, [], {
              #fromJson: fromJson,
              #queryParams: queryParams,
            }),
            returnValue: _i6.Future<_i7.Result<List<T>>>.value(
              _i5.dummyValue<_i7.Result<List<T>>>(
                this,
                Invocation.method(#getAll, [], {
                  #fromJson: fromJson,
                  #queryParams: queryParams,
                }),
              ),
            ),
          )
          as _i6.Future<_i7.Result<List<T>>>);

  @override
  _i6.Future<_i7.Result<T>> create<T>(
    Map<String, dynamic>? data, {
    required T Function(Map<String, dynamic>)? fromJson,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#create, [data], {#fromJson: fromJson}),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(#create, [data], {#fromJson: fromJson}),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<T>> update<T>(
    String? id,
    Map<String, dynamic>? data, {
    required T Function(Map<String, dynamic>)? fromJson,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#update, [id, data], {#fromJson: fromJson}),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(#update, [id, data], {#fromJson: fromJson}),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<void>> delete(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#delete, [id]),
            returnValue: _i6.Future<_i7.Result<void>>.value(
              _i5.dummyValue<_i7.Result<void>>(
                this,
                Invocation.method(#delete, [id]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<void>>);
}

/// A class which mocks [VehicleModelRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockVehicleModelRepository extends _i1.Mock
    implements _i9.VehicleModelRepository {
  MockVehicleModelRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get apiPath =>
      (super.noSuchMethod(
            Invocation.getter(#apiPath),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.getter(#apiPath),
            ),
          )
          as String);

  @override
  _i2.Ref<Object?> get ref =>
      (super.noSuchMethod(
            Invocation.getter(#ref),
            returnValue: _FakeRef_0<Object?>(this, Invocation.getter(#ref)),
          )
          as _i2.Ref<Object?>);

  @override
  _i3.SimpleApiClient get apiClient =>
      (super.noSuchMethod(
            Invocation.getter(#apiClient),
            returnValue: _FakeSimpleApiClient_1(
              this,
              Invocation.getter(#apiClient),
            ),
          )
          as _i3.SimpleApiClient);

  @override
  _i6.Future<_i7.Result<List<_i10.VehicleModel>>> getModels({int? makeId}) =>
      (super.noSuchMethod(
            Invocation.method(#getModels, [], {#makeId: makeId}),
            returnValue: _i6.Future<_i7.Result<List<_i10.VehicleModel>>>.value(
              _i5.dummyValue<_i7.Result<List<_i10.VehicleModel>>>(
                this,
                Invocation.method(#getModels, [], {#makeId: makeId}),
              ),
            ),
          )
          as _i6.Future<_i7.Result<List<_i10.VehicleModel>>>);

  @override
  _i6.Future<_i7.Result<_i10.VehicleModel>> createModel(
    _i10.VehicleModel? model,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createModel, [model]),
            returnValue: _i6.Future<_i7.Result<_i10.VehicleModel>>.value(
              _i5.dummyValue<_i7.Result<_i10.VehicleModel>>(
                this,
                Invocation.method(#createModel, [model]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<_i10.VehicleModel>>);

  @override
  _i6.Future<_i7.Result<_i10.VehicleModel>> updateModel(
    String? id,
    _i10.VehicleModel? model,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateModel, [id, model]),
            returnValue: _i6.Future<_i7.Result<_i10.VehicleModel>>.value(
              _i5.dummyValue<_i7.Result<_i10.VehicleModel>>(
                this,
                Invocation.method(#updateModel, [id, model]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<_i10.VehicleModel>>);

  @override
  _i6.Future<_i7.Result<void>> deleteModel(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteModel, [id]),
            returnValue: _i6.Future<_i7.Result<void>>.value(
              _i5.dummyValue<_i7.Result<void>>(
                this,
                Invocation.method(#deleteModel, [id]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<List<_i10.VehicleModel>>> searchModels(
    String? query, {
    int? makeId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#searchModels, [query], {#makeId: makeId}),
            returnValue: _i6.Future<_i7.Result<List<_i10.VehicleModel>>>.value(
              _i5.dummyValue<_i7.Result<List<_i10.VehicleModel>>>(
                this,
                Invocation.method(#searchModels, [query], {#makeId: makeId}),
              ),
            ),
          )
          as _i6.Future<_i7.Result<List<_i10.VehicleModel>>>);

  @override
  _i6.Future<_i7.Result<T>> executeWithErrorHandling<T>(
    _i6.Future<T> Function()? operation, {
    String? operationName,
    bool? shouldLog = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #executeWithErrorHandling,
              [operation],
              {#operationName: operationName, #shouldLog: shouldLog},
            ),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(
                  #executeWithErrorHandling,
                  [operation],
                  {#operationName: operationName, #shouldLog: shouldLog},
                ),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<T>> getById<T>(
    String? id, {
    required T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic>? queryParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getById,
              [id],
              {#fromJson: fromJson, #queryParams: queryParams},
            ),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(
                  #getById,
                  [id],
                  {#fromJson: fromJson, #queryParams: queryParams},
                ),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<List<T>>> getAll<T>({
    required T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic>? queryParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAll, [], {
              #fromJson: fromJson,
              #queryParams: queryParams,
            }),
            returnValue: _i6.Future<_i7.Result<List<T>>>.value(
              _i5.dummyValue<_i7.Result<List<T>>>(
                this,
                Invocation.method(#getAll, [], {
                  #fromJson: fromJson,
                  #queryParams: queryParams,
                }),
              ),
            ),
          )
          as _i6.Future<_i7.Result<List<T>>>);

  @override
  _i6.Future<_i7.Result<T>> create<T>(
    Map<String, dynamic>? data, {
    required T Function(Map<String, dynamic>)? fromJson,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#create, [data], {#fromJson: fromJson}),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(#create, [data], {#fromJson: fromJson}),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<T>> update<T>(
    String? id,
    Map<String, dynamic>? data, {
    required T Function(Map<String, dynamic>)? fromJson,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#update, [id, data], {#fromJson: fromJson}),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(#update, [id, data], {#fromJson: fromJson}),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<void>> delete(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#delete, [id]),
            returnValue: _i6.Future<_i7.Result<void>>.value(
              _i5.dummyValue<_i7.Result<void>>(
                this,
                Invocation.method(#delete, [id]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<void>>);
}

/// A class which mocks [VehicleGenerationRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockVehicleGenerationRepository extends _i1.Mock
    implements _i11.VehicleGenerationRepository {
  MockVehicleGenerationRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get apiPath =>
      (super.noSuchMethod(
            Invocation.getter(#apiPath),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.getter(#apiPath),
            ),
          )
          as String);

  @override
  _i2.Ref<Object?> get ref =>
      (super.noSuchMethod(
            Invocation.getter(#ref),
            returnValue: _FakeRef_0<Object?>(this, Invocation.getter(#ref)),
          )
          as _i2.Ref<Object?>);

  @override
  _i3.SimpleApiClient get apiClient =>
      (super.noSuchMethod(
            Invocation.getter(#apiClient),
            returnValue: _FakeSimpleApiClient_1(
              this,
              Invocation.getter(#apiClient),
            ),
          )
          as _i3.SimpleApiClient);

  @override
  _i6.Future<_i7.Result<List<_i12.VehicleGeneration>>> getGenerations({
    int? modelId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getGenerations, [], {#modelId: modelId}),
            returnValue:
                _i6.Future<_i7.Result<List<_i12.VehicleGeneration>>>.value(
                  _i5.dummyValue<_i7.Result<List<_i12.VehicleGeneration>>>(
                    this,
                    Invocation.method(#getGenerations, [], {#modelId: modelId}),
                  ),
                ),
          )
          as _i6.Future<_i7.Result<List<_i12.VehicleGeneration>>>);

  @override
  _i6.Future<_i7.Result<_i12.VehicleGeneration>> createGeneration(
    _i12.VehicleGeneration? generation,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createGeneration, [generation]),
            returnValue: _i6.Future<_i7.Result<_i12.VehicleGeneration>>.value(
              _i5.dummyValue<_i7.Result<_i12.VehicleGeneration>>(
                this,
                Invocation.method(#createGeneration, [generation]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<_i12.VehicleGeneration>>);

  @override
  _i6.Future<_i7.Result<_i12.VehicleGeneration>> updateGeneration(
    String? id,
    _i12.VehicleGeneration? generation,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateGeneration, [id, generation]),
            returnValue: _i6.Future<_i7.Result<_i12.VehicleGeneration>>.value(
              _i5.dummyValue<_i7.Result<_i12.VehicleGeneration>>(
                this,
                Invocation.method(#updateGeneration, [id, generation]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<_i12.VehicleGeneration>>);

  @override
  _i6.Future<_i7.Result<void>> deleteGeneration(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteGeneration, [id]),
            returnValue: _i6.Future<_i7.Result<void>>.value(
              _i5.dummyValue<_i7.Result<void>>(
                this,
                Invocation.method(#deleteGeneration, [id]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<T>> executeWithErrorHandling<T>(
    _i6.Future<T> Function()? operation, {
    String? operationName,
    bool? shouldLog = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #executeWithErrorHandling,
              [operation],
              {#operationName: operationName, #shouldLog: shouldLog},
            ),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(
                  #executeWithErrorHandling,
                  [operation],
                  {#operationName: operationName, #shouldLog: shouldLog},
                ),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<T>> getById<T>(
    String? id, {
    required T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic>? queryParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getById,
              [id],
              {#fromJson: fromJson, #queryParams: queryParams},
            ),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(
                  #getById,
                  [id],
                  {#fromJson: fromJson, #queryParams: queryParams},
                ),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<List<T>>> getAll<T>({
    required T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic>? queryParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAll, [], {
              #fromJson: fromJson,
              #queryParams: queryParams,
            }),
            returnValue: _i6.Future<_i7.Result<List<T>>>.value(
              _i5.dummyValue<_i7.Result<List<T>>>(
                this,
                Invocation.method(#getAll, [], {
                  #fromJson: fromJson,
                  #queryParams: queryParams,
                }),
              ),
            ),
          )
          as _i6.Future<_i7.Result<List<T>>>);

  @override
  _i6.Future<_i7.Result<T>> create<T>(
    Map<String, dynamic>? data, {
    required T Function(Map<String, dynamic>)? fromJson,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#create, [data], {#fromJson: fromJson}),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(#create, [data], {#fromJson: fromJson}),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<T>> update<T>(
    String? id,
    Map<String, dynamic>? data, {
    required T Function(Map<String, dynamic>)? fromJson,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#update, [id, data], {#fromJson: fromJson}),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(#update, [id, data], {#fromJson: fromJson}),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<void>> delete(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#delete, [id]),
            returnValue: _i6.Future<_i7.Result<void>>.value(
              _i5.dummyValue<_i7.Result<void>>(
                this,
                Invocation.method(#delete, [id]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<void>>);
}

/// A class which mocks [VehicleTrimRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockVehicleTrimRepository extends _i1.Mock
    implements _i13.VehicleTrimRepository {
  MockVehicleTrimRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get apiPath =>
      (super.noSuchMethod(
            Invocation.getter(#apiPath),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.getter(#apiPath),
            ),
          )
          as String);

  @override
  _i2.Ref<Object?> get ref =>
      (super.noSuchMethod(
            Invocation.getter(#ref),
            returnValue: _FakeRef_0<Object?>(this, Invocation.getter(#ref)),
          )
          as _i2.Ref<Object?>);

  @override
  _i3.SimpleApiClient get apiClient =>
      (super.noSuchMethod(
            Invocation.getter(#apiClient),
            returnValue: _FakeSimpleApiClient_1(
              this,
              Invocation.getter(#apiClient),
            ),
          )
          as _i3.SimpleApiClient);

  @override
  _i6.Future<_i7.Result<List<_i14.VehicleTrim>>> getTrims({int? modelId}) =>
      (super.noSuchMethod(
            Invocation.method(#getTrims, [], {#modelId: modelId}),
            returnValue: _i6.Future<_i7.Result<List<_i14.VehicleTrim>>>.value(
              _i5.dummyValue<_i7.Result<List<_i14.VehicleTrim>>>(
                this,
                Invocation.method(#getTrims, [], {#modelId: modelId}),
              ),
            ),
          )
          as _i6.Future<_i7.Result<List<_i14.VehicleTrim>>>);

  @override
  _i6.Future<_i7.Result<_i14.VehicleTrim>> createTrim(_i14.VehicleTrim? trim) =>
      (super.noSuchMethod(
            Invocation.method(#createTrim, [trim]),
            returnValue: _i6.Future<_i7.Result<_i14.VehicleTrim>>.value(
              _i5.dummyValue<_i7.Result<_i14.VehicleTrim>>(
                this,
                Invocation.method(#createTrim, [trim]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<_i14.VehicleTrim>>);

  @override
  _i6.Future<_i7.Result<_i14.VehicleTrim>> updateTrim(
    String? id,
    _i14.VehicleTrim? trim,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateTrim, [id, trim]),
            returnValue: _i6.Future<_i7.Result<_i14.VehicleTrim>>.value(
              _i5.dummyValue<_i7.Result<_i14.VehicleTrim>>(
                this,
                Invocation.method(#updateTrim, [id, trim]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<_i14.VehicleTrim>>);

  @override
  _i6.Future<_i7.Result<void>> deleteTrim(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteTrim, [id]),
            returnValue: _i6.Future<_i7.Result<void>>.value(
              _i5.dummyValue<_i7.Result<void>>(
                this,
                Invocation.method(#deleteTrim, [id]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<void>>);

  @override
  _i6.Future<_i7.Result<T>> executeWithErrorHandling<T>(
    _i6.Future<T> Function()? operation, {
    String? operationName,
    bool? shouldLog = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #executeWithErrorHandling,
              [operation],
              {#operationName: operationName, #shouldLog: shouldLog},
            ),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(
                  #executeWithErrorHandling,
                  [operation],
                  {#operationName: operationName, #shouldLog: shouldLog},
                ),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<T>> getById<T>(
    String? id, {
    required T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic>? queryParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getById,
              [id],
              {#fromJson: fromJson, #queryParams: queryParams},
            ),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(
                  #getById,
                  [id],
                  {#fromJson: fromJson, #queryParams: queryParams},
                ),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<List<T>>> getAll<T>({
    required T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic>? queryParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAll, [], {
              #fromJson: fromJson,
              #queryParams: queryParams,
            }),
            returnValue: _i6.Future<_i7.Result<List<T>>>.value(
              _i5.dummyValue<_i7.Result<List<T>>>(
                this,
                Invocation.method(#getAll, [], {
                  #fromJson: fromJson,
                  #queryParams: queryParams,
                }),
              ),
            ),
          )
          as _i6.Future<_i7.Result<List<T>>>);

  @override
  _i6.Future<_i7.Result<T>> create<T>(
    Map<String, dynamic>? data, {
    required T Function(Map<String, dynamic>)? fromJson,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#create, [data], {#fromJson: fromJson}),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(#create, [data], {#fromJson: fromJson}),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<T>> update<T>(
    String? id,
    Map<String, dynamic>? data, {
    required T Function(Map<String, dynamic>)? fromJson,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#update, [id, data], {#fromJson: fromJson}),
            returnValue: _i6.Future<_i7.Result<T>>.value(
              _i5.dummyValue<_i7.Result<T>>(
                this,
                Invocation.method(#update, [id, data], {#fromJson: fromJson}),
              ),
            ),
          )
          as _i6.Future<_i7.Result<T>>);

  @override
  _i6.Future<_i7.Result<void>> delete(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#delete, [id]),
            returnValue: _i6.Future<_i7.Result<void>>.value(
              _i5.dummyValue<_i7.Result<void>>(
                this,
                Invocation.method(#delete, [id]),
              ),
            ),
          )
          as _i6.Future<_i7.Result<void>>);
}
