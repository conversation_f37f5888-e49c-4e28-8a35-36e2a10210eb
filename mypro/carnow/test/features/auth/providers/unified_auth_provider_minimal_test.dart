import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// CarNow imports
import 'package:carnow/core/auth/auth_models.dart';
import 'package:carnow/core/auth/enhanced_secure_token_storage.dart';
import 'package:carnow/core/auth/unified_auth_provider.dart';

// Generate mocks
@GenerateMocks([
  EnhancedSecureTokenStorage,
], customMocks: [
  MockSpec<EnhancedSecureTokenStorage>(
    as: #MockUnifiedAuthEnhancedSecureTokenStorage,
  ),
])
import 'unified_auth_provider_minimal_test.mocks.dart';

// Remove the manual mock class
// class MockEnhancedSecureTokenStorage extends Mock implements EnhancedSecureTokenStorage {}

void main() {
  group('UnifiedAuthProvider Minimal Tests', () {
    late ProviderContainer container;
    late MockUnifiedAuthEnhancedSecureTokenStorage mockTokenStorage;

    setUp(() {
      // Setup mocks
      mockTokenStorage = MockUnifiedAuthEnhancedSecureTokenStorage();
      
      // Setup basic token storage methods
      when(mockTokenStorage.hasValidToken()).thenAnswer((_) async => false);
      when(mockTokenStorage.storeToken('test_token', expiryDate: anyNamed('expiryDate')))
        .thenAnswer((_) async => true);
      when(mockTokenStorage.storeRefreshToken('test_refresh_token', expiryDate: anyNamed('expiryDate')))
        .thenAnswer((_) async => true);
      when(mockTokenStorage.clearAllData()).thenAnswer((_) async => true);
      when(mockTokenStorage.hasToken()).thenAnswer((_) async => false);
      when(mockTokenStorage.hasRefreshToken()).thenAnswer((_) async => false);
      
      container = ProviderContainer(
        overrides: [
          enhancedSecureTokenStorageProvider.overrideWithValue(mockTokenStorage),
        ],
      );

      addTearDown(container.dispose);
    });

    group('Initial State', () {
      test('should initialize with initial state', () {
        final initialState = container.read(unifiedAuthProviderProvider);
        expect(initialState, isA<AuthState>());
      });

      test('should transition to unauthenticated when no valid token', () async {
        // Mock token storage - no valid token
        when(mockTokenStorage.hasValidToken()).thenAnswer((_) async => false);

        // Force provider initialization
        container.read(unifiedAuthProviderProvider);
        
        // Allow async operations to complete
        await Future.delayed(const Duration(milliseconds: 100));

        final state = container.read(unifiedAuthProviderProvider);
        expect(state, isA<AuthStateUnauthenticated>());
      });
    });

    group('Token Storage Integration', () {
      test('should check for existing tokens on initialization', () async {
        // Mock token storage - has valid token
        when(mockTokenStorage.hasValidToken()).thenAnswer((_) async => true);
        when(mockTokenStorage.getToken()).thenAnswer((_) async => 'valid_token');
        when(mockTokenStorage.hasToken()).thenAnswer((_) async => true);

        // Create new container to trigger initialization
        final newContainer = ProviderContainer(
          overrides: [
            enhancedSecureTokenStorageProvider.overrideWithValue(mockTokenStorage),
          ],
        );

        // Allow async operations to complete
        await Future.delayed(const Duration(milliseconds: 100));

        // Verify token storage methods were called
        verify(mockTokenStorage.hasValidToken()).called(greaterThan(0));
        
        newContainer.dispose();
      });

      test('should handle token storage errors gracefully', () async {
        // Mock token storage to throw error
        when(mockTokenStorage.hasValidToken()).thenThrow(Exception('Storage error'));

        // Create new container
        final newContainer = ProviderContainer(
          overrides: [
            enhancedSecureTokenStorageProvider.overrideWithValue(mockTokenStorage),
          ],
        );

        // Allow async operations to complete
        await Future.delayed(const Duration(milliseconds: 100));

        final state = newContainer.read(unifiedAuthProviderProvider);
        
        // Should handle error gracefully and default to unauthenticated
        expect(state, isA<AuthStateUnauthenticated>());
        
        newContainer.dispose();
      });
    });

    group('State Management', () {
      test('should maintain state consistency', () async {
        final notifier = container.read(unifiedAuthProviderProvider.notifier);
        
        // Initial state
        expect(container.read(unifiedAuthProviderProvider), isA<AuthStateInitial>());

        // Simulate state change to loading
        // Note: This would normally be triggered by authentication methods
        // but we're testing the state management directly
        
        // Allow async operations to complete
        await Future.delayed(const Duration(milliseconds: 50));

        // State should be consistent
        final currentState = container.read(unifiedAuthProviderProvider);
        expect(currentState, isA<AuthState>());
      });
    });

    group('Enhanced Token Storage Methods', () {
      test('should call hasToken method', () async {
        when(mockTokenStorage.hasToken()).thenAnswer((_) async => true);
        
        final result = await mockTokenStorage.hasToken();
        
        expect(result, isTrue);
        verify(mockTokenStorage.hasToken()).called(1);
      });

      test('should call hasRefreshToken method', () async {
        when(mockTokenStorage.hasRefreshToken()).thenAnswer((_) async => true);
        
        final result = await mockTokenStorage.hasRefreshToken();
        
        expect(result, isTrue);
        verify(mockTokenStorage.hasRefreshToken()).called(1);
      });

      test('should call willTokenExpireWithin method', () async {
        const testDuration = Duration(minutes: 10);
        when(mockTokenStorage.willTokenExpireWithin(testDuration)).thenAnswer((_) async => false);
        
        final result = await mockTokenStorage.willTokenExpireWithin(testDuration);
        
        expect(result, isFalse);
        verify(mockTokenStorage.willTokenExpireWithin(testDuration)).called(1);
      });

      test('should call getRefreshTokenExpiry method', () async {
        final expectedExpiry = DateTime.now().add(const Duration(days: 30));
        when(mockTokenStorage.getRefreshTokenExpiry()).thenAnswer((_) async => expectedExpiry);
        
        final result = await mockTokenStorage.getRefreshTokenExpiry();
        
        expect(result, equals(expectedExpiry));
        verify(mockTokenStorage.getRefreshTokenExpiry()).called(1);
      });
    });

    group('Error Handling', () {
      test('should handle authentication errors', () async {
        final notifier = container.read(unifiedAuthProviderProvider.notifier);
        
        // This would normally test error handling in authentication methods
        // For now, we verify the provider can handle errors gracefully
        expect(notifier, isNotNull);
        expect(container.read(unifiedAuthProviderProvider), isA<AuthState>());
      });
    });
  });
}
