import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';

// CarNow imports
import 'package:carnow/core/auth/auth_models.dart';
import 'package:carnow/core/auth/unified_auth_provider.dart';
import 'package:carnow/core/auth/auth_interfaces.dart';

// Mock classes
class MockTokenStorage extends Mock implements ITokenStorage {}
class MockGoogleAuthService extends Mock implements IGoogleAuthService {}

void main() {
  group('UnifiedAuthProvider Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
      addTearDown(container.dispose);
    });

    group('Initial State', () {
      test('should initialize with initial state', () {
        final initialState = container.read(unifiedAuthProviderProvider);
        expect(initialState, isA<AuthState>());
      });

      test('should be in initial state on startup', () {
        final state = container.read(unifiedAuthProviderProvider);
        expect(state, isA<AuthStateInitial>());
      });
    });

    group('Provider Structure', () {
      test('should provide UnifiedAuthProvider notifier', () {
        final notifier = container.read(unifiedAuthProviderProvider.notifier);
        expect(notifier, isA<UnifiedAuthProvider>());
      });

      test('should have proper state management', () {
        final state = container.read(unifiedAuthProviderProvider);
        expect(state, isA<AuthState>());
      });
    });

    group('AuthState Types', () {
      test('should support all AuthState variants', () {
        // Test that all AuthState variants are properly defined
        expect(AuthState.initial(), isA<AuthStateInitial>());
        expect(AuthState.loading(), isA<AuthStateLoading>());
        expect(AuthState.unauthenticated(), isA<AuthStateUnauthenticated>());
        expect(AuthState.error(message: 'test'), isA<AuthStateError>());
      });

      test('should handle authenticated state with user data', () {
        final user = User(
          id: 'test-user',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          emailVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final authState = AuthState.authenticated(
          user: user,
          token: 'test-token',
        );

        expect(authState, isA<AuthStateAuthenticated>());
        expect((authState as AuthStateAuthenticated).user.email, equals('<EMAIL>'));
        expect((authState as AuthStateAuthenticated).token, equals('test-token'));
      });

      test('should handle email verification pending state', () {
        final pendingState = AuthState.emailVerificationPending(email: '<EMAIL>');
        
        expect(pendingState, isA<AuthStateEmailVerificationPending>());
        expect((pendingState as AuthStateEmailVerificationPending).email, equals('<EMAIL>'));
      });
    });

    group('AuthResult Types', () {
      test('should support all AuthResult variants', () {
        final user = User(
          id: 'test-user',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          emailVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Test success result
        final successResult = AuthResult.success(
          user: user,
          token: 'test-token',
        );
        expect(successResult, isA<AuthResultSuccess>());

        // Test failure result
        final failureResult = AuthResult.failure(
          error: 'Test error',
          errorType: AuthErrorType.invalidCredentials,
        );
        expect(failureResult, isA<AuthResultFailure>());

        // Test cancelled result
        final cancelledResult = AuthResult.cancelled(reason: 'User cancelled');
        expect(cancelledResult, isA<AuthResultCancelled>());

        // Test pending result
        final pendingResult = AuthResult.pending(
          message: 'Verification required',
          pendingAction: AuthOperation.verifyEmail,
        );
        expect(pendingResult, isA<AuthResultPending>());
      });

      test('should handle AuthResult pattern matching', () {
        final user = User(
          id: 'test-user',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          emailVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final result = AuthResult.success(
          user: user,
          token: 'test-token',
        );

        result.when(
          success: (user, token, refreshToken, tokenExpiry, metadata) {
            expect(user.email, equals('<EMAIL>'));
            expect(token, equals('test-token'));
          },
          failure: (error, errorCode, errorType, isRecoverable, details) {
            fail('Should not be failure');
          },
          cancelled: (reason) {
            fail('Should not be cancelled');
          },
          pending: (message, pendingAction, actionData) {
            fail('Should not be pending');
          },
        );
      });
    });

    group('Error Handling', () {
      test('should handle AuthErrorType enum values', () {
        expect(AuthErrorType.values, contains(AuthErrorType.unknown));
        expect(AuthErrorType.values, contains(AuthErrorType.invalidCredentials));
        expect(AuthErrorType.values, contains(AuthErrorType.networkError));
        expect(AuthErrorType.values, contains(AuthErrorType.emailAlreadyExists));
        expect(AuthErrorType.values, contains(AuthErrorType.weakPassword));
        expect(AuthErrorType.values, contains(AuthErrorType.invalidEmail));
        expect(AuthErrorType.values, contains(AuthErrorType.sessionExpired));
        expect(AuthErrorType.values, contains(AuthErrorType.rateLimitExceeded));
        expect(AuthErrorType.values, contains(AuthErrorType.serverError));
        expect(AuthErrorType.values, contains(AuthErrorType.oauthError));
        expect(AuthErrorType.values, contains(AuthErrorType.verificationRequired));
        expect(AuthErrorType.values, contains(AuthErrorType.emailNotVerified));
        expect(AuthErrorType.values, contains(AuthErrorType.accountDisabled));
      });

      test('should handle AuthOperation enum values', () {
        expect(AuthOperation.values, contains(AuthOperation.signIn));
        expect(AuthOperation.values, contains(AuthOperation.signUp));
        expect(AuthOperation.values, contains(AuthOperation.signOut));
        expect(AuthOperation.values, contains(AuthOperation.refreshToken));
        expect(AuthOperation.values, contains(AuthOperation.verifyEmail));
        expect(AuthOperation.values, contains(AuthOperation.resetPassword));
      });
    });

    group('Provider Integration', () {
      test('should provide consistent state across reads', () {
        final state1 = container.read(unifiedAuthProviderProvider);
        final state2 = container.read(unifiedAuthProviderProvider);
        
        expect(state1, equals(state2));
      });

      test('should handle provider disposal gracefully', () {
        expect(() => container.dispose(), returnsNormally);
      });
    });
  });
}
