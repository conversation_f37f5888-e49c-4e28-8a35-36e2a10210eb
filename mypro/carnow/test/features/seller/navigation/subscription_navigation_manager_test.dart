import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/features/seller/navigation/subscription_navigation_manager.dart';

void main() {
  group('SubscriptionNavigationManager', () {
    test('Route constants are defined correctly', () {
      expect(SubscriptionNavigationManager.subscriptionFormRoute, isNotEmpty);
      expect(SubscriptionNavigationManager.subscriptionStatusRoute, isNotEmpty);
      expect(SubscriptionNavigationManager.subscriptionSuccessRoute, isNotEmpty);
      expect(SubscriptionNavigationManager.subscriptionPlansRoute, isNotEmpty);
      expect(SubscriptionNavigationManager.subscriptionUpgradeRoute, isNotEmpty);
      expect(SubscriptionNavigationManager.subscriptionBillingRoute, isNotEmpty);
      expect(SubscriptionNavigationManager.subscriptionHistoryRoute, isNotEmpty);
    });

    test('SubscriptionNavigationError can be created', () {
      final error = SubscriptionNavigationError(
        message: 'Test error',
        attemptedRoute: '/test/route',
      );
      
      expect(error.message, equals('Test error'));
      expect(error.attemptedRoute, equals('/test/route'));
      expect(error, isA<SubscriptionNavigationError>());
    });

    test('SubscriptionNavigationError toString works correctly', () {
      final error = SubscriptionNavigationError(
        message: 'Test error',
        attemptedRoute: '/test/route',
      );
      
      final errorString = error.toString();
      expect(errorString, contains('SubscriptionNavigationError'));
      expect(errorString, contains('Test error'));
      expect(errorString, contains('/test/route'));
    });
  });
}
