// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in carnow/test/features/seller/services/subscription_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:carnow/core/models/api_response.dart' as _i2;
import 'package:carnow/core/networking/simple_api_client.dart' as _i5;
import 'package:dio/dio.dart' as _i7;
import 'package:flutter_riverpod/flutter_riverpod.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;
import 'package:riverpod/src/internals.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeApiResponse_0<T1> extends _i1.SmartFake
    implements _i2.ApiResponse<T1> {
  _FakeApiResponse_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeProviderScheduler_1 extends _i1.SmartFake
    implements _i3.ProviderScheduler {
  _FakeProviderScheduler_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeProviderSubscription_2<State1> extends _i1.SmartFake
    implements _i4.ProviderSubscription<State1> {
  _FakeProviderSubscription_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeProviderElementBase_3<StateT> extends _i1.SmartFake
    implements _i4.ProviderElementBase<StateT> {
  _FakeProviderElementBase_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [SimpleApiClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockSimpleApiClient extends _i1.Mock implements _i5.SimpleApiClient {
  MockSimpleApiClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i2.ApiResponse<T>> get<T>(
    String? endpoint, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #get,
              [endpoint],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i6.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #get,
                  [endpoint],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.ApiResponse<T>>);

  @override
  _i6.Future<_i2.ApiResponse<T>> getApi<T>(
    String? endpoint, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getApi,
              [endpoint],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i6.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #getApi,
                  [endpoint],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.ApiResponse<T>>);

  @override
  _i6.Future<_i2.ApiResponse<T>> postApi<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #postApi,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i6.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #postApi,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.ApiResponse<T>>);

  @override
  _i6.Future<_i2.ApiResponse<T>> putApi<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #putApi,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i6.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #putApi,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.ApiResponse<T>>);

  @override
  _i6.Future<_i2.ApiResponse<T>> deleteApi<T>(
    String? endpoint, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #deleteApi,
              [endpoint],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i6.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #deleteApi,
                  [endpoint],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.ApiResponse<T>>);

  @override
  _i6.Future<_i2.ApiResponse<T>> post<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #post,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i6.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #post,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.ApiResponse<T>>);

  @override
  _i6.Future<_i2.ApiResponse<T>> put<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #put,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i6.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #put,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.ApiResponse<T>>);

  @override
  _i6.Future<_i2.ApiResponse<T>> delete<T>(
    String? endpoint, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #delete,
              [endpoint],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i6.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #delete,
                  [endpoint],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.ApiResponse<T>>);

  @override
  _i6.Future<_i2.ApiResponse<T>> patch<T>(
    String? endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #patch,
              [endpoint],
              {#data: data, #queryParameters: queryParameters},
            ),
            returnValue: _i6.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #patch,
                  [endpoint],
                  {#data: data, #queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.ApiResponse<T>>);

  @override
  _i6.Future<_i2.ApiResponse<T>> uploadFormData<T>(
    String? endpoint,
    _i7.FormData? formData, {
    Map<String, dynamic>? queryParameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #uploadFormData,
              [endpoint, formData],
              {#queryParameters: queryParameters},
            ),
            returnValue: _i6.Future<_i2.ApiResponse<T>>.value(
              _FakeApiResponse_0<T>(
                this,
                Invocation.method(
                  #uploadFormData,
                  [endpoint, formData],
                  {#queryParameters: queryParameters},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.ApiResponse<T>>);
}

/// A class which mocks [ProviderContainer].
///
/// See the documentation for Mockito's code generation for more information.
class MockProviderContainer extends _i1.Mock implements _i4.ProviderContainer {
  MockProviderContainer() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.ProviderScheduler get scheduler =>
      (super.noSuchMethod(
            Invocation.getter(#scheduler),
            returnValue: _FakeProviderScheduler_1(
              this,
              Invocation.getter(#scheduler),
            ),
          )
          as _i3.ProviderScheduler);

  @override
  int get depth =>
      (super.noSuchMethod(Invocation.getter(#depth), returnValue: 0) as int);

  @override
  List<_i4.ProviderObserver> get observers =>
      (super.noSuchMethod(
            Invocation.getter(#observers),
            returnValue: <_i4.ProviderObserver>[],
          )
          as List<_i4.ProviderObserver>);

  @override
  void Function(void Function()) get vsync =>
      (super.noSuchMethod(
            Invocation.getter(#vsync),
            returnValue: (void Function() task) {},
          )
          as void Function(void Function()));

  @override
  List<_i4.ProviderContainer> get debugChildren =>
      (super.noSuchMethod(
            Invocation.getter(#debugChildren),
            returnValue: <_i4.ProviderContainer>[],
          )
          as List<_i4.ProviderContainer>);

  @override
  set vsyncOverride(void Function(void Function())? _vsyncOverride) =>
      super.noSuchMethod(
        Invocation.setter(#vsyncOverride, _vsyncOverride),
        returnValueForMissingStub: null,
      );

  @override
  set debugCanModifyProviders(void Function()? _debugCanModifyProviders) =>
      super.noSuchMethod(
        Invocation.setter(#debugCanModifyProviders, _debugCanModifyProviders),
        returnValueForMissingStub: null,
      );

  @override
  bool hasStateReaderFor(_i4.ProviderListenable<Object?>? provider) =>
      (super.noSuchMethod(
            Invocation.method(#hasStateReaderFor, [provider]),
            returnValue: false,
          )
          as bool);

  @override
  _i6.Future<void> pump() =>
      (super.noSuchMethod(
            Invocation.method(#pump, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  Result read<Result>(_i4.ProviderListenable<Result>? provider) =>
      (super.noSuchMethod(
            Invocation.method(#read, [provider]),
            returnValue: _i8.dummyValue<Result>(
              this,
              Invocation.method(#read, [provider]),
            ),
          )
          as Result);

  @override
  bool exists(_i4.ProviderBase<Object?>? provider) =>
      (super.noSuchMethod(
            Invocation.method(#exists, [provider]),
            returnValue: false,
          )
          as bool);

  @override
  void debugReassemble() => super.noSuchMethod(
    Invocation.method(#debugReassemble, []),
    returnValueForMissingStub: null,
  );

  @override
  _i4.ProviderSubscription<State> listen<State>(
    _i4.ProviderListenable<State>? provider,
    void Function(State?, State)? listener, {
    bool? fireImmediately = false,
    void Function(Object, StackTrace)? onError,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #listen,
              [provider, listener],
              {#fireImmediately: fireImmediately, #onError: onError},
            ),
            returnValue: _FakeProviderSubscription_2<State>(
              this,
              Invocation.method(
                #listen,
                [provider, listener],
                {#fireImmediately: fireImmediately, #onError: onError},
              ),
            ),
          )
          as _i4.ProviderSubscription<State>);

  @override
  void invalidate(_i4.ProviderOrFamily? provider) => super.noSuchMethod(
    Invocation.method(#invalidate, [provider]),
    returnValueForMissingStub: null,
  );

  @override
  State refresh<State>(_i4.Refreshable<State>? provider) =>
      (super.noSuchMethod(
            Invocation.method(#refresh, [provider]),
            returnValue: _i8.dummyValue<State>(
              this,
              Invocation.method(#refresh, [provider]),
            ),
          )
          as State);

  @override
  void updateOverrides(List<_i4.Override>? overrides) => super.noSuchMethod(
    Invocation.method(#updateOverrides, [overrides]),
    returnValueForMissingStub: null,
  );

  @override
  _i4.ProviderElementBase<State> readProviderElement<State>(
    _i4.ProviderBase<State>? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#readProviderElement, [provider]),
            returnValue: _FakeProviderElementBase_3<State>(
              this,
              Invocation.method(#readProviderElement, [provider]),
            ),
          )
          as _i4.ProviderElementBase<State>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  Iterable<_i4.ProviderElementBase<dynamic>> getAllProviderElements() =>
      (super.noSuchMethod(
            Invocation.method(#getAllProviderElements, []),
            returnValue: <_i4.ProviderElementBase<dynamic>>[],
          )
          as Iterable<_i4.ProviderElementBase<dynamic>>);

  @override
  Iterable<_i4.ProviderElementBase<dynamic>> getAllProviderElementsInOrder() =>
      (super.noSuchMethod(
            Invocation.method(#getAllProviderElementsInOrder, []),
            returnValue: <_i4.ProviderElementBase<dynamic>>[],
          )
          as Iterable<_i4.ProviderElementBase<dynamic>>);
}
