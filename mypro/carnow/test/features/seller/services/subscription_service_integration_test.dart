import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:logger/logger.dart';

import 'package:carnow/core/networking/simple_api_client.dart';
import 'package:carnow/core/models/subscription_request.dart';
import 'package:carnow/core/models/subscription_response.dart';
import 'package:carnow/core/models/subscription_error.dart';
import 'package:carnow/core/models/api_response.dart';
import 'package:carnow/core/services/subscription_service.dart';

import 'subscription_service_integration_test.mocks.dart';

@GenerateMocks([SimpleApiClient])
void main() {
  group('SubscriptionService Integration with Core Models', () {
    late SubscriptionServiceImpl subscriptionService;
    late MockSimpleApiClient mockApiClient;

    setUp(() {
      mockApiClient = MockSimpleApiClient();
      subscriptionService = SubscriptionServiceImpl(
        apiClient: mockApiClient,
        logger: Logger(),
      );
    });

    group('Core Models Integration', () {
      test(
        'should successfully submit subscription request using core models',
        () async {
          // Arrange
          final request = SubscriptionRequest(
            storeName: 'Test Store',
            phone: '0501234567',
            city: 'Riyadh',
            address: 'Test Address, Test District, Riyadh',
            description: 'Test store description for testing purposes',
            planType: 'premium',
            price: 299.99,
            userId: '12345678-1234-1234-1234-123456789012',
          );

          final responseData = {
            'id': 'sub-123',
            'status': 'pending',
            'created_at': '2024-01-01T00:00:00Z',
            'message': 'Subscription request submitted successfully',
          };

          final apiResponse = ApiResponse.success(responseData);

          when(
            mockApiClient.post<Map<String, dynamic>>(
              '/api/subscriptions',
              data: anyNamed('data'),
            ),
          ).thenAnswer((_) async => apiResponse);

          // Act
          final result = await subscriptionService.submitSubscriptionRequest(
            request,
          );

          // Assert
          expect(result, isA<SubscriptionSuccess<SubscriptionResponse>>());

          result.when(
            success: (response) {
              expect(response.id, equals('sub-123'));
              expect(response.status, equals('pending'));
              expect(response.isPending, isTrue);
              expect(response.statusMessageArabic, contains('قيد المراجعة'));
            },
            failure: (error) {
              fail(
                'Expected success but got failure: ${error.technicalMessage}',
              );
            },
          );

          verify(
            mockApiClient.post<Map<String, dynamic>>(
              '/api/subscriptions',
              data: request.toJson(),
            ),
          ).called(1);
        },
      );

      test(
        'should validate subscription request using core model validation',
        () async {
          // Arrange
          final invalidRequest = SubscriptionRequest(
            storeName: '', // Invalid: empty store name
            phone: '123', // Invalid: too short
            city: '',
            address: '',
            description: '',
            planType: 'invalid',
            price: -100, // Invalid: negative price
            userId: 'invalid-uuid',
          );

          // Act
          final result = await subscriptionService.submitSubscriptionRequest(
            invalidRequest,
          );

          // Assert
          expect(result, isA<SubscriptionFailure<SubscriptionResponse>>());

          result.when(
            success: (response) {
              fail('Expected validation failure but got success');
            },
            failure: (error) {
              expect(error, isA<ValidationError>());
              final validationError = error as ValidationError;
              expect(validationError.fieldErrors, isNotEmpty);
              expect(
                validationError.fieldErrors.containsKey('storeName'),
                isTrue,
              );
              expect(validationError.fieldErrors.containsKey('phone'), isTrue);
              expect(validationError.fieldErrors.containsKey('price'), isTrue);
              expect(
                validationError.userFriendlyMessageArabic,
                contains('بيانات غير صحيحة'),
              );
            },
          );

          // Verify API was not called due to validation failure
          verifyNever(
            mockApiClient.post<Map<String, dynamic>>(
              any,
              data: anyNamed('data'),
            ),
          );
        },
      );

      test('should handle network errors using core error types', () async {
        // Arrange
        final request = SubscriptionRequest(
          storeName: 'Test Store',
          phone: '0501234567',
          city: 'Riyadh',
          address: 'Test Address, Test District, Riyadh',
          description: 'Test store description for testing purposes',
          planType: 'premium',
          price: 299.99,
          userId: '12345678-1234-1234-1234-123456789012',
        );

        final apiResponse = ApiResponse<Map<String, dynamic>>.error(
          'Connection timeout. Please check your internet connection.',
        );

        when(
          mockApiClient.post<Map<String, dynamic>>(
            '/api/subscriptions',
            data: anyNamed('data'),
          ),
        ).thenAnswer((_) async => apiResponse);

        // Act
        final result = await subscriptionService.submitSubscriptionRequest(
          request,
        );

        // Assert
        expect(result, isA<SubscriptionFailure<SubscriptionResponse>>());

        result.when(
          success: (response) {
            fail('Expected network error but got success');
          },
          failure: (error) {
            expect(error, isA<NetworkError>());
            final networkError = error as NetworkError;
            expect(networkError.message, contains('خطأ في الاتصال'));
            expect(networkError.isRetryable, isTrue);
            expect(
              networkError.suggestedActionArabic,
              contains('تحقق من اتصال الإنترنت'),
            );
          },
        );
      });

      test('should get user subscriptions using core models', () async {
        // Arrange
        const userId = '12345678-1234-1234-1234-123456789012';
        final responseData = [
          {
            'id': 'sub-1',
            'status': 'active',
            'created_at': '2024-01-01T00:00:00Z',
            'store_name': 'Store 1',
          },
          {
            'id': 'sub-2',
            'status': 'pending',
            'created_at': '2024-01-02T00:00:00Z',
            'store_name': 'Store 2',
          },
        ];

        final apiResponse = ApiResponse.success(responseData);

        when(
          mockApiClient.get<List<dynamic>>('/api/subscriptions/user/$userId'),
        ).thenAnswer((_) async => apiResponse);

        // Act
        final result = await subscriptionService.getUserSubscriptions(userId);

        // Assert
        expect(result, isA<SubscriptionSuccess<List<SubscriptionResponse>>>());

        result.when(
          success: (subscriptions) {
            expect(subscriptions, hasLength(2));
            expect(subscriptions[0].id, equals('sub-1'));
            expect(subscriptions[0].isSuccess, isTrue);
            expect(subscriptions[1].id, equals('sub-2'));
            expect(subscriptions[1].isPending, isTrue);
          },
          failure: (error) {
            fail('Expected success but got failure: ${error.technicalMessage}');
          },
        );

        verify(
          mockApiClient.get<List<dynamic>>('/api/subscriptions/user/$userId'),
        ).called(1);
      });

      test('should get subscription status using core models', () async {
        // Arrange
        const subscriptionId = 'sub-123';
        final responseData = {
          'id': subscriptionId,
          'status': 'active',
          'created_at': '2024-01-01T00:00:00Z',
          'store_name': 'Test Store',
          'plan_type': 'premium',
          'price': 299.99,
        };

        final apiResponse = ApiResponse.success(responseData);

        when(
          mockApiClient.get<Map<String, dynamic>>(
            '/api/subscriptions/$subscriptionId',
          ),
        ).thenAnswer((_) async => apiResponse);

        // Act
        final result = await subscriptionService.getSubscriptionStatus(
          subscriptionId,
        );

        // Assert
        expect(result, isA<SubscriptionSuccess<SubscriptionResponse>>());

        result.when(
          success: (response) {
            expect(response.id, equals(subscriptionId));
            expect(response.status, equals('active'));
            expect(response.isSuccess, isTrue);
            expect(response.storeName, equals('Test Store'));
            expect(response.displayDetails, isNotEmpty);
            expect(response.displayDetails['اسم المتجر'], equals('Test Store'));
          },
          failure: (error) {
            fail('Expected success but got failure: ${error.technicalMessage}');
          },
        );

        verify(
          mockApiClient.get<Map<String, dynamic>>(
            '/api/subscriptions/$subscriptionId',
          ),
        ).called(1);
      });

      test('should update subscription using core models', () async {
        // Arrange
        const subscriptionId = 'sub-123';
        final request = SubscriptionRequest(
          storeName: 'Updated Store',
          phone: '0501234567',
          city: 'Riyadh',
          address: 'Updated Address, Test District, Riyadh',
          description: 'Updated store description for testing purposes',
          planType: 'premium',
          price: 399.99,
          userId: '12345678-1234-1234-1234-123456789012',
        );

        final responseData = {
          'id': subscriptionId,
          'status': 'pending',
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-02T00:00:00Z',
          'store_name': 'Updated Store',
          'price': 399.99,
        };

        final apiResponse = ApiResponse.success(responseData);

        when(
          mockApiClient.put<Map<String, dynamic>>(
            '/api/subscriptions/$subscriptionId',
            data: anyNamed('data'),
          ),
        ).thenAnswer((_) async => apiResponse);

        // Act
        final result = await subscriptionService.updateSubscription(
          subscriptionId,
          request,
        );

        // Assert
        expect(result, isA<SubscriptionSuccess<SubscriptionResponse>>());

        result.when(
          success: (response) {
            expect(response.id, equals(subscriptionId));
            expect(response.storeName, equals('Updated Store'));
            expect(response.price, equals(399.99));
            expect(response.isPending, isTrue);
          },
          failure: (error) {
            fail('Expected success but got failure: ${error.technicalMessage}');
          },
        );

        verify(
          mockApiClient.put<Map<String, dynamic>>(
            '/api/subscriptions/$subscriptionId',
            data: request.toJson(),
          ),
        ).called(1);
      });

      test('should cancel subscription using core models', () async {
        // Arrange
        const subscriptionId = 'sub-123';
        final responseData = {
          'id': subscriptionId,
          'status': 'cancelled',
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-02T00:00:00Z',
        };

        final apiResponse = ApiResponse.success(responseData);

        when(
          mockApiClient.delete<Map<String, dynamic>>(
            '/api/subscriptions/$subscriptionId',
          ),
        ).thenAnswer((_) async => apiResponse);

        // Act
        final result = await subscriptionService.cancelSubscription(
          subscriptionId,
        );

        // Assert
        expect(result, isA<SubscriptionSuccess<SubscriptionResponse>>());

        result.when(
          success: (response) {
            expect(response.id, equals(subscriptionId));
            expect(response.status, equals('cancelled'));
            expect(response.statusMessageArabic, contains('إلغاء'));
          },
          failure: (error) {
            fail('Expected success but got failure: ${error.technicalMessage}');
          },
        );

        verify(
          mockApiClient.delete<Map<String, dynamic>>(
            '/api/subscriptions/$subscriptionId',
          ),
        ).called(1);
      });

      test('should handle database errors using core error types', () async {
        // Arrange
        final request = SubscriptionRequest(
          storeName: 'Test Store',
          phone: '0501234567',
          city: 'Riyadh',
          address: 'Test Address, Test District, Riyadh',
          description: 'Test store description for testing purposes',
          planType: 'premium',
          price: 299.99,
          userId: '12345678-1234-1234-1234-123456789012',
        );

        final apiResponse = ApiResponse<Map<String, dynamic>>.error(
          'Database connection failed',
        );

        when(
          mockApiClient.post<Map<String, dynamic>>(
            '/api/subscriptions',
            data: anyNamed('data'),
          ),
        ).thenAnswer((_) async => apiResponse);

        // Act
        final result = await subscriptionService.submitSubscriptionRequest(
          request,
        );

        // Assert
        expect(result, isA<SubscriptionFailure<SubscriptionResponse>>());

        result.when(
          success: (response) {
            fail('Expected database error but got success');
          },
          failure: (error) {
            expect(error, isA<DatabaseError>());
            final databaseError = error as DatabaseError;
            expect(databaseError.message, contains('قاعدة البيانات'));
            expect(databaseError.isRetryable, isTrue);
            expect(databaseError.severity, equals(ErrorSeverity.high));
          },
        );
      });

      test(
        'should handle authentication errors using core error types',
        () async {
          // Arrange
          final request = SubscriptionRequest(
            storeName: 'Test Store',
            phone: '0501234567',
            city: 'Riyadh',
            address: 'Test Address, Test District, Riyadh',
            description: 'Test store description for testing purposes',
            planType: 'premium',
            price: 299.99,
            userId: '12345678-1234-1234-1234-123456789012',
          );

          final apiResponse = ApiResponse<Map<String, dynamic>>.error(
            'Unauthorized access',
          );

          when(
            mockApiClient.post<Map<String, dynamic>>(
              '/api/subscriptions',
              data: anyNamed('data'),
            ),
          ).thenAnswer((_) async => apiResponse);

          // Act
          final result = await subscriptionService.submitSubscriptionRequest(
            request,
          );

          // Assert
          expect(result, isA<SubscriptionFailure<SubscriptionResponse>>());

          result.when(
            success: (response) {
              fail('Expected authentication error but got success');
            },
            failure: (error) {
              expect(error, isA<AuthenticationError>());
              final authError = error as AuthenticationError;
              expect(authError.message, contains('المصادقة'));
              expect(authError.isRetryable, isFalse);
              expect(authError.severity, equals(ErrorSeverity.high));
            },
          );
        },
      );
    });

    group('Core Model Validation', () {
      test('should validate all required fields in SubscriptionRequest', () {
        // Arrange
        final validRequest = SubscriptionRequest(
          storeName: 'Valid Store Name',
          phone: '0501234567',
          city: 'Riyadh',
          address: 'Valid Address, Test District, Riyadh',
          description: 'Valid store description for testing purposes',
          planType: 'premium',
          price: 299.99,
          userId: '12345678-1234-1234-1234-123456789012',
        );

        final invalidRequest = SubscriptionRequest(
          storeName: '',
          phone: '123',
          city: '',
          address: 'short',
          description: 'short',
          planType: 'invalid',
          price: -100,
          userId: 'invalid',
        );

        // Act & Assert
        expect(validRequest.isValid, isTrue);
        expect(validRequest.validate(), isEmpty);

        expect(invalidRequest.isValid, isFalse);
        final errors = invalidRequest.validate();
        expect(errors, isNotEmpty);
        expect(errors.containsKey('storeName'), isTrue);
        expect(errors.containsKey('phone'), isTrue);
        expect(errors.containsKey('city'), isTrue);
        expect(errors.containsKey('address'), isTrue);
        expect(errors.containsKey('description'), isTrue);
        expect(errors.containsKey('planType'), isTrue);
        expect(errors.containsKey('price'), isTrue);
        expect(errors.containsKey('userId'), isTrue);
      });

      test('should provide Arabic error messages for validation', () {
        // Arrange
        final invalidRequest = SubscriptionRequest(
          storeName: '',
          phone: '123',
          city: '',
          address: '',
          description: '',
          planType: 'invalid',
          price: -100,
          userId: 'invalid',
        );

        // Act
        final errors = invalidRequest.validate();

        // Assert
        expect(errors['storeName'], contains('اسم المتجر مطلوب'));
        expect(errors['phone'], contains('رقم الهاتف غير صحيح'));
        expect(errors['city'], contains('المدينة مطلوبة'));
        expect(errors['address'], contains('العنوان مطلوب'));
        expect(errors['description'], contains('وصف المتجر مطلوب'));
        expect(errors['planType'], contains('نوع الخطة غير صحيح'));
        expect(errors['price'], contains('السعر يجب أن يكون أكبر من صفر'));
        expect(errors['userId'], contains('معرف المستخدم غير صحيح'));
      });
    });

    group('Core Model Response Helpers', () {
      test('should provide correct status helpers in SubscriptionResponse', () {
        // Arrange & Act & Assert
        final pendingResponse = SubscriptionResponse(
          id: 'sub-1',
          status: 'pending',
          createdAt: DateTime.now(),
        );
        expect(pendingResponse.isPending, isTrue);
        expect(pendingResponse.isSuccess, isFalse);
        expect(pendingResponse.isFailed, isFalse);

        final successResponse = SubscriptionResponse(
          id: 'sub-2',
          status: 'success',
          createdAt: DateTime.now(),
        );
        expect(successResponse.isSuccess, isTrue);
        expect(successResponse.isPending, isFalse);
        expect(successResponse.isFailed, isFalse);

        final failedResponse = SubscriptionResponse(
          id: 'sub-3',
          status: 'failed',
          createdAt: DateTime.now(),
        );
        expect(failedResponse.isFailed, isTrue);
        expect(failedResponse.isSuccess, isFalse);
        expect(failedResponse.isPending, isFalse);
      });

      test('should provide Arabic status messages', () {
        // Arrange & Act & Assert
        final pendingResponse = SubscriptionResponse(
          id: 'sub-1',
          status: 'pending',
          createdAt: DateTime.now(),
        );
        expect(pendingResponse.statusMessageArabic, contains('قيد المراجعة'));

        final successResponse = SubscriptionResponse(
          id: 'sub-2',
          status: 'approved',
          createdAt: DateTime.now(),
        );
        expect(successResponse.statusMessageArabic, contains('تم قبول'));

        final failedResponse = SubscriptionResponse(
          id: 'sub-3',
          status: 'rejected',
          createdAt: DateTime.now(),
        );
        expect(failedResponse.statusMessageArabic, contains('تم رفض'));
      });
    });
  });
}
