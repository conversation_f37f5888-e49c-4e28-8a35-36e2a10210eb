/// ============================================================================
/// AUTHENTICATION INTEGRATION TESTS
/// ============================================================================
///
/// Integration tests for the unified authentication system
/// Tests the complete authentication flow from UI to backend
///
/// Forever Plan Architecture: Flutter UI Only → Go API → Supabase Data
/// ============================================================================

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:carnow/core/auth/auth_models.dart';
import 'package:carnow/core/auth/unified_auth_provider.dart';

void main() {
  group('Authentication Integration Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    group('Email Authentication Flow', () {
      testWidgets('should complete full email authentication flow', (tester) async {
        // Arrange
        final authProvider = container.read(unifiedAuthProviderProvider.notifier);
        const testEmail = '<EMAIL>';
        const testPassword = 'TestPassword123!';
        const testFirstName = 'Integration';
        const testLastName = 'Test';

        // Act & Assert - Initial state
        var currentState = container.read(unifiedAuthProviderProvider);
        expect(currentState, isA<AuthStateInitial>());

        // Act - Sign up
        final signUpResult = await authProvider.signUp(
          firstName: testFirstName,
          lastName: testLastName,
          email: testEmail,
          password: testPassword,
        );

        signUpResult.when(
          success: (user, token, refreshToken, tokenExpiry, metadata) {
            expect(user.email, equals(testEmail));
            expect(user.firstName, equals(testFirstName));
            expect(user.lastName, equals(testLastName));
            expect(token, isNotEmpty);
          },
          failure: (error, errorCode, errorType, isRecoverable, details) {
            fail('Sign up should succeed: $error');
          },
          cancelled: (reason) {
            fail('Sign up should not be cancelled');
          },
          pending: (message, pendingAction, actionData) {
            fail('Sign up should not be pending');
          },
        );

        // Assert - Should be in email verification state
        currentState = container.read(unifiedAuthProviderProvider);
        expect(currentState, isA<AuthStateEmailVerificationPending>());

        // Act - Sign in with same credentials (simulating verification)
        final signInResult = await authProvider.signInWithEmail(
          email: testEmail,
          password: testPassword,
        );

        signInResult.when(
          success: (user, token, refreshToken, tokenExpiry, metadata) {
            expect(user.email, equals(testEmail));
            expect(token, isNotEmpty);
          },
          failure: (error, errorCode, errorType, isRecoverable, details) {
            fail('Sign in should succeed: $error');
          },
          cancelled: (reason) {
            fail('Sign in should not be cancelled');
          },
          pending: (message, pendingAction, actionData) {
            fail('Sign in should not be pending');
          },
        );

        // Assert - Should be authenticated
        currentState = container.read(unifiedAuthProviderProvider);
        expect(currentState, isA<AuthStateAuthenticated>());

        if (currentState is AuthStateAuthenticated) {
          expect(currentState.user.email, equals(testEmail));
          expect(currentState.token, isNotEmpty);
        }
      });

      testWidgets('should handle sign out flow', (tester) async {
        // Arrange
        final authProvider = container.read(unifiedAuthProviderProvider.notifier);
        const testEmail = '<EMAIL>';
        const testPassword = 'TestPassword123!';

        // Act - Sign in first
        await authProvider.signInWithEmail(
          email: testEmail,
          password: testPassword,
        );

        // Assert - Should be authenticated
        var currentState = container.read(unifiedAuthProviderProvider);
        expect(currentState, isA<AuthStateAuthenticated>());

        // Act - Sign out
        final signOutResult = await authProvider.signOut();

        signOutResult.when(
          success: (user, token, refreshToken, tokenExpiry, metadata) {
            // Successfully signed out
            expect(user.email, isEmpty);
            expect(token, isEmpty);
          },
          failure: (error, errorCode, errorType, isRecoverable, details) {
            fail('Sign out should succeed: $error');
          },
          cancelled: (reason) {
            fail('Sign out should not be cancelled');
          },
          pending: (message, pendingAction, actionData) {
            fail('Sign out should not be pending');
          },
        );
        final signOutCurrentState = container.read(unifiedAuthProviderProvider);
        expect(signOutCurrentState, isA<AuthStateUnauthenticated>());
      });
    });

    // =========================================================================
    // GOOGLE AUTHENTICATION INTEGRATION TESTS
    // =========================================================================

    group('Google Authentication Flow', () {
      testWidgets('should handle Google sign in flow', (tester) async {
        // Arrange
        final authProvider = container.read(unifiedAuthProviderProvider.notifier);

        // Act - Try Google sign in
        try {
          final signInResult = await authProvider.signInWithGoogle();

          signInResult.when(
            success: (user, token, refreshToken, tokenExpiry, metadata) {
              expect(user.email, isNotEmpty);
              expect(token, isNotEmpty);
            },
            failure: (error, errorCode, errorType, isRecoverable, details) {
              // Expected in test environment without proper Google OAuth setup
              expect(error, isNotEmpty);
            },
            cancelled: (reason) {
              // Expected in test environment
              expect(reason, isNotEmpty);
            },
            pending: (message, pendingAction, actionData) {
              fail('Google sign in should not be pending');
            },
          );
        } catch (e) {
          // Expected in test environment without proper Google OAuth setup
          expect(e, isA<Exception>());
        }
      });

      testWidgets('should handle Google sign out flow', (tester) async {
        // Arrange
        final authProvider = container.read(unifiedAuthProviderProvider.notifier);

        // Act - Try Google sign out
        try {
          final signOutResult = await authProvider.signOut();

          signOutResult.when(
            success: (user, token, refreshToken, tokenExpiry, metadata) {
              // Successfully signed out
              expect(user.email, isEmpty);
              expect(token, isEmpty);
            },
            failure: (error, errorCode, errorType, isRecoverable, details) {
              fail('Sign out should succeed: $error');
            },
            cancelled: (reason) {
              fail('Sign out should not be cancelled');
            },
            pending: (message, pendingAction, actionData) {
              fail('Sign out should not be pending');
            },
          );
          final signOutState = container.read(unifiedAuthProviderProvider);
          expect(signOutState, isA<AuthStateUnauthenticated>());
        } catch (e) {
          // Expected in test environment without proper Google OAuth setup
          expect(e, isA<Exception>());
        }
      });
    });

    // =========================================================================
    // SESSION MANAGEMENT INTEGRATION TESTS
    // =========================================================================

    group('Session Management', () {
      testWidgets('should handle session persistence across app restarts', (tester) async {
        // Arrange
        final authProvider = container.read(unifiedAuthProviderProvider.notifier);
        const testEmail = '<EMAIL>';
        const testPassword = 'TestPassword123!';

        // Act - Sign in
        await authProvider.signInWithEmail(
          email: testEmail,
          password: testPassword,
        );

        // Assert - Should be authenticated
        var currentState = container.read(unifiedAuthProviderProvider);
        expect(currentState, isA<AuthStateAuthenticated>());

        // Act - Create new container (simulating app restart)
        final newContainer = ProviderContainer();
        
        // Assert - Should restore session from storage
        final newState = newContainer.read(unifiedAuthProviderProvider);
        expect(newState, isA<AuthStateAuthenticated>());
        
        if (newState is AuthStateAuthenticated) {
          expect(newState.user.email, equals(testEmail));
          expect(newState.token, isNotNull);
        }

        newContainer.dispose();
      });
    });

    // =========================================================================
    // ERROR HANDLING INTEGRATION TESTS
    // =========================================================================

    group('Error Handling', () {
      testWidgets('should handle network errors gracefully', (tester) async {
        // Arrange
        final authProvider = container.read(unifiedAuthProviderProvider.notifier);

        // Act - Try to sign in with invalid credentials
        await authProvider.signInWithEmail(
          email: '<EMAIL>',
          password: 'wrongpassword',
        );

        // Assert - Should handle error appropriately
        final currentState = container.read(unifiedAuthProviderProvider);
        expect(currentState, anyOf([
          isA<AuthStateError>(),
          isA<AuthStateUnauthenticated>(),
        ]));
      });

      testWidgets('should handle session expiration', (tester) async {
        // Arrange
        final authProvider = container.read(unifiedAuthProviderProvider.notifier);
        const testEmail = '<EMAIL>';
        const testPassword = 'TestPassword123!';

        // Act - Sign in
        await authProvider.signInWithEmail(
          email: testEmail,
          password: testPassword,
        );

        // Assert - Should be authenticated
        var currentState = container.read(unifiedAuthProviderProvider);
        expect(currentState, isA<AuthStateAuthenticated>());

        // Act - Simulate session expiration (in real app, this would happen when token expires)
        // This would typically be handled by the token refresh mechanism
        // For testing, we can simulate by clearing tokens manually
        
        // Assert - Should handle expiration gracefully
        // The exact behavior depends on the implementation
      });
    });

    // =========================================================================
    // TOKEN REFRESH INTEGRATION TESTS
    // =========================================================================

    group('Token Refresh', () {
      testWidgets('should handle token refresh automatically', (tester) async {
        // Arrange
        final authProvider = container.read(unifiedAuthProviderProvider.notifier);
        const testEmail = '<EMAIL>';
        const testPassword = 'TestPassword123!';

        // Act - Sign in
        await authProvider.signInWithEmail(
          email: testEmail,
          password: testPassword,
        );

        // Assert - Should be authenticated
        final signInState = container.read(unifiedAuthProviderProvider);
        expect(signInState, isA<AuthStateAuthenticated>());

        // Act - Sign out to test refresh
        await authProvider.signOut();
        final signOutState = container.read(unifiedAuthProviderProvider);
        expect(signOutState, isA<AuthStateUnauthenticated>());

        // Note: Token refresh testing would require more complex setup
        // with actual token expiration simulation
      });
    });

    // =========================================================================
    // SECURITY INTEGRATION TESTS
    // =========================================================================

    group('Security Features', () {
      testWidgets('should handle secure token storage', (tester) async {
        // Arrange
        final authProvider = container.read(unifiedAuthProviderProvider.notifier);
        const testEmail = '<EMAIL>';
        const testPassword = 'TestPassword123!';

        // Act - Sign in
        await authProvider.signInWithEmail(
          email: testEmail,
          password: testPassword,
        );

        // Assert - Should be authenticated with secure token
        final finalState = container.read(unifiedAuthProviderProvider);
        expect(finalState, isA<AuthStateAuthenticated>());

        if (finalState is AuthStateAuthenticated) {
          expect(finalState.token, isNotEmpty);
          expect(finalState.refreshToken, isNotEmpty);
        }
      });
    });

    // =========================================================================
    // PERFORMANCE INTEGRATION TESTS
    // =========================================================================

    group('Performance Tests', () {
      testWidgets('should complete authentication flow within reasonable time', (tester) async {
        // Arrange
        final authProvider = container.read(unifiedAuthProviderProvider.notifier);
        const testEmail = '<EMAIL>';
        const testPassword = 'TestPassword123!';

        // Act & Measure - Sign in
        final stopwatch = Stopwatch()..start();
        
        await authProvider.signInWithEmail(
          email: testEmail,
          password: testPassword,
        );
        
        stopwatch.stop();

        // Assert - Should complete within reasonable time (5 seconds for test environment)
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));

        // Assert - Should be authenticated
        final finalState = container.read(unifiedAuthProviderProvider);
        expect(finalState, isA<AuthStateAuthenticated>());
      });
    });
  });
}
