import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:carnow/core/app/carnow_app.dart';
import 'package:carnow/core/networking/simple_api_client.dart';
import 'package:mockito/mockito.dart';

// Mock classes for testing
class MockSimpleApiClient extends Mock implements SimpleApiClient {}

late MockSimpleApiClient mockApiClient;

// Helper: returns provider override list with mock clients. Declared at
// top-level so it can be reused by multiple test groups.
List<Override> _providerOverrides() => [
  simpleApiClientProvider.overrideWithValue(mockApiClient),
];

// Detect CI environment to optionally skip integration tests when external
// services (e.g. Supabase) are not available.
const bool _isCI = bool.fromEnvironment('CI');

void main() {
  group('App Integration Tests', () {
    setUp(() {
      mockApiClient = MockSimpleApiClient();
      // Basic setup without complex auth mocking
    });

    testWidgets('App starts without crashing', (WidgetTester tester) async {
      // Create a test container with mocked dependencies
      final container = ProviderContainer(
        overrides: _providerOverrides(),
      );

      // Build the app with proper test container
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: const CarNowApp(),
        ),
      );

      // Wait for the app to settle
      await tester.pumpAndSettle();

      // Just verify the app loaded without crashing
      expect(find.byType(MaterialApp), findsOneWidget);
      
      container.dispose();
    });

    testWidgets('should show loading state initially', (
      WidgetTester tester,
    ) async {
      // Create a test container
      final container = ProviderContainer(
        overrides: _providerOverrides(),
      );

      // Build the app
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: const CarNowApp(),
        ),
      );

      // Initially, we might see a loading indicator or splash screen
      // This depends on the app's initialization flow
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('should handle theme switching', (WidgetTester tester) async {
      // Create a test container
      final container = ProviderContainer(
        overrides: _providerOverrides(),
      );

      // Build the app
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: const CarNowApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Find the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));

      // Verify that theme is set
      expect(materialApp.theme, isNotNull);
      expect(materialApp.darkTheme, isNotNull);
    });

    testWidgets('should handle localization correctly', (
      WidgetTester tester,
    ) async {
      // Create a test container
      final container = ProviderContainer(
        overrides: _providerOverrides(),
      );

      // Build the app
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: const CarNowApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Find the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));

      // Verify localization setup
      expect(materialApp.localizationsDelegates, isNotEmpty);
      expect(materialApp.supportedLocales, isNotEmpty);
      expect(materialApp.locale, isNotNull);
    });

    group('Navigation Tests', () {
      testWidgets('should handle deep links', (WidgetTester tester) async {
        // This test would verify deep link handling
        // For now, we'll just verify the router is set up

        final container = ProviderContainer(
          overrides: _providerOverrides(),
        );

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const CarNowApp(),
          ),
        );

        await tester.pumpAndSettle();

        final materialApp = tester.widget<MaterialApp>(
          find.byType(MaterialApp),
        );
        expect(materialApp.routerConfig, isNotNull);
      });
    });

    group('Error Handling Tests', () {
      testWidgets('should handle initialization errors gracefully', (
        WidgetTester tester,
      ) async {
        // Test with basic container - no complex auth mocking needed
        final container = ProviderContainer(
          overrides: _providerOverrides(),
        );

        // The app should still build even if there are initialization errors
        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const CarNowApp(),
          ),
        );

        await tester.pumpAndSettle();

        // App should handle gracefully
        expect(find.byType(MaterialApp), findsOneWidget);
        
        container.dispose();
      });
    });

    group('Performance Tests', () {
      testWidgets('should build within reasonable time', (
        WidgetTester tester,
      ) async {
        final stopwatch = Stopwatch()..start();

        final container = ProviderContainer(
          overrides: _providerOverrides(),
        );

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const CarNowApp(),
          ),
        );

        await tester.pumpAndSettle();

        stopwatch.stop();

        // App should build within 5 seconds (generous limit for CI)
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
      });

      testWidgets('should not have memory leaks in basic navigation', (
        WidgetTester tester,
      ) async {
        final container = ProviderContainer(
          overrides: _providerOverrides(),
        );

        // Build and rebuild the app multiple times
        for (var i = 0; i < 3; i++) {
          await tester.pumpWidget(
            UncontrolledProviderScope(
              container: container,
              child: const CarNowApp(),
            ),
          );

          await tester.pumpAndSettle();

          // Simulate navigation or state changes
          await tester.pump();
        }

        // If we get here without memory issues, the test passes
        expect(find.byType(MaterialApp), findsOneWidget);
      });
    });

    group('Accessibility Tests', () {
      testWidgets('should meet basic accessibility requirements', (
        WidgetTester tester,
      ) async {
        final container = ProviderContainer(
          overrides: _providerOverrides(),
        );

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const CarNowApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Check for basic accessibility compliance
        final handle = tester.ensureSemantics();

        // Verify that semantic information is available
        expect(tester.getSemantics(find.byType(MaterialApp)), isNotNull);

        handle.dispose();
      });
    });

    tearDown(() {
      // Clean up any resources if needed
    });
  }, skip: _isCI);

  group('Widget Integration Tests', () {
    testWidgets('should handle provider dependencies correctly', (
      WidgetTester tester,
    ) async {
      // Test that widgets can access their required providers
      final container = ProviderContainer(
        overrides: _providerOverrides(),
      );

      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: Consumer(
              builder: (context, ref, child) {
                // This tests that the provider system is working
                return const Scaffold(
                  body: Center(child: Text('Provider Test')),
                );
              },
            ),
          ),
        ),
      );

      expect(find.text('Provider Test'), findsOneWidget);
    });
  }, skip: _isCI);
}
