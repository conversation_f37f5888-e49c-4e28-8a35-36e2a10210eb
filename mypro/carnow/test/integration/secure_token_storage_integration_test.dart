import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/auth/enhanced_secure_token_storage.dart';

void main() {
  group('SecureTokenStorage Integration Tests', () {
    late EnhancedSecureTokenStorage tokenStorage;

    setUp(() {
      tokenStorage = EnhancedSecureTokenStorage();
    });

    testWidgets('should store and retrieve tokens in real environment', (
      tester,
    ) async {
      // Arrange
      const accessToken = 'real_access_token_12345';
      const refreshToken = 'real_refresh_token_67890';

      // Act - Store tokens
      await tokenStorage.storeToken(accessToken);
      await tokenStorage.storeRefreshToken(refreshToken);

      // Assert - Verify tokens exist
      expect(await tokenStorage.hasToken(), isTrue);
      expect(await tokenStorage.hasRefreshToken(), isTrue);

      // Assert - Retrieve tokens
      final retrievedAccessToken = await tokenStorage.getToken();
      final retrievedRefreshToken = await tokenStorage.getRefreshToken();

      expect(retrievedAccessToken, equals(accessToken));
      expect(retrievedRefreshToken, equals(refreshToken));
    });

    testWidgets('should handle token expiry correctly', (tester) async {
      // Arrange
      const accessToken = 'expiring_access_token';

      // Act - Store token
      await tokenStorage.storeToken(accessToken);

      // Assert - Token should exist initially
      expect(await tokenStorage.hasToken(), isTrue);

      // Act - Check if token will expire within 10 minutes (it should, since default is 15 minutes)
      final willExpire = await tokenStorage.willTokenExpireWithin(
        const Duration(minutes: 20),
      );
      expect(willExpire, isTrue);

      // Act - Check if token will expire within 5 minutes (it shouldn't)
      final willExpireSoon = await tokenStorage.willTokenExpireWithin(
        const Duration(minutes: 5),
      );
      expect(willExpireSoon, isFalse);
    });

    testWidgets('should cleanup expired tokens automatically', (tester) async {
      // Arrange
      const accessToken = 'test_access_token';
      const refreshToken = 'test_refresh_token';

      // Act - Store tokens
      await tokenStorage.storeToken(accessToken);
      await tokenStorage.storeRefreshToken(refreshToken);

      // Assert - Tokens should exist
      expect(await tokenStorage.hasToken(), isTrue);
      expect(await tokenStorage.hasRefreshToken(), isTrue);

      // Act - Run cleanup (should not remove valid tokens)
      await tokenStorage.clearExpiredTokens();

      // Assert - Tokens should still exist
      expect(await tokenStorage.hasToken(), isTrue);
      expect(await tokenStorage.hasRefreshToken(), isTrue);
    });

    testWidgets('should clear all tokens', (tester) async {
      // Arrange
      const accessToken = 'test_access_token';
      const refreshToken = 'test_refresh_token';

      // Act - Store tokens
      await tokenStorage.storeToken(accessToken);
      await tokenStorage.storeRefreshToken(refreshToken);

      // Assert - Tokens should exist
      expect(await tokenStorage.hasToken(), isTrue);
      expect(await tokenStorage.hasRefreshToken(), isTrue);

      // Act - Clear all tokens
      await tokenStorage.clearAllData();

      // Assert - Tokens should not exist
      expect(await tokenStorage.hasValidToken(), isFalse);
      expect(await tokenStorage.hasValidRefreshToken(), isFalse);
    });

    testWidgets('should check storage health', (tester) async {
      // Act
      final isHealthy = await tokenStorage.validateStorageIntegrity();

      // Assert
      expect(isHealthy, isTrue);
    });

    testWidgets('should get token expiry times', (tester) async {
      // Arrange
      const accessToken = 'test_access_token';
      const refreshToken = 'test_refresh_token';

      // Act - Store tokens
      await tokenStorage.storeToken(accessToken);
      await tokenStorage.storeRefreshToken(refreshToken);

      // Act - Get expiry times
      final accessTokenExpiry = await tokenStorage.getTokenExpiry();
      final refreshTokenExpiry = await tokenStorage.getRefreshTokenExpiry();

      // Assert
      expect(accessTokenExpiry, isNotNull);
      expect(refreshTokenExpiry, isNotNull);
      expect(accessTokenExpiry!.isAfter(DateTime.now()), isTrue);
      expect(refreshTokenExpiry!.isAfter(DateTime.now()), isTrue);

      // Access token should expire before refresh token
      expect(accessTokenExpiry.isBefore(refreshTokenExpiry), isTrue);
    });

    testWidgets('should handle multiple storage operations', (tester) async {
      // Test multiple store/retrieve cycles
      for (int i = 0; i < 5; i++) {
        final accessToken = 'access_token_$i';
        final refreshToken = 'refresh_token_$i';

        await tokenStorage.storeToken(accessToken);
        await tokenStorage.storeRefreshToken(refreshToken);

        final retrievedAccessToken = await tokenStorage.getToken();
        final retrievedRefreshToken = await tokenStorage.getRefreshToken();

        expect(retrievedAccessToken, equals(accessToken));
        expect(retrievedRefreshToken, equals(refreshToken));
      }
    });

    tearDown(() async {
      // Clean up after each test
      await tokenStorage.clearAllData();
    });
  });
}
