import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:integration_test/integration_test.dart';
import 'package:dio/dio.dart';
import 'package:carnow/core/networking/simple_api_client.dart';
import 'package:carnow/core/auth/simple_auth_system.dart';
import 'package:carnow/core/config/backend_config.dart';
import 'package:carnow/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('End-to-End Architecture Tests - Forever Plan', () {
    late ProviderContainer container;
    late Dio apiClient;

    setUpAll(() async {
      // Initialize the app for testing
      app.main();
      await Future.delayed(const Duration(seconds: 3));
      
      container = ProviderContainer();
      apiClient = container.read(simpleApiClientProvider);
    });

    tearDownAll(() {
      container.dispose();
    });

    group('1. Backend Health and Connectivity', () {
      testWidgets('Go backend should be accessible and respond correctly', 
        (WidgetTester tester) async {
        
        // Test backend health endpoint
        final response = await apiClient.get('/health');
        
        expect(response.statusCode, 200);
        expect(response.data, isNotNull);
        
        // Test API base structure
        expect(response.data, containsPair('status', 'healthy'));
      });

      testWidgets('Backend should handle authentication headers correctly',
        (WidgetTester tester) async {
        
        // Test that requests include proper headers
        final response = await apiClient.get('/health');
        
        expect(response.statusCode, 200);
        // Verify the request was processed correctly
      });
    });

    group('2. Authentication Flow Tests', () {
      testWidgets('SimpleAuthSystem should work with Go backend JWT validation',
        (WidgetTester tester) async {
        
        final authSystem = container.read(simpleAuthSystemProvider.notifier);
        
        // Test that auth system is initialized
        expect(authSystem, isNotNull);
        
        // Test unauthenticated state
        final initialState = container.read(simpleAuthSystemProvider);
        expect(initialState.status, AuthStatus.unauthenticated);
        
        // Note: Actual login test would require real credentials
        // This tests the structure and availability
      });

      testWidgets('JWT token should be properly passed to Go backend',
        (WidgetTester tester) async {
        
        // Test that SimpleApiClient includes JWT tokens when available
        final client = container.read(simpleApiClientProvider);
        
        // Verify interceptor is configured
        expect(client.interceptors.length, greaterThan(0));
        
        // Test protected endpoint (should get 401 when unauthenticated)
        try {
          await client.get('/user/profile');
        } on DioException catch (e) {
          // Expect 401 for unauthenticated requests
          expect(e.response?.statusCode, anyOf([401, 404]));
        }
      });
    });

    group('3. Data Flow Tests - Flutter → Go → Supabase', () {
      testWidgets('Products endpoint should follow Forever Plan architecture',
        (WidgetTester tester) async {
        
        try {
          final response = await apiClient.get('/products');
          
          // Should get data from Go backend (not direct Supabase)
          expect(response.statusCode, anyOf([200, 401])); // 401 if auth required
          
          if (response.statusCode == 200) {
            expect(response.data, isNotNull);
            expect(response.data, isA<Map<String, dynamic>>());
            
            // Verify Go backend response structure
            if (response.data['success'] != null) {
              expect(response.data['success'], isA<bool>());
            }
          }
        } on DioException catch (e) {
          // Log the error for debugging
          print('Products endpoint test: ${e.response?.statusCode} - ${e.message}');
          
          // Accept various responses as the endpoint might not be fully implemented
          expect(e.response?.statusCode, anyOf([401, 404, 500]));
        }
      });

      testWidgets('Categories endpoint should work through Go backend',
        (WidgetTester tester) async {
        
        try {
          final response = await apiClient.get('/categories');
          
          expect(response.statusCode, anyOf([200, 401, 404]));
          
          if (response.statusCode == 200) {
            expect(response.data, isNotNull);
          }
        } on DioException catch (e) {
          print('Categories endpoint test: ${e.response?.statusCode} - ${e.message}');
          
          // Accept various responses as long as the architecture is working
          expect(e.response?.statusCode, anyOf([401, 404, 500]));
        }
      });

      testWidgets('Search endpoint should work through Go backend',
        (WidgetTester tester) async {
        
        try {
          final response = await apiClient.get('/search', 
            queryParameters: {'q': 'test', 'limit': 10});
          
          expect(response.statusCode, anyOf([200, 401, 404]));
          
          if (response.statusCode == 200) {
            expect(response.data, isNotNull);
          }
        } on DioException catch (e) {
          print('Search endpoint test: ${e.response?.statusCode} - ${e.message}');
          
          expect(e.response?.statusCode, anyOf([401, 404, 500]));
        }
      });
    });

    group('4. Backend Stability and Performance', () {
      testWidgets('Backend should respond within acceptable time limits',
        (WidgetTester tester) async {
        
        final stopwatch = Stopwatch()..start();
        
        try {
          await apiClient.get('/health');
          stopwatch.stop();
          
          // Backend should respond within 5 seconds
          expect(stopwatch.elapsedMilliseconds, lessThan(5000));
          
          print('Backend response time: ${stopwatch.elapsedMilliseconds}ms');
        } catch (e) {
          stopwatch.stop();
          print('Backend response test failed: $e');
          
          // Even if it fails, it shouldn't take too long
          expect(stopwatch.elapsedMilliseconds, lessThan(10000));
        }
      });

      testWidgets('Backend should handle multiple concurrent requests',
        (WidgetTester tester) async {
        
        final futures = List.generate(5, (index) => 
          apiClient.get('/health').catchError((e) => Response(
            requestOptions: RequestOptions(path: '/health'),
            statusCode: 500,
          ))
        );
        
        final responses = await Future.wait(futures);
        
        // At least some requests should succeed
        final successfulResponses = responses
            .where((r) => r.statusCode == 200)
            .length;
        
        expect(successfulResponses, greaterThanOrEqualTo(1));
        print('Successful concurrent requests: $successfulResponses/5');
      });

      testWidgets('Backend configuration should be correct',
        (WidgetTester tester) async {
        
        // Verify backend configuration
        expect(CarnowBackendConfig.apiBaseUrl, isNotEmpty);
        expect(CarnowBackendConfig.apiBaseUrl, contains('backend-go-8klm.onrender.com'));
        
        // Verify timeout configuration
        expect(apiClient.options.connectTimeout?.inSeconds, greaterThan(0));
        expect(apiClient.options.receiveTimeout?.inSeconds, greaterThan(0));
        
        print('Backend URL: ${CarnowBackendConfig.apiBaseUrl}');
      });
    });

    group('5. Architecture Compliance Verification', () {
      testWidgets('SimpleApiClient should be properly configured',
        (WidgetTester tester) async {
        
        final client = container.read(simpleApiClientProvider);
        
        // Verify base URL points to Go backend
        expect(client.options.baseUrl, CarnowBackendConfig.apiBaseUrl);
        
        // Verify headers are set correctly
        expect(client.options.headers['Content-Type'], 'application/json');
        expect(client.options.headers['Accept'], 'application/json');
        
        // Verify timeout configuration
        expect(client.options.connectTimeout, isNotNull);
        expect(client.options.receiveTimeout, isNotNull);
      });

      testWidgets('Error handling should work correctly',
        (WidgetTester tester) async {
        
        try {
          // Test non-existent endpoint
          await apiClient.get('/non-existent-endpoint');
        } on DioException catch (e) {
          // Should get proper error response
          expect(e.response?.statusCode, anyOf([404, 500]));
          expect(e.message, isNotNull);
          
          print('Error handling test passed: ${e.response?.statusCode}');
        }
      });

      testWidgets('Provider system should work correctly',
        (WidgetTester tester) async {
        
        // Test that providers can be accessed
        expect(container.read(simpleApiClientProvider), isA<Dio>());
        expect(container.read(simpleAuthSystemProvider), isNotNull);
        
        // Test provider lifecycle
        container.invalidate(simpleApiClientProvider);
        final newClient = container.read(simpleApiClientProvider);
        expect(newClient, isA<Dio>());
      });
    });

    group('6. Integration with Real App Flow', () {
      testWidgets('App should start and initialize correctly with simplified architecture',
        (WidgetTester tester) async {
        
        // Test app initialization
        app.main();
        await tester.pumpAndSettle(const Duration(seconds: 5));
        
        // Verify app is running
        expect(find.byType(MaterialApp), findsOneWidget);
        
        // Verify no critical errors in initialization
        expect(tester.takeException(), isNull);
      });

      testWidgets('Navigation should work without direct Supabase calls',
        (WidgetTester tester) async {
        
        app.main();
        await tester.pumpAndSettle(const Duration(seconds: 3));
        
        // Test navigation to different sections
        if (find.text('الرئيسية').evaluate().isNotEmpty) {
          await tester.tap(find.text('الرئيسية'));
          await tester.pumpAndSettle();
          expect(find.byType(Scaffold), findsAtLeastNWidgets(1));
        }
        
        if (find.text('الفئات').evaluate().isNotEmpty) {
          await tester.tap(find.text('الفئات'));
          await tester.pumpAndSettle();
          expect(find.byType(Scaffold), findsAtLeastNWidgets(1));
        }
      });
    });
  });

  group('Performance and Load Tests', () {
    testWidgets('Memory usage should remain stable during navigation',
      (WidgetTester tester) async {
      
      app.main();
      await tester.pumpAndSettle();
      
      // Simulate navigation stress test
      for (int i = 0; i < 3; i++) {
        // Navigate to different sections
        if (find.text('الرئيسية').evaluate().isNotEmpty) {
          await tester.tap(find.text('الرئيسية'));
          await tester.pumpAndSettle();
        }
        
        await tester.pump(const Duration(milliseconds: 100));
      }
      
      // Should not have memory leaks or crashes
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('App should handle network errors gracefully',
      (WidgetTester tester) async {
      
      // Test app behavior when backend is not reachable
      // (This would require mocking network failures)
      
      app.main();
      await tester.pumpAndSettle();
      
      // App should still function for basic navigation
      expect(find.byType(MaterialApp), findsOneWidget);
      
      // Should not crash on network errors
      expect(tester.takeException(), isNull);
    });
  });
} 