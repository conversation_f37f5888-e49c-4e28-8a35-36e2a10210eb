import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/services/simple_connection_service.dart';

void main() {
  group('SimpleConnectionService Tests', () {
    late SimpleConnectionService connectionService;

    setUp(() {
      connectionService = SimpleConnectionService();
    });

    tearDown(() {
      connectionService.dispose();
    });

    test('should initialize with connected status', () {
      expect(connectionService.isConnected, isTrue);
    });

    test('should provide connection stream', () {
      expect(connectionService.connectionStream, isA<Stream<bool>>());
    });

    test('should execute operation when connected', () async {
      var executed = false;
      
      final result = await connectionService.executeWithConnectionCheck(
        () async {
          executed = true;
          return 'success';
        },
        operationName: 'test_operation',
      );

      expect(result, equals('success'));
      expect(executed, isTrue);
    });

    test('should use fallback when operation fails', () async {
      final result = await connectionService.executeWithConnectionCheck(
        () async {
          throw Exception('network error');
        },
        operationName: 'test_operation',
        fallbackValue: 'fallback',
      );

      expect(result, equals('fallback'));
    });

    test('should throw when no fallback provided and operation fails', () async {
      expect(
        () => connectionService.executeWithConnectionCheck(
          () async {
            throw Exception('network error');
          },
          operationName: 'test_operation',
        ),
        throwsA(isA<Exception>()),
      );
    });

    test('should start and stop monitoring', () {
      connectionService.startMonitoring();
      // Just verify no exceptions are thrown
      
      connectionService.stopMonitoring();
      // Just verify no exceptions are thrown
    });
  });
} 