// =============================================================================
// Session Persistence Configuration Tests - CarNow
// اختبارات تكوين استمرارية الجلسة - كار ناو
// =============================================================================

import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/config/session_persistence_config.dart';

void main() {
  group('SessionPersistenceConfig Tests', () {
    test('should have correct token expiry durations', () {
      // Test access token expiry (7 days)
      expect(SessionPersistenceConfig.accessTokenExpiry.inDays, equals(7));
      
      // Test refresh token expiry (30 days)
      expect(SessionPersistenceConfig.refreshTokenExpiry.inDays, equals(30));
    });

    test('should have correct session timeout duration', () {
      // Test session timeout (7 days)
      expect(SessionPersistenceConfig.sessionTimeout.inDays, equals(7));
    });

    test('should generate correct expiry dates', () {
      final now = DateTime.now();
      
      // Test access token expiry date
      final accessTokenExpiry = SessionPersistenceConfig.getAccessTokenExpiryFromNow();
      final expectedAccessExpiry = now.add(const Duration(days: 7));
      
      // Allow 1 second difference for test execution time
      expect(
        accessTokenExpiry.difference(expectedAccessExpiry).abs().inSeconds,
        lessThan(2),
      );
      
      // Test refresh token expiry date
      final refreshTokenExpiry = SessionPersistenceConfig.getRefreshTokenExpiryFromNow();
      final expectedRefreshExpiry = now.add(const Duration(days: 30));
      
      expect(
        refreshTokenExpiry.difference(expectedRefreshExpiry).abs().inSeconds,
        lessThan(2),
      );
    });

    test('should correctly determine if token should be refreshed', () {
      final now = DateTime.now();
      
      // Token expiring in 2 days - should not refresh yet
      final futureExpiry = now.add(const Duration(days: 2));
      expect(SessionPersistenceConfig.shouldRefreshToken(futureExpiry), isFalse);
      
      // Token expiring in 12 hours - should refresh
      final soonExpiry = now.add(const Duration(hours: 12));
      expect(SessionPersistenceConfig.shouldRefreshToken(soonExpiry), isTrue);
      
      // Token already expired - should refresh
      final pastExpiry = now.subtract(const Duration(hours: 1));
      expect(SessionPersistenceConfig.shouldRefreshToken(pastExpiry), isTrue);
    });

    test('should correctly determine session expiry', () {
      final lastActivity = DateTime.now().subtract(const Duration(days: 3));
      
      // Session should not be expired (within 7 days)
      expect(SessionPersistenceConfig.isSessionExpired(lastActivity), isFalse);
      
      // Session should be expired (more than 7 days)
      final oldActivity = DateTime.now().subtract(const Duration(days: 8));
      expect(SessionPersistenceConfig.isSessionExpired(oldActivity), isTrue);
    });

    test('should calculate time until session expiry correctly', () {
      final lastActivity = DateTime.now().subtract(const Duration(days: 6));
      
      final timeUntilExpiry = SessionPersistenceConfig.getTimeUntilSessionExpiry(lastActivity);
      
      expect(timeUntilExpiry, isNotNull);
      expect(timeUntilExpiry!.inDays, equals(0)); // Less than 1 day remaining
      expect(timeUntilExpiry.inHours, greaterThan(20)); // But more than 20 hours
    });

    test('should determine when to show session warning', () {
      final recentActivity = DateTime.now().subtract(const Duration(minutes: 30));
      expect(SessionPersistenceConfig.shouldShowSessionWarning(recentActivity), isFalse);
      
      final oldActivity = DateTime.now().subtract(const Duration(days: 6, hours: 23, minutes: 30));
      expect(SessionPersistenceConfig.shouldShowSessionWarning(oldActivity), isTrue);
    });

    test('should provide correct configuration summary', () {
      final config = SessionPersistenceConfig.getConfigSummary();
      
      expect(config['access_token_expiry_days'], equals(7));
      expect(config['refresh_token_expiry_days'], equals(30));
      expect(config['session_timeout_days'], equals(7));
      expect(config['enable_token_encryption'], isTrue);
      expect(config['require_biometric_auth'], isFalse);
    });

    test('should have reasonable heartbeat and refresh intervals', () {
      // Heartbeat should be less frequent than session timeout
      expect(
        SessionPersistenceConfig.heartbeatInterval.inHours,
        lessThan(SessionPersistenceConfig.sessionTimeout.inHours),
      );
      
      // Token refresh should happen before token expiry
      expect(
        SessionPersistenceConfig.tokenRefreshInterval.inDays,
        lessThan(SessionPersistenceConfig.accessTokenExpiry.inDays),
      );
      
      // Refresh threshold should be reasonable
      expect(
        SessionPersistenceConfig.tokenRefreshThreshold.inDays,
        lessThan(SessionPersistenceConfig.accessTokenExpiry.inDays),
      );
    });
  });
}
