import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:carnow/core/networking/simple_api_client.dart';
import 'package:carnow/core/models/api_response.dart';

void main() {
  group('SimpleApiClient Tests', () {
    late Dio dio;

    setUp(() {
      dio = Dio();
    });

    group('ApiResponse', () {
      test('should create successful response', () {
        final response = ApiResponse.success(
          'test data',
          message: 'Success',
        );

        expect(response.isSuccess, true);
        expect(response.data, 'test data');
        expect(response.message, 'Success');
        expect(response.isSuccess, true);
      });

      test('should create error response', () {
        const response = ApiResponse<String>(
          success: false,
          message: 'Error',
          statusCode: 400,
        );

        expect(response.success, false);
        expect(response.data, null);
        expect(response.message, 'Error');
        expect(response.statusCode, 400);
        expect(response.isSuccess, false);
      });
    });

    group('Extension Methods - Error Handling', () {
      test('should handle DioException properly', () {
        // Test ApiException
        final apiError = DioException(
          requestOptions: RequestOptions(path: '/test'),
          response: Response(
            requestOptions: RequestOptions(path: '/test'),
            statusCode: 400,
            data: {'message': 'Bad request'},
          ),
        );
        
        final apiException = dio.handleDioError(apiError);
        expect(apiException, isA<ApiException>());
        expect((apiException as ApiException).statusCode, 400);
        
        // Test AuthException
        final authError = DioException(
          requestOptions: RequestOptions(path: '/test'),
          response: Response(
            requestOptions: RequestOptions(path: '/test'),
            statusCode: 401,
            data: {'message': 'Unauthorized'},
          ),
        );
        
        final authException = dio.handleDioError(authError);
        expect(authException, isA<AuthException>());
        
        // Test ServerException
        final serverError = DioException(
          requestOptions: RequestOptions(path: '/test'),
          response: Response(
            requestOptions: RequestOptions(path: '/test'),
            statusCode: 500,
            data: {'message': 'Server error'},
          ),
        );
        
        final serverException = dio.handleDioError(serverError);
        expect(serverException, isA<ServerException>());
        
        // Test NetworkException
        final networkError = DioException(
          requestOptions: RequestOptions(path: '/test'),
          type: DioExceptionType.connectionTimeout,
        );
        
        final networkException = dio.handleDioError(networkError);
        expect(networkException, isA<NetworkException>());
      });

      test('should parse API response correctly', () {
        final response = Response<Map<String, dynamic>>(
          requestOptions: RequestOptions(path: '/test'),
          statusCode: 200,
          data: {
            'success': true,
            'data': {'id': '1', 'name': 'Test Item'},
            'message': 'Success',
          },
        );

        final apiResponse = dio.parseApiResponse<Map<String, dynamic>>(
          response,
          null,
        );

        expect(apiResponse.success, true);
        expect(apiResponse.data, {'id': '1', 'name': 'Test Item'});
        expect(apiResponse.message, 'Success');
        expect(apiResponse.statusCode, 200);
      });

      test('should parse API response with fromJson', () {
        final response = Response<Map<String, dynamic>>(
          requestOptions: RequestOptions(path: '/test'),
          statusCode: 200,
          data: {
            'success': true,
            'data': {'id': '1', 'name': 'Test Item'},
            'message': 'Success',
          },
        );

        final apiResponse = dio.parseApiResponse<TestModel>(
          response,
          (json) => TestModel.fromJson(json as Map<String, dynamic>),
        );

        expect(apiResponse.success, true);
        expect(apiResponse.data?.id, '1');
        expect(apiResponse.data?.name, 'Test Item');
      });
    });

    group('Custom Exceptions', () {
      test('ApiException should format correctly', () {
        const exception = ApiException('Test error', 400);
        
        expect(exception.message, 'Test error');
        expect(exception.statusCode, 400);
        expect(exception.toString(), 'ApiException: Test error (Status: 400)');
      });

      test('AuthException should format correctly', () {
        const exception = AuthException('Auth failed');
        
        expect(exception.message, 'Auth failed');
        expect(exception.toString(), 'AuthException: Auth failed');
      });

      test('ServerException should format correctly', () {
        const exception = ServerException('Server error');
        
        expect(exception.message, 'Server error');
        expect(exception.toString(), 'ServerException: Server error');
      });

      test('NetworkException should format correctly', () {
        const exception = NetworkException('Network error');
        
        expect(exception.message, 'Network error');
        expect(exception.toString(), 'NetworkException: Network error');
      });
    });
  });
}

// Test model for fromJson testing
class TestModel {
  const TestModel({
    required this.id,
    required this.name,
  });

  final String id;
  final String name;

  factory TestModel.fromJson(Map<String, dynamic> json) {
    return TestModel(
      id: json['id'] as String,
      name: json['name'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}