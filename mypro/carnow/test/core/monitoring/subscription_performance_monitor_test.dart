import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/monitoring/subscription_performance_monitor.dart';

void main() {
  group('SubscriptionPerformanceMonitor', () {
    late SubscriptionPerformanceMonitor monitor;

    setUp(() {
      monitor = SubscriptionPerformanceMonitor();
      monitor.clearMetrics(); // Start with clean state
    });

    test('should track operation timing', () async {
      // Start and end an operation
      monitor.startOperation('test_operation');
      await Future.delayed(const Duration(milliseconds: 100));
      monitor.endOperation('test_operation');

      // Check metrics
      final metrics = monitor.getMetrics('test_operation');
      expect(metrics, isNotNull);
      expect(metrics!.operationName, equals('test_operation'));
      expect(metrics.executionCount, equals(1));
      expect(metrics.lastDuration, isNotNull);
      expect(metrics.lastDuration!.inMilliseconds, greaterThan(90));
    });

    test('should track multiple executions', () {
      // Execute operation multiple times
      for (int i = 0; i < 3; i++) {
        monitor.startOperation('repeated_operation');
        monitor.endOperation('repeated_operation');
      }

      final metrics = monitor.getMetrics('repeated_operation');
      expect(metrics, isNotNull);
      expect(metrics!.executionCount, equals(3));
    });

    test('should log errors', () {
      monitor.logError('error_operation', 'Test error message');

      final metrics = monitor.getMetrics('error_operation');
      expect(metrics, isNotNull);
      expect(metrics!.errors.length, equals(1));
      expect(metrics.errors.first, contains('Test error message'));
    });

    test('should track slow operations', () {
      monitor.logSlowOperation('slow_operation', const Duration(seconds: 3));

      // Check that the slow operation was logged in the error logs
      final metrics = monitor.getMetrics('slow_operations');
      expect(metrics, isNotNull);
      expect(metrics!.errors.isNotEmpty, isTrue);
    });

    test('should generate performance report', () {
      // Add some test data
      monitor.startOperation('report_test');
      monitor.endOperation('report_test');
      monitor.logError('report_test', 'Test error');

      final report = monitor.generateReport();
      expect(report, contains('Subscription Performance Report'));
      expect(report, contains('report_test'));
      expect(report, contains('Executions: 1'));
    });

    test('should calculate error rate correctly', () {
      // Execute operation 10 times with 2 errors
      for (int i = 0; i < 10; i++) {
        monitor.startOperation('error_rate_test');
        monitor.endOperation('error_rate_test');
      }

      monitor.logError('error_rate_test', 'Error 1');
      monitor.logError('error_rate_test', 'Error 2');

      final metrics = monitor.getMetrics('error_rate_test');
      expect(metrics, isNotNull);
      expect(metrics!.errorRate, equals(20.0)); // 2/10 * 100 = 20%
    });

    test('should determine performance health', () {
      // Good performance: fast execution, no errors
      monitor.startOperation('healthy_operation');
      monitor.endOperation('healthy_operation');

      final healthyMetrics = monitor.getMetrics('healthy_operation');
      expect(healthyMetrics!.isPerformingWell, isTrue);

      // Poor performance: slow execution with errors
      monitor.logSlowOperation(
        'unhealthy_operation',
        const Duration(seconds: 5),
      );
      monitor.logError('unhealthy_operation', 'Performance error');

      final unhealthyMetrics = monitor.getMetrics('unhealthy_operation');
      expect(unhealthyMetrics, isNotNull);
      // Note: isPerformingWell might still be true if execution count is low
    });

    test('should track operations with extension method', () async {
      final result = await monitor.trackOperation('async_test', () async {
        await Future.delayed(const Duration(milliseconds: 50));
        return 'success';
      });

      expect(result, equals('success'));

      final metrics = monitor.getMetrics('async_test');
      expect(metrics, isNotNull);
      expect(metrics!.executionCount, equals(1));
    });

    test('should handle exceptions in tracked operations', () async {
      try {
        await monitor.trackOperation('failing_operation', () async {
          throw Exception('Test exception');
        });
      } catch (e) {
        expect(e.toString(), contains('Test exception'));
      }

      final metrics = monitor.getMetrics('failing_operation');
      expect(metrics, isNotNull);
      expect(metrics!.errors.length, equals(1));
      expect(metrics.errors.first, contains('Test exception'));
    });

    test('should track synchronous operations', () {
      final result = monitor.trackSyncOperation('sync_test', () {
        return 42;
      });

      expect(result, equals(42));

      final metrics = monitor.getMetrics('sync_test');
      expect(metrics, isNotNull);
      expect(metrics!.executionCount, equals(1));
    });
  });
}
