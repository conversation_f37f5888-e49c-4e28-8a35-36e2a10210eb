import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/models/subscription_request.dart';
import 'package:carnow/core/models/subscription_response.dart';
import 'package:carnow/core/models/subscription_error.dart';

void main() {
  group('SubscriptionRequest', () {
    test('should create SubscriptionRequest with valid data', () {
      const request = SubscriptionRequest(
        storeName: 'متجر قطع غيار السيارات',
        phone: '0501234567',
        city: 'الرياض',
        address: 'شارع الملك فهد، حي العليا، الرياض',
        description: 'متجر متخصص في بيع قطع غيار السيارات الأصلية والبديلة',
        planType: 'premium',
        price: 299.99,
        userId: '123e4567-e89b-12d3-a456-426614174000',
      );

      expect(request.storeName, 'متجر قطع غيار السيارات');
      expect(request.phone, '0501234567');
      expect(request.city, 'الرياض');
      expect(request.planType, 'premium');
      expect(request.price, 299.99);
    });

    test('should serialize to and from JSON', () {
      const request = SubscriptionRequest(
        storeName: 'Test Store',
        phone: '0501234567',
        city: 'Riyadh',
        address: 'Test Address',
        description: 'Test Description',
        planType: 'basic',
        price: 199.99,
        userId: '123e4567-e89b-12d3-a456-426614174000',
      );

      final json = request.toJson();
      final fromJson = SubscriptionRequest.fromJson(json);

      expect(fromJson, request);
      expect(json['store_name'], 'Test Store');
      expect(json['plan_type'], 'basic');
      expect(json['user_id'], '123e4567-e89b-12d3-a456-426614174000');
    });

    group('validation', () {
      test('should return no errors for valid data', () {
        const request = SubscriptionRequest(
          storeName: 'متجر قطع غيار السيارات',
          phone: '0501234567',
          city: 'الرياض',
          address: 'شارع الملك فهد، حي العليا، الرياض',
          description: 'متجر متخصص في بيع قطع غيار السيارات الأصلية والبديلة',
          planType: 'premium',
          price: 299.99,
          userId: '123e4567-e89b-12d3-a456-426614174000',
        );

        final errors = request.validate();
        expect(errors, isEmpty);
        expect(request.isValid, isTrue);
      });

      test('should return error for empty store name', () {
        const request = SubscriptionRequest(
          storeName: '',
          phone: '0501234567',
          city: 'الرياض',
          address: 'شارع الملك فهد، حي العليا، الرياض',
          description: 'متجر متخصص في بيع قطع غيار السيارات الأصلية والبديلة',
          planType: 'premium',
          price: 299.99,
          userId: '123e4567-e89b-12d3-a456-426614174000',
        );

        final errors = request.validate();
        expect(errors['storeName'], 'اسم المتجر مطلوب');
        expect(request.isValid, isFalse);
      });

      test('should return error for invalid phone number', () {
        const request = SubscriptionRequest(
          storeName: 'Test Store',
          phone: '123',
          city: 'الرياض',
          address: 'شارع الملك فهد، حي العليا، الرياض',
          description: 'متجر متخصص في بيع قطع غيار السيارات الأصلية والبديلة',
          planType: 'premium',
          price: 299.99,
          userId: '123e4567-e89b-12d3-a456-426614174000',
        );

        final errors = request.validate();
        expect(errors['phone'], 'رقم الهاتف غير صحيح');
      });

      test('should return error for invalid price', () {
        const request = SubscriptionRequest(
          storeName: 'Test Store',
          phone: '0501234567',
          city: 'الرياض',
          address: 'شارع الملك فهد، حي العليا، الرياض',
          description: 'متجر متخصص في بيع قطع غيار السيارات الأصلية والبديلة',
          planType: 'premium',
          price: -10.0,
          userId: '123e4567-e89b-12d3-a456-426614174000',
        );

        final errors = request.validate();
        expect(errors['price'], 'السعر يجب أن يكون أكبر من صفر');
      });

      test('should validate Saudi phone numbers correctly', () {
        const validPhones = [
          '0501234567',
          '+966501234567',
          '966501234567',
          '0112345678', // Riyadh landline
          '+966112345678',
        ];

        for (final phone in validPhones) {
          final request = SubscriptionRequest(
            storeName: 'Test Store',
            phone: phone,
            city: 'الرياض',
            address: 'شارع الملك فهد، حي العليا، الرياض',
            description: 'متجر متخصص في بيع قطع غيار السيارات الأصلية والبديلة',
            planType: 'premium',
            price: 299.99,
            userId: '123e4567-e89b-12d3-a456-426614174000',
          );

          final errors = request.validate();
          expect(
            errors['phone'],
            isNull,
            reason: 'Phone $phone should be valid',
          );
        }
      });
    });
  });

  group('SubscriptionResponse', () {
    test('should create SubscriptionResponse with valid data', () {
      final response = SubscriptionResponse(
        id: '123e4567-e89b-12d3-a456-426614174000',
        status: 'success',
        createdAt: DateTime.now(),
        message: 'Subscription created successfully',
      );

      expect(response.id, '123e4567-e89b-12d3-a456-426614174000');
      expect(response.status, 'success');
      expect(response.message, 'Subscription created successfully');
    });

    test('should serialize to and from JSON', () {
      final now = DateTime.now();
      final response = SubscriptionResponse(
        id: '123e4567-e89b-12d3-a456-426614174000',
        status: 'pending',
        createdAt: now,
        storeName: 'Test Store',
        planType: 'premium',
        price: 299.99,
      );

      final json = response.toJson();
      final fromJson = SubscriptionResponse.fromJson(json);

      expect(fromJson.id, response.id);
      expect(fromJson.status, response.status);
      expect(fromJson.storeName, response.storeName);
      expect(json['store_name'], 'Test Store');
      expect(json['plan_type'], 'premium');
    });

    group('status helpers', () {
      test('should correctly identify success status', () {
        final response = SubscriptionResponse(
          id: '123',
          status: 'success',
          createdAt: DateTime.now(),
        );

        expect(response.isSuccess, isTrue);
        expect(response.isPending, isFalse);
        expect(response.isFailed, isFalse);
      });

      test('should correctly identify pending status', () {
        final response = SubscriptionResponse(
          id: '123',
          status: 'pending',
          createdAt: DateTime.now(),
        );

        expect(response.isSuccess, isFalse);
        expect(response.isPending, isTrue);
        expect(response.isFailed, isFalse);
      });

      test('should return correct Arabic status messages', () {
        final successResponse = SubscriptionResponse(
          id: '123',
          status: 'success',
          createdAt: DateTime.now(),
        );

        final pendingResponse = SubscriptionResponse(
          id: '123',
          status: 'pending',
          createdAt: DateTime.now(),
        );

        expect(
          successResponse.statusMessageArabic,
          'تم قبول طلب الاشتراك بنجاح',
        );
        expect(
          pendingResponse.statusMessageArabic,
          'طلب الاشتراك قيد المراجعة',
        );
      });
    });
  });

  group('SubscriptionError', () {
    test('should create network error', () {
      const error = SubscriptionError.networkError(
        message: 'Connection failed',
        code: 'NETWORK_ERROR',
      );

      expect(
        error.userFriendlyMessageArabic,
        'خطأ في الاتصال: تحقق من اتصال الإنترنت وحاول مرة أخرى',
      );
      expect(error.isRetryable, isTrue);
      expect(error.severity, ErrorSeverity.medium);
    });

    test('should create validation error', () {
      const error = SubscriptionError.validationError(
        message: 'Invalid data',
        fieldErrors: {'storeName': 'Store name is required'},
        code: 'VALIDATION_ERROR',
      );

      expect(
        error.userFriendlyMessageArabic,
        'بيانات غير صحيحة: يرجى مراجعة البيانات المدخلة',
      );
      expect(error.isRetryable, isFalse);
      expect(error.severity, ErrorSeverity.low);
    });

    test('should handle different error types correctly', () {
      const networkError = SubscriptionError.networkError(
        message: 'Connection failed',
        code: 'NETWORK_ERROR',
      );

      const databaseError = SubscriptionError.databaseError(
        message: 'Database connection failed',
        code: 'DB_ERROR',
      );

      expect(networkError, isA<NetworkError>());
      expect(databaseError, isA<DatabaseError>());
      expect(networkError.technicalMessage, contains('Network Error'));
      expect(databaseError.technicalMessage, contains('Database Error'));
    });

    test('should provide correct suggested actions', () {
      const networkError = SubscriptionError.networkError(
        message: 'Connection failed',
        code: 'NETWORK_ERROR',
      );

      const validationError = SubscriptionError.validationError(
        message: 'Invalid data',
        fieldErrors: {'field': 'error'},
        code: 'VALIDATION_ERROR',
      );

      expect(
        networkError.suggestedActionArabic,
        'تحقق من اتصال الإنترنت وحاول مرة أخرى',
      );
      expect(
        validationError.suggestedActionArabic,
        'راجع البيانات المدخلة وصحح الأخطاء',
      );
    });
  });
}
