// =============================================================================
// CARNOW AUTH MODELS TESTS
// =============================================================================
// 
// Comprehensive unit tests for authentication models covering:
// - User model validation and helper methods
// - AuthState sealed class functionality
// - AuthResult sealed class functionality
// - AuthErrorType enum values
// - AuthProvider and AuthOperation enums
// - JSON serialization and deserialization
//
// Test Coverage:
// - User Model Tests (8 tests)
// - AuthState Tests (7 tests)
// - AuthResult Tests (4 tests)
// - AuthData Model Tests (1 test)
// - Request/Response Model Tests (4 tests)
//
// Author: CarNow Development Team
// =============================================================================

import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/auth/auth_models.dart';

void main() {
  group('Auth Models Tests', () {
    // =========================================================================
    // User Model Tests
    // =========================================================================
    
    group('User Model Tests', () {
      test('should create user with required fields', () {
        final user = User(
          id: 'test-user-123',
          email: '<EMAIL>',
          firstName: 'أحمد',
          lastName: 'محمد',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(user.id, equals('test-user-123'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.firstName, equals('أحمد'));
        expect(user.lastName, equals('محمد'));
        expect(user.isActive, isTrue);
        expect(user.emailVerified, isFalse);
        expect(user.authProvider, equals(AuthProvider.email));
        expect(user.locale, equals('en'));
      });

      test('should create user with optional fields', () {
        final user = User(
          id: 'test-user-456',
          email: '<EMAIL>',
          firstName: 'فاطمة',
          lastName: 'علي',
          displayName: 'فاطمة علي',
          phoneNumber: '+966501234567',
          avatarUrl: 'https://example.com/avatar.jpg',
          isActive: true,
          emailVerified: true,
          authProvider: AuthProvider.google,
          locale: 'ar',
          timezone: 'Asia/Riyadh',
          lastLoginAt: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(user.displayName, equals('فاطمة علي'));
        expect(user.phoneNumber, equals('+966501234567'));
        expect(user.avatarUrl, equals('https://example.com/avatar.jpg'));
        expect(user.emailVerified, isTrue);
        expect(user.authProvider, equals(AuthProvider.google));
        expect(user.locale, equals('ar'));
        expect(user.timezone, equals('Asia/Riyadh'));
        expect(user.lastLoginAt, isNotNull);
      });

      test('should serialize and deserialize correctly', () {
        final user = User(
          id: 'serialize-test-user',
          email: '<EMAIL>',
          firstName: 'محمد',
          lastName: 'أحمد',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final json = user.toJson();
        final deserializedUser = User.fromJson(json);

        expect(deserializedUser.id, equals(user.id));
        expect(deserializedUser.email, equals(user.email));
        expect(deserializedUser.firstName, equals(user.firstName));
        expect(deserializedUser.lastName, equals(user.lastName));
      });

      test('should compute helper methods correctly', () {
        final user = User(
          id: 'helper-test-user',
          email: '<EMAIL>',
          firstName: 'أحمد',
          lastName: 'محمد',
          displayName: 'أحمد محمد',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(user.fullName, equals('أحمد محمد'));
        expect(user.initials, equals('أم'));
        expect(user.hasCompleteProfile, isFalse); // email not verified
        expect(user.displayAvatar, equals('أم')); // returns initials when no avatar

        final userWithAvatar = user.copyWith(
          avatarUrl: 'https://example.com/avatar.jpg',
          emailVerified: true,
        );

        expect(userWithAvatar.displayAvatar, equals('https://example.com/avatar.jpg'));
        expect(userWithAvatar.hasCompleteProfile, isTrue);
      });

      test('should handle empty names for initials', () {
        final user = User(
          id: 'empty-names-user',
          email: '<EMAIL>',
          firstName: '',
          lastName: '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(user.initials, equals(''));
      });
    });

    // =========================================================================
    // AuthState Sealed Class Tests
    // =========================================================================
    
    group('AuthState Sealed Class Tests', () {
      test('should create initial state', () {
        const state = AuthState.initial();
        expect(state, isA<AuthStateInitial>());
      });

      test('should create loading state', () {
        const state = AuthState.loading(
          message: 'جاري تسجيل الدخول...',
          operation: AuthOperation.signIn,
        );
        expect(state, isA<AuthStateLoading>());
        
        // Use pattern matching to access properties
        state.when(
          initial: () => fail('Should not be initial'),
          loading: (message, operation) {
            expect(message, equals('جاري تسجيل الدخول...'));
            expect(operation, equals(AuthOperation.signIn));
          },
          authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) => 
              fail('Should not be authenticated'),
          unauthenticated: (reason) => fail('Should not be unauthenticated'),
          error: (message, errorCode, errorType, isRecoverable, originalException) => 
              fail('Should not be error'),
          emailVerificationPending: (email, sentAt) => fail('Should not be email verification pending'),
          sessionExpired: (expiredAt, autoRefreshAttempted) => fail('Should not be session expired'),
        );
      });

      test('should create authenticated state', () {
        final user = User(
          id: 'auth-test-user',
          email: '<EMAIL>',
          firstName: 'أحمد',
          lastName: 'محمد',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final state = AuthState.authenticated(
          user: user,
          token: 'test-jwt-token',
          refreshToken: 'test-refresh-token',
          tokenExpiry: DateTime.now().add(const Duration(hours: 1)),
          sessionStart: DateTime.now(),
        );

        expect(state, isA<AuthStateAuthenticated>());
        
        // Use pattern matching to access properties
        state.when(
          initial: () => fail('Should not be initial'),
          loading: (message, operation) => fail('Should not be loading'),
          authenticated: (authUser, token, refreshToken, tokenExpiry, sessionStart) {
            expect(authUser, equals(user));
            expect(token, equals('test-jwt-token'));
            expect(refreshToken, equals('test-refresh-token'));
            expect(tokenExpiry, isNotNull);
            expect(sessionStart, isNotNull);
          },
          unauthenticated: (reason) => fail('Should not be unauthenticated'),
          error: (message, errorCode, errorType, isRecoverable, originalException) => 
              fail('Should not be error'),
          emailVerificationPending: (email, sentAt) => fail('Should not be email verification pending'),
          sessionExpired: (expiredAt, autoRefreshAttempted) => fail('Should not be session expired'),
        );
      });

      test('should create unauthenticated state', () {
        const state = AuthState.unauthenticated(
          reason: 'تم تسجيل الخروج بنجاح',
        );
        expect(state, isA<AuthStateUnauthenticated>());
        
        state.when(
          initial: () => fail('Should not be initial'),
          loading: (message, operation) => fail('Should not be loading'),
          authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) => 
              fail('Should not be authenticated'),
          unauthenticated: (reason) {
            expect(reason, equals('تم تسجيل الخروج بنجاح'));
          },
          error: (message, errorCode, errorType, isRecoverable, originalException) => 
              fail('Should not be error'),
          emailVerificationPending: (email, sentAt) => fail('Should not be email verification pending'),
          sessionExpired: (expiredAt, autoRefreshAttempted) => fail('Should not be session expired'),
        );
      });

      test('should create error state', () {
        const state = AuthState.error(
          message: 'خطأ في تسجيل الدخول',
          errorCode: 'AUTH_FAILED',
          errorType: AuthErrorType.invalidCredentials,
          isRecoverable: true,
        );
        expect(state, isA<AuthStateError>());
        
        state.when(
          initial: () => fail('Should not be initial'),
          loading: (message, operation) => fail('Should not be loading'),
          authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) => 
              fail('Should not be authenticated'),
          unauthenticated: (reason) => fail('Should not be unauthenticated'),
          error: (message, errorCode, errorType, isRecoverable, originalException) {
            expect(message, equals('خطأ في تسجيل الدخول'));
            expect(errorCode, equals('AUTH_FAILED'));
            expect(errorType, equals(AuthErrorType.invalidCredentials));
            expect(isRecoverable, isTrue);
          },
          emailVerificationPending: (email, sentAt) => fail('Should not be email verification pending'),
          sessionExpired: (expiredAt, autoRefreshAttempted) => fail('Should not be session expired'),
        );
      });

      test('should create email verification pending state', () {
        final state = AuthState.emailVerificationPending(
          email: '<EMAIL>',
          sentAt: DateTime.now(),
        );
        expect(state, isA<AuthStateEmailVerificationPending>());
        
        state.when(
          initial: () => fail('Should not be initial'),
          loading: (message, operation) => fail('Should not be loading'),
          authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) => 
              fail('Should not be authenticated'),
          unauthenticated: (reason) => fail('Should not be unauthenticated'),
          error: (message, errorCode, errorType, isRecoverable, originalException) => 
              fail('Should not be error'),
          emailVerificationPending: (email, sentAt) {
            expect(email, equals('<EMAIL>'));
            expect(sentAt, isNotNull);
          },
          sessionExpired: (expiredAt, autoRefreshAttempted) => fail('Should not be session expired'),
        );
      });

      test('should create session expired state', () {
        final state = AuthState.sessionExpired(
          expiredAt: DateTime.now(),
          autoRefreshAttempted: true,
        );
        expect(state, isA<AuthStateSessionExpired>());
        
        state.when(
          initial: () => fail('Should not be initial'),
          loading: (message, operation) => fail('Should not be loading'),
          authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) => 
              fail('Should not be authenticated'),
          unauthenticated: (reason) => fail('Should not be unauthenticated'),
          error: (message, errorCode, errorType, isRecoverable, originalException) => 
              fail('Should not be error'),
          emailVerificationPending: (email, sentAt) => fail('Should not be email verification pending'),
          sessionExpired: (expiredAt, autoRefreshAttempted) {
            expect(expiredAt, isNotNull);
            expect(autoRefreshAttempted, isTrue);
          },
        );
      });
    });

    // =========================================================================
    // AuthResult Sealed Class Tests
    // =========================================================================
    
    group('AuthResult Sealed Class Tests', () {
      test('should create success result', () {
        final user = User(
          id: 'result-test-user',
          email: '<EMAIL>',
          firstName: 'أحمد',
          lastName: 'محمد',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final result = AuthResult.success(
          user: user,
          token: 'success-jwt-token',
          refreshToken: 'success-refresh-token',
          tokenExpiry: DateTime.now().add(const Duration(hours: 1)),
          metadata: {'source': 'google_oauth'},
        );

        expect(result, isA<AuthResultSuccess>());
        
        result.when(
          success: (user, token, refreshToken, tokenExpiry, metadata) {
            expect(user, equals(user));
            expect(token, equals('success-jwt-token'));
            expect(refreshToken, equals('success-refresh-token'));
            expect(tokenExpiry, isNotNull);
            expect(metadata?['source'], equals('google_oauth'));
          },
          failure: (error, errorCode, errorType, isRecoverable, details) => 
              fail('Should not be failure'),
          cancelled: (reason) => fail('Should not be cancelled'),
          pending: (message, pendingAction, actionData) => fail('Should not be pending'),
        );
      });

      test('should create failure result', () {
        final result = AuthResult.failure(
          error: 'فشل في تسجيل الدخول',
          errorCode: 'AUTH_FAILED',
          errorType: AuthErrorType.invalidCredentials,
          isRecoverable: true,
          details: {'attempts': 3},
        );

        expect(result, isA<AuthResultFailure>());
        
        result.when(
          success: (user, token, refreshToken, tokenExpiry, metadata) => 
              fail('Should not be success'),
          failure: (error, errorCode, errorType, isRecoverable, details) {
            expect(error, equals('فشل في تسجيل الدخول'));
            expect(errorCode, equals('AUTH_FAILED'));
            expect(errorType, equals(AuthErrorType.invalidCredentials));
            expect(isRecoverable, isTrue);
            expect(details?['attempts'], equals(3));
          },
          cancelled: (reason) => fail('Should not be cancelled'),
          pending: (message, pendingAction, actionData) => fail('Should not be pending'),
        );
      });

      test('should create cancelled result', () {
        const result = AuthResult.cancelled(
          reason: 'تم إلغاء العملية بواسطة المستخدم',
        );
        expect(result, isA<AuthResultCancelled>());
        
        result.when(
          success: (user, token, refreshToken, tokenExpiry, metadata) => 
              fail('Should not be success'),
          failure: (error, errorCode, errorType, isRecoverable, details) => 
              fail('Should not be failure'),
          cancelled: (reason) {
            expect(reason, equals('تم إلغاء العملية بواسطة المستخدم'));
          },
          pending: (message, pendingAction, actionData) => fail('Should not be pending'),
        );
      });

      test('should create pending result', () {
        final result = AuthResult.pending(
          message: 'يرجى التحقق من بريدك الإلكتروني',
          pendingAction: AuthOperation.verifyEmail,
          actionData: {'email': '<EMAIL>'},
        );

        expect(result, isA<AuthResultPending>());
        
        result.when(
          success: (user, token, refreshToken, tokenExpiry, metadata) => 
              fail('Should not be success'),
          failure: (error, errorCode, errorType, isRecoverable, details) => 
              fail('Should not be failure'),
          cancelled: (reason) => fail('Should not be cancelled'),
          pending: (message, pendingAction, actionData) {
            expect(message, equals('يرجى التحقق من بريدك الإلكتروني'));
            expect(pendingAction, equals(AuthOperation.verifyEmail));
            expect(actionData?['email'], equals('<EMAIL>'));
          },
        );
      });
    });

    // =========================================================================
    // AuthData Model Tests
    // =========================================================================
    
    group('AuthData Model Tests', () {
      test('should serialize and deserialize correctly', () {
        final user = User(
          id: 'authdata-test-user',
          email: '<EMAIL>',
          firstName: 'أحمد',
          lastName: 'محمد',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final authData = AuthData(
          user: user,
          accessToken: 'test-access-token',
          refreshToken: 'test-refresh-token',
          expiresIn: 3600,
        );

        // Convert to JSON and back to test serialization
        final json = authData.toJson();
        final deserializedAuthData = AuthData.fromJson(json);

        expect(deserializedAuthData.user.id, equals(authData.user.id));
        expect(deserializedAuthData.user.email, equals(authData.user.email));
        expect(deserializedAuthData.user.firstName, equals(authData.user.firstName));
        expect(deserializedAuthData.user.lastName, equals(authData.user.lastName));
        expect(deserializedAuthData.accessToken, equals(authData.accessToken));
        expect(deserializedAuthData.refreshToken, equals(authData.refreshToken));
        expect(deserializedAuthData.expiresIn, equals(authData.expiresIn));
      });
    });

    // =========================================================================
    // Request/Response Model Tests
    // =========================================================================
    
    group('Request/Response Model Tests', () {
      test('should handle LoginRequest correctly', () {
        const loginRequest = LoginRequest(
          email: '<EMAIL>',
          password: 'securepassword123',
        );

        final json = loginRequest.toJson();
        final deserializedRequest = LoginRequest.fromJson(json);

        expect(deserializedRequest.email, equals(loginRequest.email));
        expect(deserializedRequest.password, equals(loginRequest.password));
      });

      test('should handle RegisterRequest correctly', () {
        const registerRequest = RegisterRequest(
          name: 'أحمد محمد',
          email: '<EMAIL>',
          password: 'securepassword123',
        );

        final json = registerRequest.toJson();
        final deserializedRequest = RegisterRequest.fromJson(json);

        expect(deserializedRequest.name, equals(registerRequest.name));
        expect(deserializedRequest.email, equals(registerRequest.email));
        expect(deserializedRequest.password, equals(registerRequest.password));
      });

      test('should handle GoogleAuthRequest correctly', () {
        const googleAuthRequest = GoogleAuthRequest(
          idToken: 'google-id-token-123',
        );

        final json = googleAuthRequest.toJson();
        final deserializedRequest = GoogleAuthRequest.fromJson(json);

        expect(deserializedRequest.idToken, equals(googleAuthRequest.idToken));
      });

      test('should handle RefreshTokenRequest correctly', () {
        const refreshTokenRequest = RefreshTokenRequest(
          refreshToken: 'refresh-token-123',
        );

        final json = refreshTokenRequest.toJson();
        final deserializedRequest = RefreshTokenRequest.fromJson(json);

        expect(deserializedRequest.refreshToken, equals(refreshTokenRequest.refreshToken));
      });

      test('should handle AuthResponse correctly', () {
        const authResponse = AuthResponse(
          success: true,
          data: {'user_id': '123'},
          error: null,
          errorCode: null,
        );

        final json = authResponse.toJson();
        final deserializedResponse = AuthResponse.fromJson(json);

        expect(deserializedResponse.success, equals(authResponse.success));
        expect(deserializedResponse.data?['user_id'], equals('123'));
        expect(deserializedResponse.error, isNull);
        expect(deserializedResponse.errorCode, isNull);
      });

      test('should handle AuthResponse with error correctly', () {
        const authResponse = AuthResponse(
          success: false,
          data: null,
          error: 'Authentication failed',
          errorCode: 'AUTH_FAILED',
        );

        final json = authResponse.toJson();
        final deserializedResponse = AuthResponse.fromJson(json);

        expect(deserializedResponse.success, equals(authResponse.success));
        expect(deserializedResponse.data, isNull);
        expect(deserializedResponse.error, equals('Authentication failed'));
        expect(deserializedResponse.errorCode, equals('AUTH_FAILED'));
      });
    });

    // =========================================================================
    // Enum Tests
    // =========================================================================
    
    group('Enum Tests', () {
      test('AuthProvider should have correct values', () {
        expect(AuthProvider.email, isNotNull);
        expect(AuthProvider.google, isNotNull);
        expect(AuthProvider.apple, isNotNull);
      });

      test('AuthOperation should have correct values', () {
        expect(AuthOperation.signIn, isNotNull);
        expect(AuthOperation.signUp, isNotNull);
        expect(AuthOperation.signOut, isNotNull);
        expect(AuthOperation.refreshToken, isNotNull);
        expect(AuthOperation.verifyEmail, isNotNull);
        expect(AuthOperation.resetPassword, isNotNull);
      });

      test('AuthErrorType should have correct values', () {
        expect(AuthErrorType.unknown, isNotNull);
        expect(AuthErrorType.network, isNotNull);
        expect(AuthErrorType.invalidCredentials, isNotNull);
        expect(AuthErrorType.userNotFound, isNotNull);
        expect(AuthErrorType.emailAlreadyExists, isNotNull);
        expect(AuthErrorType.weakPassword, isNotNull);
        expect(AuthErrorType.invalidEmail, isNotNull);
        expect(AuthErrorType.sessionExpired, isNotNull);
        expect(AuthErrorType.rateLimitExceeded, isNotNull);
        expect(AuthErrorType.serverError, isNotNull);
        expect(AuthErrorType.oauthError, isNotNull);
        expect(AuthErrorType.verificationRequired, isNotNull);
        expect(AuthErrorType.networkError, isNotNull);
        expect(AuthErrorType.emailNotVerified, isNotNull);
        expect(AuthErrorType.accountDisabled, isNotNull);
      });
    });
  });
}
