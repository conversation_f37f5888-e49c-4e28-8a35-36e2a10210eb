import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/auth/auth_models.dart';

void main() {
  group('Unified Auth Models Tests', () {
    test('User model should create instance correctly', () {
      final user = User(
        id: '123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(user.id, '123');
      expect(user.email, '<EMAIL>');
      expect(user.firstName, 'John');
      expect(user.lastName, 'Doe');
      expect(user.isActive, true);
      expect(user.emailVerified, false);
      expect(user.authProvider, AuthProvider.email);
    });

    test('AuthState should handle different states', () {
      const initialState = AuthState.initial();
      const loadingState = AuthState.loading();
      const unauthenticatedState = AuthState.unauthenticated();
      const errorState = AuthState.error(message: 'Test error');

      expect(initialState, isA<AuthStateInitial>());
      expect(loadingState, isA<AuthStateLoading>());
      expect(unauthenticatedState, isA<AuthStateUnauthenticated>());
      expect(errorState, isA<AuthStateError>());
    });

    test('AuthResult should handle different results', () {
      final user = User(
        id: '123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final successResult = AuthResult.success(
        user: user,
        token: 'test-token',
      );
      const failureResult = AuthResult.failure(error: 'Test error');
      const cancelledResult = AuthResult.cancelled();

      expect(successResult, isA<AuthResultSuccess>());
      expect(failureResult, isA<AuthResultFailure>());
      expect(cancelledResult, isA<AuthResultCancelled>());
    });

    test('User helper methods should work correctly', () {
      final user = User(
        id: '123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        displayName: 'John Doe',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(user.fullName, 'John Doe');
      expect(user.initials, 'JD');
      expect(user.hasCompleteProfile, false); // email not verified
      expect(user.displayAvatar, 'JD'); // returns initials when no avatar

      final userWithAvatar = user.copyWith(
        avatarUrl: 'https://example.com/avatar.jpg',
        emailVerified: true,
      );

      expect(userWithAvatar.displayAvatar, 'https://example.com/avatar.jpg');
      expect(userWithAvatar.hasCompleteProfile, true);
    });

    test('AuthState pattern matching should work correctly', () {
      const loadingState = AuthState.loading(
        message: 'Loading...',
        operation: AuthOperation.signIn,
      );

      loadingState.when(
        initial: () => fail('Should not be initial'),
        loading: (message, operation) {
          expect(message, 'Loading...');
          expect(operation, AuthOperation.signIn);
        },
        authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) => 
            fail('Should not be authenticated'),
        unauthenticated: (reason) => fail('Should not be unauthenticated'),
        error: (message, errorCode, errorType, isRecoverable, originalException) => 
            fail('Should not be error'),
        emailVerificationPending: (email, sentAt) => fail('Should not be email verification pending'),
        sessionExpired: (expiredAt, autoRefreshAttempted) => fail('Should not be session expired'),
      );
    });

    test('AuthResult pattern matching should work correctly', () {
      final user = User(
        id: '123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final successResult = AuthResult.success(
        user: user,
        token: 'test-token',
        metadata: {'source': 'test'},
      );

      successResult.when(
        success: (authUser, token, refreshToken, tokenExpiry, metadata) {
          expect(authUser, user);
          expect(token, 'test-token');
          expect(metadata?['source'], 'test');
        },
        failure: (error, errorCode, errorType, isRecoverable, details) => 
            fail('Should not be failure'),
        cancelled: (reason) => fail('Should not be cancelled'),
        pending: (message, pendingAction, actionData) => fail('Should not be pending'),
      );
    });

    test('Enum values should be accessible', () {
      expect(AuthProvider.values, contains(AuthProvider.email));
      expect(AuthProvider.values, contains(AuthProvider.google));
      expect(AuthProvider.values, contains(AuthProvider.apple));

      expect(AuthOperation.values, contains(AuthOperation.signIn));
      expect(AuthOperation.values, contains(AuthOperation.signUp));
      expect(AuthOperation.values, contains(AuthOperation.signOut));

      expect(AuthErrorType.values, contains(AuthErrorType.unknown));
      expect(AuthErrorType.values, contains(AuthErrorType.network));
      expect(AuthErrorType.values, contains(AuthErrorType.invalidCredentials));
    });
  });
}
