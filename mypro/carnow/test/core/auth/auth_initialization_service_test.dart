// =============================================================================
// AUTH INITIALIZATION SERVICE TESTS
// =============================================================================

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:carnow/core/auth/auth_initialization_service.dart';
import 'package:carnow/core/auth/auth_models.dart';

// Simple adapter to convert ProviderContainer to Ref<Object?>
class RefAdapter implements Ref<Object?> {
  final ProviderContainer _container;
  
  RefAdapter(this._container);
  
  @override
  T read<T>(ProviderListenable<T> provider) => _container.read(provider);
  
  @override
  T watch<T>(ProviderListenable<T> provider) => _container.read(provider);
  
  @override
  ProviderSubscription<T> listen<T>(ProviderListenable<T> provider, void Function(T? previous, T next) listener, {bool fireImmediately = false, void Function(Object error, StackTrace stackTrace)? onError}) {
    return _container.listen(provider, listener, fireImmediately: fireImmediately, onError: onError);
  }
  
  @override
  void invalidate(ProviderOrFamily provider) {}
  
  @override
  void invalidateSelf() {}
  
  @override
  T refresh<T>(Refreshable<T> provider) => _container.refresh(provider);
  
  @override
  void dispose() {}
  
  @override
  void state(ProviderOrFamily provider) {}
  
  @override
  ProviderContainer get container => _container;
  
  @override
  bool exists(ProviderBase<Object?> provider) => _container.exists(provider);
  
  @override
  KeepAliveLink keepAlive() => throw UnimplementedError();
  
  @override
  void listenSelf(void Function(Object? previous, Object next) listener, {void Function(Object error, StackTrace stackTrace)? onError}) {}
  
  @override
  void notifyListeners() {}
  
  @override
  void onAddListener(void Function() cb) {}
  
  @override
  void onCancel(void Function() cb) {}
  
  @override
  void onDispose(void Function() cb) {}
  
  @override
  void onRemoveListener(void Function() cb) {}
  
  @override
  void onResume(void Function() cb) {}
}

void main() {
  group('AuthInitializationService', () {
    late ProviderContainer container;
    late AuthInitializationService service;

    setUp(() {
      container = ProviderContainer();
      // Create a proper Ref adapter using the container
      final mockRef = RefAdapter(container);
      service = AuthInitializationService(ref: mockRef);
    });

    tearDown(() {
      container.dispose();
    });

    test('should create service with default configuration', () {
      expect(service, isNotNull);
      expect(service.configuration, isNotNull);
      expect(service.configuration.isValid, isTrue);
      expect(service.hasInitialized, isFalse);
      expect(service.isInitializing, isFalse);
    });

    test('should validate configuration correctly', () async {
      final result = await service.validateConfiguration();

      expect(result, isNotNull);
      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });

    test('should handle custom configuration', () {
      const customConfig = AuthConfiguration(
        apiBaseUrl: 'https://test.example.com',
        googleAuthEnabled: false,
        emailAuthEnabled: true,
        initializationTimeout: Duration(seconds: 10),
        maxRetryAttempts: 5,
      );

      final mockRef = RefAdapter(container);
      final customService = AuthInitializationService(
        ref: mockRef,
        configuration: customConfig,
      );

      expect(
        customService.configuration.apiBaseUrl,
        'https://test.example.com',
      );
      expect(customService.configuration.googleAuthEnabled, isFalse);
      expect(customService.configuration.maxRetryAttempts, 5);
    });

    test('should detect invalid configuration', () {
      const invalidConfig = AuthConfiguration(
        apiBaseUrl: '', // Invalid empty URL
        googleAuthEnabled: false,
        emailAuthEnabled: false, // Both auth methods disabled
        maxRetryAttempts: 0, // Invalid retry count
      );

      expect(invalidConfig.isValid, isFalse);
      expect(invalidConfig.validationErrors, isNotEmpty);
      expect(
        invalidConfig.validationErrors,
        contains('API base URL is required'),
      );
      expect(
        invalidConfig.validationErrors,
        contains('Max retry attempts must be positive'),
      );
      expect(
        invalidConfig.validationErrors,
        contains('At least one authentication method must be enabled'),
      );
    });

    test('should provide correct getters', () {
      expect(service.configuration, isA<AuthConfiguration>());
      expect(service.hasInitialized, isFalse);
      expect(service.isInitializing, isFalse);
      expect(service.lastResult, isNull);
      expect(service.isCircuitOpen, isFalse);
      expect(service.failureCount, 0);
    });
  });

  group('AuthConfiguration', () {
    test('should create valid default configuration', () {
      const config = AuthConfiguration(apiBaseUrl: 'https://api.example.com');

      expect(config.isValid, isTrue);
      expect(config.validationErrors, isEmpty);
      expect(config.googleAuthEnabled, isTrue);
      expect(config.emailAuthEnabled, isTrue);
      expect(config.enableOfflineMode, isTrue);
    });

    test('should detect validation errors', () {
      const config = AuthConfiguration(
        apiBaseUrl: '',
        maxRetryAttempts: -1,
        initializationTimeout: Duration(seconds: -5),
      );

      expect(config.isValid, isFalse);
      expect(config.validationErrors, isNotEmpty);
    });
  });

  group('AuthInitializationResult', () {
    test('should create success result', () {
      const result = AuthInitializationResult.success(
        initialState: AuthState.unauthenticated(),
        initializationTime: Duration(seconds: 2),
        configuration: AuthConfiguration(apiBaseUrl: 'https://api.example.com'),
      );

      expect(result, isA<AuthInitializationSuccess>());

      result.when(
        success: (state, time, config, metadata) {
          expect(state, isA<AuthStateUnauthenticated>());
          expect(time, const Duration(seconds: 2));
          expect(config.apiBaseUrl, 'https://api.example.com');
        },
        failure: (_, __, ___, ____, _____) => fail('Should not be failure'),
        timeout: (_, __, ___) => fail('Should not be timeout'),
      );
    });

    test('should create failure result', () {
      const result = AuthInitializationResult.failure(
        error: 'Test error',
        errorType: AuthErrorType.networkError,
        canRetry: true,
      );

      expect(result, isA<AuthInitializationFailure>());

      result.when(
        success: (_, __, ___, ____) => fail('Should not be success'),
        failure: (error, errorType, canRetry, fallbackState, details) {
          expect(error, 'Test error');
          expect(errorType, AuthErrorType.networkError);
          expect(canRetry, isTrue);
        },
        timeout: (_, __, ___) => fail('Should not be timeout'),
      );
    });

    test('should create timeout result', () {
      const result = AuthInitializationResult.timeout(
        timeoutDuration: Duration(seconds: 5),
        fallbackState: AuthState.unauthenticated(),
      );

      expect(result, isA<AuthInitializationTimeout>());

      result.when(
        success: (_, __, ___, ____) => fail('Should not be success'),
        failure: (_, __, ___, ____, _____) => fail('Should not be failure'),
        timeout: (duration, fallbackState, shouldRetry) {
          expect(duration, const Duration(seconds: 5));
          expect(fallbackState, isA<AuthStateUnauthenticated>());
          expect(shouldRetry, isTrue);
        },
      );
    });
  });

  group('ConfigurationValidationResult', () {
    test('should create valid result', () {
      const result = ConfigurationValidationResult(
        isValid: true,
        errors: [],
        warnings: [],
      );

      expect(result.isValid, isTrue);
      expect(result.hasIssues, isFalse);
      expect(result.allIssues, isEmpty);
    });

    test('should create invalid result with errors', () {
      const result = ConfigurationValidationResult(
        isValid: false,
        errors: ['Error 1', 'Error 2'],
        warnings: ['Warning 1'],
      );

      expect(result.isValid, isFalse);
      expect(result.hasIssues, isTrue);
      expect(result.allIssues, hasLength(3));
      expect(result.allIssues, contains('Error 1'));
      expect(result.allIssues, contains('Warning 1'));
    });
  });
}
