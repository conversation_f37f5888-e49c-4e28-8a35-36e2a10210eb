/// ============================================================================
/// UNIFIED AUTH PROVIDER COMPREHENSIVE UNIT TESTS
/// ============================================================================
///
/// Task 10: اختبار الوحدة (Unit Tests)
/// Comprehensive unit tests for UnifiedAuthProvider
/// Forever Plan Architecture: Flutter UI Only → Go API → Supabase Data
/// ============================================================================

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:carnow/core/auth/auth_models.dart';
import 'package:carnow/core/auth/auth_interfaces.dart';


void main() {
  group('UnifiedAuthProvider Comprehensive Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    group('Auth Models Tests', () {
      test('User model should have correct properties and methods', () {
        final user = User(
          id: 'test-123',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          emailVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(user.id, equals('test-123'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.fullName, equals('John Doe'));
        expect(user.initials, equals('JD'));
        expect(user.hasCompleteProfile, isTrue);
        expect(user.displayAvatar, equals('JD'));
      });

      test('User model should handle incomplete profiles', () {
        final incompleteUser = User(
          id: 'test-456',
          email: '<EMAIL>',
          firstName: '',
          lastName: '',
          emailVerified: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(incompleteUser.hasCompleteProfile, isFalse);
        expect(incompleteUser.fullName, equals('')); // Empty string when both names are empty
        expect(incompleteUser.initials, equals(''));
        expect(incompleteUser.displayAvatar, equals('')); // Empty string when no avatar and no initials
      });

      test('AuthState sealed classes should work correctly', () {
        const initialState = AuthState.initial();
        expect(initialState, isA<AuthStateInitial>());

        const loadingState = AuthState.loading();
        expect(loadingState, isA<AuthStateLoading>());

        const unauthenticatedState = AuthState.unauthenticated();
        expect(unauthenticatedState, isA<AuthStateUnauthenticated>());

        final user = User(
          id: 'test-id',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          emailVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final authenticatedState = AuthState.authenticated(
          user: user,
          token: 'test-token',
        );
        expect(authenticatedState, isA<AuthStateAuthenticated>());
        expect((authenticatedState as AuthStateAuthenticated).user, equals(user));
        expect(authenticatedState.token, equals('test-token'));

        const errorState = AuthState.error(message: 'Test error');
        expect(errorState, isA<AuthStateError>());
        expect((errorState as AuthStateError).message, equals('Test error'));

        const emailVerificationState = AuthState.emailVerificationPending(email: '<EMAIL>');
        expect(emailVerificationState, isA<AuthStateEmailVerificationPending>());

        const sessionExpiredState = AuthState.sessionExpired();
        expect(sessionExpiredState, isA<AuthStateSessionExpired>());
      });

      test('AuthResult sealed classes should work correctly', () {
        final user = User(
          id: 'test-id',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          emailVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final successResult = AuthResult.success(
          user: user,
          token: 'test-token',
        );
        expect(successResult, isA<AuthResultSuccess>());
        expect((successResult as AuthResultSuccess).user, equals(user));
        expect(successResult.token, equals('test-token'));

        const failureResult = AuthResult.failure(error: 'Test error');
        expect(failureResult, isA<AuthResultFailure>());
        expect((failureResult as AuthResultFailure).error, equals('Test error'));

        const cancelledResult = AuthResult.cancelled();
        expect(cancelledResult, isA<AuthResultCancelled>());

        const pendingResult = AuthResult.pending(message: 'Verification required');
        expect(pendingResult, isA<AuthResultPending>());
      });

      test('AuthErrorType enum should have all expected values', () {
        expect(AuthErrorType.values, contains(AuthErrorType.unknown));
        expect(AuthErrorType.values, contains(AuthErrorType.invalidCredentials));
        expect(AuthErrorType.values, contains(AuthErrorType.userNotFound));
        expect(AuthErrorType.values, contains(AuthErrorType.emailNotVerified));
        expect(AuthErrorType.values, contains(AuthErrorType.networkError));
        expect(AuthErrorType.values, contains(AuthErrorType.sessionExpired));
        expect(AuthErrorType.values, contains(AuthErrorType.rateLimitExceeded));
        expect(AuthErrorType.values, contains(AuthErrorType.accountDisabled));
      });

      test('AuthProvider enum should have all expected values', () {
        expect(AuthProvider.values, contains(AuthProvider.email));
        expect(AuthProvider.values, contains(AuthProvider.google));
        expect(AuthProvider.values, contains(AuthProvider.apple));
      });

      test('AuthOperation enum should have all expected values', () {
        expect(AuthOperation.values, contains(AuthOperation.signIn));
        expect(AuthOperation.values, contains(AuthOperation.signUp));
        expect(AuthOperation.values, contains(AuthOperation.signOut));
        expect(AuthOperation.values, contains(AuthOperation.refreshToken));
        expect(AuthOperation.values, contains(AuthOperation.verifyEmail));
        expect(AuthOperation.values, contains(AuthOperation.resetPassword));
      });
    });

    group('Google Auth Models Tests', () {
      test('GoogleUserInfo should work correctly', () {
        final userInfo = GoogleUserInfo(
          id: 'google-123',
          email: '<EMAIL>',
          displayName: 'Google User',
          firstName: 'Google',
          lastName: 'User',
        );

        expect(userInfo.id, equals('google-123'));
        expect(userInfo.email, equals('<EMAIL>'));
        expect(userInfo.displayName, equals('Google User'));
        expect(userInfo.firstName, equals('Google'));
        expect(userInfo.lastName, equals('User'));
      });

      test('GoogleAuthResult should work correctly', () {
        final userInfo = GoogleUserInfo(
          id: 'google-123',
          email: '<EMAIL>',
        );

        final successResult = GoogleAuthResult.success(
          idToken: 'google-token',
          userInfo: userInfo,
        );

        // Test success result using pattern matching
        successResult.when(
          success: (idToken, accessToken, userInfo, expiryDate) {
            expect(idToken, equals('google-token'));
            expect(userInfo.email, equals('<EMAIL>'));
          },
          failure: (error, errorCode) => fail('Should not be failure'),
          cancelled: (reason) => fail('Should not be cancelled'),
        );

        final cancelledResult = GoogleAuthResult.cancelled();
        // Test cancelled result using pattern matching
        cancelledResult.when(
          success: (idToken, accessToken, userInfo, expiryDate) => fail('Should not be success'),
          failure: (error, errorCode) => fail('Should not be failure'),
          cancelled: (reason) {
            // Successfully cancelled - no additional checks needed
          },
        );

        final failureResult = GoogleAuthResult.failure(
          error: 'Authentication failed',
          errorCode: 'AUTH_ERROR',
        );
        // Test failure result using pattern matching
        failureResult.when(
          success: (idToken, accessToken, userInfo, expiryDate) => fail('Should not be success'),
          failure: (error, errorCode) {
            expect(error, equals('Authentication failed'));
            expect(errorCode, equals('AUTH_ERROR'));
          },
          cancelled: (reason) => fail('Should not be cancelled'),
        );
      });
    });

    group('Error Handling Tests', () {
      test('should handle authentication errors correctly', () {
        const networkError = AuthResult.failure(
          error: 'Network connection failed',
          errorType: AuthErrorType.networkError,
        );
        expect(networkError, isA<AuthResultFailure>());
        expect((networkError as AuthResultFailure).errorType, equals(AuthErrorType.networkError));

        const invalidCredentialsError = AuthResult.failure(
          error: 'Invalid email or password',
          errorType: AuthErrorType.invalidCredentials,
        );
        expect(invalidCredentialsError, isA<AuthResultFailure>());
        expect((invalidCredentialsError as AuthResultFailure).errorType, equals(AuthErrorType.invalidCredentials));

        const rateLimitError = AuthResult.failure(
          error: 'Too many requests',
          errorType: AuthErrorType.rateLimitExceeded,
        );
        expect(rateLimitError, isA<AuthResultFailure>());
        expect((rateLimitError as AuthResultFailure).errorType, equals(AuthErrorType.rateLimitExceeded));
      });
    });

    group('State Management Tests', () {
      test('should handle state transitions correctly', () {
        // Test initial state
        const initialState = AuthState.initial();
        expect(initialState, isA<AuthStateInitial>());

        // Test loading state
        const loadingState = AuthState.loading();
        expect(loadingState, isA<AuthStateLoading>());

        // Test successful authentication
        final user = User(
          id: 'test-id',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          emailVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final authenticatedState = AuthState.authenticated(
          user: user,
          token: 'test-token',
        );
        expect(authenticatedState, isA<AuthStateAuthenticated>());

        // Test error state
        const errorState = AuthState.error(message: 'Authentication failed');
        expect(errorState, isA<AuthStateError>());

        // Test unauthenticated state
        const unauthenticatedState = AuthState.unauthenticated();
        expect(unauthenticatedState, isA<AuthStateUnauthenticated>());
      });
    });

    group('Validation Tests', () {
      test('should validate user data correctly', () {
        // Test valid user
        final validUser = User(
          id: 'valid-123',
          email: '<EMAIL>',
          firstName: 'Valid',
          lastName: 'User',
          emailVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        expect(validUser.hasCompleteProfile, isTrue);

        // Test invalid user (missing required fields)
        final invalidUser = User(
          id: '',
          email: 'invalid-email',
          firstName: '',
          lastName: '',
          emailVerified: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        expect(invalidUser.hasCompleteProfile, isFalse);
      });
    });
  });
}
