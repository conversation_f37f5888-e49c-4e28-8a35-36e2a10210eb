/// Test to verify that the UnifiedAuthProvider race condition fix works correctly
/// This test ensures that the provider can be read immediately after creation
/// without causing "uninitialized provider" errors.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:carnow/core/auth/unified_auth_provider.dart';
import 'package:carnow/core/auth/auth_models.dart';

void main() {
  group('UnifiedAuthProvider Race Condition Fix', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should return valid state immediately without race condition', () {
      // This test verifies that the provider can be read immediately
      // without causing "Bad state: Tried to read the state of an uninitialized provider"

      // The key test: reading the provider state immediately should not throw
      expect(() {
        final authState = container.read(unifiedAuthProviderProvider);
        // The state should be valid (not null)
        expect(authState, isNotNull);
      }, returnsNormally);
    });

    test('should handle multiple rapid reads without errors', () {
      // Test multiple rapid reads to ensure no race conditions
      expect(() {
        for (int i = 0; i < 10; i++) {
          final authState = container.read(unifiedAuthProviderProvider);
          expect(authState, isNotNull);
        }
      }, returnsNormally);
    });

    test('should provide initialization phase information', () {
      expect(() {
        final provider = container.read(unifiedAuthProviderProvider.notifier);

        // Should have initialization phase information
        expect(provider.initializationPhase, isNotNull);
        expect(provider.isInitializing, isA<bool>());
        expect(provider.isReady, isA<bool>());
      }, returnsNormally);
    });

    test('should have valid initialization phases', () {
      expect(() {
        final provider = container.read(unifiedAuthProviderProvider.notifier);

        // Should have a valid initialization phase
        expect(provider.initializationPhase, isA<AuthInitializationPhase>());

        // The phase should be one of the defined phases
        expect(
          provider.initializationPhase,
          anyOf([
            AuthInitializationPhase.notStarted,
            AuthInitializationPhase.initializing,
            AuthInitializationPhase.validatingTokens,
            AuthInitializationPhase.restoringSession,
            AuthInitializationPhase.ready,
            AuthInitializationPhase.failed,
          ]),
        );
      }, returnsNormally);
    });
  });
}
