import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/auth/auth_interfaces.dart';
import 'package:carnow/core/auth/auth_validator.dart';
import 'package:carnow/core/auth/auth_error_handler.dart';

void main() {
  group('Auth Interfaces Tests', () {
    test('AuthValidator should validate email correctly', () {
      final validator = AuthValidator();

      expect(validator.isValidEmail('<EMAIL>'), true);
      expect(validator.isValidEmail('invalid-email'), false);
      expect(validator.isValidEmail(''), false);
    });

    test('AuthValidator should validate password correctly', () {
      final validator = AuthValidator();

      final weakPassword = validator.validatePassword('123');
      expect(weakPassword.isValid, false);
      expect(weakPassword.errors.isNotEmpty, true);

      final strongPassword = validator.validatePassword('StrongPass123!');
      expect(strongPassword.isValid, true);
      expect(strongPassword.errors.isEmpty, true);
    });

    test('AuthValidator should validate name correctly', () {
      final validator = AuthValidator();

      expect(validator.isValidName('John Doe'), true);
      expect(validator.isValidName('محمد أحمد'), true);
      expect(validator.isValidName(''), false);
      expect(validator.isValidName('A'), false);
    });

    test('AuthErrorHandler should handle errors correctly', () {
      final errorHandler = AuthErrorHandler();

      expect(
        errorHandler.handleError('INVALID_CREDENTIALS'),
        'Invalid email or password',
      );

      expect(
        errorHandler.getLocalizedMessage('INVALID_CREDENTIALS'),
        'البريد الإلكتروني أو كلمة المرور غير صحيحة',
      );

      expect(errorHandler.isRecoverableError('NETWORK_ERROR'), true);

      expect(errorHandler.isRecoverableError('INVALID_CREDENTIALS'), false);
    });

    test('GoogleAuthResult class should work correctly', () {
      final success = GoogleAuthResult.success(
        idToken: 'test-token',
        userInfo: const GoogleUserInfo(
          id: '123',
          email: '<EMAIL>',
        ),
      );

      final failure = GoogleAuthResult.failure(
        error: 'Test error',
      );
      
      final cancelled = GoogleAuthResult.cancelled();

      // Test success case using pattern matching
      success.when(
        success: (idToken, accessToken, userInfo, expiryDate) {
          expect(idToken, 'test-token');
          expect(userInfo.email, '<EMAIL>');
          expect(userInfo.id, '123');
        },
        failure: (error, errorCode) => fail('Should not be failure'),
        cancelled: (reason) => fail('Should not be cancelled'),
      );

      // Test failure case using pattern matching
      failure.when(
        success: (idToken, accessToken, userInfo, expiryDate) => fail('Should not be success'),
        failure: (error, errorCode) {
          expect(error, 'Test error');
        },
        cancelled: (reason) => fail('Should not be cancelled'),
      );

      // Test cancelled case using pattern matching
      cancelled.when(
        success: (idToken, accessToken, userInfo, expiryDate) => fail('Should not be success'),
        failure: (error, errorCode) => fail('Should not be failure'),
        cancelled: (reason) {
          // Successfully cancelled - no additional checks needed
        },
      );
    });

    test('PasswordValidationResult should work correctly', () {
      final result = PasswordValidationResult(
        isValid: false,
        errors: ['Password too short'],
      );

      expect(result.isValid, false);
      expect(result.errors.length, 1);
      expect(result.errors.first, 'Password too short');
    });

    test('AuthEvent should work correctly', () {
      final event = AuthEvent(
        type: AuthEventType.loginAttempt,
        userId: '123',
        email: '<EMAIL>',
        timestamp: DateTime.now(),
        metadata: {'ip': '127.0.0.1'},
      );

      expect(event.type, AuthEventType.loginAttempt);
      expect(event.userId, '123');
      expect(event.email, '<EMAIL>');
      expect(event.metadata?['ip'], '127.0.0.1');
    });
  });
}
