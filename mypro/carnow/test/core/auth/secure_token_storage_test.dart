import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:mocktail/mocktail.dart';
import 'package:carnow/core/auth/secure_token_storage.dart';

class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}

void main() {
  group('SecureTokenStorage', () {
    late MockFlutterSecureStorage mockSecureStorage;
    late SecureTokenStorage secureTokenStorage;
    late TokenData testTokenData;

    setUp(() {
      mockSecureStorage = MockFlutterSecureStorage();
      secureTokenStorage = SecureTokenStorage(secureStorage: mockSecureStorage);

      testTokenData = TokenData(
        accessToken: 'test_access_token',
        refreshToken: 'test_refresh_token',
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
        createdAt: DateTime.now(),
        userId: 'test_user_id',
      );

      // Setup default mock responses
      when(
        () => mockSecureStorage.read(key: any(named: 'key')),
      ).thenAnswer((_) async => null);
      when(
        () => mockSecureStorage.write(
          key: any(named: 'key'),
          value: any(named: 'value'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockSecureStorage.delete(key: any(named: 'key')),
      ).thenAnswer((_) async {});
      when(() => mockSecureStorage.deleteAll()).thenAnswer((_) async {});
    });

    group('Initialization', () {
      test('should generate encryption key on first initialization', () async {
        // Arrange
        when(
          () => mockSecureStorage.read(key: 'carnow_encryption_key'),
        ).thenAnswer((_) async => null);

        // Act
        await secureTokenStorage.initialize();

        // Assert
        verify(
          () => mockSecureStorage.write(
            key: 'carnow_encryption_key',
            value: any(named: 'value'),
          ),
        ).called(1);
      });

      test('should use existing encryption key if available', () async {
        // Arrange
        const existingKey = 'existing_encryption_key';
        when(
          () => mockSecureStorage.read(key: 'carnow_encryption_key'),
        ).thenAnswer((_) async => existingKey);

        // Act
        await secureTokenStorage.initialize();

        // Assert
        verify(
          () => mockSecureStorage.read(key: 'carnow_encryption_key'),
        ).called(1);
        verifyNever(
          () => mockSecureStorage.write(
            key: 'carnow_encryption_key',
            value: any(named: 'value'),
          ),
        );
      });
    });

    group('Token Storage', () {
      setUp(() async {
        // Setup encryption key
        when(
          () => mockSecureStorage.read(key: 'carnow_encryption_key'),
        ).thenAnswer((_) async => base64Encode(List.filled(32, 42)));
        await secureTokenStorage.initialize();
      });

      test('should store token data successfully', () async {
        // Act
        final result = await secureTokenStorage.storeTokenData(testTokenData);

        // Assert
        expect(result, isA<StorageResultSuccess>());
        verify(
          () => mockSecureStorage.write(
            key: 'carnow_token_data',
            value: any(named: 'value'),
          ),
        ).called(1);
        verify(
          () => mockSecureStorage.write(
            key: 'carnow_access_token',
            value: any(named: 'value'),
          ),
        ).called(1);
        verify(
          () => mockSecureStorage.write(
            key: 'carnow_refresh_token',
            value: any(named: 'value'),
          ),
        ).called(1);
      });

      test('should handle storage errors gracefully', () async {
        // Arrange
        when(
          () => mockSecureStorage.write(
            key: any(named: 'key'),
            value: any(named: 'value'),
          ),
        ).thenThrow(Exception('Storage error'));

        // Act
        final result = await secureTokenStorage.storeTokenData(testTokenData);

        // Assert
        expect(result, isA<StorageResultFailure>());
        expect(
          (result as StorageResultFailure).error,
          contains('Failed to store token data'),
        );
      });

      test('should store token without refresh token', () async {
        // Arrange
        final tokenDataWithoutRefresh = testTokenData.copyWith(
          refreshToken: null,
        );

        // Act
        final result = await secureTokenStorage.storeTokenData(
          tokenDataWithoutRefresh,
        );

        // Assert
        expect(result, isA<StorageResultSuccess>());
        verify(
          () => mockSecureStorage.write(
            key: 'carnow_token_data',
            value: any(named: 'value'),
          ),
        ).called(1);
        verify(
          () => mockSecureStorage.write(
            key: 'carnow_access_token',
            value: any(named: 'value'),
          ),
        ).called(1);
        verifyNever(
          () => mockSecureStorage.write(
            key: 'carnow_refresh_token',
            value: any(named: 'value'),
          ),
        );
      });
    });

    group('Token Retrieval', () {
      setUp(() async {
        // Setup encryption key
        when(
          () => mockSecureStorage.read(key: 'carnow_encryption_key'),
        ).thenAnswer((_) async => base64Encode(List.filled(32, 42)));
        await secureTokenStorage.initialize();
      });

      test('should retrieve valid token data successfully', () async {
        // Arrange
        final encryptedData = await secureTokenStorage.storeTokenData(
          testTokenData,
        );
        when(() => mockSecureStorage.read(key: 'carnow_token_data')).thenAnswer(
          (_) async {
            // Simulate encrypted data retrieval
            final jsonData = jsonEncode(testTokenData.toJson());
            return await _encryptTestData(jsonData);
          },
        );

        // Act
        final result = await secureTokenStorage.getTokenData();

        // Assert
        expect(result, isA<StorageResultSuccess<TokenData?>>());
        final tokenData = (result as StorageResultSuccess<TokenData?>).data;
        expect(tokenData, isNotNull);
        expect(tokenData!.accessToken, equals(testTokenData.accessToken));
        expect(tokenData.refreshToken, equals(testTokenData.refreshToken));
        expect(tokenData.userId, equals(testTokenData.userId));
      });

      test('should return null when no token data exists', () async {
        // Arrange
        when(
          () => mockSecureStorage.read(key: 'carnow_token_data'),
        ).thenAnswer((_) async => null);

        // Act
        final result = await secureTokenStorage.getTokenData();

        // Assert
        expect(result, isA<StorageResultSuccess<TokenData?>>());
        final tokenData = (result as StorageResultSuccess<TokenData?>).data;
        expect(tokenData, isNull);
      });

      test('should handle decryption errors gracefully', () async {
        // Arrange
        when(
          () => mockSecureStorage.read(key: 'carnow_token_data'),
        ).thenAnswer((_) async => 'invalid_encrypted_data');

        // Act
        final result = await secureTokenStorage.getTokenData();

        // Assert
        expect(result, isA<StorageResultFailure<TokenData?>>());
        expect(
          (result as StorageResultFailure).error,
          contains('Failed to retrieve token data'),
        );
      });

      test('should get access token successfully', () async {
        // Arrange
        when(() => mockSecureStorage.read(key: 'carnow_token_data')).thenAnswer(
          (_) async {
            final jsonData = jsonEncode(testTokenData.toJson());
            return await _encryptTestData(jsonData);
          },
        );

        // Act
        final result = await secureTokenStorage.getAccessToken();

        // Assert
        expect(result, isA<StorageResultSuccess<String?>>());
        final accessToken = (result as StorageResultSuccess<String?>).data;
        expect(accessToken, equals(testTokenData.accessToken));
      });

      test('should get refresh token successfully', () async {
        // Arrange
        when(() => mockSecureStorage.read(key: 'carnow_token_data')).thenAnswer(
          (_) async {
            final jsonData = jsonEncode(testTokenData.toJson());
            return await _encryptTestData(jsonData);
          },
        );

        // Act
        final result = await secureTokenStorage.getRefreshToken();

        // Assert
        expect(result, isA<StorageResultSuccess<String?>>());
        final refreshToken = (result as StorageResultSuccess<String?>).data;
        expect(refreshToken, equals(testTokenData.refreshToken));
      });
    });

    group('Token Expiry Validation', () {
      setUp(() async {
        // Setup encryption key
        when(
          () => mockSecureStorage.read(key: 'carnow_encryption_key'),
        ).thenAnswer((_) async => base64Encode(List.filled(32, 42)));
        await secureTokenStorage.initialize();
      });

      test('should detect expired tokens and clean them up', () async {
        // Arrange
        final expiredTokenData = testTokenData.copyWith(
          expiresAt: DateTime.now().subtract(const Duration(hours: 1)),
        );

        when(() => mockSecureStorage.read(key: 'carnow_token_data')).thenAnswer(
          (_) async {
            final jsonData = jsonEncode(expiredTokenData.toJson());
            return await _encryptTestData(jsonData);
          },
        );

        // Act
        final result = await secureTokenStorage.getTokenData();

        // Assert
        expect(result, isA<StorageResultSuccess<TokenData?>>());
        final tokenData = (result as StorageResultSuccess<TokenData?>).data;
        expect(tokenData, isNull);

        // Verify cleanup was called
        verify(
          () => mockSecureStorage.delete(key: 'carnow_token_data'),
        ).called(1);
        verify(
          () => mockSecureStorage.delete(key: 'carnow_access_token'),
        ).called(1);
        verify(
          () => mockSecureStorage.delete(key: 'carnow_refresh_token'),
        ).called(1);
      });

      test(
        'should return false for hasValidToken when token is expired',
        () async {
          // Arrange
          final expiredTokenData = testTokenData.copyWith(
            expiresAt: DateTime.now().subtract(const Duration(hours: 1)),
          );

          when(
            () => mockSecureStorage.read(key: 'carnow_token_data'),
          ).thenAnswer((_) async {
            final jsonData = jsonEncode(expiredTokenData.toJson());
            return await _encryptTestData(jsonData);
          });

          // Act
          final hasValidToken = await secureTokenStorage.hasValidToken();

          // Assert
          expect(hasValidToken, isFalse);
        },
      );

      test(
        'should return true for isTokenExpired when token is expired',
        () async {
          // Arrange
          final expiredTokenData = testTokenData.copyWith(
            expiresAt: DateTime.now().subtract(const Duration(hours: 1)),
          );

          when(
            () => mockSecureStorage.read(key: 'carnow_token_data'),
          ).thenAnswer((_) async {
            final jsonData = jsonEncode(expiredTokenData.toJson());
            return await _encryptTestData(jsonData);
          });

          // Act
          final isExpired = await secureTokenStorage.isTokenExpired();

          // Assert
          expect(isExpired, isTrue);
        },
      );

      test(
        'should return false for isTokenExpired when token is valid',
        () async {
          // Arrange
          when(
            () => mockSecureStorage.read(key: 'carnow_token_data'),
          ).thenAnswer((_) async {
            final jsonData = jsonEncode(testTokenData.toJson());
            return await _encryptTestData(jsonData);
          });

          // Act
          final isExpired = await secureTokenStorage.isTokenExpired();

          // Assert
          expect(isExpired, isFalse);
        },
      );
    });

    group('Token Updates', () {
      setUp(() async {
        // Setup encryption key
        when(
          () => mockSecureStorage.read(key: 'carnow_encryption_key'),
        ).thenAnswer((_) async => base64Encode(List.filled(32, 42)));
        await secureTokenStorage.initialize();
      });

      test('should update access token successfully', () async {
        // Arrange
        when(() => mockSecureStorage.read(key: 'carnow_token_data')).thenAnswer(
          (_) async {
            final jsonData = jsonEncode(testTokenData.toJson());
            return await _encryptTestData(jsonData);
          },
        );

        const newAccessToken = 'new_access_token';
        final newExpiresAt = DateTime.now().add(const Duration(hours: 2));

        // Act
        final result = await secureTokenStorage.updateAccessToken(
          newAccessToken,
          newExpiresAt,
        );

        // Assert
        expect(result, isA<StorageResultSuccess>());
        verify(
          () => mockSecureStorage.write(
            key: 'carnow_token_data',
            value: any(named: 'value'),
          ),
        ).called(1);
      });

      test(
        'should fail to update access token when no existing data',
        () async {
          // Arrange
          when(
            () => mockSecureStorage.read(key: 'carnow_token_data'),
          ).thenAnswer((_) async => null);

          const newAccessToken = 'new_access_token';
          final newExpiresAt = DateTime.now().add(const Duration(hours: 2));

          // Act
          final result = await secureTokenStorage.updateAccessToken(
            newAccessToken,
            newExpiresAt,
          );

          // Assert
          expect(result, isA<StorageResultFailure>());
          expect(
            (result as StorageResultFailure).error,
            contains('No existing token data found'),
          );
        },
      );
    });

    group('Cleanup Operations', () {
      setUp(() async {
        // Setup encryption key
        when(
          () => mockSecureStorage.read(key: 'carnow_encryption_key'),
        ).thenAnswer((_) async => base64Encode(List.filled(32, 42)));
        await secureTokenStorage.initialize();
      });

      test('should clear token data successfully', () async {
        // Act
        final result = await secureTokenStorage.clearTokenData();

        // Assert
        expect(result, isA<StorageResultSuccess>());
        verify(
          () => mockSecureStorage.delete(key: 'carnow_token_data'),
        ).called(1);
        verify(
          () => mockSecureStorage.delete(key: 'carnow_access_token'),
        ).called(1);
        verify(
          () => mockSecureStorage.delete(key: 'carnow_refresh_token'),
        ).called(1);
      });

      test('should perform cleanup on expired tokens', () async {
        // Arrange
        final expiredTokenData = testTokenData.copyWith(
          expiresAt: DateTime.now().subtract(const Duration(hours: 1)),
        );

        when(() => mockSecureStorage.read(key: 'carnow_token_data')).thenAnswer(
          (_) async {
            final jsonData = jsonEncode(expiredTokenData.toJson());
            return await _encryptTestData(jsonData);
          },
        );

        // Act
        final result = await secureTokenStorage.performCleanup();

        // Assert
        expect(result, isA<StorageResultSuccess>());
        verify(
          () => mockSecureStorage.delete(key: 'carnow_token_data'),
        ).called(1);
      });

      test('should not perform cleanup on valid tokens', () async {
        // Arrange
        when(() => mockSecureStorage.read(key: 'carnow_token_data')).thenAnswer(
          (_) async {
            final jsonData = jsonEncode(testTokenData.toJson());
            return await _encryptTestData(jsonData);
          },
        );

        // Act
        final result = await secureTokenStorage.performCleanup();

        // Assert
        expect(result, isA<StorageResultSuccess>());
        verifyNever(() => mockSecureStorage.delete(key: any(named: 'key')));
      });

      test('should clear all data successfully', () async {
        // Act
        final result = await secureTokenStorage.clearAllData();

        // Assert
        expect(result, isA<StorageResultSuccess>());
        verify(() => mockSecureStorage.deleteAll()).called(1);
      });

      test('should handle cleanup errors gracefully', () async {
        // Arrange
        when(
          () => mockSecureStorage.delete(key: any(named: 'key')),
        ).thenThrow(Exception('Delete error'));

        // Act
        final result = await secureTokenStorage.clearTokenData();

        // Assert
        expect(result, isA<StorageResultFailure>());
        expect(
          (result as StorageResultFailure).error,
          contains('Failed to clear token data'),
        );
      });
    });

    group('Platform Configuration', () {
      test('should use correct Android configuration', () {
        // Arrange & Act
        final storage = SecureTokenStorage();

        // Assert - This tests that the constructor doesn't throw
        // and uses the correct Android configuration
        expect(storage, isNotNull);
      });

      test('should use correct iOS configuration', () {
        // Arrange & Act
        final storage = SecureTokenStorage();

        // Assert - This tests that the constructor doesn't throw
        // and uses the correct iOS configuration
        expect(storage, isNotNull);
      });
    });
  });
}

// Helper function to simulate encryption for testing
Future<String> _encryptTestData(String data) async {
  // Simple XOR encryption for testing
  final keyBytes = List.filled(32, 42);
  final dataBytes = utf8.encode(data);

  final encryptedBytes = <int>[];
  for (int i = 0; i < dataBytes.length; i++) {
    encryptedBytes.add(dataBytes[i] ^ keyBytes[i % keyBytes.length]);
  }

  return base64Encode(encryptedBytes);
}
