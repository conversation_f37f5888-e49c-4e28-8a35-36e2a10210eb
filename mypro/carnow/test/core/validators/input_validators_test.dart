import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/validators/input_validators.dart';

void main() {
  group('InputValidators', () {
    group('sanitizeInput', () {
      test('should remove dangerous HTML/JS content', () {
        expect(
          InputValidators.sanitizeInput('<script>alert("xss")</script>test'),
          equals('test'),
        );

        expect(
          InputValidators.sanitizeInput('javascript:alert("xss")'),
          equals(''),
        );

        expect(
          InputValidators.sanitizeInput('<iframe src="evil.com"></iframe>'),
          equals(''),
        );
      });

      test('should normalize whitespace', () {
        expect(
          InputValidators.sanitizeInput('  test   multiple   spaces  '),
          equals('test multiple spaces'),
        );
      });

      test('should remove control characters', () {
        expect(
          InputValidators.sanitizeInput('test\x00\x01\x02'),
          equals('test'),
        );
      });
    });

    group('containsDangerousContent', () {
      test('should detect XSS attempts', () {
        expect(
          InputValidators.containsDangerousContent(
            '<script>alert("xss")</script>',
          ),
          isTrue,
        );

        expect(
          InputValidators.containsDangerousContent('javascript:void(0)'),
          isTrue,
        );

        expect(
          InputValidators.containsDangerousContent('onclick="alert(1)"'),
          isTrue,
        );
      });

      test('should detect SQL injection attempts', () {
        expect(
          InputValidators.containsDangerousContent("'; DROP TABLE users; --"),
          isTrue,
        );

        expect(InputValidators.containsDangerousContent('1 OR 1=1'), isTrue);

        expect(
          InputValidators.containsDangerousContent('SELECT * FROM users'),
          isTrue,
        );
      });

      test('should allow safe content', () {
        expect(
          InputValidators.containsDangerousContent('This is safe text'),
          isFalse,
        );

        expect(
          InputValidators.containsDangerousContent('<EMAIL>'),
          isFalse,
        );
      });
    });

    group('validateName', () {
      test('should accept valid names', () {
        expect(InputValidators.validateName('John Doe', null), isNull);
        expect(InputValidators.validateName('أحمد محمد', null), isNull);
        expect(InputValidators.validateName("O'Connor", null), isNull);
        expect(InputValidators.validateName('Jean-Pierre', null), isNull);
      });

      test('should reject empty names', () {
        expect(InputValidators.validateName('', null), isNotNull);
        expect(InputValidators.validateName('   ', null), isNotNull);
        expect(InputValidators.validateName(null, null), isNotNull);
      });

      test('should reject short names', () {
        expect(InputValidators.validateName('A', null), isNotNull);
      });

      test('should reject names with invalid characters', () {
        expect(InputValidators.validateName('John123', null), isNotNull);
        expect(InputValidators.validateName('John@Doe', null), isNotNull);
        expect(InputValidators.validateName('<script>', null), isNotNull);
      });

      test('should reject overly long names', () {
        final longName = 'A' * 101;
        expect(InputValidators.validateName(longName, null), isNotNull);
      });
    });

    group('validateEmail', () {
      test('should accept valid emails', () {
        expect(InputValidators.validateEmail('<EMAIL>', null), isNull);
        expect(
          InputValidators.validateEmail('<EMAIL>', null),
          isNull,
        );
        expect(
          InputValidators.validateEmail('<EMAIL>', null),
          isNull,
        );
      });

      test('should reject invalid emails', () {
        expect(InputValidators.validateEmail('invalid-email', null), isNotNull);
        expect(InputValidators.validateEmail('@example.com', null), isNotNull);
        expect(InputValidators.validateEmail('user@', null), isNotNull);
        expect(
          InputValidators.validateEmail('<EMAIL>', null),
          isNotNull,
        );
        expect(
          InputValidators.validateEmail('.<EMAIL>', null),
          isNotNull,
        );
      });

      test('should reject empty emails', () {
        expect(InputValidators.validateEmail('', null), isNotNull);
        expect(InputValidators.validateEmail(null, null), isNotNull);
      });

      test('should reject overly long emails', () {
        final longEmail = '${'a' * 250}@example.com';
        expect(InputValidators.validateEmail(longEmail, null), isNotNull);
      });
    });

    group('validatePassword', () {
      test('should accept strong passwords', () {
        expect(InputValidators.validatePassword('StrongP@ss123', null), isNull);
        expect(
          InputValidators.validatePassword('MySecure#Pass456', null),
          isNull,
        );
      });

      test('should reject weak passwords', () {
        expect(InputValidators.validatePassword('weak', null), isNotNull);
        expect(InputValidators.validatePassword('password', null), isNotNull);
        expect(InputValidators.validatePassword('123456', null), isNotNull);
        expect(InputValidators.validatePassword('PASSWORD', null), isNotNull);
      });

      test('should require minimum length', () {
        expect(InputValidators.validatePassword('Short1!', null), isNotNull);
      });

      test('should require uppercase letters', () {
        expect(
          InputValidators.validatePassword('lowercase123!', null),
          isNotNull,
        );
      });

      test('should require lowercase letters', () {
        expect(
          InputValidators.validatePassword('UPPERCASE123!', null),
          isNotNull,
        );
      });

      test('should require numbers', () {
        expect(InputValidators.validatePassword('NoNumbers!', null), isNotNull);
      });

      test('should require special characters', () {
        expect(
          InputValidators.validatePassword('NoSpecialChars123', null),
          isNotNull,
        );
      });

      test('should reject common passwords', () {
        expect(
          InputValidators.validatePassword('password123', null),
          isNotNull,
        );
        expect(InputValidators.validatePassword('qwerty', null), isNotNull);
      });

      test('should reject overly long passwords', () {
        final longPassword = 'A' * 129;
        expect(InputValidators.validatePassword(longPassword, null), isNotNull);
      });
    });

    group('validatePhone', () {
      test('should accept valid phone numbers', () {
        expect(InputValidators.validatePhone('+**********', null), isNull);
        expect(InputValidators.validatePhone('(*************', null), isNull);
        expect(InputValidators.validatePhone('************', null), isNull);
        expect(InputValidators.validatePhone('05012345678', null), isNull);
      });

      test('should reject invalid phone numbers', () {
        expect(InputValidators.validatePhone('123', null), isNotNull);
        expect(InputValidators.validatePhone('abc123def', null), isNotNull);
        expect(InputValidators.validatePhone('', null), isNotNull);
        expect(InputValidators.validatePhone(null, null), isNotNull);
      });

      test('should reject overly long phone numbers', () {
        final longPhone = '1' * 21;
        expect(InputValidators.validatePhone(longPhone, null), isNotNull);
      });
    });

    group('validateAmount', () {
      test('should accept valid amounts', () {
        expect(InputValidators.validateAmount('100.50', null), isNull);
        expect(InputValidators.validateAmount('1000', null), isNull);
        expect(InputValidators.validateAmount('0.01', null), isNull);
      });

      test('should reject invalid amounts', () {
        expect(InputValidators.validateAmount('abc', null), isNotNull);
        expect(InputValidators.validateAmount('-100', null), isNotNull);
        expect(InputValidators.validateAmount('0', null), isNotNull);
        expect(InputValidators.validateAmount('', null), isNotNull);
      });

      test('should respect min/max limits', () {
        expect(
          InputValidators.validateAmount('50', null, 100),  // minAmount as positional
          isNotNull,
        );
        expect(
          InputValidators.validateAmount('1500', null, null, 1000),  // maxAmount as positional
          isNotNull,
        );
        expect(
          InputValidators.validateAmount(
            '500',
            null,
            100,    // minAmount
            1000,   // maxAmount
          ),
          isNull,
        );
      });
    });

    group('validateVIN', () {
      test('should accept valid VINs', () {
        expect(InputValidators.validateVIN('1HGBH41JXMN109186', null), isNull);
        expect(InputValidators.validateVIN('JH4TB2H26CC000000', null), isNull);
      });

      test('should reject invalid VINs', () {
        expect(InputValidators.validateVIN('123456789', null), isNotNull);
        expect(
          InputValidators.validateVIN('1HGBH41JXMN10918I', null),
          isNotNull,
        ); // Contains I
        expect(
          InputValidators.validateVIN('1HGBH41JXMN10918O', null),
          isNotNull,
        ); // Contains O
        expect(
          InputValidators.validateVIN('1HGBH41JXMN10918Q', null),
          isNotNull,
        ); // Contains Q
        expect(InputValidators.validateVIN('', null), isNotNull);
      });
    });

    group('validateBankAccount', () {
      test('should accept valid account numbers', () {
        expect(InputValidators.validateBankAccount('**********', null), isNull);
        expect(
          InputValidators.validateBankAccount(
            'SA03 8000 0000 6080 1016 7519',
            null,
          ),
          isNull,
        );
        expect(
          InputValidators.validateBankAccount(
            'GB29 NWBK 6016 1331 9268 19',
            null,
          ),
          isNull,
        );
      });

      test('should reject invalid account numbers', () {
        expect(InputValidators.validateBankAccount('123', null), isNotNull);
        expect(InputValidators.validateBankAccount('abc@123', null), isNotNull);
        expect(InputValidators.validateBankAccount('', null), isNotNull);
      });
    });

    group('validateURL', () {
      test('should accept valid URLs', () {
        expect(
          InputValidators.validateURL('https://example.com', null),
          isNull,
        );
        expect(
          InputValidators.validateURL('http://test.org/path', null),
          isNull,
        );
        expect(
          InputValidators.validateURL(null, null),
          isNull,
        ); // Optional field
      });

      test('should reject invalid URLs', () {
        expect(InputValidators.validateURL('not-a-url', null), isNotNull);
        expect(
          InputValidators.validateURL('ftp://example.com', null),
          isNotNull,
        );
        expect(
          InputValidators.validateURL('javascript:alert(1)', null),
          isNotNull,
        );
      });
    });

    group('validatePostalCode', () {
      test('should accept valid postal codes', () {
        expect(InputValidators.validatePostalCode('12345', null), isNull);
        expect(InputValidators.validatePostalCode('SW1A 1AA', null), isNull);
        expect(InputValidators.validatePostalCode('K1A-0A6', null), isNull);
        expect(
          InputValidators.validatePostalCode(null, null),
          isNull,
        ); // Optional
      });

      test('should reject invalid postal codes', () {
        expect(InputValidators.validatePostalCode('12', null), isNotNull);
        expect(
          InputValidators.validatePostalCode('**********1', null),
          isNotNull,
        );
        expect(InputValidators.validatePostalCode('ABC@123', null), isNotNull);
      });
    });
  });
}
