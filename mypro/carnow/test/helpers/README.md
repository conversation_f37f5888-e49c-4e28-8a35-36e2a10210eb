# CarNow Test Data Guidelines
# إرشادات بيانات اختبار CarNow

## نظرة عامة - Overview

هذا الدليل يوضح كيفية استخدام البيانات الوهمية بشكل صحيح في اختبارات CarNow مع الالتزام بـ **Forever Plan Architecture**.

## ⚠️ تحذيرات مهمة - Important Warnings

### ❌ ممنوع تماماً - Absolutely Forbidden
- استخدام البيانات الوهمية في الإنتاج
- استخدام البيانات الوهمية في الكود الرئيسي
- استخدام البيانات الوهمية في business logic
- استخدام البيانات الوهمية كـ fallback في الإنتاج

### ✅ مسموح فقط - Allowed Only
- اختبارات الوحدة (Unit Tests)
- اختبارات المكونات (Widget Tests)
- اختبارات التكامل (Integration Tests)
- اختبارات E2E (End-to-End Tests)

## 🔒 Forever Plan Compliance

### المبدأ الأساسي - Core Principle
```
❌ NO MOCK DATA IN PRODUCTION
✅ REAL DATA FROM SUPABASE ONLY IN PRODUCTION
✅ MOCK DATA ONLY IN TEST ENVIRONMENT
```

### الالتزامات - Commitments
1. **بيانات حقيقية فقط من Supabase في الإنتاج**
2. **بيانات وهمية فقط في بيئة الاختبار**
3. **فصل واضح بين بيانات الاختبار والإنتاج**
4. **تحقق من صحة البيانات في كل مرة**

## 📁 ملفات البيانات الوهمية - Test Data Files

### 1. `test_data.dart`
```dart
// ✅ الاستخدام الصحيح
final testUser = TestDataFactory.createTestUser(
  email: '<EMAIL>',
  name: 'MOCK_Test User',
);

// ✅ التحقق من صحة البيانات
TestDataValidator.validateTestDataOnly(testUser);
```

### 2. `mock_data.dart` (ملف قديم - deprecated)
```dart
// ❌ لا تستخدم هذا الملف
// ❌ Don't use this file
```

## 🧪 أمثلة الاستخدام - Usage Examples

### مثال 1: اختبار وحدة - Unit Test Example
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/helpers/test_data.dart';

void main() {
  group('ProductModel Tests', () {
    test('should create product from test data', () {
      // ✅ إنشاء بيانات اختبار
      final testData = TestDataFactory.createTestProduct(
        name: 'MOCK_Test Product',
        price: 500.0,
      );

      // ✅ التحقق من صحة البيانات
      TestDataValidator.validateTestDataOnly(testData);

      // ✅ إنشاء النموذج
      final product = ProductModel.fromJson(testData);

      // ✅ التأكيدات
      expect(product.name, contains('MOCK_'));
      expect(product.price, 500.0);
    });
  });
}
```

### مثال 2: اختبار مكون - Widget Test Example
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/helpers/test_data.dart';

void main() {
  group('ProductCard Widget Tests', () {
    testWidgets('should display product correctly', (tester) async {
      // ✅ إنشاء بيانات اختبار
      final testData = TestDataFactory.createTestProduct(
        name: 'MOCK_Test Product',
        price: 999.99,
      );

      // ✅ التحقق من بيئة الاختبار
      TestDataValidator.ensureTestEnvironment();

      // ✅ بناء المكون
      await tester.pumpWidget(
        ProductCard(product: ProductModel.fromJson(testData)),
      );

      // ✅ التأكيدات
      expect(find.textContaining('MOCK_'), findsOneWidget);
    });
  });
}
```

### مثال 3: اختبار تكامل - Integration Test Example
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/helpers/test_data.dart';

void main() {
  group('Product List Integration Tests', () {
    test('should load multiple products', () async {
      // ✅ إنشاء قائمة بيانات اختبار
      final testProducts = TestDataFactory.createTestProducts(count: 5);
      
      // ✅ التحقق من جميع البيانات
      for (final productData in testProducts) {
        TestDataValidator.validateTestDataOnly(productData);
      }

      // ✅ التحويل إلى نماذج
      final products = testProducts
          .map((data) => ProductModel.fromJson(data))
          .toList();

      // ✅ التأكيدات
      expect(products, hasLength(5));
      expect(products.every((p) => p.name.contains('MOCK_')), isTrue);
    });
  });
}
```

## 🛡️ التحقق من صحة البيانات - Data Validation

### التحقق من بيئة الاختبار - Test Environment Validation
```dart
// ✅ التحقق من أننا في بيئة اختبار
TestDataValidator.ensureTestEnvironment();

// ✅ التحقق من صحة بيانات الاختبار
TestDataValidator.validateTestDataOnly(testData);
```

### مؤشرات البيانات الوهمية - Mock Data Indicators
```dart
// ✅ جميع البيانات الوهمية تحتوي على هذه المؤشرات
{
  'test_environment': true,
  'test_data_indicator': true,
  'test_created_at': DateTime.now().toIso8601String(),
}
```

## 📊 سيناريوهات الاختبار - Test Scenarios

### البيانات الفارغة - Empty Data
```dart
final emptyData = TestDataScenarios.emptyData;
expect(emptyData['data'], isEmpty);
```

### بيانات الخطأ - Error Data
```dart
final errorData = TestDataScenarios.errorData;
expect(errorData['error'], contains('MOCK_'));
```

### بيانات التحميل - Loading Data
```dart
final loadingData = TestDataScenarios.loadingData;
expect(loadingData['loading'], isTrue);
```

### خطأ الشبكة - Network Error
```dart
final networkError = TestDataScenarios.networkErrorData;
expect(networkError['statusCode'], 500);
```

### خطأ المصادقة - Auth Error
```dart
final authError = TestDataScenarios.authErrorData;
expect(authError['statusCode'], 401);
```

## 🔧 أدوات مساعدة - Helper Tools

### TestDataFactory
```dart
// إنشاء بيانات مستخدم
TestDataFactory.createTestUser()

// إنشاء بيانات منتج
TestDataFactory.createTestProduct()

// إنشاء بيانات فئة
TestDataFactory.createTestCategory()

// إنشاء بيانات طلب
TestDataFactory.createTestOrder()

// إنشاء بيانات تحليلات
TestDataFactory.createTestAnalytics()
```

### TestDataValidator
```dart
// التحقق من بيئة الاختبار
TestDataValidator.isTestEnvironment

// التأكد من بيئة الاختبار
TestDataValidator.ensureTestEnvironment()

// التحقق من صحة البيانات
TestDataValidator.validateTestDataOnly(data)
```

### TestDataConstants
```dart
// ثوابت معرفات الاختبار
TestDataConstants.testUserId
TestDataConstants.testProductId
TestDataConstants.testCategoryId

// ثوابت البيانات
TestDataConstants.testEmail
TestDataConstants.testPrice
TestDataConstants.testPhoneNumber
```

## 🚫 أنماط سيئة يجب تجنبها - Anti-Patterns to Avoid

### ❌ ممنوع - Forbidden
```dart
// ❌ بيانات ثابتة في الاختبارات
return {'total': 1250, 'users': 890};

// ❌ استخدام البيانات الوهمية في الإنتاج
if (isProduction) {
  return mockData; // NO!
}

// ❌ عدم التحقق من صحة البيانات
final data = createTestData();
// Missing validation!

// ❌ خلط البيانات الوهمية مع البيانات الحقيقية
final mixedData = {...realData, ...mockData}; // NO!
```

### ✅ صحيح - Correct
```dart
// ✅ استخدام مصنع البيانات الوهمية
final testData = TestDataFactory.createTestProduct();

// ✅ التحقق من صحة البيانات
TestDataValidator.validateTestDataOnly(testData);

// ✅ التحقق من بيئة الاختبار
TestDataValidator.ensureTestEnvironment();

// ✅ استخدام البيانات الحقيقية في الإنتاج
if (isProduction) {
  return await apiClient.getRealData();
}
```

## 📋 قائمة التحقق - Checklist

### قبل كتابة الاختبار - Before Writing Tests
- [ ] تأكد من أنك في بيئة اختبار
- [ ] استخدم TestDataFactory لإنشاء البيانات
- [ ] أضف مؤشرات البيانات الوهمية
- [ ] خطط لسيناريوهات الاختبار المختلفة

### أثناء كتابة الاختبار - During Writing Tests
- [ ] تحقق من صحة البيانات الوهمية
- [ ] استخدم TestDataValidator
- [ ] تأكد من أن البيانات تحتوي على MOCK_ أو TEST_
- [ ] اختبر الحالات الفارغة والأخطاء

### بعد كتابة الاختبار - After Writing Tests
- [ ] تأكد من عدم استخدام البيانات الوهمية في الإنتاج
- [ ] تحقق من تغطية الاختبارات
- [ ] تأكد من الالتزام بـ Forever Plan
- [ ] وثق أي استثناءات أو حالات خاصة

## 🎯 أفضل الممارسات - Best Practices

### 1. استخدام مصنع البيانات - Use Data Factory
```dart
// ✅ صحيح
final testData = TestDataFactory.createTestProduct();

// ❌ خاطئ
final testData = {
  'name': 'Test Product',
  'price': 100.0,
};
```

### 2. التحقق من صحة البيانات - Validate Data
```dart
// ✅ صحيح
TestDataValidator.validateTestDataOnly(testData);

// ❌ خاطئ
// Missing validation
```

### 3. استخدام المؤشرات - Use Indicators
```dart
// ✅ صحيح
'name': 'MOCK_Test Product',
'test_environment': true,

// ❌ خاطئ
'name': 'Test Product',
```

### 4. فصل واضح - Clear Separation
```dart
// ✅ صحيح - بيانات وهمية للاختبار
final testData = TestDataFactory.createTestProduct();

// ✅ صحيح - بيانات حقيقية للإنتاج
final realData = await apiClient.getProducts();
```

## 🔍 مراقبة الجودة - Quality Monitoring

### مؤشرات الجودة - Quality Indicators
- [ ] جميع الاختبارات تستخدم TestDataFactory
- [ ] جميع البيانات الوهمية تحتوي على مؤشرات صحيحة
- [ ] لا توجد بيانات وهمية في الإنتاج
- [ ] تغطية اختبارات 85%+
- [ ] الالتزام بـ Forever Plan 100%

### أدوات المراقبة - Monitoring Tools
```dart
// التحقق من بيئة الاختبار
TestDataValidator.isTestEnvironment

// التحقق من صحة البيانات
TestDataValidator.validateTestDataOnly(data)

// التأكد من عدم وجود بيانات وهمية في الإنتاج
TestDataValidator.ensureTestEnvironment()
```

## 📞 الدعم والمساعدة - Support

### الموارد - Resources
- [Forever Plan Architecture](../Forever_Plan_Architecture.md)
- [Test Configuration](../config/test_config.dart)
- [Test Pipeline](../config/test_pipeline.dart)

### الفريق - Team
- **QA Lead**: مسؤول عن استراتيجية الاختبارات
- **Developers**: مسؤولون عن كتابة الاختبارات
- **Architecture Team**: مسؤولون عن الالتزام بـ Forever Plan

---

**🎯 الهدف: اختبارات شاملة وموثوقة مع الالتزام الكامل بـ Forever Plan Architecture!** 