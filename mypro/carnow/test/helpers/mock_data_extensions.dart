// =============================================================================
// CarNow Mock Data Extensions for Testing ONLY
// امتدادات البيانات الوهمية لـ CarNow للاختبارات فقط
// =============================================================================
// 
// ⚠️  WARNING: This file contains MOCK DATA for TESTING ONLY
// ⚠️  تحذير: هذا الملف يحتوي على بيانات وهمية للاختبارات فقط
// 
// ❌ NEVER use this data in production
// ❌ لا تستخدم هذه البيانات في الإنتاج أبداً
// 
// ✅ ONLY use for unit tests, widget tests, and integration tests
// ✅ استخدم فقط لاختبارات الوحدة والمكونات والتكامل
// 
// 🔒 Forever Plan Compliance: Real data from Supabase only in production
// 🔒 الالتزام بخطة للأبد: بيانات حقيقية من Supabase فقط في الإنتاج
// =============================================================================

import 'package:carnow/core/models/carnow_user.dart';
import 'package:carnow/core/models/carnow_wallet.dart';
import 'package:carnow/core/models/carnow_transaction.dart';
import 'mock_data.dart';

/// Mock data class that provides the specific methods expected by tests
/// فئة البيانات الوهمية التي توفر الطرق المحددة المتوقعة من الاختبارات
class MockData {
  static bool _isInitialized = false;
  
  /// Initialize mock data system
  /// تهيئة نظام البيانات الوهمية
  static void initialize() {
    if (_isInitialized) return;
    
    // Initialize the base mock data factory
    // MockDataFactory is already available from mock_data.dart
    
    _isInitialized = true;
  }

  /// Get mock user by ID
  /// الحصول على مستخدم وهمي بالمعرف
  static CarnowUser? getMockUser(String userId) {
    final user = MockDataFactory.createMockUser(
      id: userId,
      email: 'test_$<EMAIL>',
      name: 'MOCK_Test User $userId',
      phone: '+966501234567',
    );
    
    // Convert UserModel to CarnowUser
    return CarnowUser(
      id: user.id,
      email: user.email ?? 'test_$<EMAIL>',
      firstName: user.name?.split(' ').first ?? 'MOCK_Test',
      lastName: user.name?.split(' ').last ?? 'User',
      phoneNumber: user.phone,
      avatarUrl: user.profileImageUrl,
      isActive: user.isActive,
      createdAt: user.createdAt ?? DateTime.now(),
      updatedAt: user.updatedAt ?? DateTime.now(),
    );
  }

  /// Create random mock user
  /// إنشاء مستخدم وهمي عشوائي
  static CarnowUser createRandomMockUser() {
    final user = MockDataFactory.createMockUser();
    
    return CarnowUser(
      id: user.id,
      email: user.email ?? '<EMAIL>',
      firstName: user.name?.split(' ').first ?? 'MOCK_Random',
      lastName: user.name?.split(' ').last ?? 'User',
      phoneNumber: user.phone,
      avatarUrl: user.profileImageUrl,
      isActive: user.isActive,
      createdAt: user.createdAt ?? DateTime.now(),
      updatedAt: user.updatedAt ?? DateTime.now(),
    );
  }

  /// Get mock wallet by user ID
  /// الحصول على محفظة وهمية بمعرف المستخدم
  static CarnowWallet? getMockWalletByUserId(String userId) {
    return CarnowWallet(
      id: 'wallet_$userId',
      userId: userId,
      balance: 1000.0,
      availableBalance: 950.0,
      frozenBalance: 50.0,
      currency: 'LYD',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
      lastTransactionId: 'txn_${DateTime.now().millisecondsSinceEpoch}',
      lastTransactionDate: DateTime.now().subtract(const Duration(hours: 2)),
    );
  }

  /// Create random mock wallet
  /// إنشاء محفظة وهمية عشوائية
  static CarnowWallet createRandomMockWallet() {
    final userId = 'user_${DateTime.now().millisecondsSinceEpoch}';
    
    return CarnowWallet(
      id: 'wallet_$userId',
      userId: userId,
      balance: 500.0 + (DateTime.now().millisecondsSinceEpoch % 1000),
      availableBalance: 450.0 + (DateTime.now().millisecondsSinceEpoch % 900),
      frozenBalance: 50.0,
      currency: 'LYD',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now(),
      lastTransactionId: 'txn_${DateTime.now().millisecondsSinceEpoch}',
      lastTransactionDate: DateTime.now().subtract(const Duration(hours: 1)),
    );
  }

  /// Create random mock financial transaction
  /// إنشاء معاملة مالية وهمية عشوائية
  static CarnowTransaction createRandomMockFinancialTransaction({
    String? walletId,
    String type = 'deposit',
  }) {
    final transactionId = 'txn_${DateTime.now().millisecondsSinceEpoch}';
    final amount = 100.0 + (DateTime.now().millisecondsSinceEpoch % 500);
    
    return CarnowTransaction(
      id: transactionId,
      walletId: walletId ?? 'wallet_${DateTime.now().millisecondsSinceEpoch}',
      userId: 'user_${DateTime.now().millisecondsSinceEpoch}',
      amount: amount,
      type: type,
      status: 'completed',
      description: 'MOCK_Test transaction',
      reference: 'ref_$transactionId',
      fee: 5.0,
      metadata: {
        'test_environment': true,
        'test_data_indicator': true,
        'created_by': 'MockData',
      },
      createdAt: DateTime.now().subtract(const Duration(hours: 1)),
      updatedAt: DateTime.now(),
      processedAt: DateTime.now().subtract(const Duration(minutes: 30)),
    );
  }

  /// Get mock transactions by wallet ID
  /// الحصول على معاملات وهمية بمعرف المحفظة
  static List<CarnowTransaction> getMockTransactionsByWalletId(String walletId) {
    return [
      CarnowTransaction(
        id: 'txn_1_$walletId',
        walletId: walletId,
        userId: walletId.replaceFirst('wallet_', 'user_'),
        amount: 500.0,
        type: 'deposit',
        status: 'completed',
        description: 'MOCK_Initial deposit',
        reference: 'ref_initial_deposit',
        fee: 0.0,
        metadata: {
          'test_environment': true,
          'test_data_indicator': true,
        },
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
        processedAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      CarnowTransaction(
        id: 'txn_2_$walletId',
        walletId: walletId,
        userId: walletId.replaceFirst('wallet_', 'user_'),
        amount: 200.0,
        type: 'withdrawal',
        status: 'completed',
        description: 'MOCK_Test withdrawal',
        reference: 'ref_test_withdrawal',
        fee: 5.0,
        metadata: {
          'test_environment': true,
          'test_data_indicator': true,
        },
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        updatedAt: DateTime.now().subtract(const Duration(days: 7)),
        processedAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
      CarnowTransaction(
        id: 'txn_3_$walletId',
        walletId: walletId,
        userId: walletId.replaceFirst('wallet_', 'user_'),
        amount: 100.0,
        type: 'deposit',
        status: 'pending',
        description: 'MOCK_Pending deposit',
        reference: 'ref_pending_deposit',
        fee: 0.0,
        metadata: {
          'test_environment': true,
          'test_data_indicator': true,
        },
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
        processedAt: null,
      ),
    ];
  }

  /// Create mock user for testing
  /// إنشاء مستخدم وهمي للاختبار
  static CarnowUser createMockUser({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
  }) {
    final user = MockDataFactory.createMockUser(
      id: id,
      email: email,
      name: firstName != null && lastName != null ? '$firstName $lastName' : null,
      phone: phoneNumber,
    );
    
    return CarnowUser(
      id: user.id,
      email: user.email ?? '<EMAIL>',
      firstName: firstName ?? user.name?.split(' ').first ?? 'MOCK_Test',
      lastName: lastName ?? user.name?.split(' ').last ?? 'User',
      phoneNumber: user.phone,
      avatarUrl: user.profileImageUrl,
      isActive: user.isActive,
      createdAt: user.createdAt ?? DateTime.now(),
      updatedAt: user.updatedAt ?? DateTime.now(),
    );
  }

  /// Create mock wallet for testing
  /// إنشاء محفظة وهمية للاختبار
  static CarnowWallet createMockWallet({
    String? id,
    String? userId,
    double? balance,
    String? currency,
  }) {
    return CarnowWallet(
      id: id ?? 'wallet_${DateTime.now().millisecondsSinceEpoch}',
      userId: userId ?? 'user_${DateTime.now().millisecondsSinceEpoch}',
      balance: balance ?? 1000.0,
      availableBalance: (balance ?? 1000.0) * 0.95,
      frozenBalance: (balance ?? 1000.0) * 0.05,
      currency: currency ?? 'LYD',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
      lastTransactionId: 'txn_${DateTime.now().millisecondsSinceEpoch}',
      lastTransactionDate: DateTime.now().subtract(const Duration(hours: 1)),
    );
  }

  /// Create mock transaction for testing
  /// إنشاء معاملة وهمية للاختبار
  static CarnowTransaction createMockTransaction({
    String? id,
    String? walletId,
    String? userId,
    double? amount,
    String? type,
    String? status,
  }) {
    return CarnowTransaction(
      id: id ?? 'txn_${DateTime.now().millisecondsSinceEpoch}',
      walletId: walletId ?? 'wallet_${DateTime.now().millisecondsSinceEpoch}',
      userId: userId ?? 'user_${DateTime.now().millisecondsSinceEpoch}',
      amount: amount ?? 100.0,
      type: type ?? 'deposit',
      status: status ?? 'completed',
      description: 'MOCK_Test transaction',
      reference: 'ref_${DateTime.now().millisecondsSinceEpoch}',
      fee: 5.0,
      metadata: {
        'test_environment': true,
        'test_data_indicator': true,
        'created_by': 'MockData',
      },
      createdAt: DateTime.now().subtract(const Duration(hours: 1)),
      updatedAt: DateTime.now(),
      processedAt: DateTime.now().subtract(const Duration(minutes: 30)),
    );
  }
} 