// =============================================================================
// CarNow Mock Data for Testing ONLY
// بيانات وهمية لـ CarNow للاختبارات فقط
// =============================================================================
// 
// ⚠️  WARNING: This file contains MOCK DATA for TESTING ONLY
// ⚠️  تحذير: هذا الملف يحتوي على بيانات وهمية للاختبارات فقط
// 
// ❌ NEVER use this data in production
// ❌ لا تستخدم هذه البيانات في الإنتاج أبداً
// 
// ✅ ONLY use for unit tests, widget tests, and integration tests
// ✅ استخدم فقط لاختبارات الوحدة والمكونات والتكامل
// 
// 🔒 Forever Plan Compliance: Real data from Supabase only in production
// 🔒 الالتزام بخطة للأبد: بيانات حقيقية من Supabase فقط في الإنتاج
// =============================================================================

import 'package:carnow/core/models/category_model.dart';
import 'package:carnow/core/models/product_model.dart';
import 'package:carnow/core/models/user_model.dart';
import 'package:carnow/core/models/order_model.dart';
import 'package:carnow/core/models/order_item_model.dart';
import 'package:carnow/core/models/enums.dart';
import 'package:carnow/features/analytics/models/analytics_models.dart';

/// Mock data factory for testing purposes only
/// مصنع البيانات الوهمية لأغراض الاختبار فقط
class MockDataFactory {
  static const String _testPrefix = 'TEST_';
  static const String _mockPrefix = 'MOCK_';
  
  // Test data identifiers to prevent production usage
  static const String _testEnvironment = 'TEST_ENVIRONMENT';
  static const String _mockDataIndicator = 'MOCK_DATA_ONLY';

  /// Generate mock user data for testing
  /// إنشاء بيانات مستخدم وهمية للاختبار
  static UserModel createMockUser({
    String? id,
    String? email,
    String? name,
    String? phone,
    UserRole role = UserRole.customer,
  }) {
    return UserModel(
      id: id ?? '${_testPrefix}USER_${DateTime.now().millisecondsSinceEpoch}',
      authId: '${_testPrefix}AUTH_${DateTime.now().millisecondsSinceEpoch}',
      name: name ?? '${_mockPrefix}Test User',
      email: email ?? '${_mockPrefix}<EMAIL>',
      phone: phone ?? '+966501234567',
      role: role.name,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    );
  }

  /// Generate mock product data for testing
  /// إنشاء بيانات منتج وهمية للاختبار
  static ProductModel createMockProduct({
    String? id,
    String? name,
    String? description,
    double? price,
    String? categoryId,
    ProductStatus status = ProductStatus.active,
  }) {
    return ProductModel(
      id: id ?? '${_testPrefix}PRODUCT_${DateTime.now().millisecondsSinceEpoch}',
      name: name ?? '${_mockPrefix}Test Product',
      description: description ?? '${_mockPrefix}This is a test product for testing purposes only',
      price: price ?? 999.99,
      categoryId: categoryId ?? '${_testPrefix}CATEGORY_1',
      sellerId: '${_testPrefix}SELLER_1',
      images: [
        '${_mockPrefix}https://test.carnow.com/images/test1.jpg',
        '${_mockPrefix}https://test.carnow.com/images/test2.jpg',
      ],
      specifications: {
        'brand': '${_mockPrefix}Test Brand',
        'model': '${_mockPrefix}Test Model',
        'year': '2024',
      },
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
      updatedAt: DateTime.now(),
    );
  }

  /// Generate mock category data for testing
  /// إنشاء بيانات فئة وهمية للاختبار
  static CategoryModel createMockCategory({
    String? id,
    String? name,
    String? description,
    String? parentId,
  }) {
    return CategoryModel(
      id: id ?? '${_testPrefix}CATEGORY_${DateTime.now().millisecondsSinceEpoch}',
      name: name ?? '${_mockPrefix}Test Category',
      nameEn: '${_mockPrefix}Test Category',
      type: CategoryType.autoParts,
      description: description ?? '${_mockPrefix}Test category for testing purposes',
      parentId: parentId,
      imageUrl: '${_mockPrefix}https://test.carnow.com/categories/test.jpg',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now(),
    );
  }

  /// Generate mock order data for testing
  /// إنشاء بيانات طلب وهمية للاختبار
  static OrderModel createMockOrder({
    String? id,
    int? buyerId,
    OrderStatus status = OrderStatus.pending,
    double? totalAmount,
  }) {
    return OrderModel(
      id: int.tryParse(id ?? '1'),
      buyerId: buyerId ?? 1,
      status: status,
      totalAmount: totalAmount ?? 999.99,
      items: [
        const OrderItemModel(
          productId: '1',
          quantity: 1,
          unitPrice: 999.99,
        ),
      ],
      createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      updatedAt: DateTime.now(),
    );
  }

  /// Generate mock analytics data for testing
  /// إنشاء بيانات تحليلات وهمية للاختبار
  static AnalyticsData createMockAnalytics({
    int? totalUsers,
    int? totalOrders,
    double? totalSales,
  }) {
    return AnalyticsData(
      overview: OverviewData(
        totalUsers: totalUsers ?? 150,
        totalSales: totalSales ?? 15000.0,
        totalOrders: totalOrders ?? 25,
        conversionRate: 0.167, // 16.7%
        userGrowth: 5.2,
        salesGrowth: 12.5,
        ordersGrowth: 8.3,
        conversionGrowth: 2.1,
      ),
      users: UsersData(
        activity: [
          ActivityPoint(
            hour: 12,
            users: totalUsers ?? 150,
            date: DateTime.now().subtract(const Duration(days: 1)),
          ),
        ],
        sessionsStats: SessionStats(
          averageDuration: const Duration(minutes: 12),
          bounceRate: 25.3,
          pagesPerSession: 3.5,
        ),
        demographics: Demographics(
          ageGroups: {'18-25': 25, '26-35': 40, '36-45': 20, '46+': 15},
          genderDistribution: {'male': 60, 'female': 40},
          locations: {'Riyadh': 50, 'Jeddah': 30, 'Dammam': 20},
        ),
        geographic: [
          GeographicData(
            region: 'Riyadh',
            users: totalUsers ?? 150,
            percentage: 85.0,
          ),
        ],
      ),
      products: ProductsData(
        topCategories: [
          CategoryData(
            name: 'Auto Parts',
            sales: 5000.0,
            percentage: 33.3,
            growth: 15.2,
          ),
        ],
        topProducts: [
          ProductPerformanceData(
            id: '1',
            name: 'Test Product 1',
            sales: 150,
            orders: 25,
            revenue: 1500.0,
            rating: 4.5,
          ),
        ],
        inventory: InventoryData(
          totalProducts: 500,
          lowStock: 25,
          outOfStock: 5,
          totalValue: 50000.0,
        ),
      ),
      sales: SalesData(
        monthly: [
          SalesPoint(
            date: DateTime.now().subtract(const Duration(days: 30)),
            amount: totalSales ?? 15000.0,
            orders: totalOrders ?? 25,
          ),
        ],
        daily: [
          SalesPoint(
            date: DateTime.now().subtract(const Duration(days: 1)),
            amount: (totalSales ?? 15000.0) / 30,
            orders: (totalOrders ?? 25) ~/ 30,
          ),
        ],
        revenue: RevenueData(
          total: totalSales ?? 15000.0,
          growth: 12.5,
          forecast: (totalSales ?? 15000.0) * 1.15,
        ),
      ),
      engagement: EngagementData(
        bounceRate: 25.3,
        sessionDuration: const Duration(minutes: 12),
        pageViews: 15000,
        interactions: [
          InteractionData(
            type: 'page_view',
            count: 15000,
            date: DateTime.now().subtract(const Duration(days: 1)),
          ),
        ],
      ),
    );
  }

  /// Generate a list of mock users for testing
  /// إنشاء قائمة مستخدمين وهميين للاختبار
  static List<UserModel> createMockUsers({int count = 5}) {
    return List.generate(count, (index) {
      return createMockUser(
        id: '${_testPrefix}USER_${index + 1}',
        email: '${_mockPrefix}user${index + 1}@carnow.test',
        name: '${_mockPrefix}Test User ${index + 1}',
        role: index == 0 ? UserRole.admin : UserRole.customer,
      );
    });
  }

  /// Generate a list of mock products for testing
  /// إنشاء قائمة منتجات وهمية للاختبار
  static List<ProductModel> createMockProducts({int count = 10}) {
    return List.generate(count, (index) {
      return createMockProduct(
        id: '${_testPrefix}PRODUCT_${index + 1}',
        name: '${_mockPrefix}Test Product ${index + 1}',
        price: 100.0 + (index * 50.0),
        categoryId: '${_testPrefix}CATEGORY_${(index % 3) + 1}',
        status: index % 3 == 0 ? ProductStatus.active : ProductStatus.sold,
      );
    });
  }

  /// Generate a list of mock categories for testing
  /// إنشاء قائمة فئات وهمية للاختبار
  static List<CategoryModel> createMockCategories({int count = 5}) {
    return List.generate(count, (index) {
      return createMockCategory(
        id: '${_testPrefix}CATEGORY_${index + 1}',
        name: '${_mockPrefix}Test Category ${index + 1}',
        parentId: index > 2 ? '${_testPrefix}CATEGORY_1' : null,
      );
    });
  }

  /// Generate a list of mock orders for testing
  /// إنشاء قائمة طلبات وهمية للاختبار
  static List<OrderModel> createMockOrders({int count = 8}) {
    return List.generate(count, (index) {
      return createMockOrder(
        id: '${index + 1}',
        buyerId: (index % 3) + 1,
        status: OrderStatus.values[index % OrderStatus.values.length],
        totalAmount: 200.0 + (index * 100.0),
      );
    });
  }

  /// Generate a list of mock analytics for testing
  /// إنشاء قائمة تحليلات وهمية للاختبار
  static List<AnalyticsData> createMockAnalyticsList({int days = 7}) {
    return List.generate(days, (index) {
      return createMockAnalytics(
        totalUsers: 100 + (index * 10),
        totalOrders: 15 + (index * 2),
        totalSales: 10000.0 + (index * 1000.0),
      );
    });
  }

  /// Check if data is mock data (for validation)
  /// التحقق من أن البيانات وهمية (للتحقق)
  static bool isMockData(Map<String, dynamic> metadata) {
    return metadata[_mockDataIndicator] == true;
  }

  /// Check if data is test data (for validation)
  /// التحقق من أن البيانات بيانات اختبار (للتحقق)
  static bool isTestData(Map<String, dynamic> metadata) {
    return metadata[_testEnvironment] == true;
  }

  /// Validate that data should only be used in tests
  /// التحقق من أن البيانات يجب استخدامها في الاختبارات فقط
  static void validateTestDataOnly(Map<String, dynamic> metadata) {
    if (!isMockData(metadata) || !isTestData(metadata)) {
      throw StateError(
        'This data is not properly marked as test data. '
        'Only use mock data in test environments.',
      );
    }
  }

  /// Create mock error response for testing
  /// إنشاء استجابة خطأ وهمية للاختبار
  static Map<String, dynamic> createMockErrorResponse({
    String error = 'Test error',
    String code = 'TEST_ERROR',
    int statusCode = 400,
  }) {
    return {
      'error': '$_mockPrefix$error',
      'code': '$_testPrefix$code',
      'statusCode': statusCode,
      'timestamp': DateTime.now().toIso8601String(),
      'testData': true,
      'mockData': true,
    };
  }

  /// Create mock success response for testing
  /// إنشاء استجابة نجاح وهمية للاختبار
  static Map<String, dynamic> createMockSuccessResponse({
    dynamic data,
    String message = 'Test success',
  }) {
    return {
      'data': data,
      'message': '$_mockPrefix$message',
      'success': true,
      'timestamp': DateTime.now().toIso8601String(),
      'testData': true,
      'mockData': true,
    };
  }
}

/// Mock data constants for testing
/// ثوابت البيانات الوهمية للاختبار
class MockDataConstants {
  // Test IDs
  static const String testUserId = 'TEST_USER_1';
  static const String testProductId = 'TEST_PRODUCT_1';
  static const String testCategoryId = 'TEST_CATEGORY_1';
  static const String testOrderId = 'TEST_ORDER_1';
  
  // Test emails
  static const String testEmail = '<EMAIL>';
  static const String testAdminEmail = '<EMAIL>';
  
  // Test names
  static const String testUserName = 'MOCK_Test User';
  static const String testProductName = 'MOCK_Test Product';
  static const String testCategoryName = 'MOCK_Test Category';
  
  // Test URLs
  static const String testImageUrl = 'MOCK_https://test.carnow.com/images/test.jpg';
  static const String testApiUrl = 'MOCK_https://test-api.carnow.com';
  
  // Test phone numbers
  static const String testPhoneNumber = '+966501234567';
  
  // Test prices
  static const double testPrice = 999.99;
  static const double testTotalAmount = 1499.99;
  
  // Test quantities
  static const int testQuantity = 1;
  static const int testUserCount = 150;
  static const int testOrderCount = 25;
  
  // Test dates
  static final DateTime testCreatedAt = DateTime.now().subtract(const Duration(days: 7));
  static final DateTime testUpdatedAt = DateTime.now();
}

/// Mock data validation utilities
/// أدوات التحقق من البيانات الوهمية
class MockDataValidator {
  /// Validate that all mock data has proper test indicators
  /// التحقق من أن جميع البيانات الوهمية لها مؤشرات اختبار مناسبة
  static void validateMockData(dynamic data) {
    if (data is Map<String, dynamic>) {
      if (!data.containsKey('testData') || !data.containsKey('mockData')) {
        throw ArgumentError(
          'Mock data must contain testData and mockData indicators',
        );
      }
    }
  }

  /// Check if running in test environment
  /// التحقق من تشغيل البيئة في وضع الاختبار
  static bool get isTestEnvironment {
    return const bool.fromEnvironment('FLUTTER_TEST', defaultValue: false) ||
           const bool.fromEnvironment('TEST_ENV', defaultValue: false);
  }

  /// Ensure mock data is only used in test environment
  /// التأكد من استخدام البيانات الوهمية في بيئة الاختبار فقط
  static void ensureTestEnvironment() {
    if (!isTestEnvironment) {
      throw StateError(
        'Mock data can only be used in test environment. '
        'Current environment is not marked as test.',
      );
    }
  }
} 