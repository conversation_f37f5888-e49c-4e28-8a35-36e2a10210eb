// Enhanced Secure Token Storage Tests
// اختبارات تخزين الرموز الآمنة المحسّنة
//
// This file contains unit tests for EnhancedSecureTokenStorage
// focusing on the methods that actually exist in the implementation.

import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../../../../lib/core/auth/enhanced_secure_token_storage.dart';

import 'enhanced_secure_token_storage_test.mocks.dart';

// Generate mocks for testing
@GenerateMocks([
  FlutterSecureStorage,
])
void main() {
  group('EnhancedSecureTokenStorage Tests', () {
    late MockFlutterSecureStorage mockSecureStorage;
    late EnhancedSecureTokenStorage tokenStorage;

    setUp(() {
      mockSecureStorage = MockFlutterSecureStorage();
      tokenStorage = EnhancedSecureTokenStorage(secureStorage: mockSecureStorage);
      
      // Setup default stubs for common operations
      when(mockSecureStorage.read(key: anyNamed('key')))
          .thenAnswer((_) async => null);
      when(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
          .thenAnswer((_) async {});
      when(mockSecureStorage.delete(key: anyNamed('key')))
          .thenAnswer((_) async {});
      when(mockSecureStorage.deleteAll())
          .thenAnswer((_) async {});
      when(mockSecureStorage.readAll())
          .thenAnswer((_) async => <String, String>{});
    });

    tearDown(() {
      tokenStorage.dispose();
    });

    group('Token Storage Operations', () {
      test('should store token successfully', () async {
        // Arrange
        const testToken = 'test_access_token';
        final expiryDate = DateTime.now().add(const Duration(hours: 1));

        // Act & Assert - Should not throw
        await expectLater(
          tokenStorage.storeToken(testToken, expiryDate: expiryDate),
          completes,
        );

        // Verify storage was called
        verify(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .called(greaterThan(0));
      });

      test('should store refresh token successfully', () async {
        // Arrange
        const testRefreshToken = 'test_refresh_token';

        // Act & Assert - Should not throw
        await expectLater(
          tokenStorage.storeRefreshToken(testRefreshToken),
          completes,
        );

        // Verify storage was called
        verify(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .called(greaterThan(0));
      });

      test('should store session data successfully', () async {
        // Arrange
        final testSessionData = {
          'userId': '123',
          'sessionId': 'abc-def-ghi',
          'loginTime': DateTime.now().toIso8601String(),
        };

        // Act & Assert - Should not throw
        await expectLater(
          tokenStorage.storeSessionData(testSessionData),
          completes,
        );

        // Verify storage was called
        verify(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .called(greaterThan(0));
      });

      test('should reject empty token', () async {
        // Act & Assert
        await expectLater(
          tokenStorage.storeToken(''),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should reject past expiry date', () async {
        // Arrange
        const testToken = 'test_token';
        final pastDate = DateTime.now().subtract(const Duration(hours: 1));

        // Act & Assert
        await expectLater(
          tokenStorage.storeToken(testToken, expiryDate: pastDate),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Token Retrieval Operations', () {
      test('should return null when no token stored', () async {
        // Act
        final result = await tokenStorage.getToken();

        // Assert
        expect(result, isNull);
      });

      test('should return null when no refresh token stored', () async {
        // Act
        final result = await tokenStorage.getRefreshToken();

        // Assert
        expect(result, isNull);
      });

      test('should return empty map when no session data stored', () async {
        // Act
        final result = await tokenStorage.getSessionData();

        // Assert
        expect(result, isEmpty);
      });

      test('should handle storage read errors gracefully', () async {
        // Arrange
        when(mockSecureStorage.read(key: anyNamed('key')))
            .thenThrow(Exception('Storage error'));

        // Act
        final result = await tokenStorage.getToken();

        // Assert
        expect(result, isNull);
      });
    });

    group('Token Validation Operations', () {
      test('should validate token correctly when no token exists', () async {
        // Act
        final hasValidToken = await tokenStorage.hasValidToken();
        final hasValidRefreshToken = await tokenStorage.hasValidRefreshToken();

        // Assert
        expect(hasValidToken, isFalse);
        expect(hasValidRefreshToken, isFalse);
      });

      test('should check token expiry when no expiry date stored', () async {
        // Act
        final isExpired = await tokenStorage.isTokenExpired();

        // Assert
        expect(isExpired, isFalse); // No expiry date means not expired (default behavior)
      });

      test('should return null for time until expiry when no expiry stored', () async {
        // Act
        final timeUntilExpiry = await tokenStorage.getTimeUntilExpiry();

        // Assert
        expect(timeUntilExpiry, isNull);
      });
    });

    group('Token Management Operations', () {
      test('should clear all data successfully', () async {
        // Act & Assert - Should not throw
        await expectLater(
          tokenStorage.clearAllData(),
          completes,
        );

        // Verify delete was called multiple times (for individual keys)
        verify(mockSecureStorage.delete(key: anyNamed('key')))
            .called(greaterThan(0));
      });

      test('should clear expired tokens successfully', () async {
        // Act & Assert - Should not throw
        await expectLater(
          tokenStorage.clearExpiredTokens(),
          completes,
        );
      });

      test('should update token expiry successfully', () async {
        // Arrange
        final newExpiryDate = DateTime.now().add(const Duration(hours: 2));

        // Act & Assert - Should not throw
        await expectLater(
          tokenStorage.updateTokenExpiry(newExpiryDate),
          completes,
        );
      });

      test('should rotate tokens successfully', () async {
        // Arrange
        const newToken = 'new_access_token';
        const newRefreshToken = 'new_refresh_token';
        final newExpiryDate = DateTime.now().add(const Duration(hours: 1));

        // Act & Assert - Should not throw
        await expectLater(
          tokenStorage.rotateTokens(
            newToken: newToken,
            newRefreshToken: newRefreshToken,
            newExpiryDate: newExpiryDate,
          ),
          completes,
        );

        // Verify multiple storage operations
        verify(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .called(greaterThan(1));
      });
    });

    group('Storage Integrity Operations', () {
      test('should validate storage integrity', () async {
        // Act
        final isValid = await tokenStorage.validateStorageIntegrity();

        // Assert
        expect(isValid, isA<bool>());
      });

      test('should handle integrity validation errors gracefully', () async {
        // Arrange
        when(mockSecureStorage.read(key: anyNamed('key')))
            .thenThrow(Exception('Storage error'));

        // Act
        final isValid = await tokenStorage.validateStorageIntegrity();

        // Assert
        expect(isValid, isFalse);
      });
    });

    group('Error Handling', () {
      test('should handle storage write errors gracefully', () async {
        // Arrange
        const testToken = 'test_token';
        // Override the default stub for this specific test
        when(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .thenThrow(Exception('Storage write error'));

        // Act & Assert
        await expectLater(
          tokenStorage.storeToken(testToken),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle storage delete errors gracefully', () async {
        // Arrange
        // Override the default stub for this specific test
        when(mockSecureStorage.delete(key: anyNamed('key')))
            .thenThrow(Exception('Storage delete error'));

        // Act & Assert
        await expectLater(
          tokenStorage.clearAllData(),
          throwsA(isA<Exception>()),
        );
      });
    });
  });
}
