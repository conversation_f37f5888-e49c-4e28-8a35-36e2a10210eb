// =============================================================================
// CarNow API Service Unit Tests
// اختبارات وحدة خدمة API في CarNow
// =============================================================================

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dio/dio.dart';
import 'package:carnow/core/services/carnow_api_service.dart';
import 'package:carnow/core/models/carnow_user.dart';
import 'package:carnow/core/models/carnow_wallet.dart';
import '../../../helpers/test_helpers.dart';
import '../../../helpers/mock_data_extensions.dart';

// Mock classes
class MockCarnowApiService extends Mock implements CarnowApiService {}

class MockRef extends Mock implements Ref {}

void main() {
  group('CarnowApiService Tests', () {
    late MockCarnowApiService mockApiService;
    late ProviderContainer container;

    setUpAll(() {
      TestHelpers.setupTestEnvironment();
      MockData.initialize();

      // Register fallback values for request types
      registerFallbackValue(
        CreateCarnowUserRequest(
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
        ),
      );
      registerFallbackValue(
        DepositRequest(
          amount: 100.0,
          description: 'Test deposit',
          reference: 'test_ref',
        ),
      );
      registerFallbackValue(
        WithdrawRequest(
          amount: 100.0,
          description: 'Test withdrawal',
          reference: 'test_ref',
        ),
      );
    });

    setUp(() {
      container = ProviderContainer();
      mockApiService = MockCarnowApiService();
    });

    tearDown(() {
      container.dispose();
    });

    group('Health Checks', () {
      test('checkHealth returns true when API is healthy', () async {
        // Arrange
        when(() => mockApiService.checkHealth()).thenAnswer((_) async => true);

        // Act
        final result = await mockApiService.checkHealth();

        // Assert
        expect(result, isTrue);
        verify(() => mockApiService.checkHealth()).called(1);
      });

      test('checkHealth returns false when API is unhealthy', () async {
        // Arrange
        when(() => mockApiService.checkHealth()).thenAnswer((_) async => false);

        // Act
        final result = await mockApiService.checkHealth();

        // Assert
        expect(result, isFalse);
        verify(() => mockApiService.checkHealth()).called(1);
      });

      test('checkReady returns true when API is ready', () async {
        // Arrange
        when(() => mockApiService.checkReady()).thenAnswer((_) async => true);

        // Act
        final result = await mockApiService.checkReady();

        // Assert
        expect(result, isTrue);
        verify(() => mockApiService.checkReady()).called(1);
      });
    });

    group('User Management', () {
      test('getUserByEmail returns user when found', () async {
        // Arrange
        final mockUser = MockData.createRandomMockUser();
        when(() => mockApiService.getUserByEmail(any())).thenAnswer((_) async => mockUser);

        // Act
        final result = await mockApiService.getUserByEmail('<EMAIL>');

        // Assert
        expect(result, equals(mockUser));
        verify(() => mockApiService.getUserByEmail('<EMAIL>')).called(1);
      });

      test('createUser creates new user successfully', () async {
        // Arrange
        final mockUser = MockData.createRandomMockUser();
        final createRequest = CreateCarnowUserRequest(
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
        );
        
        when(() => mockApiService.createUser(any())).thenAnswer((_) async => mockUser);

        // Act
        final result = await mockApiService.createUser(createRequest);

        // Assert
        expect(result, equals(mockUser));
        verify(() => mockApiService.createUser(createRequest)).called(1);
      });
    });

    group('Wallet Management', () {
      test('getWalletByUserId returns wallet when found', () async {
        // Arrange
        final mockWallet = MockData.createRandomMockWallet();
        when(() => mockApiService.getWalletByUserId(any())).thenAnswer((_) async => mockWallet);

        // Act
        final result = await mockApiService.getWalletByUserId('user_1');

        // Assert
        expect(result, equals(mockWallet));
        verify(() => mockApiService.getWalletByUserId('user_1')).called(1);
      });

      test('depositToWallet creates deposit transaction', () async {
        // Arrange
        const walletId = 'wallet_user_1';
        const amount = 500.0;
        final depositRequest = DepositRequest(
          amount: amount,
          description: 'Test deposit',
        );

        final mockTransaction = MockData.createRandomMockFinancialTransaction(
          walletId: walletId,
          type: 'deposit',
        );

        when(() => mockApiService.depositToWallet(any(), any())).thenAnswer((_) async => mockTransaction);

        // Act
        final result = await mockApiService.depositToWallet(walletId, depositRequest);

        // Assert
        expect(result.walletId, equals(walletId));
        expect(result.type, equals('deposit'));
        verify(() => mockApiService.depositToWallet(walletId, depositRequest)).called(1);
      });

      test('withdrawFromWallet creates withdrawal transaction', () async {
        // Arrange
        const walletId = 'wallet_user_1';
        const amount = 200.0;
        final withdrawRequest = WithdrawRequest(
          amount: amount,
          description: 'Test withdrawal',
        );

        final mockTransaction = MockData.createRandomMockFinancialTransaction(
          walletId: walletId,
          type: 'withdrawal',
        );

        when(() => mockApiService.withdrawFromWallet(any(), any())).thenAnswer((_) async => mockTransaction);

        // Act
        final result = await mockApiService.withdrawFromWallet(walletId, withdrawRequest);

        // Assert
        expect(result.walletId, equals(walletId));
        expect(result.type, equals('withdrawal'));
        verify(() => mockApiService.withdrawFromWallet(walletId, withdrawRequest)).called(1);
      });

      test('getWalletTransactions returns transaction list', () async {
        // Arrange
        const walletId = 'wallet_user_1';
        final mockTransactions = MockData.getMockTransactionsByWalletId(walletId);

        when(() => mockApiService.getWalletTransactions(any())).thenAnswer((_) async => mockTransactions);

        // Act
        final result = await mockApiService.getWalletTransactions(walletId);

        // Assert
        expect(result.length, equals(mockTransactions.length));
        expect(result.first.walletId, equals(walletId));
        verify(() => mockApiService.getWalletTransactions(walletId)).called(1);
      });
    });

    group('Error Handling', () {
      test('handles network errors gracefully', () async {
        // Arrange
        when(() => mockApiService.checkHealth()).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: '/health'),
            type: DioExceptionType.connectionTimeout,
          ),
        );

        // Act & Assert
        expect(
          () => mockApiService.checkHealth(),
          throwsA(isA<DioException>()),
        );
      });
    });
  });
}
