import 'package:flutter_test/flutter_test.dart';
import 'package:riverpod/riverpod.dart';
import 'package:carnow/core/networking/production_api_client.dart';

// Simple token provider that returns null
final tokenProvider = Provider<String?>((ref) => null);

// Simple implementation of Ref<Object?> for testing
class TestRef implements Ref<Object?> {
  @override
  T read<T>(ProviderListenable<T> provider) {
    // Return null for any provider to avoid secure storage issues
    return null as T;
  }
  
  @override
  T watch<T>(ProviderListenable<T> provider) => read(provider);
  
  @override
  ProviderSubscription<T> listen<T>(ProviderListenable<T> provider, void Function(T? previous, T next) listener, {bool fireImmediately = false, void Function(Object error, StackTrace stackTrace)? onError}) {
    throw UnimplementedError();
  }
  
  @override
  void invalidate(ProviderOrFamily provider) {}
  
  @override
  void invalidateSelf() {}
  
  @override
  T refresh<T>(Refreshable<T> provider) => throw UnimplementedError();
  
  @override
  void dispose() {}
  
  @override
  void state(ProviderOrFamily provider) {}
  
  @override
  ProviderContainer get container => throw UnimplementedError();
  
  @override
  bool exists(ProviderBase<Object?> provider) => false;
  
  @override
  KeepAliveLink keepAlive() => throw UnimplementedError();
  
  @override
  void listenSelf(void Function(Object? previous, Object next) listener, {void Function(Object error, StackTrace stackTrace)? onError}) {}
  
  @override
  void notifyListeners() {}
  
  @override
  void onAddListener(void Function() cb) {}
  
  @override
  void onCancel(void Function() cb) {}
  
  @override
  void onDispose(void Function() cb) {}
  
  @override
  void onRemoveListener(void Function() cb) {}
  
  @override
  void onResume(void Function() cb) {}
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('ProductionApiClient Basic Tests', () {
    late ProductionApiClient client;
    
    setUp(() {
      client = ProductionApiClient(
        ref: TestRef(),
        baseUrl: 'https://jsonplaceholder.typicode.com',
        timeout: const Duration(seconds: 10),
      );
    });

    tearDown(() {
      client.dispose();
    });

    test('ProductionApiClient can be instantiated', () {
      expect(client, isA<ProductionApiClient>());
    });

    test('has all required HTTP methods', () {
      expect(client.get, isA<Function>());
      expect(client.post, isA<Function>());
      expect(client.put, isA<Function>());
      expect(client.delete, isA<Function>());
    });

    test('has uploadFile method', () {
      expect(client.uploadFile, isA<Function>());
    });

    test('has cancelRequests method', () {
      expect(client.cancelRequests, isA<Function>());
    });

    test('has dispose method', () {
      expect(client.dispose, isA<Function>());
    });

    test('can be instantiated with custom base URL', () {
      final customClient = ProductionApiClient(
        ref: TestRef(),
        baseUrl: 'https://api.example.com',
      );
      expect(customClient, isA<ProductionApiClient>());
      customClient.dispose();
    });

    test('can be instantiated with custom timeout', () {
      final customClient = ProductionApiClient(
        ref: TestRef(),
        timeout: const Duration(seconds: 30),
      );
      expect(customClient, isA<ProductionApiClient>());
      customClient.dispose();
    });

    test('dispose method works without errors', () {
      expect(() => client.dispose(), returnsNormally);
    });

    test('cancelRequests method works without errors', () {
      expect(() => client.cancelRequests(), returnsNormally);
    });

    test('methods return Future when called', () {
      // These will fail due to test environment HTTP blocking, but we can test that they return Futures
      expect(
        client.get('/test', fromJson: (json) => json),
        isA<Future>(),
      );
      
      expect(
        client.post('/test', data: {}, fromJson: (json) => json),
        isA<Future>(),
      );
      
      expect(
        client.put('/test', data: {}, fromJson: (json) => json),
        isA<Future>(),
      );
      
      expect(
        client.delete('/test', fromJson: (json) => json),
        isA<Future>(),
      );
    });
  });
}
