import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'package:riverpod/riverpod.dart';
import 'package:carnow/core/networking/production_api_client.dart';

class TestRef implements Ref<Object?> {
  final ProviderContainer container;

  TestRef(this.container);

  @override
  T read<T>(ProviderListenable<T> provider) => container.read(provider);

  @override
  T watch<T>(ProviderListenable<T> provider) => container.read(provider);

  @override
  State refresh<State>(Refreshable<State> provider) => container.refresh(provider);

  @override
  ProviderSubscription<State> listen<State>(
    ProviderListenable<State> provider,
    void Function(State? previous, State next) listener, {
    bool fireImmediately = false,
    void Function(Object error, StackTrace stackTrace)? onError,
  }) => container.listen(provider, listener, fireImmediately: fireImmediately, onError: onError);

  @override
  void invalidate(ProviderOrFamily provider) => container.invalidate(provider);

  @override
  void invalidateSelf() {}

  @override
  KeepAliveLink keepAlive() => throw UnimplementedError();

  @override
  void disposeDelay(Duration duration) {}

  @override
  bool exists(ProviderListenable<Object?> provider) => false;

  @override
  void listenSelf(void Function(Object? previous, Object next) listener, {void Function(Object error, StackTrace stackTrace)? onError}) {}

  @override
  void notifyListeners() {}

  @override
  void onAddListener(void Function() listener) {}

  @override
  void onCancel(void Function() listener) {}

  @override
  void onDispose(void Function() listener) {}

  @override
  void onResume(void Function() listener) {}

  @override
  void onRemoveListener(void Function() listener) {}
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('ProductionApiClient Basic Tests', () {
    late TestRef testRef;
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
      testRef = TestRef(container);
    });

    tearDown(() {
      container.dispose();
    });

    test('should create ProductionApiClient instance', () {
      final client = ProductionApiClient(
        ref: testRef,
        baseUrl: 'https://test-api.example.com',
      );
      
      expect(client, isNotNull);
      expect(client, isA<ProductionApiClient>());
      client.dispose();
    });

    test('should create with custom base URL', () {
      final client = ProductionApiClient(
        ref: testRef,
        baseUrl: 'https://custom-api.example.com',
      );
      
      expect(client, isNotNull);
      expect(client, isA<ProductionApiClient>());
      client.dispose();
    });

    test('should create with custom timeout', () {
      final client = ProductionApiClient(
        ref: testRef,
        baseUrl: 'https://test-api.example.com',
        timeout: const Duration(seconds: 30),
      );
      
      expect(client, isNotNull);
      client.dispose();
    });

    test('should dispose without errors', () {
      final client = ProductionApiClient(ref: testRef);
      expect(() => client.dispose(), returnsNormally);
    });

    test('should cancel requests without errors', () {
      final client = ProductionApiClient(ref: testRef);
      expect(() => client.cancelRequests(), returnsNormally);
      client.dispose();
    });
  });

  group('ProductionApiClient HTTP Tests', () {
    late ProductionApiClient client;
    late ProviderContainer container;
    late TestRef testRef;

    setUp(() {
      container = ProviderContainer();
      testRef = TestRef(container);
      client = ProductionApiClient(
        ref: testRef,
        baseUrl: 'https://jsonplaceholder.typicode.com',
      );
    });

    tearDown(() {
      client.dispose();
      container.dispose();
    });

    test('should make successful GET request', () async {
      final response = await client.get(
        '/posts/1',
        fromJson: (json) => json as Map<String, dynamic>,
      );

      expect(response, isA<Map<String, dynamic>>());
      expect(response['id'], 1);
      expect(response['title'], isNotNull);
    });

    test('should handle GET request with query parameters', () async {
      final response = await client.get(
        '/posts',
        queryParameters: {'userId': '1'},
        fromJson: (json) => json as List<dynamic>,
      );

      expect(response, isA<List<dynamic>>());
      expect(response.length, greaterThan(0));
    });

    test('should make successful POST request', () async {
      final response = await client.post(
        '/posts',
        data: {
          'title': 'Test Post',
          'body': 'Test body content',
          'userId': 1,
        },
        fromJson: (json) => json as Map<String, dynamic>,
      );

      expect(response, isA<Map<String, dynamic>>());
      expect(response['title'], 'Test Post');
    });

    test('should handle PUT request', () async {
      final response = await client.put(
        '/posts/1',
        data: {
          'id': 1,
          'title': 'Updated Post',
          'body': 'Updated body',
          'userId': 1,
        },
        fromJson: (json) => json as Map<String, dynamic>,
      );

      expect(response, isA<Map<String, dynamic>>());
      expect(response['title'], 'Updated Post');
    });

    test('should handle DELETE request', () async {
      final response = await client.delete(
        '/posts/1',
        fromJson: (json) => json as Map<String, dynamic>,
      );

      expect(response, isA<Map<String, dynamic>>());
    });

    test('should handle 404 errors gracefully', () async {
      try {
        await client.get(
          '/posts/99999',
          fromJson: (json) => json as Map<String, dynamic>,
        );
        fail('Expected ApiException');
      } catch (e) {
        expect(e, isA<ApiException>());
      }
    });

    test('should handle network timeout errors', () async {
      final timeoutClient = ProductionApiClient(
        ref: testRef,
        baseUrl: 'https://httpstat.us',
        timeout: const Duration(milliseconds: 100),
      );

      try {
        await timeoutClient.get(
          '/delay/5',
          fromJson: (json) => json as Map<String, dynamic>,
        );
        fail('Expected timeout error');
      } catch (e) {
        expect(e, isA<ApiException>());
      }
    });
  });
}
