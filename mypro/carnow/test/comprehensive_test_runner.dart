import 'package:flutter_test/flutter_test.dart';

// Import all test files
import 'features/cart/cart_service_test.dart' as cart_tests;
import 'features/orders/order_service_test.dart' as order_tests;
import 'features/payment/payment_service_test.dart' as payment_tests;
import 'core/services/websocket_service_test.dart' as websocket_tests;
import 'core/services/image_service_test.dart' as image_tests;

void main() {
  group('🧪 CarNow Comprehensive Test Suite', () {
    group('🛒 Cart Service Tests', () {
      cart_tests.main();
    });

    group('📦 Order Service Tests', () {
      order_tests.main();
    });

    group('💳 Payment Service Tests', () {
      payment_tests.main();
    });

    group('🔄 WebSocket Service Tests', () {
      websocket_tests.main();
    });

    group('🖼️ Image Service Tests', () {
      image_tests.main();
    });
  });
}
