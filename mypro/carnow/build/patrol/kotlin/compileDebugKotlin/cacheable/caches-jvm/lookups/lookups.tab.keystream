  Instrumentation android.app  UiAutomation android.app  
targetContext android.app.Instrumentation  uiAutomation android.app.Instrumentation  )FLAG_DONT_SUPPRESS_ACCESSIBILITY_SERVICES android.app.UiAutomation  rootInActiveWindow android.app.UiAutomation  windows android.app.UiAutomation  Context android.content  Intent android.content  LOCATION_SERVICE android.content.Context  contentResolver android.content.Context  getSystemService android.content.Context  packageManager android.content.Context  
startActivity android.content.Context  ACTION_VIEW android.content.Intent  CATEGORY_BROWSABLE android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  addCategory android.content.Intent  addFlags android.content.Intent  PackageManager android.content.pm  getLaunchIntentForPackage !android.content.pm.PackageManager  Rect android.graphics  x android.graphics.Point  y android.graphics.Point  bottom android.graphics.Rect  centerX android.graphics.Rect  centerY android.graphics.Rect  height android.graphics.Rect  left android.graphics.Rect  right android.graphics.Rect  top android.graphics.Rect  width android.graphics.Rect  Location android.location  LocationManager android.location  accuracy android.location.Location  altitude android.location.Location  elapsedRealtimeNanos android.location.Location  latitude android.location.Location  	longitude android.location.Location  time android.location.Location  GPS_PROVIDER  android.location.LocationManager  addTestProvider  android.location.LocationManager  isLocationEnabled  android.location.LocationManager  setTestProviderEnabled  android.location.LocationManager  setTestProviderLocation  android.location.LocationManager  ProviderProperties android.location.provider  
ACCURACY_FINE ,android.location.provider.ProviderProperties  POWER_USAGE_LOW ,android.location.provider.ProviderProperties  Uri android.net  parse android.net.Uri  Build 
android.os  ConditionVariable 
android.os  SystemClock 
android.os  BRAND android.os.Build  DEVICE android.os.Build  FINGERPRINT android.os.Build  HARDWARE android.os.Build  MANUFACTURER android.os.Build  MODEL android.os.Build  PRODUCT android.os.Build  CODENAME android.os.Build.VERSION  SDK_INT android.os.Build.VERSION  LOLLIPOP android.os.Build.VERSION_CODES  P android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  open android.os.ConditionVariable  sleep android.os.SystemClock  Settings android.provider  ACTION_AIRPLANE_MODE_SETTINGS android.provider.Settings  ACTION_LOCATION_SOURCE_SETTINGS android.provider.Settings  AIRPLANE_MODE_ON  android.provider.Settings.Global  
LOCATION_MODE  android.provider.Settings.Secure  LOCATION_MODE_OFF  android.provider.Settings.Secure  getInt  android.provider.Settings.Secure  getInt  android.provider.Settings.System  Log android.util  d android.util.Log  e android.util.Log  i android.util.Log  KEYCODE_VOLUME_DOWN android.view.KeyEvent  KEYCODE_VOLUME_UP android.view.KeyEvent  AccessibilityNodeInfo android.view.accessibility  
childCount 0android.view.accessibility.AccessibilityNodeInfo  	className 0android.view.accessibility.AccessibilityNodeInfo  contentDescription 0android.view.accessibility.AccessibilityNodeInfo  getBoundsInScreen 0android.view.accessibility.AccessibilityNodeInfo  getChild 0android.view.accessibility.AccessibilityNodeInfo  isCheckable 0android.view.accessibility.AccessibilityNodeInfo  	isChecked 0android.view.accessibility.AccessibilityNodeInfo  isClickable 0android.view.accessibility.AccessibilityNodeInfo  	isEnabled 0android.view.accessibility.AccessibilityNodeInfo  isFocusable 0android.view.accessibility.AccessibilityNodeInfo  	isFocused 0android.view.accessibility.AccessibilityNodeInfo  isLongClickable 0android.view.accessibility.AccessibilityNodeInfo  isScrollable 0android.view.accessibility.AccessibilityNodeInfo  
isSelected 0android.view.accessibility.AccessibilityNodeInfo  isVisibleToUser 0android.view.accessibility.AccessibilityNodeInfo  packageName 0android.view.accessibility.AccessibilityNodeInfo  text 0android.view.accessibility.AccessibilityNodeInfo  viewIdResourceName 0android.view.accessibility.AccessibilityNodeInfo  root 2android.view.accessibility.AccessibilityWindowInfo  EditText android.widget  InstrumentationRegistry androidx.test.platform.app  getInstrumentation 2androidx.test.platform.app.InstrumentationRegistry  By androidx.test.uiautomator  
BySelector androidx.test.uiautomator  Configurator androidx.test.uiautomator  UiDevice androidx.test.uiautomator  UiObject androidx.test.uiautomator  	UiObject2 androidx.test.uiautomator  UiObjectNotFoundException androidx.test.uiautomator  
UiSelector androidx.test.uiautomator  	checkable androidx.test.uiautomator.By  checked androidx.test.uiautomator.By  clazz androidx.test.uiautomator.By  	clickable androidx.test.uiautomator.By  copy androidx.test.uiautomator.By  desc androidx.test.uiautomator.By  descContains androidx.test.uiautomator.By  descStartsWith androidx.test.uiautomator.By  enabled androidx.test.uiautomator.By  	focusable androidx.test.uiautomator.By  focused androidx.test.uiautomator.By  
longClickable androidx.test.uiautomator.By  pkg androidx.test.uiautomator.By  res androidx.test.uiautomator.By  
scrollable androidx.test.uiautomator.By  selected androidx.test.uiautomator.By  text androidx.test.uiautomator.By  textContains androidx.test.uiautomator.By  textStartsWith androidx.test.uiautomator.By  	checkable $androidx.test.uiautomator.BySelector  checked $androidx.test.uiautomator.BySelector  clazz $androidx.test.uiautomator.BySelector  	clickable $androidx.test.uiautomator.BySelector  desc $androidx.test.uiautomator.BySelector  descContains $androidx.test.uiautomator.BySelector  descStartsWith $androidx.test.uiautomator.BySelector  enabled $androidx.test.uiautomator.BySelector  	focusable $androidx.test.uiautomator.BySelector  focused $androidx.test.uiautomator.BySelector  
longClickable $androidx.test.uiautomator.BySelector  pkg $androidx.test.uiautomator.BySelector  res $androidx.test.uiautomator.BySelector  
scrollable $androidx.test.uiautomator.BySelector  selected $androidx.test.uiautomator.BySelector  text $androidx.test.uiautomator.BySelector  textContains $androidx.test.uiautomator.BySelector  textStartsWith $androidx.test.uiautomator.BySelector  actionAcknowledgmentTimeout &androidx.test.uiautomator.Configurator  getInstance &androidx.test.uiautomator.Configurator  keyInjectionDelay &androidx.test.uiautomator.Configurator  scrollAcknowledgmentTimeout &androidx.test.uiautomator.Configurator  toolType &androidx.test.uiautomator.Configurator  uiAutomationFlags &androidx.test.uiautomator.Configurator  waitForIdleTimeout &androidx.test.uiautomator.Configurator  waitForSelectorTimeout &androidx.test.uiautomator.Configurator  click "androidx.test.uiautomator.UiDevice  
displayHeight "androidx.test.uiautomator.UiDevice  displayWidth "androidx.test.uiautomator.UiDevice  executeShellCommand "androidx.test.uiautomator.UiDevice  
findObject "androidx.test.uiautomator.UiDevice  findObjects "androidx.test.uiautomator.UiDevice  getInstance "androidx.test.uiautomator.UiDevice  openNotification "androidx.test.uiautomator.UiDevice  openQuickSettings "androidx.test.uiautomator.UiDevice  	pressBack "androidx.test.uiautomator.UiDevice  	pressHome "androidx.test.uiautomator.UiDevice  pressKeyCode "androidx.test.uiautomator.UiDevice  pressRecentApps "androidx.test.uiautomator.UiDevice  swipe "androidx.test.uiautomator.UiDevice  waitForIdle "androidx.test.uiautomator.UiDevice  bounds "androidx.test.uiautomator.UiObject  click "androidx.test.uiautomator.UiObject  getChild "androidx.test.uiautomator.UiObject  getClassName "androidx.test.uiautomator.UiObject  text "androidx.test.uiautomator.UiObject  
visibleBounds "androidx.test.uiautomator.UiObject  applicationPackage #androidx.test.uiautomator.UiObject2  
childCount #androidx.test.uiautomator.UiObject2  children #androidx.test.uiautomator.UiObject2  	className #androidx.test.uiautomator.UiObject2  contentDescription #androidx.test.uiautomator.UiObject2  
findObject #androidx.test.uiautomator.UiObject2  isCheckable #androidx.test.uiautomator.UiObject2  	isChecked #androidx.test.uiautomator.UiObject2  isClickable #androidx.test.uiautomator.UiObject2  	isEnabled #androidx.test.uiautomator.UiObject2  isFocusable #androidx.test.uiautomator.UiObject2  	isFocused #androidx.test.uiautomator.UiObject2  isLongClickable #androidx.test.uiautomator.UiObject2  isScrollable #androidx.test.uiautomator.UiObject2  
isSelected #androidx.test.uiautomator.UiObject2  resourceName #androidx.test.uiautomator.UiObject2  text #androidx.test.uiautomator.UiObject2  
visibleBounds #androidx.test.uiautomator.UiObject2  
visibleCenter #androidx.test.uiautomator.UiObject2  message 3androidx.test.uiautomator.UiObjectNotFoundException  	checkable $androidx.test.uiautomator.UiSelector  checked $androidx.test.uiautomator.UiSelector  	className $androidx.test.uiautomator.UiSelector  	clickable $androidx.test.uiautomator.UiSelector  description $androidx.test.uiautomator.UiSelector  descriptionContains $androidx.test.uiautomator.UiSelector  descriptionStartsWith $androidx.test.uiautomator.UiSelector  enabled $androidx.test.uiautomator.UiSelector  	focusable $androidx.test.uiautomator.UiSelector  focused $androidx.test.uiautomator.UiSelector  instance $androidx.test.uiautomator.UiSelector  
longClickable $androidx.test.uiautomator.UiSelector  packageName $androidx.test.uiautomator.UiSelector  
resourceId $androidx.test.uiautomator.UiSelector  
scrollable $androidx.test.uiautomator.UiSelector  selected $androidx.test.uiautomator.UiSelector  text $androidx.test.uiautomator.UiSelector  textContains $androidx.test.uiautomator.UiSelector  textStartsWith $androidx.test.uiautomator.UiSelector  Gson com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  	MediaType com.squareup.okhttp  OkHttpClient com.squareup.okhttp  Request com.squareup.okhttp  RequestBody com.squareup.okhttp  Response com.squareup.okhttp  ResponseBody com.squareup.okhttp  execute com.squareup.okhttp.Call  parse com.squareup.okhttp.MediaType  apply  com.squareup.okhttp.OkHttpClient  newCall  com.squareup.okhttp.OkHttpClient  setConnectTimeout  com.squareup.okhttp.OkHttpClient  setReadTimeout  com.squareup.okhttp.OkHttpClient  setWriteTimeout  com.squareup.okhttp.OkHttpClient  timeUnit  com.squareup.okhttp.OkHttpClient  timeout  com.squareup.okhttp.OkHttpClient  Builder com.squareup.okhttp.Request  build #com.squareup.okhttp.Request.Builder  post #com.squareup.okhttp.Request.Builder  url #com.squareup.okhttp.Request.Builder  create com.squareup.okhttp.RequestBody  body com.squareup.okhttp.Response  code com.squareup.okhttp.Response  string  com.squareup.okhttp.ResponseBody  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  Class 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  IllegalStateException 	java.lang  Runnable 	java.lang  Thread 	java.lang  name java.lang.Class  stackTraceToString java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  addShutdownHook java.lang.Runtime  
getRuntime java.lang.Runtime  currentTimeMillis java.lang.System  nanoTime java.lang.System  TimeUnit java.util.concurrent  HOURS java.util.concurrent.TimeUnit  Array kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  NotImplementedError kotlin  Nothing kotlin  Result kotlin  	Throwable kotlin  also kotlin  apply kotlin  arrayOf kotlin  
isInitialized kotlin  map kotlin  run kotlin  stackTraceToString kotlin  toString 
kotlin.Any  iterator kotlin.Array  map kotlin.Array  size kotlin.Array  not kotlin.Boolean  isEmpty kotlin.CharSequence  toString kotlin.CharSequence  toFloat 
kotlin.Double  rangeTo kotlin.Float  
roundToInt kotlin.Float  toInt kotlin.Float  invoke kotlin.Function1  	compareTo 
kotlin.Int  plus 
kotlin.Int  times 
kotlin.Int  toDouble 
kotlin.Int  toInt 
kotlin.Int  toLong 
kotlin.Int  	compareTo kotlin.Long  minus kotlin.Long  toInt kotlin.Long  message kotlin.NotImplementedError  bind 
kotlin.String  isEmpty 
kotlin.String  toIntOrNull 
kotlin.String  message kotlin.Throwable  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableSet kotlin.collections  contains kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mutableListOf kotlin.collections  mutableSetOf kotlin.collections  removeFirstOrNull kotlin.collections  toTypedArray kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  iterator kotlin.collections.List  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  get kotlin.collections.MutableList  iterator kotlin.collections.MutableList  size kotlin.collections.MutableList  add kotlin.collections.MutableSet  toTypedArray kotlin.collections.MutableSet  
startsWith 	kotlin.io  Throws 
kotlin.jvm  java 
kotlin.jvm  
roundToInt kotlin.math  	CharRange 
kotlin.ranges  ClosedFloatingPointRange 
kotlin.ranges  ClosedRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  contains 
kotlin.ranges  rangeTo 
kotlin.ranges  until 
kotlin.ranges  contains &kotlin.ranges.ClosedFloatingPointRange  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KMutableProperty0 kotlin.reflect  java kotlin.reflect.KClass  
isInitialized  kotlin.reflect.KMutableProperty0  Sequence kotlin.sequences  contains kotlin.sequences  map kotlin.sequences  contains kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  
startsWith kotlin.text  toIntOrNull kotlin.text  ContentType org.http4k.core  Filter org.http4k.core  Method org.http4k.core  Request org.http4k.core  Response org.http4k.core  Status org.http4k.core  Uri org.http4k.core  	Companion org.http4k.core.ContentType  
TEXT_PLAIN org.http4k.core.ContentType  
TEXT_PLAIN %org.http4k.core.ContentType.Companion  
bodyString org.http4k.core.HttpMessage  POST org.http4k.core.Method  
bodyString org.http4k.core.Request  method org.http4k.core.Request  uri org.http4k.core.Request  	Companion org.http4k.core.Response  body org.http4k.core.Response  invoke org.http4k.core.Response  invoke "org.http4k.core.Response.Companion  INTERNAL_SERVER_ERROR  org.http4k.core.Status.Companion  	NOT_FOUND  org.http4k.core.Status.Companion  NOT_IMPLEMENTED  org.http4k.core.Status.Companion  OK  org.http4k.core.Status.Companion  
ServerFilters org.http4k.filter  SetContentType org.http4k.filter.ServerFilters  invoke .org.http4k.filter.ServerFilters.SetContentType  
PathMethod org.http4k.routing  RoutingHttpHandler org.http4k.routing  bind org.http4k.routing  routes org.http4k.routing  to org.http4k.routing.PathMethod  asServer %org.http4k.routing.RoutingHttpHandler  
withFilter %org.http4k.routing.RoutingHttpHandler  Http4kServer org.http4k.server  KtorCIO org.http4k.server  asServer org.http4k.server  close org.http4k.server.Http4kServer  start org.http4k.server.Http4kServer  AccessibilityNodeInfo pl.leancode.patrol  AndroidNativeView pl.leancode.patrol  AndroidSelector pl.leancode.patrol  Array pl.leancode.patrol  	Automator pl.leancode.patrol  AutomatorServer pl.leancode.patrol  Boolean pl.leancode.patrol  Build pl.leancode.patrol  BuildConfig pl.leancode.patrol  By pl.leancode.patrol  
BySelector pl.leancode.patrol  Client pl.leancode.patrol  ConditionVariable pl.leancode.patrol  Configurator pl.leancode.patrol  ConfigureRequest pl.leancode.patrol  ContentType pl.leancode.patrol  Context pl.leancode.patrol  	Contracts pl.leancode.patrol  DarkModeRequest pl.leancode.patrol  DartGroupEntry pl.leancode.patrol  DartTestResults pl.leancode.patrol  Double pl.leancode.patrol  EditText pl.leancode.patrol  EnterTextRequest pl.leancode.patrol  	Exception pl.leancode.patrol  Float pl.leancode.patrol  
FlutterPlugin pl.leancode.patrol  GetNativeUITreeRequest pl.leancode.patrol  GetNativeUITreeRespone pl.leancode.patrol  GetNativeViewsRequest pl.leancode.patrol  GetNativeViewsResponse pl.leancode.patrol  GetNotificationsRequest pl.leancode.patrol  GetNotificationsResponse pl.leancode.patrol  GroupEntryType pl.leancode.patrol  HandlePermissionRequest pl.leancode.patrol  HandlePermissionRequestCode pl.leancode.patrol  Http4kServer pl.leancode.patrol  IllegalArgumentException pl.leancode.patrol  IllegalStateException pl.leancode.patrol  Instrumentation pl.leancode.patrol  InstrumentationRegistry pl.leancode.patrol  Int pl.leancode.patrol  Intent pl.leancode.patrol  KEYCODE_VOLUME_DOWN pl.leancode.patrol  KEYCODE_VOLUME_UP pl.leancode.patrol  KeyboardBehavior pl.leancode.patrol  KtorCIO pl.leancode.patrol  LOCATION_SERVICE pl.leancode.patrol  List pl.leancode.patrol  Location pl.leancode.patrol  LocationManager pl.leancode.patrol  Log pl.leancode.patrol  Logger pl.leancode.patrol  Long pl.leancode.patrol  Map pl.leancode.patrol  
MethodCall pl.leancode.patrol  MethodCallHandler pl.leancode.patrol  
MethodChannel pl.leancode.patrol  NativeAutomatorServer pl.leancode.patrol  
NativeView pl.leancode.patrol  NotImplementedError pl.leancode.patrol  Notification pl.leancode.patrol  OpenAppRequest pl.leancode.patrol  OpenQuickSettingsRequest pl.leancode.patrol  PatrolAppServiceClient pl.leancode.patrol  PatrolAppServiceClientException pl.leancode.patrol  PatrolException pl.leancode.patrol  PatrolPlugin pl.leancode.patrol  PatrolServer pl.leancode.patrol  PermissionDialogVisibleRequest pl.leancode.patrol  PermissionDialogVisibleResponse pl.leancode.patrol  ProviderProperties pl.leancode.patrol  Result pl.leancode.patrol  Runtime pl.leancode.patrol  Selector pl.leancode.patrol  
ServerFilters pl.leancode.patrol  SetLocationAccuracyRequest pl.leancode.patrol  *SetLocationAccuracyRequestLocationAccuracy pl.leancode.patrol  SetMockLocationRequest pl.leancode.patrol  Settings pl.leancode.patrol  String pl.leancode.patrol  SwipeRequest pl.leancode.patrol  System pl.leancode.patrol  SystemClock pl.leancode.patrol  TapOnNotificationRequest pl.leancode.patrol  
TapRequest pl.leancode.patrol  Thread pl.leancode.patrol  	Throwable pl.leancode.patrol  Throws pl.leancode.patrol  TimeUnit pl.leancode.patrol  UiAutomation pl.leancode.patrol  UiDevice pl.leancode.patrol  UiObject pl.leancode.patrol  	UiObject2 pl.leancode.patrol  UiObjectNotFoundException pl.leancode.patrol  
UiSelector pl.leancode.patrol  Uri pl.leancode.patrol  WaitUntilVisibleRequest pl.leancode.patrol  arrayOf pl.leancode.patrol  asServer pl.leancode.patrol  catcher pl.leancode.patrol  contains pl.leancode.patrol  d pl.leancode.patrol  e pl.leancode.patrol  fromUiAccessibilityNodeInfo pl.leancode.patrol  fromUiAccessibilityNodeInfoV2 pl.leancode.patrol  
fromUiObject2 pl.leancode.patrol  fromUiObject2V2 pl.leancode.patrol  getWindowRoots pl.leancode.patrol  getWindowRootsV2 pl.leancode.patrol  getWindowTrees pl.leancode.patrol  getWindowTreesV2 pl.leancode.patrol  i pl.leancode.patrol  isEmpty pl.leancode.patrol  
isInitialized pl.leancode.patrol  
isNotEmpty pl.leancode.patrol  java pl.leancode.patrol  listOf pl.leancode.patrol  
listTestsFlat pl.leancode.patrol  map pl.leancode.patrol  
mutableListOf pl.leancode.patrol  mutableSetOf pl.leancode.patrol  printer pl.leancode.patrol  rangeTo pl.leancode.patrol  removeFirstOrNull pl.leancode.patrol  
roundToInt pl.leancode.patrol  run pl.leancode.patrol  stackTraceToString pl.leancode.patrol  
startsWith pl.leancode.patrol  toBySelector pl.leancode.patrol  toIntOrNull pl.leancode.patrol  toTypedArray pl.leancode.patrol  toUiSelector pl.leancode.patrol  until pl.leancode.patrol  AndroidNativeView pl.leancode.patrol.Automator  AndroidSelector pl.leancode.patrol.Automator  	Automator pl.leancode.patrol.Automator  Boolean pl.leancode.patrol.Automator  Build pl.leancode.patrol.Automator  By pl.leancode.patrol.Automator  
BySelector pl.leancode.patrol.Automator  	Companion pl.leancode.patrol.Automator  Configurator pl.leancode.patrol.Automator  Context pl.leancode.patrol.Automator  Double pl.leancode.patrol.Automator  EditText pl.leancode.patrol.Automator  	Exception pl.leancode.patrol.Automator  Float pl.leancode.patrol.Automator  IllegalArgumentException pl.leancode.patrol.Automator  Instrumentation pl.leancode.patrol.Automator  InstrumentationRegistry pl.leancode.patrol.Automator  Int pl.leancode.patrol.Automator  Intent pl.leancode.patrol.Automator  KEYCODE_VOLUME_DOWN pl.leancode.patrol.Automator  KEYCODE_VOLUME_UP pl.leancode.patrol.Automator  KeyboardBehavior pl.leancode.patrol.Automator  LOCATION_SERVICE pl.leancode.patrol.Automator  List pl.leancode.patrol.Automator  Location pl.leancode.patrol.Automator  LocationManager pl.leancode.patrol.Automator  Logger pl.leancode.patrol.Automator  Long pl.leancode.patrol.Automator  
NativeView pl.leancode.patrol.Automator  Notification pl.leancode.patrol.Automator  PatrolException pl.leancode.patrol.Automator  ProviderProperties pl.leancode.patrol.Automator  Selector pl.leancode.patrol.Automator  Settings pl.leancode.patrol.Automator  String pl.leancode.patrol.Automator  System pl.leancode.patrol.Automator  SystemClock pl.leancode.patrol.Automator  UiAutomation pl.leancode.patrol.Automator  UiDevice pl.leancode.patrol.Automator  UiObject pl.leancode.patrol.Automator  	UiObject2 pl.leancode.patrol.Automator  UiObjectNotFoundException pl.leancode.patrol.Automator  
UiSelector pl.leancode.patrol.Automator  Uri pl.leancode.patrol.Automator  allowPermissionOnce pl.leancode.patrol.Automator  allowPermissionWhileUsingApp pl.leancode.patrol.Automator  arrayOf pl.leancode.patrol.Automator  closeNotifications pl.leancode.patrol.Automator  configurator pl.leancode.patrol.Automator  	configure pl.leancode.patrol.Automator  d pl.leancode.patrol.Automator  delay pl.leancode.patrol.Automator  denyPermission pl.leancode.patrol.Automator  disableAirplaneMode pl.leancode.patrol.Automator  disableBluetooth pl.leancode.patrol.Automator  disableCellular pl.leancode.patrol.Automator  disableDarkMode pl.leancode.patrol.Automator  disableLocation pl.leancode.patrol.Automator  disableWifi pl.leancode.patrol.Automator  	doubleTap pl.leancode.patrol.Automator  e pl.leancode.patrol.Automator  enableAirplaneMode pl.leancode.patrol.Automator  enableBluetooth pl.leancode.patrol.Automator  enableCellular pl.leancode.patrol.Automator  enableDarkMode pl.leancode.patrol.Automator  enableLocation pl.leancode.patrol.Automator  
enableWifi pl.leancode.patrol.Automator  	enterText pl.leancode.patrol.Automator  executeShellCommand pl.leancode.patrol.Automator  
fromUiObject2 pl.leancode.patrol.Automator  fromUiObject2V2 pl.leancode.patrol.Automator  getNativeUITrees pl.leancode.patrol.Automator  getNativeUITreesV2 pl.leancode.patrol.Automator  getNativeViews pl.leancode.patrol.Automator  getNativeViewsV2 pl.leancode.patrol.Automator  getNotifications pl.leancode.patrol.Automator  getWindowTrees pl.leancode.patrol.Automator  getWindowTreesV2 pl.leancode.patrol.Automator  i pl.leancode.patrol.Automator  
initialize pl.leancode.patrol.Automator  instance pl.leancode.patrol.Automator  instrumentation pl.leancode.patrol.Automator  isAirplaneModeOn pl.leancode.patrol.Automator  
isInitialized pl.leancode.patrol.Automator  isLocationEnabled pl.leancode.patrol.Automator  
isNotEmpty pl.leancode.patrol.Automator  isPermissionDialogVisible pl.leancode.patrol.Automator  java pl.leancode.patrol.Automator  listOf pl.leancode.patrol.Automator  map pl.leancode.patrol.Automator  
mutableListOf pl.leancode.patrol.Automator  openApp pl.leancode.patrol.Automator  openNotifications pl.leancode.patrol.Automator  openQuickSettings pl.leancode.patrol.Automator  openUrl pl.leancode.patrol.Automator  pickImageFromGallery pl.leancode.patrol.Automator  pickMultipleImagesFromGallery pl.leancode.patrol.Automator  	pressBack pl.leancode.patrol.Automator  pressDoubleRecentApps pl.leancode.patrol.Automator  	pressHome pl.leancode.patrol.Automator  pressRecentApps pl.leancode.patrol.Automator  pressVolumeDown pl.leancode.patrol.Automator  
pressVolumeUp pl.leancode.patrol.Automator  rangeTo pl.leancode.patrol.Automator  removeFirstOrNull pl.leancode.patrol.Automator  
roundToInt pl.leancode.patrol.Automator  selectCoarseLocation pl.leancode.patrol.Automator  selectFineLocation pl.leancode.patrol.Automator  setMockLocation pl.leancode.patrol.Automator  swipe pl.leancode.patrol.Automator  takeCameraPhoto pl.leancode.patrol.Automator  tap pl.leancode.patrol.Automator  tapAt pl.leancode.patrol.Automator  tapOnNotification pl.leancode.patrol.Automator  
targetContext pl.leancode.patrol.Automator  
timeoutMillis pl.leancode.patrol.Automator  toBySelector pl.leancode.patrol.Automator  toUiSelector pl.leancode.patrol.Automator  toggleAirplaneMode pl.leancode.patrol.Automator  toggleLocation pl.leancode.patrol.Automator  uiAutomation pl.leancode.patrol.Automator  uiDevice pl.leancode.patrol.Automator  waitForUiObjectByResourceId pl.leancode.patrol.Automator  waitForView pl.leancode.patrol.Automator  waitUntilVisible pl.leancode.patrol.Automator  AndroidSelector &pl.leancode.patrol.Automator.Companion  	Automator &pl.leancode.patrol.Automator.Companion  Build &pl.leancode.patrol.Automator.Companion  By &pl.leancode.patrol.Automator.Companion  Configurator &pl.leancode.patrol.Automator.Companion  Context &pl.leancode.patrol.Automator.Companion  EditText &pl.leancode.patrol.Automator.Companion  	Exception &pl.leancode.patrol.Automator.Companion  IllegalArgumentException &pl.leancode.patrol.Automator.Companion  InstrumentationRegistry &pl.leancode.patrol.Automator.Companion  Intent &pl.leancode.patrol.Automator.Companion  KEYCODE_VOLUME_DOWN &pl.leancode.patrol.Automator.Companion  KEYCODE_VOLUME_UP &pl.leancode.patrol.Automator.Companion  KeyboardBehavior &pl.leancode.patrol.Automator.Companion  LOCATION_SERVICE &pl.leancode.patrol.Automator.Companion  Location &pl.leancode.patrol.Automator.Companion  LocationManager &pl.leancode.patrol.Automator.Companion  Logger &pl.leancode.patrol.Automator.Companion  Notification &pl.leancode.patrol.Automator.Companion  PatrolException &pl.leancode.patrol.Automator.Companion  ProviderProperties &pl.leancode.patrol.Automator.Companion  Selector &pl.leancode.patrol.Automator.Companion  Settings &pl.leancode.patrol.Automator.Companion  System &pl.leancode.patrol.Automator.Companion  SystemClock &pl.leancode.patrol.Automator.Companion  UiAutomation &pl.leancode.patrol.Automator.Companion  UiDevice &pl.leancode.patrol.Automator.Companion  UiObjectNotFoundException &pl.leancode.patrol.Automator.Companion  
UiSelector &pl.leancode.patrol.Automator.Companion  Uri &pl.leancode.patrol.Automator.Companion  arrayOf &pl.leancode.patrol.Automator.Companion  d &pl.leancode.patrol.Automator.Companion  e &pl.leancode.patrol.Automator.Companion  
fromUiObject2 &pl.leancode.patrol.Automator.Companion  fromUiObject2V2 &pl.leancode.patrol.Automator.Companion  getWindowTrees &pl.leancode.patrol.Automator.Companion  getWindowTreesV2 &pl.leancode.patrol.Automator.Companion  i &pl.leancode.patrol.Automator.Companion  instance &pl.leancode.patrol.Automator.Companion  
isInitialized &pl.leancode.patrol.Automator.Companion  
isNotEmpty &pl.leancode.patrol.Automator.Companion  java &pl.leancode.patrol.Automator.Companion  listOf &pl.leancode.patrol.Automator.Companion  map &pl.leancode.patrol.Automator.Companion  
mutableListOf &pl.leancode.patrol.Automator.Companion  rangeTo &pl.leancode.patrol.Automator.Companion  removeFirstOrNull &pl.leancode.patrol.Automator.Companion  
roundToInt &pl.leancode.patrol.Automator.Companion  toBySelector &pl.leancode.patrol.Automator.Companion  toUiSelector &pl.leancode.patrol.Automator.Companion  Build "pl.leancode.patrol.AutomatorServer  	Contracts "pl.leancode.patrol.AutomatorServer  GetNativeUITreeRespone "pl.leancode.patrol.AutomatorServer  GetNativeViewsResponse "pl.leancode.patrol.AutomatorServer  GetNotificationsResponse "pl.leancode.patrol.AutomatorServer  HandlePermissionRequestCode "pl.leancode.patrol.AutomatorServer  PatrolException "pl.leancode.patrol.AutomatorServer  PatrolServer "pl.leancode.patrol.AutomatorServer  PermissionDialogVisibleResponse "pl.leancode.patrol.AutomatorServer  Selector "pl.leancode.patrol.AutomatorServer  *SetLocationAccuracyRequestLocationAccuracy "pl.leancode.patrol.AutomatorServer  
automation "pl.leancode.patrol.AutomatorServer  contains "pl.leancode.patrol.AutomatorServer  getOsVersion "pl.leancode.patrol.AutomatorServer  isVirtualDevice "pl.leancode.patrol.AutomatorServer  listOf "pl.leancode.patrol.AutomatorServer  router "pl.leancode.patrol.AutomatorServer  
startsWith "pl.leancode.patrol.AutomatorServer  toBySelector "pl.leancode.patrol.AutomatorServer  toUiSelector "pl.leancode.patrol.AutomatorServer  PATROL_APP_PORT pl.leancode.patrol.BuildConfig  PATROL_TEST_PORT pl.leancode.patrol.BuildConfig  DartGroupEntry pl.leancode.patrol.Contracts  GetOsVersionResponse pl.leancode.patrol.Contracts  IsVirtualDeviceResponse pl.leancode.patrol.Contracts  OpenUrlRequest pl.leancode.patrol.Contracts  PickImageFromGalleryRequest pl.leancode.patrol.Contracts  $PickMultipleImagesFromGalleryRequest pl.leancode.patrol.Contracts  RunDartTestResponse pl.leancode.patrol.Contracts  TakeCameraPhotoRequest pl.leancode.patrol.Contracts  TapAtRequest pl.leancode.patrol.Contracts  FlutterPluginBinding  pl.leancode.patrol.FlutterPlugin  Log pl.leancode.patrol.Logger  TAG pl.leancode.patrol.Logger  d pl.leancode.patrol.Logger  e pl.leancode.patrol.Logger  i pl.leancode.patrol.Logger  BuildConfig )pl.leancode.patrol.PatrolAppServiceClient  Client )pl.leancode.patrol.PatrolAppServiceClient  	Contracts )pl.leancode.patrol.PatrolAppServiceClient  Logger )pl.leancode.patrol.PatrolAppServiceClient  PatrolAppServiceClientException )pl.leancode.patrol.PatrolAppServiceClient  TimeUnit )pl.leancode.patrol.PatrolAppServiceClient  client )pl.leancode.patrol.PatrolAppServiceClient  defaultPort )pl.leancode.patrol.PatrolAppServiceClient  i )pl.leancode.patrol.PatrolAppServiceClient  port )pl.leancode.patrol.PatrolAppServiceClient  run )pl.leancode.patrol.PatrolAppServiceClient  timeUnit )pl.leancode.patrol.PatrolAppServiceClient  timeout )pl.leancode.patrol.PatrolAppServiceClient  toIntOrNull )pl.leancode.patrol.PatrolAppServiceClient  
MethodChannel pl.leancode.patrol.PatrolPlugin  channel pl.leancode.patrol.PatrolPlugin  	Automator pl.leancode.patrol.PatrolServer  AutomatorServer pl.leancode.patrol.PatrolServer  BuildConfig pl.leancode.patrol.PatrolServer  	Companion pl.leancode.patrol.PatrolServer  ConditionVariable pl.leancode.patrol.PatrolServer  ContentType pl.leancode.patrol.PatrolServer  Http4kServer pl.leancode.patrol.PatrolServer  Int pl.leancode.patrol.PatrolServer  KtorCIO pl.leancode.patrol.PatrolServer  Logger pl.leancode.patrol.PatrolServer  Runtime pl.leancode.patrol.PatrolServer  
ServerFilters pl.leancode.patrol.PatrolServer  Thread pl.leancode.patrol.PatrolServer  appReady pl.leancode.patrol.PatrolServer  asServer pl.leancode.patrol.PatrolServer  automatorServer pl.leancode.patrol.PatrolServer  catcher pl.leancode.patrol.PatrolServer  defaultPort pl.leancode.patrol.PatrolServer  i pl.leancode.patrol.PatrolServer  port pl.leancode.patrol.PatrolServer  printer pl.leancode.patrol.PatrolServer  run pl.leancode.patrol.PatrolServer  server pl.leancode.patrol.PatrolServer  toIntOrNull pl.leancode.patrol.PatrolServer  	Automator )pl.leancode.patrol.PatrolServer.Companion  AutomatorServer )pl.leancode.patrol.PatrolServer.Companion  BuildConfig )pl.leancode.patrol.PatrolServer.Companion  ConditionVariable )pl.leancode.patrol.PatrolServer.Companion  ContentType )pl.leancode.patrol.PatrolServer.Companion  KtorCIO )pl.leancode.patrol.PatrolServer.Companion  Logger )pl.leancode.patrol.PatrolServer.Companion  Runtime )pl.leancode.patrol.PatrolServer.Companion  
ServerFilters )pl.leancode.patrol.PatrolServer.Companion  Thread )pl.leancode.patrol.PatrolServer.Companion  appReady )pl.leancode.patrol.PatrolServer.Companion  asServer )pl.leancode.patrol.PatrolServer.Companion  catcher )pl.leancode.patrol.PatrolServer.Companion  i )pl.leancode.patrol.PatrolServer.Companion  printer )pl.leancode.patrol.PatrolServer.Companion  run )pl.leancode.patrol.PatrolServer.Companion  toIntOrNull )pl.leancode.patrol.PatrolServer.Companion  AndroidNativeView pl.leancode.patrol.contracts  AndroidSelector pl.leancode.patrol.contracts  Boolean pl.leancode.patrol.contracts  	Contracts pl.leancode.patrol.contracts  DartGroupEntry pl.leancode.patrol.contracts  Double pl.leancode.patrol.contracts  	Exception pl.leancode.patrol.contracts  GroupEntryType pl.leancode.patrol.contracts  Gson pl.leancode.patrol.contracts  HandlePermissionRequestCode pl.leancode.patrol.contracts  IOSElementType pl.leancode.patrol.contracts  
IOSNativeView pl.leancode.patrol.contracts  IOSSelector pl.leancode.patrol.contracts  Int pl.leancode.patrol.contracts  KeyboardBehavior pl.leancode.patrol.contracts  List pl.leancode.patrol.contracts  Long pl.leancode.patrol.contracts  	MediaType pl.leancode.patrol.contracts  NativeAutomatorServer pl.leancode.patrol.contracts  
NativeView pl.leancode.patrol.contracts  Notification pl.leancode.patrol.contracts  OK pl.leancode.patrol.contracts  OkHttpClient pl.leancode.patrol.contracts  POST pl.leancode.patrol.contracts  PatrolAppServiceClient pl.leancode.patrol.contracts  PatrolAppServiceClientException pl.leancode.patrol.contracts  Point2D pl.leancode.patrol.contracts  	Rectangle pl.leancode.patrol.contracts  Request pl.leancode.patrol.contracts  RequestBody pl.leancode.patrol.contracts  Response pl.leancode.patrol.contracts  RunDartTestResponseResult pl.leancode.patrol.contracts  Selector pl.leancode.patrol.contracts  *SetLocationAccuracyRequestLocationAccuracy pl.leancode.patrol.contracts  String pl.leancode.patrol.contracts  TimeUnit pl.leancode.patrol.contracts  also pl.leancode.patrol.contracts  apply pl.leancode.patrol.contracts  bind pl.leancode.patrol.contracts  java pl.leancode.patrol.contracts  routes pl.leancode.patrol.contracts  timeUnit pl.leancode.patrol.contracts  timeout pl.leancode.patrol.contracts  AndroidNativeView &pl.leancode.patrol.contracts.Contracts  AndroidSelector &pl.leancode.patrol.contracts.Contracts  Boolean &pl.leancode.patrol.contracts.Contracts  ConfigureRequest &pl.leancode.patrol.contracts.Contracts  DarkModeRequest &pl.leancode.patrol.contracts.Contracts  DartGroupEntry &pl.leancode.patrol.contracts.Contracts  Double &pl.leancode.patrol.contracts.Contracts  EnterTextRequest &pl.leancode.patrol.contracts.Contracts  GetNativeUITreeRequest &pl.leancode.patrol.contracts.Contracts  GetNativeUITreeRespone &pl.leancode.patrol.contracts.Contracts  GetNativeViewsRequest &pl.leancode.patrol.contracts.Contracts  GetNativeViewsResponse &pl.leancode.patrol.contracts.Contracts  GetNotificationsRequest &pl.leancode.patrol.contracts.Contracts  GetNotificationsResponse &pl.leancode.patrol.contracts.Contracts  GetOsVersionResponse &pl.leancode.patrol.contracts.Contracts  GroupEntryType &pl.leancode.patrol.contracts.Contracts  HandlePermissionRequest &pl.leancode.patrol.contracts.Contracts  HandlePermissionRequestCode &pl.leancode.patrol.contracts.Contracts  IOSElementType &pl.leancode.patrol.contracts.Contracts  
IOSNativeView &pl.leancode.patrol.contracts.Contracts  IOSSelector &pl.leancode.patrol.contracts.Contracts  IsVirtualDeviceResponse &pl.leancode.patrol.contracts.Contracts  KeyboardBehavior &pl.leancode.patrol.contracts.Contracts  List &pl.leancode.patrol.contracts.Contracts  ListDartTestsResponse &pl.leancode.patrol.contracts.Contracts  Long &pl.leancode.patrol.contracts.Contracts  
NativeView &pl.leancode.patrol.contracts.Contracts  Notification &pl.leancode.patrol.contracts.Contracts  OpenAppRequest &pl.leancode.patrol.contracts.Contracts  OpenQuickSettingsRequest &pl.leancode.patrol.contracts.Contracts  OpenUrlRequest &pl.leancode.patrol.contracts.Contracts  PermissionDialogVisibleRequest &pl.leancode.patrol.contracts.Contracts  PermissionDialogVisibleResponse &pl.leancode.patrol.contracts.Contracts  PickImageFromGalleryRequest &pl.leancode.patrol.contracts.Contracts  $PickMultipleImagesFromGalleryRequest &pl.leancode.patrol.contracts.Contracts  Point2D &pl.leancode.patrol.contracts.Contracts  	Rectangle &pl.leancode.patrol.contracts.Contracts  RunDartTestRequest &pl.leancode.patrol.contracts.Contracts  RunDartTestResponse &pl.leancode.patrol.contracts.Contracts  RunDartTestResponseResult &pl.leancode.patrol.contracts.Contracts  Selector &pl.leancode.patrol.contracts.Contracts  SetLocationAccuracyRequest &pl.leancode.patrol.contracts.Contracts  *SetLocationAccuracyRequestLocationAccuracy &pl.leancode.patrol.contracts.Contracts  SetMockLocationRequest &pl.leancode.patrol.contracts.Contracts  String &pl.leancode.patrol.contracts.Contracts  SwipeRequest &pl.leancode.patrol.contracts.Contracts  TakeCameraPhotoRequest &pl.leancode.patrol.contracts.Contracts  TapAtRequest &pl.leancode.patrol.contracts.Contracts  TapOnNotificationRequest &pl.leancode.patrol.contracts.Contracts  
TapRequest &pl.leancode.patrol.contracts.Contracts  WaitUntilVisibleRequest &pl.leancode.patrol.contracts.Contracts  applicationPackage 8pl.leancode.patrol.contracts.Contracts.AndroidNativeView  	className 8pl.leancode.patrol.contracts.Contracts.AndroidNativeView  contentDescription 8pl.leancode.patrol.contracts.Contracts.AndroidNativeView  resourceName 8pl.leancode.patrol.contracts.Contracts.AndroidNativeView  text 8pl.leancode.patrol.contracts.Contracts.AndroidNativeView  By 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  IllegalArgumentException 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  PatrolException 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  
UiSelector 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  applicationPackage 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  	className 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  contentDescription 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  contentDescriptionContains 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  contentDescriptionStartsWith 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  copy 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasApplicationPackage 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasClassName 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasContentDescription 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasContentDescriptionContains 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasContentDescriptionStartsWith 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasInstance 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasIsCheckable 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasIsChecked 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasIsClickable 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasIsEnabled 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasIsFocusable 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasIsFocused 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasIsLongClickable 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasIsScrollable 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  
hasIsSelected 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasResourceName 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasText 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasTextContains 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  hasTextStartsWith 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  instance 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  isCheckable 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  	isChecked 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  isClickable 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  	isEnabled 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  isFocusable 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  	isFocused 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  isLongClickable 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  isScrollable 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  
isSelected 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  resourceName 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  text 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  textContains 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  textStartsWith 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  toBySelector 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  toUiSelector 6pl.leancode.patrol.contracts.Contracts.AndroidSelector  findTimeoutMillis 7pl.leancode.patrol.contracts.Contracts.ConfigureRequest  GroupEntryType 5pl.leancode.patrol.contracts.Contracts.DartGroupEntry  IllegalStateException 5pl.leancode.patrol.contracts.Contracts.DartGroupEntry  copy 5pl.leancode.patrol.contracts.Contracts.DartGroupEntry  entries 5pl.leancode.patrol.contracts.Contracts.DartGroupEntry  isEmpty 5pl.leancode.patrol.contracts.Contracts.DartGroupEntry  
listTestsFlat 5pl.leancode.patrol.contracts.Contracts.DartGroupEntry  
mutableListOf 5pl.leancode.patrol.contracts.Contracts.DartGroupEntry  name 5pl.leancode.patrol.contracts.Contracts.DartGroupEntry  type 5pl.leancode.patrol.contracts.Contracts.DartGroupEntry  androidSelector 7pl.leancode.patrol.contracts.Contracts.EnterTextRequest  data 7pl.leancode.patrol.contracts.Contracts.EnterTextRequest  dx 7pl.leancode.patrol.contracts.Contracts.EnterTextRequest  dy 7pl.leancode.patrol.contracts.Contracts.EnterTextRequest  index 7pl.leancode.patrol.contracts.Contracts.EnterTextRequest  iosSelector 7pl.leancode.patrol.contracts.Contracts.EnterTextRequest  keyboardBehavior 7pl.leancode.patrol.contracts.Contracts.EnterTextRequest  selector 7pl.leancode.patrol.contracts.Contracts.EnterTextRequest  
timeoutMillis 7pl.leancode.patrol.contracts.Contracts.EnterTextRequest  iosInstalledApps =pl.leancode.patrol.contracts.Contracts.GetNativeUITreeRequest  useNativeViewHierarchy =pl.leancode.patrol.contracts.Contracts.GetNativeUITreeRequest  androidSelector <pl.leancode.patrol.contracts.Contracts.GetNativeViewsRequest  iosSelector <pl.leancode.patrol.contracts.Contracts.GetNativeViewsRequest  selector <pl.leancode.patrol.contracts.Contracts.GetNativeViewsRequest  	osVersion ;pl.leancode.patrol.contracts.Contracts.GetOsVersionResponse  group 5pl.leancode.patrol.contracts.Contracts.GroupEntryType  test 5pl.leancode.patrol.contracts.Contracts.GroupEntryType  code >pl.leancode.patrol.contracts.Contracts.HandlePermissionRequest  denied Bpl.leancode.patrol.contracts.Contracts.HandlePermissionRequestCode  onlyThisTime Bpl.leancode.patrol.contracts.Contracts.HandlePermissionRequestCode  
whileUsing Bpl.leancode.patrol.contracts.Contracts.HandlePermissionRequestCode  placeholderValue 4pl.leancode.patrol.contracts.Contracts.IOSNativeView  value 4pl.leancode.patrol.contracts.Contracts.IOSNativeView  elementType 2pl.leancode.patrol.contracts.Contracts.IOSSelector  hasFocus 2pl.leancode.patrol.contracts.Contracts.IOSSelector  
identifier 2pl.leancode.patrol.contracts.Contracts.IOSSelector  instance 2pl.leancode.patrol.contracts.Contracts.IOSSelector  	isEnabled 2pl.leancode.patrol.contracts.Contracts.IOSSelector  
isSelected 2pl.leancode.patrol.contracts.Contracts.IOSSelector  label 2pl.leancode.patrol.contracts.Contracts.IOSSelector  
labelContains 2pl.leancode.patrol.contracts.Contracts.IOSSelector  labelStartsWith 2pl.leancode.patrol.contracts.Contracts.IOSSelector  placeholderValue 2pl.leancode.patrol.contracts.Contracts.IOSSelector  placeholderValueContains 2pl.leancode.patrol.contracts.Contracts.IOSSelector  placeholderValueStartsWith 2pl.leancode.patrol.contracts.Contracts.IOSSelector  title 2pl.leancode.patrol.contracts.Contracts.IOSSelector  
titleContains 2pl.leancode.patrol.contracts.Contracts.IOSSelector  titleStartsWith 2pl.leancode.patrol.contracts.Contracts.IOSSelector  value 2pl.leancode.patrol.contracts.Contracts.IOSSelector  isVirtualDevice >pl.leancode.patrol.contracts.Contracts.IsVirtualDeviceResponse  showAndDismiss 7pl.leancode.patrol.contracts.Contracts.KeyboardBehavior  group <pl.leancode.patrol.contracts.Contracts.ListDartTestsResponse  applicationPackage 1pl.leancode.patrol.contracts.Contracts.NativeView  
childCount 1pl.leancode.patrol.contracts.Contracts.NativeView  	className 1pl.leancode.patrol.contracts.Contracts.NativeView  contentDescription 1pl.leancode.patrol.contracts.Contracts.NativeView  resourceName 1pl.leancode.patrol.contracts.Contracts.NativeView  text 1pl.leancode.patrol.contracts.Contracts.NativeView  appName 3pl.leancode.patrol.contracts.Contracts.Notification  raw 3pl.leancode.patrol.contracts.Contracts.Notification  appId 5pl.leancode.patrol.contracts.Contracts.OpenAppRequest  url 5pl.leancode.patrol.contracts.Contracts.OpenUrlRequest  
timeoutMillis Epl.leancode.patrol.contracts.Contracts.PermissionDialogVisibleRequest  androidImageSelector Bpl.leancode.patrol.contracts.Contracts.PickImageFromGalleryRequest  
imageIndex Bpl.leancode.patrol.contracts.Contracts.PickImageFromGalleryRequest  
imageSelector Bpl.leancode.patrol.contracts.Contracts.PickImageFromGalleryRequest  iosImageSelector Bpl.leancode.patrol.contracts.Contracts.PickImageFromGalleryRequest  	isNative2 Bpl.leancode.patrol.contracts.Contracts.PickImageFromGalleryRequest  
timeoutMillis Bpl.leancode.patrol.contracts.Contracts.PickImageFromGalleryRequest  androidImageSelector Kpl.leancode.patrol.contracts.Contracts.PickMultipleImagesFromGalleryRequest  imageIndexes Kpl.leancode.patrol.contracts.Contracts.PickMultipleImagesFromGalleryRequest  
imageSelector Kpl.leancode.patrol.contracts.Contracts.PickMultipleImagesFromGalleryRequest  iosImageSelector Kpl.leancode.patrol.contracts.Contracts.PickMultipleImagesFromGalleryRequest  	isNative2 Kpl.leancode.patrol.contracts.Contracts.PickMultipleImagesFromGalleryRequest  
timeoutMillis Kpl.leancode.patrol.contracts.Contracts.PickMultipleImagesFromGalleryRequest  details :pl.leancode.patrol.contracts.Contracts.RunDartTestResponse  By /pl.leancode.patrol.contracts.Contracts.Selector  IllegalArgumentException /pl.leancode.patrol.contracts.Contracts.Selector  PatrolException /pl.leancode.patrol.contracts.Contracts.Selector  
UiSelector /pl.leancode.patrol.contracts.Contracts.Selector  	className /pl.leancode.patrol.contracts.Contracts.Selector  contentDescription /pl.leancode.patrol.contracts.Contracts.Selector  contentDescriptionContains /pl.leancode.patrol.contracts.Contracts.Selector  contentDescriptionStartsWith /pl.leancode.patrol.contracts.Contracts.Selector  copy /pl.leancode.patrol.contracts.Contracts.Selector  enabled /pl.leancode.patrol.contracts.Contracts.Selector  focused /pl.leancode.patrol.contracts.Contracts.Selector  hasClassName /pl.leancode.patrol.contracts.Contracts.Selector  hasContentDescription /pl.leancode.patrol.contracts.Contracts.Selector  hasContentDescriptionContains /pl.leancode.patrol.contracts.Contracts.Selector  hasContentDescriptionStartsWith /pl.leancode.patrol.contracts.Contracts.Selector  
hasEnabled /pl.leancode.patrol.contracts.Contracts.Selector  
hasFocused /pl.leancode.patrol.contracts.Contracts.Selector  hasInstance /pl.leancode.patrol.contracts.Contracts.Selector  hasPkg /pl.leancode.patrol.contracts.Contracts.Selector  
hasResourceId /pl.leancode.patrol.contracts.Contracts.Selector  hasText /pl.leancode.patrol.contracts.Contracts.Selector  hasTextContains /pl.leancode.patrol.contracts.Contracts.Selector  hasTextStartsWith /pl.leancode.patrol.contracts.Contracts.Selector  instance /pl.leancode.patrol.contracts.Contracts.Selector  isEmpty /pl.leancode.patrol.contracts.Contracts.Selector  pkg /pl.leancode.patrol.contracts.Contracts.Selector  
resourceId /pl.leancode.patrol.contracts.Contracts.Selector  text /pl.leancode.patrol.contracts.Contracts.Selector  textContains /pl.leancode.patrol.contracts.Contracts.Selector  textStartsWith /pl.leancode.patrol.contracts.Contracts.Selector  toBySelector /pl.leancode.patrol.contracts.Contracts.Selector  toUiSelector /pl.leancode.patrol.contracts.Contracts.Selector  locationAccuracy Apl.leancode.patrol.contracts.Contracts.SetLocationAccuracyRequest  coarse Qpl.leancode.patrol.contracts.Contracts.SetLocationAccuracyRequestLocationAccuracy  fine Qpl.leancode.patrol.contracts.Contracts.SetLocationAccuracyRequestLocationAccuracy  latitude =pl.leancode.patrol.contracts.Contracts.SetMockLocationRequest  	longitude =pl.leancode.patrol.contracts.Contracts.SetMockLocationRequest  packageName =pl.leancode.patrol.contracts.Contracts.SetMockLocationRequest  endX 3pl.leancode.patrol.contracts.Contracts.SwipeRequest  endY 3pl.leancode.patrol.contracts.Contracts.SwipeRequest  startX 3pl.leancode.patrol.contracts.Contracts.SwipeRequest  startY 3pl.leancode.patrol.contracts.Contracts.SwipeRequest  steps 3pl.leancode.patrol.contracts.Contracts.SwipeRequest  androidDoneButtonSelector =pl.leancode.patrol.contracts.Contracts.TakeCameraPhotoRequest  androidShutterButtonSelector =pl.leancode.patrol.contracts.Contracts.TakeCameraPhotoRequest  doneButtonSelector =pl.leancode.patrol.contracts.Contracts.TakeCameraPhotoRequest  iosDoneButtonSelector =pl.leancode.patrol.contracts.Contracts.TakeCameraPhotoRequest  iosShutterButtonSelector =pl.leancode.patrol.contracts.Contracts.TakeCameraPhotoRequest  	isNative2 =pl.leancode.patrol.contracts.Contracts.TakeCameraPhotoRequest  shutterButtonSelector =pl.leancode.patrol.contracts.Contracts.TakeCameraPhotoRequest  
timeoutMillis =pl.leancode.patrol.contracts.Contracts.TakeCameraPhotoRequest  x 3pl.leancode.patrol.contracts.Contracts.TapAtRequest  y 3pl.leancode.patrol.contracts.Contracts.TapAtRequest  androidSelector ?pl.leancode.patrol.contracts.Contracts.TapOnNotificationRequest  index ?pl.leancode.patrol.contracts.Contracts.TapOnNotificationRequest  iosSelector ?pl.leancode.patrol.contracts.Contracts.TapOnNotificationRequest  selector ?pl.leancode.patrol.contracts.Contracts.TapOnNotificationRequest  
timeoutMillis ?pl.leancode.patrol.contracts.Contracts.TapOnNotificationRequest  androidSelector 1pl.leancode.patrol.contracts.Contracts.TapRequest  delayBetweenTapsMillis 1pl.leancode.patrol.contracts.Contracts.TapRequest  iosSelector 1pl.leancode.patrol.contracts.Contracts.TapRequest  selector 1pl.leancode.patrol.contracts.Contracts.TapRequest  
timeoutMillis 1pl.leancode.patrol.contracts.Contracts.TapRequest  androidSelector >pl.leancode.patrol.contracts.Contracts.WaitUntilVisibleRequest  iosSelector >pl.leancode.patrol.contracts.Contracts.WaitUntilVisibleRequest  selector >pl.leancode.patrol.contracts.Contracts.WaitUntilVisibleRequest  
timeoutMillis >pl.leancode.patrol.contracts.Contracts.WaitUntilVisibleRequest  	Contracts 2pl.leancode.patrol.contracts.NativeAutomatorServer  Gson 2pl.leancode.patrol.contracts.NativeAutomatorServer  OK 2pl.leancode.patrol.contracts.NativeAutomatorServer  POST 2pl.leancode.patrol.contracts.NativeAutomatorServer  Response 2pl.leancode.patrol.contracts.NativeAutomatorServer  bind 2pl.leancode.patrol.contracts.NativeAutomatorServer  closeHeadsUpNotification 2pl.leancode.patrol.contracts.NativeAutomatorServer  closeNotifications 2pl.leancode.patrol.contracts.NativeAutomatorServer  	configure 2pl.leancode.patrol.contracts.NativeAutomatorServer  debug 2pl.leancode.patrol.contracts.NativeAutomatorServer  disableAirplaneMode 2pl.leancode.patrol.contracts.NativeAutomatorServer  disableBluetooth 2pl.leancode.patrol.contracts.NativeAutomatorServer  disableCellular 2pl.leancode.patrol.contracts.NativeAutomatorServer  disableDarkMode 2pl.leancode.patrol.contracts.NativeAutomatorServer  disableLocation 2pl.leancode.patrol.contracts.NativeAutomatorServer  disableWiFi 2pl.leancode.patrol.contracts.NativeAutomatorServer  doublePressRecentApps 2pl.leancode.patrol.contracts.NativeAutomatorServer  	doubleTap 2pl.leancode.patrol.contracts.NativeAutomatorServer  enableAirplaneMode 2pl.leancode.patrol.contracts.NativeAutomatorServer  enableBluetooth 2pl.leancode.patrol.contracts.NativeAutomatorServer  enableCellular 2pl.leancode.patrol.contracts.NativeAutomatorServer  enableDarkMode 2pl.leancode.patrol.contracts.NativeAutomatorServer  enableLocation 2pl.leancode.patrol.contracts.NativeAutomatorServer  
enableWiFi 2pl.leancode.patrol.contracts.NativeAutomatorServer  	enterText 2pl.leancode.patrol.contracts.NativeAutomatorServer  getNativeUITree 2pl.leancode.patrol.contracts.NativeAutomatorServer  getNativeViews 2pl.leancode.patrol.contracts.NativeAutomatorServer  getNotifications 2pl.leancode.patrol.contracts.NativeAutomatorServer  getOsVersion 2pl.leancode.patrol.contracts.NativeAutomatorServer  handlePermissionDialog 2pl.leancode.patrol.contracts.NativeAutomatorServer  
initialize 2pl.leancode.patrol.contracts.NativeAutomatorServer  isPermissionDialogVisible 2pl.leancode.patrol.contracts.NativeAutomatorServer  isVirtualDevice 2pl.leancode.patrol.contracts.NativeAutomatorServer  java 2pl.leancode.patrol.contracts.NativeAutomatorServer  json 2pl.leancode.patrol.contracts.NativeAutomatorServer  markPatrolAppServiceReady 2pl.leancode.patrol.contracts.NativeAutomatorServer  openApp 2pl.leancode.patrol.contracts.NativeAutomatorServer  openNotifications 2pl.leancode.patrol.contracts.NativeAutomatorServer  openQuickSettings 2pl.leancode.patrol.contracts.NativeAutomatorServer  openUrl 2pl.leancode.patrol.contracts.NativeAutomatorServer  pickImageFromGallery 2pl.leancode.patrol.contracts.NativeAutomatorServer  pickMultipleImagesFromGallery 2pl.leancode.patrol.contracts.NativeAutomatorServer  	pressBack 2pl.leancode.patrol.contracts.NativeAutomatorServer  	pressHome 2pl.leancode.patrol.contracts.NativeAutomatorServer  pressRecentApps 2pl.leancode.patrol.contracts.NativeAutomatorServer  pressVolumeDown 2pl.leancode.patrol.contracts.NativeAutomatorServer  
pressVolumeUp 2pl.leancode.patrol.contracts.NativeAutomatorServer  router 2pl.leancode.patrol.contracts.NativeAutomatorServer  routes 2pl.leancode.patrol.contracts.NativeAutomatorServer  setLocationAccuracy 2pl.leancode.patrol.contracts.NativeAutomatorServer  setMockLocation 2pl.leancode.patrol.contracts.NativeAutomatorServer  swipe 2pl.leancode.patrol.contracts.NativeAutomatorServer  takeCameraPhoto 2pl.leancode.patrol.contracts.NativeAutomatorServer  tap 2pl.leancode.patrol.contracts.NativeAutomatorServer  tapAt 2pl.leancode.patrol.contracts.NativeAutomatorServer  tapOnNotification 2pl.leancode.patrol.contracts.NativeAutomatorServer  waitUntilVisible 2pl.leancode.patrol.contracts.NativeAutomatorServer  	Contracts 3pl.leancode.patrol.contracts.PatrolAppServiceClient  Gson 3pl.leancode.patrol.contracts.PatrolAppServiceClient  	MediaType 3pl.leancode.patrol.contracts.PatrolAppServiceClient  OkHttpClient 3pl.leancode.patrol.contracts.PatrolAppServiceClient  PatrolAppServiceClientException 3pl.leancode.patrol.contracts.PatrolAppServiceClient  Request 3pl.leancode.patrol.contracts.PatrolAppServiceClient  RequestBody 3pl.leancode.patrol.contracts.PatrolAppServiceClient  also 3pl.leancode.patrol.contracts.PatrolAppServiceClient  apply 3pl.leancode.patrol.contracts.PatrolAppServiceClient  java 3pl.leancode.patrol.contracts.PatrolAppServiceClient  json 3pl.leancode.patrol.contracts.PatrolAppServiceClient  
jsonMediaType 3pl.leancode.patrol.contracts.PatrolAppServiceClient  
listDartTests 3pl.leancode.patrol.contracts.PatrolAppServiceClient  performRequest 3pl.leancode.patrol.contracts.PatrolAppServiceClient  runDartTest 3pl.leancode.patrol.contracts.PatrolAppServiceClient  	serverUrl 3pl.leancode.patrol.contracts.PatrolAppServiceClient  timeUnit 3pl.leancode.patrol.contracts.PatrolAppServiceClient  timeout 3pl.leancode.patrol.contracts.PatrolAppServiceClient                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            