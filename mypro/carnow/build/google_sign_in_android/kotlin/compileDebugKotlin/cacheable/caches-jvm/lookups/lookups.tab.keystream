  Log android.util  getStackTraceString android.util.Log  BasicMessageChannel io.flutter.plugin.common  BinaryMessenger io.flutter.plugin.common  MessageCodec io.flutter.plugin.common  StandardMessageCodec io.flutter.plugin.common  Message<PERSON><PERSON><PERSON> ,io.flutter.plugin.common.BasicMessageChannel  Reply ,io.flutter.plugin.common.BasicMessageChannel  setMessageHandler ,io.flutter.plugin.common.BasicMessageChannel  <SAM-CONSTRUCTOR> ;io.flutter.plugin.common.BasicMessageChannel.MessageHandler  reply 2io.flutter.plugin.common.BasicMessageChannel.Reply  Any -io.flutter.plugin.common.StandardMessageCodec  AuthorizeFailure -io.flutter.plugin.common.StandardMessageCodec  AuthorizeFailureType -io.flutter.plugin.common.StandardMessageCodec  GetCredentialFailure -io.flutter.plugin.common.StandardMessageCodec  GetCredentialFailureType -io.flutter.plugin.common.StandardMessageCodec  (GetCredentialRequestGoogleIdOptionParams -io.flutter.plugin.common.StandardMessageCodec  GetCredentialRequestParams -io.flutter.plugin.common.StandardMessageCodec  GetCredentialSuccess -io.flutter.plugin.common.StandardMessageCodec  List -io.flutter.plugin.common.StandardMessageCodec  Long -io.flutter.plugin.common.StandardMessageCodec  PlatformAuthorizationRequest -io.flutter.plugin.common.StandardMessageCodec  PlatformAuthorizationResult -io.flutter.plugin.common.StandardMessageCodec  PlatformGoogleIdTokenCredential -io.flutter.plugin.common.StandardMessageCodec  fromList -io.flutter.plugin.common.StandardMessageCodec  let -io.flutter.plugin.common.StandardMessageCodec  ofRaw -io.flutter.plugin.common.StandardMessageCodec  	readValue -io.flutter.plugin.common.StandardMessageCodec  readValueOfType -io.flutter.plugin.common.StandardMessageCodec  
writeValue -io.flutter.plugin.common.StandardMessageCodec  Any io.flutter.plugins.googlesignin  AuthorizeFailure io.flutter.plugins.googlesignin  AuthorizeFailureType io.flutter.plugins.googlesignin  AuthorizeResult io.flutter.plugins.googlesignin  BasicMessageChannel io.flutter.plugins.googlesignin  BinaryMessenger io.flutter.plugins.googlesignin  Boolean io.flutter.plugins.googlesignin  Byte io.flutter.plugins.googlesignin  ByteArrayOutputStream io.flutter.plugins.googlesignin  
ByteBuffer io.flutter.plugins.googlesignin  FlutterError io.flutter.plugins.googlesignin  GetCredentialFailure io.flutter.plugins.googlesignin  GetCredentialFailureType io.flutter.plugins.googlesignin  (GetCredentialRequestGoogleIdOptionParams io.flutter.plugins.googlesignin  GetCredentialRequestParams io.flutter.plugins.googlesignin  GetCredentialResult io.flutter.plugins.googlesignin  GetCredentialSuccess io.flutter.plugins.googlesignin  GoogleSignInApi io.flutter.plugins.googlesignin  Int io.flutter.plugins.googlesignin  JvmOverloads io.flutter.plugins.googlesignin  List io.flutter.plugins.googlesignin  Log io.flutter.plugins.googlesignin  Long io.flutter.plugins.googlesignin  MessageCodec io.flutter.plugins.googlesignin  MessagesPigeonCodec io.flutter.plugins.googlesignin  PlatformAuthorizationRequest io.flutter.plugins.googlesignin  PlatformAuthorizationResult io.flutter.plugins.googlesignin  PlatformGoogleIdTokenCredential io.flutter.plugins.googlesignin  Result io.flutter.plugins.googlesignin  StandardMessageCodec io.flutter.plugins.googlesignin  String io.flutter.plugins.googlesignin  Suppress io.flutter.plugins.googlesignin  	Throwable io.flutter.plugins.googlesignin  Unit io.flutter.plugins.googlesignin  completeWithAuthorizationResult io.flutter.plugins.googlesignin  completeWithAuthorizeFailure io.flutter.plugins.googlesignin  %completeWithClearCredentialStateError io.flutter.plugins.googlesignin  'completeWithClearCredentialStateSuccess io.flutter.plugins.googlesignin   completeWithGetCredentialFailure io.flutter.plugins.googlesignin  "completeWithGetGetCredentialResult io.flutter.plugins.googlesignin  failure io.flutter.plugins.googlesignin  firstOrNull io.flutter.plugins.googlesignin  fromList io.flutter.plugins.googlesignin  getValue io.flutter.plugins.googlesignin  
isNotEmpty io.flutter.plugins.googlesignin  	javaClass io.flutter.plugins.googlesignin  lazy io.flutter.plugins.googlesignin  let io.flutter.plugins.googlesignin  listOf io.flutter.plugins.googlesignin  ofRaw io.flutter.plugins.googlesignin  provideDelegate io.flutter.plugins.googlesignin  run io.flutter.plugins.googlesignin  success io.flutter.plugins.googlesignin  values io.flutter.plugins.googlesignin  	wrapError io.flutter.plugins.googlesignin  
wrapResult io.flutter.plugins.googlesignin  Any 0io.flutter.plugins.googlesignin.AuthorizeFailure  AuthorizeFailure 0io.flutter.plugins.googlesignin.AuthorizeFailure  AuthorizeFailureType 0io.flutter.plugins.googlesignin.AuthorizeFailure  	Companion 0io.flutter.plugins.googlesignin.AuthorizeFailure  List 0io.flutter.plugins.googlesignin.AuthorizeFailure  String 0io.flutter.plugins.googlesignin.AuthorizeFailure  details 0io.flutter.plugins.googlesignin.AuthorizeFailure  fromList 0io.flutter.plugins.googlesignin.AuthorizeFailure  listOf 0io.flutter.plugins.googlesignin.AuthorizeFailure  message 0io.flutter.plugins.googlesignin.AuthorizeFailure  toList 0io.flutter.plugins.googlesignin.AuthorizeFailure  type 0io.flutter.plugins.googlesignin.AuthorizeFailure  AuthorizeFailure :io.flutter.plugins.googlesignin.AuthorizeFailure.Companion  fromList :io.flutter.plugins.googlesignin.AuthorizeFailure.Companion  listOf :io.flutter.plugins.googlesignin.AuthorizeFailure.Companion  AuthorizeFailureType 4io.flutter.plugins.googlesignin.AuthorizeFailureType  	Companion 4io.flutter.plugins.googlesignin.AuthorizeFailureType  Int 4io.flutter.plugins.googlesignin.AuthorizeFailureType  firstOrNull 4io.flutter.plugins.googlesignin.AuthorizeFailureType  ofRaw 4io.flutter.plugins.googlesignin.AuthorizeFailureType  raw 4io.flutter.plugins.googlesignin.AuthorizeFailureType  values 4io.flutter.plugins.googlesignin.AuthorizeFailureType  firstOrNull >io.flutter.plugins.googlesignin.AuthorizeFailureType.Companion  ofRaw >io.flutter.plugins.googlesignin.AuthorizeFailureType.Companion  values >io.flutter.plugins.googlesignin.AuthorizeFailureType.Companion  code ,io.flutter.plugins.googlesignin.FlutterError  details ,io.flutter.plugins.googlesignin.FlutterError  message ,io.flutter.plugins.googlesignin.FlutterError  Any 4io.flutter.plugins.googlesignin.GetCredentialFailure  	Companion 4io.flutter.plugins.googlesignin.GetCredentialFailure  GetCredentialFailure 4io.flutter.plugins.googlesignin.GetCredentialFailure  GetCredentialFailureType 4io.flutter.plugins.googlesignin.GetCredentialFailure  List 4io.flutter.plugins.googlesignin.GetCredentialFailure  String 4io.flutter.plugins.googlesignin.GetCredentialFailure  details 4io.flutter.plugins.googlesignin.GetCredentialFailure  fromList 4io.flutter.plugins.googlesignin.GetCredentialFailure  listOf 4io.flutter.plugins.googlesignin.GetCredentialFailure  message 4io.flutter.plugins.googlesignin.GetCredentialFailure  toList 4io.flutter.plugins.googlesignin.GetCredentialFailure  type 4io.flutter.plugins.googlesignin.GetCredentialFailure  GetCredentialFailure >io.flutter.plugins.googlesignin.GetCredentialFailure.Companion  fromList >io.flutter.plugins.googlesignin.GetCredentialFailure.Companion  listOf >io.flutter.plugins.googlesignin.GetCredentialFailure.Companion  	Companion 8io.flutter.plugins.googlesignin.GetCredentialFailureType  GetCredentialFailureType 8io.flutter.plugins.googlesignin.GetCredentialFailureType  Int 8io.flutter.plugins.googlesignin.GetCredentialFailureType  firstOrNull 8io.flutter.plugins.googlesignin.GetCredentialFailureType  ofRaw 8io.flutter.plugins.googlesignin.GetCredentialFailureType  raw 8io.flutter.plugins.googlesignin.GetCredentialFailureType  values 8io.flutter.plugins.googlesignin.GetCredentialFailureType  firstOrNull Bio.flutter.plugins.googlesignin.GetCredentialFailureType.Companion  ofRaw Bio.flutter.plugins.googlesignin.GetCredentialFailureType.Companion  values Bio.flutter.plugins.googlesignin.GetCredentialFailureType.Companion  Any Hio.flutter.plugins.googlesignin.GetCredentialRequestGoogleIdOptionParams  Boolean Hio.flutter.plugins.googlesignin.GetCredentialRequestGoogleIdOptionParams  	Companion Hio.flutter.plugins.googlesignin.GetCredentialRequestGoogleIdOptionParams  (GetCredentialRequestGoogleIdOptionParams Hio.flutter.plugins.googlesignin.GetCredentialRequestGoogleIdOptionParams  List Hio.flutter.plugins.googlesignin.GetCredentialRequestGoogleIdOptionParams  autoSelectEnabled Hio.flutter.plugins.googlesignin.GetCredentialRequestGoogleIdOptionParams  filterToAuthorized Hio.flutter.plugins.googlesignin.GetCredentialRequestGoogleIdOptionParams  fromList Hio.flutter.plugins.googlesignin.GetCredentialRequestGoogleIdOptionParams  listOf Hio.flutter.plugins.googlesignin.GetCredentialRequestGoogleIdOptionParams  toList Hio.flutter.plugins.googlesignin.GetCredentialRequestGoogleIdOptionParams  (GetCredentialRequestGoogleIdOptionParams Rio.flutter.plugins.googlesignin.GetCredentialRequestGoogleIdOptionParams.Companion  fromList Rio.flutter.plugins.googlesignin.GetCredentialRequestGoogleIdOptionParams.Companion  listOf Rio.flutter.plugins.googlesignin.GetCredentialRequestGoogleIdOptionParams.Companion  Any :io.flutter.plugins.googlesignin.GetCredentialRequestParams  Boolean :io.flutter.plugins.googlesignin.GetCredentialRequestParams  	Companion :io.flutter.plugins.googlesignin.GetCredentialRequestParams  (GetCredentialRequestGoogleIdOptionParams :io.flutter.plugins.googlesignin.GetCredentialRequestParams  GetCredentialRequestParams :io.flutter.plugins.googlesignin.GetCredentialRequestParams  List :io.flutter.plugins.googlesignin.GetCredentialRequestParams  String :io.flutter.plugins.googlesignin.GetCredentialRequestParams  fromList :io.flutter.plugins.googlesignin.GetCredentialRequestParams  googleIdOptionParams :io.flutter.plugins.googlesignin.GetCredentialRequestParams  listOf :io.flutter.plugins.googlesignin.GetCredentialRequestParams  nonce :io.flutter.plugins.googlesignin.GetCredentialRequestParams  serverClientId :io.flutter.plugins.googlesignin.GetCredentialRequestParams  toList :io.flutter.plugins.googlesignin.GetCredentialRequestParams  
useButtonFlow :io.flutter.plugins.googlesignin.GetCredentialRequestParams  GetCredentialRequestParams Dio.flutter.plugins.googlesignin.GetCredentialRequestParams.Companion  fromList Dio.flutter.plugins.googlesignin.GetCredentialRequestParams.Companion  listOf Dio.flutter.plugins.googlesignin.GetCredentialRequestParams.Companion  Any 4io.flutter.plugins.googlesignin.GetCredentialSuccess  	Companion 4io.flutter.plugins.googlesignin.GetCredentialSuccess  GetCredentialSuccess 4io.flutter.plugins.googlesignin.GetCredentialSuccess  List 4io.flutter.plugins.googlesignin.GetCredentialSuccess  PlatformGoogleIdTokenCredential 4io.flutter.plugins.googlesignin.GetCredentialSuccess  
credential 4io.flutter.plugins.googlesignin.GetCredentialSuccess  fromList 4io.flutter.plugins.googlesignin.GetCredentialSuccess  listOf 4io.flutter.plugins.googlesignin.GetCredentialSuccess  toList 4io.flutter.plugins.googlesignin.GetCredentialSuccess  GetCredentialSuccess >io.flutter.plugins.googlesignin.GetCredentialSuccess.Companion  fromList >io.flutter.plugins.googlesignin.GetCredentialSuccess.Companion  listOf >io.flutter.plugins.googlesignin.GetCredentialSuccess.Companion  Any /io.flutter.plugins.googlesignin.GoogleSignInApi  AuthorizeResult /io.flutter.plugins.googlesignin.GoogleSignInApi  BasicMessageChannel /io.flutter.plugins.googlesignin.GoogleSignInApi  BinaryMessenger /io.flutter.plugins.googlesignin.GoogleSignInApi  Boolean /io.flutter.plugins.googlesignin.GoogleSignInApi  	Companion /io.flutter.plugins.googlesignin.GoogleSignInApi  GetCredentialRequestParams /io.flutter.plugins.googlesignin.GoogleSignInApi  GetCredentialResult /io.flutter.plugins.googlesignin.GoogleSignInApi  GoogleSignInApi /io.flutter.plugins.googlesignin.GoogleSignInApi  JvmOverloads /io.flutter.plugins.googlesignin.GoogleSignInApi  List /io.flutter.plugins.googlesignin.GoogleSignInApi  MessageCodec /io.flutter.plugins.googlesignin.GoogleSignInApi  MessagesPigeonCodec /io.flutter.plugins.googlesignin.GoogleSignInApi  PlatformAuthorizationRequest /io.flutter.plugins.googlesignin.GoogleSignInApi  Result /io.flutter.plugins.googlesignin.GoogleSignInApi  String /io.flutter.plugins.googlesignin.GoogleSignInApi  	Throwable /io.flutter.plugins.googlesignin.GoogleSignInApi  Unit /io.flutter.plugins.googlesignin.GoogleSignInApi  	authorize /io.flutter.plugins.googlesignin.GoogleSignInApi  clearCredentialState /io.flutter.plugins.googlesignin.GoogleSignInApi  codec /io.flutter.plugins.googlesignin.GoogleSignInApi  
getCredential /io.flutter.plugins.googlesignin.GoogleSignInApi  #getGoogleServicesJsonServerClientId /io.flutter.plugins.googlesignin.GoogleSignInApi  getValue /io.flutter.plugins.googlesignin.GoogleSignInApi  
isNotEmpty /io.flutter.plugins.googlesignin.GoogleSignInApi  lazy /io.flutter.plugins.googlesignin.GoogleSignInApi  listOf /io.flutter.plugins.googlesignin.GoogleSignInApi  provideDelegate /io.flutter.plugins.googlesignin.GoogleSignInApi  run /io.flutter.plugins.googlesignin.GoogleSignInApi  	wrapError /io.flutter.plugins.googlesignin.GoogleSignInApi  
wrapResult /io.flutter.plugins.googlesignin.GoogleSignInApi  BasicMessageChannel 9io.flutter.plugins.googlesignin.GoogleSignInApi.Companion  MessagesPigeonCodec 9io.flutter.plugins.googlesignin.GoogleSignInApi.Companion  codec 9io.flutter.plugins.googlesignin.GoogleSignInApi.Companion  getValue 9io.flutter.plugins.googlesignin.GoogleSignInApi.Companion  
isNotEmpty 9io.flutter.plugins.googlesignin.GoogleSignInApi.Companion  lazy 9io.flutter.plugins.googlesignin.GoogleSignInApi.Companion  listOf 9io.flutter.plugins.googlesignin.GoogleSignInApi.Companion  provideDelegate 9io.flutter.plugins.googlesignin.GoogleSignInApi.Companion  run 9io.flutter.plugins.googlesignin.GoogleSignInApi.Companion  	wrapError 9io.flutter.plugins.googlesignin.GoogleSignInApi.Companion  
wrapResult 9io.flutter.plugins.googlesignin.GoogleSignInApi.Companion  AuthorizeFailure 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  AuthorizeFailureType 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  GetCredentialFailure 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  GetCredentialFailureType 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  (GetCredentialRequestGoogleIdOptionParams 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  GetCredentialRequestParams 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  GetCredentialSuccess 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  PlatformAuthorizationRequest 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  PlatformAuthorizationResult 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  PlatformGoogleIdTokenCredential 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  fromList 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  let 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  ofRaw 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  	readValue 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  
writeValue 3io.flutter.plugins.googlesignin.MessagesPigeonCodec  Any <io.flutter.plugins.googlesignin.PlatformAuthorizationRequest  	Companion <io.flutter.plugins.googlesignin.PlatformAuthorizationRequest  List <io.flutter.plugins.googlesignin.PlatformAuthorizationRequest  PlatformAuthorizationRequest <io.flutter.plugins.googlesignin.PlatformAuthorizationRequest  String <io.flutter.plugins.googlesignin.PlatformAuthorizationRequest  accountEmail <io.flutter.plugins.googlesignin.PlatformAuthorizationRequest  fromList <io.flutter.plugins.googlesignin.PlatformAuthorizationRequest  hostedDomain <io.flutter.plugins.googlesignin.PlatformAuthorizationRequest  listOf <io.flutter.plugins.googlesignin.PlatformAuthorizationRequest  scopes <io.flutter.plugins.googlesignin.PlatformAuthorizationRequest  #serverClientIdForForcedRefreshToken <io.flutter.plugins.googlesignin.PlatformAuthorizationRequest  toList <io.flutter.plugins.googlesignin.PlatformAuthorizationRequest  PlatformAuthorizationRequest Fio.flutter.plugins.googlesignin.PlatformAuthorizationRequest.Companion  fromList Fio.flutter.plugins.googlesignin.PlatformAuthorizationRequest.Companion  listOf Fio.flutter.plugins.googlesignin.PlatformAuthorizationRequest.Companion  Any ;io.flutter.plugins.googlesignin.PlatformAuthorizationResult  	Companion ;io.flutter.plugins.googlesignin.PlatformAuthorizationResult  List ;io.flutter.plugins.googlesignin.PlatformAuthorizationResult  PlatformAuthorizationResult ;io.flutter.plugins.googlesignin.PlatformAuthorizationResult  String ;io.flutter.plugins.googlesignin.PlatformAuthorizationResult  accessToken ;io.flutter.plugins.googlesignin.PlatformAuthorizationResult  fromList ;io.flutter.plugins.googlesignin.PlatformAuthorizationResult  
grantedScopes ;io.flutter.plugins.googlesignin.PlatformAuthorizationResult  listOf ;io.flutter.plugins.googlesignin.PlatformAuthorizationResult  serverAuthCode ;io.flutter.plugins.googlesignin.PlatformAuthorizationResult  toList ;io.flutter.plugins.googlesignin.PlatformAuthorizationResult  PlatformAuthorizationResult Eio.flutter.plugins.googlesignin.PlatformAuthorizationResult.Companion  fromList Eio.flutter.plugins.googlesignin.PlatformAuthorizationResult.Companion  listOf Eio.flutter.plugins.googlesignin.PlatformAuthorizationResult.Companion  Any ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  	Companion ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  List ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  PlatformGoogleIdTokenCredential ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  String ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  displayName ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  
familyName ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  fromList ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  	givenName ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  id ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  idToken ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  listOf ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  profilePictureUri ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  toList ?io.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential  PlatformGoogleIdTokenCredential Iio.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential.Companion  fromList Iio.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential.Companion  listOf Iio.flutter.plugins.googlesignin.PlatformGoogleIdTokenCredential.Companion  ByteArrayOutputStream java.io  write java.io.ByteArrayOutputStream  write java.io.OutputStream  Class 	java.lang  
simpleName java.lang.Class  
ByteBuffer java.nio  Array kotlin  Enum kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Lazy kotlin  Nothing kotlin  Result kotlin  Suppress kotlin  	Throwable kotlin  getValue kotlin  lazy kotlin  let kotlin  run kotlin  toString 
kotlin.Any  firstOrNull kotlin.Array  AuthorizeFailureType kotlin.Enum  	Companion kotlin.Enum  GetCredentialFailureType kotlin.Enum  Int kotlin.Enum  firstOrNull kotlin.Enum  values kotlin.Enum  firstOrNull kotlin.Enum.Companion  values kotlin.Enum.Companion  invoke kotlin.Function1  toByte 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  let kotlin.Long  toInt kotlin.Long  	Companion 
kotlin.Result  exceptionOrNull 
kotlin.Result  failure 
kotlin.Result  	getOrNull 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  
isNotEmpty 
kotlin.String  plus 
kotlin.String  cause kotlin.Throwable  	javaClass kotlin.Throwable  message kotlin.Throwable  toString kotlin.Throwable  List kotlin.collections  firstOrNull kotlin.collections  getValue kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  get kotlin.collections.List  let kotlin.collections.List  JvmOverloads 
kotlin.jvm  	javaClass 
kotlin.jvm  firstOrNull 
kotlin.ranges  KClass kotlin.reflect  
KProperty1 kotlin.reflect  firstOrNull kotlin.sequences  firstOrNull kotlin.text  
isNotEmpty kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             