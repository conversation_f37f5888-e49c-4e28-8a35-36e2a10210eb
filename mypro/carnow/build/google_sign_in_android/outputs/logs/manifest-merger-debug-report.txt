-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/android/src/main/AndroidManifest.xml:1:1-5:12
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/android/src/main/AndroidManifest.xml:1:1-5:12
	package
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/android/src/main/AndroidManifest.xml:2:3-44
		INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/android/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/android/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/android/src/main/AndroidManifest.xml:4:3-64
	android:name
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/android/src/main/AndroidManifest.xml:4:20-62
uses-sdk
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/android/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/android/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/android/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/android/src/main/AndroidManifest.xml
