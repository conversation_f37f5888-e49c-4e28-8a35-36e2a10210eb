/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \
  -H/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=21 \
  -DANDROID_PLATFORM=android-21 \
  -DANDROID_ABI=arm64-v8a \
  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \
  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018 \
  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018 \
  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018/build/cmake/android.toolchain.cmake \
  -DCMA<PERSON>_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \
  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/mypro/carnow/build/app/intermediates/cxx/Debug/4n681a1d/obj/arm64-v8a \
  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/mypro/carnow/build/app/intermediates/cxx/Debug/4n681a1d/obj/arm64-v8a \
  -DCMAKE_BUILD_TYPE=Debug \
  -B/Users/<USER>/mypro/carnow/build/.cxx/Debug/4n681a1d/arm64-v8a \
  -GNinja \
  -Wno-dev \
  --no-warn-unused-cli
