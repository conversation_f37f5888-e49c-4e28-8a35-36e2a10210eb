{"logs": [{"outputFile": "com.example.carnowx.app-mergeDebugResources-70:/values-v18/values-v18.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/res/values-v18/values.xml", "from": {"startLines": "4,12", "startColumns": "0,0", "startOffsets": "180,596", "endLines": "11,19", "endColumns": "8,8", "endOffsets": "595,1009"}, "to": {"startLines": "3,11", "startColumns": "4,4", "startOffsets": "104,524", "endLines": "10,18", "endColumns": "8,8", "endOffsets": "519,937"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/5c5bb3e97d4de44ccc93687f8b8f5ead/transformed/appcompat-1.6.1/res/values-v18/values-v18.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "48", "endOffsets": "99"}}]}]}