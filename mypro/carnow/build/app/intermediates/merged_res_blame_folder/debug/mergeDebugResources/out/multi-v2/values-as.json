{"logs": [{"outputFile": "com.example.carnowx.app-mergeDebugResources-70:/values-as/values-as.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/2317eb6b3b283acb5034b744433cbaad/transformed/browser-1.8.0/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,269,377", "endColumns": "107,105,107,105", "endOffsets": "158,264,372,478"}, "to": {"startLines": "66,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6731,7168,7274,7382", "endColumns": "107,105,107,105", "endOffsets": "6834,7269,7377,7483"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/5c5bb3e97d4de44ccc93687f8b8f5ead/transformed/appcompat-1.6.1/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,430,529,636,727,832,952,1029,1104,1195,1288,1383,1477,1577,1670,1765,1859,1950,2041,2127,2240,2348,2451,2560,2676,2796,2963,12914", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "425,524,631,722,827,947,1024,1099,1190,1283,1378,1472,1572,1665,1760,1854,1945,2036,2122,2235,2343,2446,2555,2671,2791,2958,3060,12992"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/426e58cc6082e9ed0c5c00632bde216e/transformed/jetified-credentials-1.5.0/res/values-as/values-as.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,111", "endOffsets": "161,273"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3065,3176", "endColumns": "110,111", "endOffsets": "3171,3283"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/bdecb973927d9c715ff6d17068a8ebbb/transformed/material-1.7.0/res/values-as/values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,349,451,574,653,718,807,872,931,1017,1081,1144,1214,1278,1332,1437,1495,1557,1611,1683,1800,1887,1970,2110,2187,2268,2359,2413,2464,2530,2600,2677,2764,2835,2912,2981,3050,3141,3213,3302,3391,3465,3537,3623,3673,3739,3819,3903,3965,4029,4092,4192,4289,4381,4480,4538,4593", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,76,101,122,78,64,88,64,58,85,63,62,69,63,53,104,57,61,53,71,116,86,82,139,76,80,90,53,50,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,57,54,80", "endOffsets": "267,344,446,569,648,713,802,867,926,1012,1076,1139,1209,1273,1327,1432,1490,1552,1606,1678,1795,1882,1965,2105,2182,2263,2354,2408,2459,2525,2595,2672,2759,2830,2907,2976,3045,3136,3208,3297,3386,3460,3532,3618,3668,3734,3814,3898,3960,4024,4087,4187,4284,4376,4475,4533,4588,4669"}, "to": {"startLines": "2,35,43,44,45,68,70,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3288,4115,4217,4340,6930,7079,8821,8886,8945,9031,9095,9158,9228,9292,9346,9451,9509,9571,9625,9697,9814,9901,9984,10124,10201,10282,10373,10427,10478,10544,10614,10691,10778,10849,10926,10995,11064,11155,11227,11316,11405,11479,11551,11637,11687,11753,11833,11917,11979,12043,12106,12206,12303,12395,12494,12552,12690", "endLines": "5,35,43,44,45,68,70,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135", "endColumns": "12,76,101,122,78,64,88,64,58,85,63,62,69,63,53,104,57,61,53,71,116,86,82,139,76,80,90,53,50,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,57,54,80", "endOffsets": "317,3360,4212,4335,4414,6990,7163,8881,8940,9026,9090,9153,9223,9287,9341,9446,9504,9566,9620,9692,9809,9896,9979,10119,10196,10277,10368,10422,10473,10539,10609,10686,10773,10844,10921,10990,11059,11150,11222,11311,11400,11474,11546,11632,11682,11748,11828,11912,11974,12038,12101,12201,12298,12390,12489,12547,12602,12766"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,259,383,504,633,766,892,1064,1170,1310,1452", "endColumns": "112,90,123,120,128,132,125,171,105,139,141,139", "endOffsets": "163,254,378,499,628,761,887,1059,1165,1305,1447,1587"}, "to": {"startLines": "64,67,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6541,6839,7488,7612,7733,7862,7995,8121,8293,8399,8539,8681", "endColumns": "112,90,123,120,128,132,125,171,105,139,141,139", "endOffsets": "6649,6925,7607,7728,7857,7990,8116,8288,8394,8534,8676,8816"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1265825b88f5d413d4b023436cf71467/transformed/preference-1.2.1/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,182,266,349,492,661,752", "endColumns": "76,83,82,142,168,90,79", "endOffsets": "177,261,344,487,656,747,827"}, "to": {"startLines": "65,69,134,136,139,140,141", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6654,6995,12607,12771,13098,13267,13358", "endColumns": "76,83,82,142,168,90,79", "endOffsets": "6726,7074,12685,12909,13262,13353,13433"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "36,37,38,39,40,41,42,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3365,3466,3569,3677,3782,3886,3986,12997", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "3461,3564,3672,3777,3881,3981,4110,13093"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/res/values-as/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "121", "endOffsets": "316"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5390", "endColumns": "125", "endOffsets": "5511"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/res/values-as/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,446,565,671,797,915,1024,1132,1271,1376,1522,1643,1772,1921,1977,2039", "endColumns": "103,148,118,105,125,117,108,107,138,104,145,120,128,148,55,61,81", "endOffsets": "296,445,564,670,796,914,1023,1131,1270,1375,1521,1642,1771,1920,1976,2038,2120"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4419,4527,4680,4803,4913,5043,5165,5278,5516,5659,5768,5918,6043,6176,6329,6389,6455", "endColumns": "107,152,122,109,129,121,112,111,142,108,149,124,132,152,59,65,85", "endOffsets": "4522,4675,4798,4908,5038,5160,5273,5385,5654,5763,5913,6038,6171,6324,6384,6450,6536"}}]}]}