{"logs": [{"outputFile": "com.example.carnowx.app-mergeDebugResources-70:/values-sq/values-sq.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/426e58cc6082e9ed0c5c00632bde216e/transformed/jetified-credentials-1.5.0/res/values-sq/values-sq.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,122", "endOffsets": "165,288"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3013,3128", "endColumns": "114,122", "endOffsets": "3123,3246"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/res/values-sq/values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "36,37,38,39,40,41,42,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3337,3436,3538,3636,3733,3841,3952,13366", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3431,3533,3631,3728,3836,3947,4069,13462"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/5c5bb3e97d4de44ccc93687f8b8f5ead/transformed/appcompat-1.6.1/res/values-sq/values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "317,431,531,643,729,835,958,1040,1118,1209,1302,1397,1491,1592,1685,1780,1877,1968,2061,2142,2248,2352,2450,2556,2660,2762,2916,13284", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "426,526,638,724,830,953,1035,1113,1204,1297,1392,1486,1587,1680,1775,1872,1963,2056,2137,2243,2347,2445,2551,2655,2757,2911,3008,13361"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/res/values-sq/values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,261,386,525,668,802,937,1081,1177,1320,1468", "endColumns": "112,92,124,138,142,133,134,143,95,142,147,120", "endOffsets": "163,256,381,520,663,797,932,1076,1172,1315,1463,1584"}, "to": {"startLines": "64,67,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6725,7022,7685,7810,7949,8092,8226,8361,8505,8601,8744,8892", "endColumns": "112,92,124,138,142,133,134,143,95,142,147,120", "endOffsets": "6833,7110,7805,7944,8087,8221,8356,8500,8596,8739,8887,9008"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/res/values-sq/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,598,701,858,988,1110,1222,1388,1492,1663,1797,1955,2135,2196,2259", "endColumns": "102,168,132,102,156,129,121,111,165,103,170,133,157,179,60,62,77", "endOffsets": "295,464,597,700,857,987,1109,1221,1387,1491,1662,1796,1954,2134,2195,2258,2336"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4384,4491,4664,4801,4908,5069,5203,5329,5574,5744,5852,6027,6165,6327,6511,6576,6643", "endColumns": "106,172,136,106,160,133,125,115,169,107,174,137,161,183,64,66,81", "endOffsets": "4486,4659,4796,4903,5064,5198,5324,5440,5739,5847,6022,6160,6322,6506,6571,6638,6720"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/bdecb973927d9c715ff6d17068a8ebbb/transformed/material-1.7.0/res/values-sq/values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,267,353,454,580,663,728,828,898,957,1055,1119,1178,1250,1313,1367,1484,1541,1603,1657,1729,1864,1947,2025,2166,2250,2332,2422,2475,2534,2600,2671,2750,2838,2914,2992,3064,3137,3226,3298,3392,3491,3565,3637,3738,3788,3854,3944,4033,4095,4159,4222,4338,4446,4555,4664,4721,4784", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,85,100,125,82,64,99,69,58,97,63,58,71,62,53,116,56,61,53,71,134,82,77,140,83,81,89,52,58,65,70,78,87,75,77,71,72,88,71,93,98,73,71,100,49,65,89,88,61,63,62,115,107,108,108,56,62,82", "endOffsets": "262,348,449,575,658,723,823,893,952,1050,1114,1173,1245,1308,1362,1479,1536,1598,1652,1724,1859,1942,2020,2161,2245,2327,2417,2470,2529,2595,2666,2745,2833,2909,2987,3059,3132,3221,3293,3387,3486,3560,3632,3733,3783,3849,3939,4028,4090,4154,4217,4333,4441,4550,4659,4716,4779,4862"}, "to": {"startLines": "2,35,43,44,45,68,70,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3251,4074,4175,4301,7115,7272,9013,9083,9142,9240,9304,9363,9435,9498,9552,9669,9726,9788,9842,9914,10049,10132,10210,10351,10435,10517,10607,10660,10719,10785,10856,10935,11023,11099,11177,11249,11322,11411,11483,11577,11676,11750,11822,11923,11973,12039,12129,12218,12280,12344,12407,12523,12631,12740,12849,12906,13053", "endLines": "5,35,43,44,45,68,70,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135", "endColumns": "12,85,100,125,82,64,99,69,58,97,63,58,71,62,53,116,56,61,53,71,134,82,77,140,83,81,89,52,58,65,70,78,87,75,77,71,72,88,71,93,98,73,71,100,49,65,89,88,61,63,62,115,107,108,108,56,62,82", "endOffsets": "312,3332,4170,4296,4379,7175,7367,9078,9137,9235,9299,9358,9430,9493,9547,9664,9721,9783,9837,9909,10044,10127,10205,10346,10430,10512,10602,10655,10714,10780,10851,10930,11018,11094,11172,11244,11317,11406,11478,11572,11671,11745,11817,11918,11968,12034,12124,12213,12275,12339,12402,12518,12626,12735,12844,12901,12964,13131"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/2317eb6b3b283acb5034b744433cbaad/transformed/browser-1.8.0/res/values-sq/values-sq.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,271,382", "endColumns": "114,100,110,100", "endOffsets": "165,266,377,478"}, "to": {"startLines": "66,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6907,7372,7473,7584", "endColumns": "114,100,110,100", "endOffsets": "7017,7468,7579,7680"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/res/values-sq/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5445", "endColumns": "128", "endOffsets": "5569"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1265825b88f5d413d4b023436cf71467/transformed/preference-1.2.1/res/values-sq/values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,266,350,498,667,751", "endColumns": "68,91,83,147,168,83,78", "endOffsets": "169,261,345,493,662,746,825"}, "to": {"startLines": "65,69,134,136,139,140,141", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6838,7180,12969,13136,13467,13636,13720", "endColumns": "68,91,83,147,168,83,78", "endOffsets": "6902,7267,13048,13279,13631,13715,13794"}}]}]}