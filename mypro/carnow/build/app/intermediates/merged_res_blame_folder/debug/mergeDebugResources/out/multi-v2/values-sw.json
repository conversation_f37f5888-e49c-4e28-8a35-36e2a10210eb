{"logs": [{"outputFile": "com.example.carnowx.app-mergeDebugResources-70:/values-sw/values-sw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/2317eb6b3b283acb5034b744433cbaad/transformed/browser-1.8.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,270,387", "endColumns": "113,100,116,102", "endOffsets": "164,265,382,485"}, "to": {"startLines": "66,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6785,7255,7356,7473", "endColumns": "113,100,116,102", "endOffsets": "6894,7351,7468,7571"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "36,37,38,39,40,41,42,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3312,3406,3508,3605,3706,3813,3920,13239", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3401,3503,3600,3701,3808,3915,4030,13335"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/res/values-sw/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5361", "endColumns": "145", "endOffsets": "5502"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/res/values-sw/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,445,566,671,830,951,1066,1176,1337,1439,1589,1712,1858,2013,2077,2148", "endColumns": "99,151,120,104,158,120,114,109,160,101,149,122,145,154,63,70,91", "endOffsets": "292,444,565,670,829,950,1065,1175,1336,1438,1588,1711,1857,2012,2076,2147,2239"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4346,4450,4606,4731,4840,5003,5128,5247,5507,5672,5778,5932,6059,6209,6368,6436,6511", "endColumns": "103,155,124,108,162,124,118,113,164,105,153,126,149,158,67,74,95", "endOffsets": "4445,4601,4726,4835,4998,5123,5242,5356,5667,5773,5927,6054,6204,6363,6431,6506,6602"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,163,262,382,515,649,791,923,1066,1164,1291,1421", "endColumns": "107,98,119,132,133,141,131,142,97,126,129,124", "endOffsets": "158,257,377,510,644,786,918,1061,1159,1286,1416,1541"}, "to": {"startLines": "64,67,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6607,6899,7576,7696,7829,7963,8105,8237,8380,8478,8605,8735", "endColumns": "107,98,119,132,133,141,131,142,97,126,129,124", "endOffsets": "6710,6993,7691,7824,7958,8100,8232,8375,8473,8600,8730,8855"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/bdecb973927d9c715ff6d17068a8ebbb/transformed/material-1.7.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,332,431,560,643,711,803,876,939,1025,1088,1153,1221,1284,1338,1470,1527,1589,1643,1717,1855,1936,2016,2148,2233,2320,2408,2462,2515,2581,2653,2735,2825,2897,2972,3043,3116,3213,3287,3382,3479,3553,3638,3738,3791,3859,3947,4037,4099,4163,4226,4343,4453,4564,4676,4734,4791", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,72,98,128,82,67,91,72,62,85,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,87,53,52,65,71,81,89,71,74,70,72,96,73,94,96,73,84,99,52,67,87,89,61,63,62,116,109,110,111,57,56,80", "endOffsets": "254,327,426,555,638,706,798,871,934,1020,1083,1148,1216,1279,1333,1465,1522,1584,1638,1712,1850,1931,2011,2143,2228,2315,2403,2457,2510,2576,2648,2730,2820,2892,2967,3038,3111,3208,3282,3377,3474,3548,3633,3733,3786,3854,3942,4032,4094,4158,4221,4338,4448,4559,4671,4729,4786,4867"}, "to": {"startLines": "2,35,43,44,45,68,70,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3239,4035,4134,4263,6998,7163,8860,8933,8996,9082,9145,9210,9278,9341,9395,9527,9584,9646,9700,9774,9912,9993,10073,10205,10290,10377,10465,10519,10572,10638,10710,10792,10882,10954,11029,11100,11173,11270,11344,11439,11536,11610,11695,11795,11848,11916,12004,12094,12156,12220,12283,12400,12510,12621,12733,12791,12925", "endLines": "5,35,43,44,45,68,70,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135", "endColumns": "12,72,98,128,82,67,91,72,62,85,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,87,53,52,65,71,81,89,71,74,70,72,96,73,94,96,73,84,99,52,67,87,89,61,63,62,116,109,110,111,57,56,80", "endOffsets": "304,3307,4129,4258,4341,7061,7250,8928,8991,9077,9140,9205,9273,9336,9390,9522,9579,9641,9695,9769,9907,9988,10068,10200,10285,10372,10460,10514,10567,10633,10705,10787,10877,10949,11024,11095,11168,11265,11339,11434,11531,11605,11690,11790,11843,11911,11999,12089,12151,12215,12278,12395,12505,12616,12728,12786,12843,13001"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1265825b88f5d413d4b023436cf71467/transformed/preference-1.2.1/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,349,499,668,753", "endColumns": "69,96,76,149,168,84,82", "endOffsets": "170,267,344,494,663,748,831"}, "to": {"startLines": "65,69,134,136,139,140,141", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6715,7066,12848,13006,13340,13509,13594", "endColumns": "69,96,76,149,168,84,82", "endOffsets": "6780,7158,12920,13151,13504,13589,13672"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/426e58cc6082e9ed0c5c00632bde216e/transformed/jetified-credentials-1.5.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,121", "endOffsets": "162,284"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3005,3117", "endColumns": "111,121", "endOffsets": "3112,3234"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/5c5bb3e97d4de44ccc93687f8b8f5ead/transformed/appcompat-1.6.1/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,412,511,619,709,814,931,1014,1096,1187,1280,1375,1469,1569,1662,1757,1851,1942,2033,2115,2216,2324,2423,2530,2642,2746,2908,13156", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "407,506,614,704,809,926,1009,1091,1182,1275,1370,1464,1564,1657,1752,1846,1937,2028,2110,2211,2319,2418,2525,2637,2741,2903,3000,13234"}}]}]}