{"logs": [{"outputFile": "com.example.carnowx.app-mergeDebugResources-70:/values-bs/values-bs.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/2317eb6b3b283acb5034b744433cbaad/transformed/browser-1.8.0/res/values-bs/values-bs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,259,373", "endColumns": "103,99,113,99", "endOffsets": "154,254,368,468"}, "to": {"startLines": "67,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6818,7260,7360,7474", "endColumns": "103,99,113,99", "endOffsets": "6917,7355,7469,7569"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/bdecb973927d9c715ff6d17068a8ebbb/transformed/material-1.7.0/res/values-bs/values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,327,409,505,631,712,778,870,947,1010,1118,1184,1240,1311,1371,1425,1544,1601,1663,1717,1792,1916,2004,2087,2232,2317,2403,2491,2545,2599,2665,2739,2817,2904,2976,3053,3126,3196,3289,3361,3453,3549,3623,3699,3795,3848,3915,4002,4089,4151,4215,4278,4386,4488,4589,4694,4752,4810", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "12,81,95,125,80,65,91,76,62,107,65,55,70,59,53,118,56,61,53,74,123,87,82,144,84,85,87,53,53,65,73,77,86,71,76,72,69,92,71,91,95,73,75,95,52,66,86,86,61,63,62,107,101,100,104,57,57,79", "endOffsets": "322,404,500,626,707,773,865,942,1005,1113,1179,1235,1306,1366,1420,1539,1596,1658,1712,1787,1911,1999,2082,2227,2312,2398,2486,2540,2594,2660,2734,2812,2899,2971,3048,3121,3191,3284,3356,3448,3544,3618,3694,3790,3843,3910,3997,4084,4146,4210,4273,4381,4483,4584,4689,4747,4805,4885"}, "to": {"startLines": "2,36,44,45,46,69,71,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3335,4142,4238,4364,7015,7168,8844,8921,8984,9092,9158,9214,9285,9345,9399,9518,9575,9637,9691,9766,9890,9978,10061,10206,10291,10377,10465,10519,10573,10639,10713,10791,10878,10950,11027,11100,11170,11263,11335,11427,11523,11597,11673,11769,11822,11889,11976,12063,12125,12189,12252,12360,12462,12563,12668,12726,12867", "endLines": "6,36,44,45,46,69,71,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,136", "endColumns": "12,81,95,125,80,65,91,76,62,107,65,55,70,59,53,118,56,61,53,74,123,87,82,144,84,85,87,53,53,65,73,77,86,71,76,72,69,92,71,91,95,73,75,95,52,66,86,86,61,63,62,107,101,100,104,57,57,79", "endOffsets": "372,3412,4233,4359,4440,7076,7255,8916,8979,9087,9153,9209,9280,9340,9394,9513,9570,9632,9686,9761,9885,9973,10056,10201,10286,10372,10460,10514,10568,10634,10708,10786,10873,10945,11022,11095,11165,11258,11330,11422,11518,11592,11668,11764,11817,11884,11971,12058,12120,12184,12247,12355,12457,12558,12663,12721,12779,12942"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/5c5bb3e97d4de44ccc93687f8b8f5ead/transformed/appcompat-1.6.1/res/values-bs/values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "377,498,595,702,788,892,1014,1099,1181,1272,1365,1460,1554,1654,1747,1842,1937,2028,2119,2207,2310,2414,2515,2620,2734,2837,3006,13085", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "493,590,697,783,887,1009,1094,1176,1267,1360,1455,1549,1649,1742,1837,1932,2023,2114,2202,2305,2409,2510,2615,2729,2832,3001,3097,13167"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/426e58cc6082e9ed0c5c00632bde216e/transformed/jetified-credentials-1.5.0/res/values-bs/values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3102,3213", "endColumns": "110,121", "endOffsets": "3208,3330"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1265825b88f5d413d4b023436cf71467/transformed/preference-1.2.1/res/values-bs/values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,346,484,653,740", "endColumns": "70,86,82,137,168,86,82", "endOffsets": "171,258,341,479,648,735,818"}, "to": {"startLines": "66,70,135,137,140,141,142", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6747,7081,12784,12947,13273,13442,13529", "endColumns": "70,86,82,137,168,86,82", "endOffsets": "6813,7163,12862,13080,13437,13524,13607"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/res/values-bs/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,573,679,825,947,1055,1153,1303,1406,1563,1686,1832,1974,2038,2096", "endColumns": "101,155,121,105,145,121,107,97,149,102,156,122,145,141,63,57,80", "endOffsets": "294,450,572,678,824,946,1054,1152,1302,1405,1562,1685,1831,1973,2037,2095,2176"}, "to": {"startLines": "47,48,49,50,51,52,53,54,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4445,4551,4711,4837,4947,5097,5223,5335,5578,5732,5839,6000,6127,6277,6423,6491,6553", "endColumns": "105,159,125,109,149,125,111,101,153,106,160,126,149,145,67,61,84", "endOffsets": "4546,4706,4832,4942,5092,5218,5330,5432,5727,5834,5995,6122,6272,6418,6486,6548,6633"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/res/values-bs/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "136", "endOffsets": "331"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "5437", "endColumns": "140", "endOffsets": "5573"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/res/values-bs/values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,257,380,512,643,771,901,1035,1136,1271,1404", "endColumns": "108,92,122,131,130,127,129,133,100,134,132,122", "endOffsets": "159,252,375,507,638,766,896,1030,1131,1266,1399,1522"}, "to": {"startLines": "65,68,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6638,6922,7574,7697,7829,7960,8088,8218,8352,8453,8588,8721", "endColumns": "108,92,122,131,130,127,129,133,100,134,132,122", "endOffsets": "6742,7010,7692,7824,7955,8083,8213,8347,8448,8583,8716,8839"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/res/values-bs/values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "37,38,39,40,41,42,43,139", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3417,3515,3617,3715,3819,3923,4025,13172", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "3510,3612,3710,3814,3918,4020,4137,13268"}}]}]}