{"logs": [{"outputFile": "com.example.carnowx.app-mergeDebugResources-70:/values-v21/values-v21.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1265825b88f5d413d4b023436cf71467/transformed/preference-1.2.1/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,6", "startColumns": "4,4,4", "startOffsets": "55,120,276", "endLines": "2,5,8", "endColumns": "64,12,12", "endOffsets": "115,271,449"}, "to": {"startLines": "20,340,343", "startColumns": "4,4,4", "startOffsets": "1603,24921,25077", "endLines": "20,342,345", "endColumns": "64,12,12", "endOffsets": "1663,25072,25250"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/res/values-v21/values-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "9", "endColumns": "12", "endOffsets": "543"}, "to": {"startLines": "524", "startColumns": "4", "startOffsets": "35236", "endLines": "531", "endColumns": "12", "endOffsets": "35724"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,17,18,19,346,347,348,349,577,580", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1408,1472,1539,25255,25371,25497,25623,39068,39240", "endLines": "2,17,18,19,346,347,348,349,579,584", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1467,1534,1598,25366,25492,25618,25746,39235,39587"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/bdecb973927d9c715ff6d17068a8ebbb/transformed/material-1.7.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,24,27,30,33,36,39,42,45,48,51,52,55,60,71,77,87,97,107,117,127,137,147,157,167,177,187,197,207,217,227,233,239,245,251,255,259,260,261,262,266,269,272,275,278,281,284,288,292", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1383,1490,1595,1714,1839,2052,2311,2582,2800,3032,3268,3518,3731,3962,4163,4279,4449,4770,5799,6256,6807,7362,7918,8479,9031,9582,10134,10687,11236,11789,12345,12900,13446,14000,14555,14847,15141,15441,15741,16070,16411,16549,16693,16849,17242,17460,17682,17908,18124,18294,18484,18725,18984", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,23,26,29,32,35,38,41,44,47,50,51,54,59,70,76,86,96,106,116,126,136,146,156,166,176,186,196,206,216,226,232,238,244,250,254,258,259,260,261,265,268,271,274,277,280,283,287,291,294", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1378,1485,1590,1709,1834,2047,2306,2577,2795,3027,3263,3513,3726,3957,4158,4274,4444,4765,5794,6251,6802,7357,7913,8474,9026,9577,10129,10682,11231,11784,12340,12895,13441,13995,14550,14842,15136,15436,15736,16065,16406,16544,16688,16844,17237,17455,17677,17903,18119,18289,18479,18720,18979,19156"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,81,82,83,84,86,87,90,93,188,191,194,197,203,206,273,276,277,280,285,296,350,360,370,380,390,400,410,420,430,440,450,460,470,480,490,500,506,512,518,532,536,540,541,542,543,547,550,553,556,585,588,591,595,599", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,271,367,463,561,629,708,796,884,972,1060,1147,1234,1321,7035,7128,7235,7340,7562,7687,7900,8159,14340,14558,14790,15026,15475,15688,20475,20676,20792,20962,21283,22312,25751,26302,26857,27413,27974,28526,29077,29629,30182,30731,31284,31840,32395,32941,33495,34050,34342,34636,34936,35729,36058,36399,36537,36681,36837,37230,37448,37670,37896,39592,39762,39952,40193,40452", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,81,82,83,84,86,89,92,95,190,193,196,199,205,208,275,276,279,284,295,301,359,369,379,389,399,409,419,429,439,449,459,469,479,489,499,505,511,517,523,535,539,540,541,542,546,549,552,555,558,587,590,594,598,601", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10,10,10", "endOffsets": "266,362,458,556,624,703,791,879,967,1055,1142,1229,1316,1403,7123,7230,7335,7454,7682,7895,8154,8425,14553,14785,15021,15271,15683,15914,20671,20787,20957,21278,22307,22764,26297,26852,27408,27969,28521,29072,29624,30177,30726,31279,31835,32390,32936,33490,34045,34337,34631,34931,35231,36053,36394,36532,36676,36832,37225,37443,37665,37891,38107,39757,39947,40188,40447,40624"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/5c5bb3e97d4de44ccc93687f8b8f5ead/transformed/appcompat-1.6.1/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,36,38,39,40,41,43,45,46,47,48,49,51,53,55,57,59,61,62,67,69,71,72,73,75,77,78,79,80,85,96,139,142,185,200,209,211,213,215,218,222,225,226,227,230,231,232,233,234,235,238,239,241,243,245,247,251,253,254,255,256,258,262,264,266,267,268,269,270,271,302,303,304,314,315,316,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1668,1759,1862,1965,2070,2177,2286,2395,2504,2613,2722,2829,2932,3051,3206,3361,3466,3587,3688,3835,3976,4079,4198,4305,4408,4563,4734,4883,5048,5205,5356,5475,5826,5975,6124,6236,6383,6536,6683,6758,6847,6934,7459,8430,11188,11373,14143,15276,15919,16042,16165,16278,16461,16716,16917,17006,17117,17350,17451,17546,17669,17798,17915,18092,18191,18326,18469,18604,18723,18924,19043,19136,19247,19303,19410,19605,19716,19849,19944,20035,20126,20219,20336,22769,22840,22923,23546,23603,23661,24285", "endLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,35,37,38,39,40,42,44,45,46,47,48,50,52,54,56,58,60,61,66,68,70,71,72,74,76,77,78,79,80,85,138,141,184,187,202,210,212,214,217,221,224,225,226,229,230,231,232,233,234,237,238,240,242,244,246,250,252,253,254,255,257,261,263,265,266,267,268,269,270,272,302,303,313,314,315,327,339", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1754,1857,1960,2065,2172,2281,2390,2499,2608,2717,2824,2927,3046,3201,3356,3461,3582,3683,3830,3971,4074,4193,4300,4403,4558,4729,4878,5043,5200,5351,5470,5821,5970,6119,6231,6378,6531,6678,6753,6842,6929,7030,7557,11183,11368,14138,14335,15470,16037,16160,16273,16456,16711,16912,17001,17112,17345,17446,17541,17664,17793,17910,18087,18186,18321,18464,18599,18718,18919,19038,19131,19242,19298,19405,19600,19711,19844,19939,20030,20121,20214,20331,20470,22835,22918,23541,23598,23656,24280,24916"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/res/values-v21/values.xml", "from": {"startLines": "4,13", "startColumns": "0,0", "startOffsets": "180,655", "endLines": "12,21", "endColumns": "8,8", "endOffsets": "654,1127"}, "to": {"startLines": "559,568", "startColumns": "4,4", "startOffsets": "38112,38591", "endLines": "567,576", "endColumns": "8,8", "endOffsets": "38586,39063"}}]}]}