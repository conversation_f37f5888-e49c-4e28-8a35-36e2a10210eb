{"logs": [{"outputFile": "com.example.carnowx.app-mergeDebugResources-70:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/426e58cc6082e9ed0c5c00632bde216e/transformed/jetified-credentials-1.5.0/res/values-en-rIN/values-en-rIN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2762,2874", "endColumns": "111,113", "endOffsets": "2869,2983"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1265825b88f5d413d4b023436cf71467/transformed/preference-1.2.1/res/values-en-rIN/values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "39,42,56,57,60,61,62", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3813,4069,5708,5782,6100,6269,6349", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "3876,4151,5777,5911,6264,6344,6420"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/res/values-en-rIN/values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "31,32,33,34,35,36,37,59", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2988,3084,3186,3285,3384,3488,3591,5999", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3079,3181,3280,3379,3483,3586,3702,6095"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/5c5bb3e97d4de44ccc93687f8b8f5ead/transformed/appcompat-1.6.1/res/values-en-rIN/values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,5916", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,5994"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/2317eb6b3b283acb5034b744433cbaad/transformed/browser-1.8.0/res/values-en-rIN/values-en-rIN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "40,43,44,45", "startColumns": "4,4,4,4", "startOffsets": "3881,4156,4253,4362", "endColumns": "97,96,108,98", "endOffsets": "3974,4248,4357,4456"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/res/values-en-rIN/values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,370,499,637,764,882,1013,1113,1239,1378", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "156,246,365,494,632,759,877,1008,1108,1234,1373,1493"}, "to": {"startLines": "38,41,46,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3707,3979,4461,4580,4709,4847,4974,5092,5223,5323,5449,5588", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "3808,4064,4575,4704,4842,4969,5087,5218,5318,5444,5583,5703"}}]}]}