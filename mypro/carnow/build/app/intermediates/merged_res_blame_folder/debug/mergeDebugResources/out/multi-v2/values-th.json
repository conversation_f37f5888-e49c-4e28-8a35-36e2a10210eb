{"logs": [{"outputFile": "com.example.carnowx.app-mergeDebugResources-70:/values-th/values-th.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/5c5bb3e97d4de44ccc93687f8b8f5ead/transformed/appcompat-1.6.1/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,12648", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,12725"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/426e58cc6082e9ed0c5c00632bde216e/transformed/jetified-credentials-1.5.0/res/values-th/values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2964,3076", "endColumns": "111,113", "endOffsets": "3071,3185"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "36,37,38,39,40,41,42,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3273,3369,3472,3570,3668,3771,3876,12730", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3364,3467,3565,3663,3766,3871,3983,12826"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/bdecb973927d9c715ff6d17068a8ebbb/transformed/material-1.7.0/res/values-th/values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,368,467,580,660,730,820,890,950,1037,1102,1163,1227,1288,1342,1443,1504,1564,1618,1688,1799,1886,1967,2110,2189,2271,2363,2417,2470,2536,2606,2684,2770,2842,2920,2989,3058,3140,3228,3321,3415,3489,3558,3653,3705,3773,3858,3946,4008,4072,4135,4235,4328,4425,4518,4576,4633", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,82,98,112,79,69,89,69,59,86,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,91,53,52,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,57,56,76", "endOffsets": "280,363,462,575,655,725,815,885,945,1032,1097,1158,1222,1283,1337,1438,1499,1559,1613,1683,1794,1881,1962,2105,2184,2266,2358,2412,2465,2531,2601,2679,2765,2837,2915,2984,3053,3135,3223,3316,3410,3484,3553,3648,3700,3768,3853,3941,4003,4067,4130,4230,4323,4420,4513,4571,4628,4705"}, "to": {"startLines": "2,35,43,44,45,68,70,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3190,3988,4087,4200,6781,6937,8539,8609,8669,8756,8821,8882,8946,9007,9061,9162,9223,9283,9337,9407,9518,9605,9686,9829,9908,9990,10082,10136,10189,10255,10325,10403,10489,10561,10639,10708,10777,10859,10947,11040,11134,11208,11277,11372,11424,11492,11577,11665,11727,11791,11854,11954,12047,12144,12237,12295,12432", "endLines": "5,35,43,44,45,68,70,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135", "endColumns": "12,82,98,112,79,69,89,69,59,86,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,91,53,52,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,57,56,76", "endOffsets": "330,3268,4082,4195,4275,6846,7022,8604,8664,8751,8816,8877,8941,9002,9056,9157,9218,9278,9332,9402,9513,9600,9681,9824,9903,9985,10077,10131,10184,10250,10320,10398,10484,10556,10634,10703,10772,10854,10942,11035,11129,11203,11272,11367,11419,11487,11572,11660,11722,11786,11849,11949,12042,12139,12232,12290,12347,12504"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1265825b88f5d413d4b023436cf71467/transformed/preference-1.2.1/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,480,648,728", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "170,256,336,475,643,723,801"}, "to": {"startLines": "65,69,134,136,139,140,141", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6509,6851,12352,12509,12831,12999,13079", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "6574,6932,12427,12643,12994,13074,13152"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/2317eb6b3b283acb5034b744433cbaad/transformed/browser-1.8.0/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "66,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6579,7027,7126,7237", "endColumns": "102,98,110,97", "endOffsets": "6677,7121,7232,7330"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/res/values-th/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5250", "endColumns": "128", "endOffsets": "5374"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/res/values-th/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4280,4386,4533,4656,4763,4899,5023,5142,5379,5523,5628,5775,5897,6037,6188,6252,6320", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "4381,4528,4651,4758,4894,5018,5137,5245,5518,5623,5770,5892,6032,6183,6247,6315,6399"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,259,372,499,625,751,871,995,1090,1219,1348", "endColumns": "104,98,112,126,125,125,119,123,94,128,128,114", "endOffsets": "155,254,367,494,620,746,866,990,1085,1214,1343,1458"}, "to": {"startLines": "64,67,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6404,6682,7335,7448,7575,7701,7827,7947,8071,8166,8295,8424", "endColumns": "104,98,112,126,125,125,119,123,94,128,128,114", "endOffsets": "6504,6776,7443,7570,7696,7822,7942,8066,8161,8290,8419,8534"}}]}]}