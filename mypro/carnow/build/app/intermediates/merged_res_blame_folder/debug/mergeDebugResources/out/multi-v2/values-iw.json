{"logs": [{"outputFile": "com.example.carnowx.app-mergeDebugResources-70:/values-iw/values-iw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/bdecb973927d9c715ff6d17068a8ebbb/transformed/material-1.7.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,364,441,534,647,727,792,880,950,1013,1105,1165,1224,1287,1348,1402,1504,1561,1620,1674,1742,1853,1934,2016,2148,2219,2292,2380,2433,2487,2553,2626,2702,2788,2858,2933,3015,3083,3168,3238,3328,3419,3493,3566,3655,3706,3773,3855,3940,4002,4066,4129,4223,4318,4408,4504,4561,4619", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "12,76,92,112,79,64,87,69,62,91,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,87,52,53,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,56,57,74", "endOffsets": "359,436,529,642,722,787,875,945,1008,1100,1160,1219,1282,1343,1397,1499,1556,1615,1669,1737,1848,1929,2011,2143,2214,2287,2375,2428,2482,2548,2621,2697,2783,2853,2928,3010,3078,3163,3233,3323,3414,3488,3561,3650,3701,3768,3850,3935,3997,4061,4124,4218,4313,4403,4499,4556,4614,4689"}, "to": {"startLines": "2,37,45,46,47,70,72,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3295,4069,4162,4275,6783,6932,8562,8632,8695,8787,8847,8906,8969,9030,9084,9186,9243,9302,9356,9424,9535,9616,9698,9830,9901,9974,10062,10115,10169,10235,10308,10384,10470,10540,10615,10697,10765,10850,10920,11010,11101,11175,11248,11337,11388,11455,11537,11622,11684,11748,11811,11905,12000,12090,12186,12243,12377", "endLines": "7,37,45,46,47,70,72,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,137", "endColumns": "12,76,92,112,79,64,87,69,62,91,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,87,52,53,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,56,57,74", "endOffsets": "409,3367,4157,4270,4350,6843,7015,8627,8690,8782,8842,8901,8964,9025,9079,9181,9238,9297,9351,9419,9530,9611,9693,9825,9896,9969,10057,10110,10164,10230,10303,10379,10465,10535,10610,10692,10760,10845,10915,11005,11096,11170,11243,11332,11383,11450,11532,11617,11679,11743,11806,11900,11995,12085,12181,12238,12296,12447"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/2317eb6b3b283acb5034b744433cbaad/transformed/browser-1.8.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "68,73,74,75", "startColumns": "4,4,4,4", "startOffsets": "6601,7020,7120,7226", "endColumns": "90,99,105,101", "endOffsets": "6687,7115,7221,7323"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1265825b88f5d413d4b023436cf71467/transformed/preference-1.2.1/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,258,334,459,628,709", "endColumns": "68,83,75,124,168,80,78", "endOffsets": "169,253,329,454,623,704,783"}, "to": {"startLines": "67,71,136,138,141,142,143", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6532,6848,12301,12452,12760,12929,13010", "endColumns": "68,83,75,124,168,80,78", "endOffsets": "6596,6927,12372,12572,12924,13005,13084"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/res/values-iw/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5320", "endColumns": "117", "endOffsets": "5433"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/res/values-iw/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4355,4458,4612,4737,4841,4980,5105,5217,5438,5574,5678,5823,5946,6080,6225,6285,6345", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "4453,4607,4732,4836,4975,5100,5212,5315,5569,5673,5818,5941,6075,6220,6280,6340,6421"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/5c5bb3e97d4de44ccc93687f8b8f5ead/transformed/appcompat-1.6.1/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "414,519,619,727,811,913,1029,1108,1186,1277,1371,1465,1559,1659,1752,1847,1940,2031,2123,2204,2309,2412,2510,2615,2717,2819,2973,12577", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "514,614,722,806,908,1024,1103,1181,1272,1366,1460,1554,1654,1747,1842,1935,2026,2118,2199,2304,2407,2505,2610,2712,2814,2968,3065,12654"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/426e58cc6082e9ed0c5c00632bde216e/transformed/jetified-credentials-1.5.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,115", "endOffsets": "159,275"}, "to": {"startLines": "35,36", "startColumns": "4,4", "startOffsets": "3070,3179", "endColumns": "108,115", "endOffsets": "3174,3290"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "38,39,40,41,42,43,44,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3372,3466,3568,3665,3762,3863,3963,12659", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3461,3563,3660,3757,3858,3958,4064,12755"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,252,370,499,622,753,867,994,1088,1231,1373", "endColumns": "105,90,117,128,122,130,113,126,93,142,141,112", "endOffsets": "156,247,365,494,617,748,862,989,1083,1226,1368,1481"}, "to": {"startLines": "66,69,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6426,6692,7328,7446,7575,7698,7829,7943,8070,8164,8307,8449", "endColumns": "105,90,117,128,122,130,113,126,93,142,141,112", "endOffsets": "6527,6778,7441,7570,7693,7824,7938,8065,8159,8302,8444,8557"}}]}]}