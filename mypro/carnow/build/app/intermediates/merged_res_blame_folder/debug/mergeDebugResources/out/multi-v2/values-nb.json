{"logs": [{"outputFile": "com.example.carnowx.app-mergeDebugResources-70:/values-nb/values-nb.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/res/values-nb/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5293", "endColumns": "129", "endOffsets": "5418"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,249,371,515,650,786,913,1054,1154,1295,1439", "endColumns": "105,87,121,143,134,135,126,140,99,140,143,126", "endOffsets": "156,244,366,510,645,781,908,1049,1149,1290,1434,1561"}, "to": {"startLines": "64,67,74,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6498,6784,7425,7547,7691,7826,7962,8089,8230,8330,8471,8615", "endColumns": "105,87,121,143,134,135,126,140,99,140,143,126", "endOffsets": "6599,6867,7542,7686,7821,7957,8084,8225,8325,8466,8610,8737"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "36,37,38,39,40,41,42,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3258,3352,3454,3551,3650,3758,3864,12905", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3347,3449,3546,3645,3753,3859,3979,13001"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/5c5bb3e97d4de44ccc93687f8b8f5ead/transformed/appcompat-1.6.1/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,12825", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,12900"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/426e58cc6082e9ed0c5c00632bde216e/transformed/jetified-credentials-1.5.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,116", "endOffsets": "161,278"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2943,3054", "endColumns": "110,116", "endOffsets": "3049,3166"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/res/values-nb/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4284,4390,4549,4675,4784,4940,5070,5190,5423,5577,5684,5845,5973,6115,6291,6358,6420", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "4385,4544,4670,4779,4935,5065,5185,5288,5572,5679,5840,5968,6110,6286,6353,6415,6493"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1265825b88f5d413d4b023436cf71467/transformed/preference-1.2.1/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,482,651,730", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "170,257,335,477,646,725,801"}, "to": {"startLines": "65,69,134,136,139,140,141", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6604,6936,12527,12683,13006,13175,13254", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "6669,7018,12600,12820,13170,13249,13325"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/2317eb6b3b283acb5034b744433cbaad/transformed/browser-1.8.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "66,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6674,7115,7216,7328", "endColumns": "109,100,111,96", "endOffsets": "6779,7211,7323,7420"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/bdecb973927d9c715ff6d17068a8ebbb/transformed/material-1.7.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,348,447,566,648,712,804,872,932,1019,1081,1145,1213,1278,1332,1441,1499,1561,1615,1690,1810,1892,1972,2106,2184,2264,2352,2406,2457,2523,2591,2665,2755,2826,2904,2974,3044,3133,3211,3299,3389,3461,3533,3617,3668,3734,3815,3898,3960,4024,4087,4187,4285,4378,4476,4534,4589", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,86,98,118,81,63,91,67,59,86,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,87,53,50,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,57,54,77", "endOffsets": "256,343,442,561,643,707,799,867,927,1014,1076,1140,1208,1273,1327,1436,1494,1556,1610,1685,1805,1887,1967,2101,2179,2259,2347,2401,2452,2518,2586,2660,2750,2821,2899,2969,3039,3128,3206,3294,3384,3456,3528,3612,3663,3729,3810,3893,3955,4019,4082,4182,4280,4373,4471,4529,4584,4662"}, "to": {"startLines": "2,35,43,44,45,68,70,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3171,3984,4083,4202,6872,7023,8742,8810,8870,8957,9019,9083,9151,9216,9270,9379,9437,9499,9553,9628,9748,9830,9910,10044,10122,10202,10290,10344,10395,10461,10529,10603,10693,10764,10842,10912,10982,11071,11149,11237,11327,11399,11471,11555,11606,11672,11753,11836,11898,11962,12025,12125,12223,12316,12414,12472,12605", "endLines": "5,35,43,44,45,68,70,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135", "endColumns": "12,86,98,118,81,63,91,67,59,86,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,87,53,50,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,57,54,77", "endOffsets": "306,3253,4078,4197,4279,6931,7110,8805,8865,8952,9014,9078,9146,9211,9265,9374,9432,9494,9548,9623,9743,9825,9905,10039,10117,10197,10285,10339,10390,10456,10524,10598,10688,10759,10837,10907,10977,11066,11144,11232,11322,11394,11466,11550,11601,11667,11748,11831,11893,11957,12020,12120,12218,12311,12409,12467,12522,12678"}}]}]}