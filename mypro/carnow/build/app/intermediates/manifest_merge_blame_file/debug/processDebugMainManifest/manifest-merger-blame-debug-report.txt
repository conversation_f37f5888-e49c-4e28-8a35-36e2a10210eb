1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.carnowx"
4    android:versionCode="1"
5    android:versionName="0.4.1" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:4:5-67
15-->/Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:4:22-64
16    <!-- Location permissions for geolocator -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->/Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:7:5-79
17-->/Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:7:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- Foreground service permission for geolocator -->
18-->/Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:8:5-81
18-->/Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:8:22-78
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
19-->/Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:11:5-77
19-->/Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:11:22-74
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
20-->/Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:12:5-86
20-->/Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:12:22-83
21
22    <queries>
22-->[:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:15
23        <intent>
23-->[:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:18
24            <action android:name="android.intent.action.GET_CONTENT" />
24-->[:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-72
24-->[:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:21-69
25
26            <data android:mimeType="*/*" />
26-->[:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-44
26-->[:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:19-41
27        </intent>
28
29        <package android:name="androidx.test.orchestrator" />
29-->[androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:25:9-62
29-->[androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:25:18-59
30        <package android:name="androidx.test.services" />
30-->[androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:26:9-58
30-->[androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:26:18-55
31        <package android:name="com.google.android.apps.common.testing.services" />
31-->[androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:27:9-83
31-->[androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:27:18-80
32    </queries>
33
34    <uses-permission android:name="android.permission.CAMERA" />
34-->[:mobile_scanner] /Users/<USER>/mypro/carnow/build/mobile_scanner/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-65
34-->[:mobile_scanner] /Users/<USER>/mypro/carnow/build/mobile_scanner/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-62
35
36    <uses-feature
36-->[:mobile_scanner] /Users/<USER>/mypro/carnow/build/mobile_scanner/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-11:36
37        android:name="android.hardware.camera"
37-->[:mobile_scanner] /Users/<USER>/mypro/carnow/build/mobile_scanner/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:9-47
38        android:required="false" />
38-->[:mobile_scanner] /Users/<USER>/mypro/carnow/build/mobile_scanner/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:9-33
39
40    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
40-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:26:5-79
40-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:26:22-76
41    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
41-->[:local_auth_android] /Users/<USER>/mypro/carnow/build/local_auth_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-72
41-->[:local_auth_android] /Users/<USER>/mypro/carnow/build/local_auth_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-69
42    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
42-->[androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/AndroidManifest.xml:27:5-74
42-->[androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/AndroidManifest.xml:27:22-71
43    <uses-permission android:name="android.permission.WAKE_LOCK" />
43-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:25:5-68
43-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:25:22-65
44    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
44-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:26:5-110
44-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:26:22-107
45    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
45-->[com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:25:5-79
45-->[com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:25:22-76
46    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
46-->[com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:26:5-88
46-->[com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:26:22-85
47    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
47-->[com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:27:5-82
47-->[com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:27:22-79
48    <uses-permission android:name="android.permission.REORDER_TASKS" />
48-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:24:5-72
48-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:24:22-69
49
50    <permission
50-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
51        android:name="com.example.carnowx.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
51-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
52        android:protectionLevel="signature" />
52-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
53
54    <uses-permission android:name="com.example.carnowx.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
54-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
54-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
55
56    <application
57        android:name="android.app.Application"
58        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
58-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:28:18-86
59        android:debuggable="true"
60        android:enableOnBackInvokedCallback="true"
61        android:extractNativeLibs="true"
62        android:icon="@mipmap/ic_launcher"
63        android:label="CarNow" >
64
65        <!-- Google Services configuration -->
66        <meta-data
67            android:name="com.google.android.gms.version"
68            android:value="@integer/google_play_services_version" />
69
70        <!-- Firebase configuration -->
71        <meta-data
72            android:name="firebase_analytics_collection_enabled"
73            android:value="false" />
74        <meta-data
75            android:name="firebase_performance_collection_enabled"
76            android:value="false" />
77        <meta-data
78            android:name="firebase_crashlytics_collection_enabled"
79            android:value="false" />
80
81        <activity
82            android:name="com.example.carnowx.MainActivity"
83            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
84            android:exported="true"
85            android:hardwareAccelerated="true"
86            android:launchMode="singleTop"
87            android:theme="@style/LaunchTheme"
88            android:windowSoftInputMode="adjustResize" >
89
90            <!--
91                 Specifies an Android theme to apply to this Activity as soon as
92                 the Android process has started. This theme is visible to the user
93                 while the Flutter UI initializes. After that, this theme continues
94                 to determine the Window background behind the Flutter UI.
95            -->
96            <meta-data
97                android:name="io.flutter.embedding.android.NormalTheme"
98                android:resource="@style/NormalTheme" />
99
100            <intent-filter android:autoVerify="true" >
101                <action android:name="android.intent.action.MAIN" />
102
103                <category android:name="android.intent.category.LAUNCHER" />
103-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:17-77
103-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:27-74
104            </intent-filter>
105
106            <!-- Deep linking configuration -->
107            <intent-filter>
108                <action android:name="android.intent.action.VIEW" />
108-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:36:17-69
108-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:36:25-66
109
110                <category android:name="android.intent.category.DEFAULT" />
110-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:38:17-76
110-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:38:27-73
111                <category android:name="android.intent.category.BROWSABLE" />
111-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:39:17-78
111-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:39:27-75
112
113                <data android:scheme="com.example.carnowx" />
113-->[:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-44
114            </intent-filter>
115        </activity>
116
117        <!--
118             Don't delete the meta-data below.
119             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
120        -->
121        <meta-data
122            android:name="flutterEmbedding"
123            android:value="2" />
124
125        <!-- Geolocator foreground service -->
126        <service
127            android:name="com.baseflow.geolocator.location.GeolocatorLocationService"
128            android:foregroundServiceType="location" />
129        <!--
130           Declares a provider which allows us to store files to share in
131           '.../caches/share_plus' and grant the receiving action access
132        -->
133        <provider
133-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:9-21:20
134            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
134-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-77
135            android:authorities="com.example.carnowx.flutter.share_provider"
135-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-74
136            android:exported="false"
136-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
137            android:grantUriPermissions="true" >
137-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-47
138            <meta-data
138-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-20:68
139                android:name="android.support.FILE_PROVIDER_PATHS"
139-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:17-67
140                android:resource="@xml/flutter_share_file_paths" />
140-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:17-65
141        </provider>
142        <!--
143           This manifest declared broadcast receiver allows us to use an explicit
144           Intent when creating a PendingItent to be informed of the user's choice
145        -->
146        <receiver
146-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-32:20
147            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
147-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-82
148            android:exported="false" >
148-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-37
149            <intent-filter>
149-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-31:29
150                <action android:name="EXTRA_CHOSEN_COMPONENT" />
150-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-65
150-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:25-62
151            </intent-filter>
152        </receiver>
153
154        <activity
154-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:29:9-46:20
155            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
155-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:30:13-80
156            android:excludeFromRecents="true"
156-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:31:13-46
157            android:exported="true"
157-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:32:13-36
158            android:launchMode="singleTask"
158-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:33:13-44
159            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
159-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:34:13-72
160            <intent-filter>
160-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:35:13-45:29
161                <action android:name="android.intent.action.VIEW" />
161-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:36:17-69
161-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:36:25-66
162
163                <category android:name="android.intent.category.DEFAULT" />
163-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:38:17-76
163-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:38:27-73
164                <category android:name="android.intent.category.BROWSABLE" />
164-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:39:17-78
164-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:39:27-75
165
166                <data
166-->[:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-44
167                    android:host="firebase.auth"
168                    android:path="/"
169                    android:scheme="genericidp" />
170            </intent-filter>
171        </activity>
172        <activity
172-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:47:9-64:20
173            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
173-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:48:13-79
174            android:excludeFromRecents="true"
174-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:49:13-46
175            android:exported="true"
175-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:50:13-36
176            android:launchMode="singleTask"
176-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:51:13-44
177            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
177-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:52:13-72
178            <intent-filter>
178-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:53:13-63:29
179                <action android:name="android.intent.action.VIEW" />
179-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:36:17-69
179-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:36:25-66
180
181                <category android:name="android.intent.category.DEFAULT" />
181-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:38:17-76
181-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:38:27-73
182                <category android:name="android.intent.category.BROWSABLE" />
182-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:39:17-78
182-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:39:27-75
183
184                <data
184-->[:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-44
185                    android:host="firebase.auth"
186                    android:path="/"
187                    android:scheme="recaptcha" />
188            </intent-filter>
189        </activity>
190
191        <service
191-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:66:9-72:19
192            android:name="com.google.firebase.components.ComponentDiscoveryService"
192-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:67:13-84
193            android:directBootAware="true"
193-->[com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:32:13-43
194            android:exported="false" >
194-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:68:13-37
195            <meta-data
195-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:69:13-71:85
196                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
196-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:70:17-109
197                android:value="com.google.firebase.components.ComponentRegistrar" />
197-->[com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:71:17-82
198            <meta-data
198-->[com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:37:13-39:85
199                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
199-->[com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:38:17-139
200                android:value="com.google.firebase.components.ComponentRegistrar" />
200-->[com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:39:17-82
201            <meta-data
201-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
202                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
202-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
203                android:value="com.google.firebase.components.ComponentRegistrar" />
203-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
204            <meta-data
204-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
205                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
205-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
206                android:value="com.google.firebase.components.ComponentRegistrar" />
206-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
207            <meta-data
207-->[com.google.firebase:firebase-common-ktx:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e30594788384c5d008298c14bdf327ea/transformed/jetified-firebase-common-ktx-20.4.2/AndroidManifest.xml:12:13-14:85
208                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
208-->[com.google.firebase:firebase-common-ktx:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e30594788384c5d008298c14bdf327ea/transformed/jetified-firebase-common-ktx-20.4.2/AndroidManifest.xml:13:17-116
209                android:value="com.google.firebase.components.ComponentRegistrar" />
209-->[com.google.firebase:firebase-common-ktx:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e30594788384c5d008298c14bdf327ea/transformed/jetified-firebase-common-ktx-20.4.2/AndroidManifest.xml:14:17-82
210            <meta-data
210-->[com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:35:13-37:85
211                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
211-->[com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:36:17-109
212                android:value="com.google.firebase.components.ComponentRegistrar" />
212-->[com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:37:17-82
213        </service>
214        <service
214-->[:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:56
215            android:name="com.baseflow.geolocator.GeolocatorLocationService"
215-->[:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-77
216            android:enabled="true"
216-->[:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-35
217            android:exported="false"
217-->[:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-37
218            android:foregroundServiceType="location" />
218-->[:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-53
219
220        <provider
220-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-17:20
221            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
221-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-82
222            android:authorities="com.example.carnowx.flutter.image_provider"
222-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-74
223            android:exported="false"
223-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-37
224            android:grantUriPermissions="true" >
224-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-47
225            <meta-data
225-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-20:68
226                android:name="android.support.FILE_PROVIDER_PATHS"
226-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:17-67
227                android:resource="@xml/flutter_image_picker_file_paths" />
227-->[:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:17-65
228        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
229        <service
229-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:9-31:19
230            android:name="com.google.android.gms.metadata.ModuleDependencies"
230-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-78
231            android:enabled="false"
231-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-36
232            android:exported="false" >
232-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-37
233            <intent-filter>
233-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-26:29
234                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
234-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:17-94
234-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:25-91
235            </intent-filter>
236
237            <meta-data
237-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-30:36
238                android:name="photopicker_activity:0:required"
238-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-63
239                android:value="" />
239-->[:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-33
240        </service>
241
242        <activity
242-->[:url_launcher_android] /Users/<USER>/mypro/carnow/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
243            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
243-->[:url_launcher_android] /Users/<USER>/mypro/carnow/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
244            android:exported="false"
244-->[:url_launcher_android] /Users/<USER>/mypro/carnow/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
245            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
245-->[:url_launcher_android] /Users/<USER>/mypro/carnow/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
246
247        <service
247-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:24:9-32:19
248            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
248-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:25:13-94
249            android:enabled="true"
249-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:26:13-35
250            android:exported="false" >
250-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:27:13-37
251            <meta-data
251-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:29:13-31:104
252                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
252-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:30:17-76
253                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
253-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:31:17-101
254        </service>
255
256        <activity
256-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:34:9-41:20
257            android:name="androidx.credentials.playservices.HiddenActivity"
257-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:35:13-76
258            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
258-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:36:13-87
259            android:enabled="true"
259-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:37:13-35
260            android:exported="false"
260-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:38:13-37
261            android:fitsSystemWindows="true"
261-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:39:13-45
262            android:theme="@style/Theme.Hidden" >
262-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:40:13-48
263        </activity>
264        <activity
264-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:42:9-49:20
265            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
265-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:43:13-97
266            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
266-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:44:13-87
267            android:enabled="true"
267-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:45:13-35
268            android:exported="false"
268-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:46:13-37
269            android:fitsSystemWindows="true"
269-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:47:13-45
270            android:theme="@style/Theme.Hidden" >
270-->[androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:48:13-48
271        </activity>
272        <!--
273        Service for holding metadata. Cannot be instantiated.
274        Metadata will be merged from other manifests.
275        -->
276        <service
276-->[androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:29:9-33:78
277            android:name="androidx.camera.core.impl.MetadataHolderService"
277-->[androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:30:13-75
278            android:enabled="false"
278-->[androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:31:13-36
279            android:exported="false" >
279-->[androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:32:13-37
280            <meta-data
280-->[androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd4db4106f18e531a05c8c3faee85543/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:30:13-32:89
281                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
281-->[androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd4db4106f18e531a05c8c3faee85543/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:31:17-103
282                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
282-->[androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd4db4106f18e531a05c8c3faee85543/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:32:17-86
283        </service>
284
285        <uses-library
285-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
286            android:name="androidx.window.extensions"
286-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
287            android:required="false" />
287-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
288        <uses-library
288-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
289            android:name="androidx.window.sidecar"
289-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
290            android:required="false" />
290-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
291
292        <service
292-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:9:9-15:19
293            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
293-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:10:13-91
294            android:directBootAware="true"
294-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:17:13-43
295            android:exported="false" >
295-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:11:13-37
296            <meta-data
296-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:12:13-14:85
297                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
297-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:13:17-120
298                android:value="com.google.firebase.components.ComponentRegistrar" />
298-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:14:17-82
299            <meta-data
299-->[com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d88b73ec39358a6bd35028a74b2d001/transformed/jetified-vision-common-17.3.0/AndroidManifest.xml:12:13-14:85
300                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
300-->[com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d88b73ec39358a6bd35028a74b2d001/transformed/jetified-vision-common-17.3.0/AndroidManifest.xml:13:17-124
301                android:value="com.google.firebase.components.ComponentRegistrar" />
301-->[com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d88b73ec39358a6bd35028a74b2d001/transformed/jetified-vision-common-17.3.0/AndroidManifest.xml:14:17-82
302            <meta-data
302-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:20:13-22:85
303                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
303-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:21:17-120
304                android:value="com.google.firebase.components.ComponentRegistrar" />
304-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:22:17-82
305        </service>
306
307        <provider
307-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:9:9-13:38
308            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
308-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:10:13-78
309            android:authorities="com.example.carnowx.mlkitinitprovider"
309-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:11:13-69
310            android:exported="false"
310-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:12:13-37
311            android:initOrder="99" />
311-->[com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:13:13-35
312
313        <activity
313-->[com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:23:9-27:75
314            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
314-->[com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:24:13-93
315            android:excludeFromRecents="true"
315-->[com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:25:13-46
316            android:exported="false"
316-->[com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:26:13-37
317            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
317-->[com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:27:13-72
318        <!--
319            Service handling Google Sign-In user revocation. For apps that do not integrate with
320            Google Sign-In, this service will never be started.
321        -->
322        <service
322-->[com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:33:9-37:51
323            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
323-->[com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:34:13-89
324            android:exported="true"
324-->[com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:35:13-36
325            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
325-->[com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:36:13-107
326            android:visibleToInstantApps="true" />
326-->[com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:37:13-48
327
328        <activity
328-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:9-173
329            android:name="com.google.android.gms.common.api.GoogleApiActivity"
329-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:19-85
330            android:exported="false"
330-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:146-170
331            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
331-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:86-145
332
333        <receiver
333-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:29:9-33:20
334            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
334-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:30:13-85
335            android:enabled="true"
335-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:31:13-35
336            android:exported="false" >
336-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:32:13-37
337        </receiver>
338
339        <service
339-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:35:9-38:40
340            android:name="com.google.android.gms.measurement.AppMeasurementService"
340-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:36:13-84
341            android:enabled="true"
341-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:37:13-35
342            android:exported="false" />
342-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:38:13-37
343        <service
343-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:39:9-43:72
344            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
344-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:40:13-87
345            android:enabled="true"
345-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:41:13-35
346            android:exported="false"
346-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:42:13-37
347            android:permission="android.permission.BIND_JOB_SERVICE" />
347-->[com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:43:13-69
348
349        <property
349-->[com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:30:9-32:61
350            android:name="android.adservices.AD_SERVICES_CONFIG"
350-->[com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:31:13-65
351            android:resource="@xml/ga_ad_services_config" />
351-->[com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:32:13-58
352
353        <provider
353-->[com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:23:9-28:39
354            android:name="com.google.firebase.provider.FirebaseInitProvider"
354-->[com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:24:13-77
355            android:authorities="com.example.carnowx.firebaseinitprovider"
355-->[com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:25:13-72
356            android:directBootAware="true"
356-->[com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:26:13-43
357            android:exported="false"
357-->[com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:27:13-37
358            android:initOrder="100" />
358-->[com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:28:13-36
359        <provider
359-->[io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:16:9-20:39
360            android:name="io.sentry.android.core.SentryPerformanceProvider"
360-->[io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:17:13-76
361            android:authorities="com.example.carnowx.SentryPerformanceProvider"
361-->[io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:18:13-77
362            android:exported="false"
362-->[io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:19:13-37
363            android:initOrder="200" />
363-->[io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:20:13-36
364
365        <activity
365-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:27:9-34:20
366            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
366-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:28:13-99
367            android:exported="true"
367-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:29:13-36
368            android:theme="@style/WhiteBackgroundTheme" >
368-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:30:13-56
369            <intent-filter android:priority="-100" >
369-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:13-33:29
369-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:28-51
370                <category android:name="android.intent.category.LAUNCHER" />
370-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:17-77
370-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:27-74
371            </intent-filter>
372        </activity>
373        <activity
373-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:35:9-42:20
374            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
374-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:36:13-95
375            android:exported="true"
375-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:37:13-36
376            android:theme="@style/WhiteBackgroundTheme" >
376-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:38:13-56
377            <intent-filter android:priority="-100" >
377-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:13-33:29
377-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:28-51
378                <category android:name="android.intent.category.LAUNCHER" />
378-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:17-77
378-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:27-74
379            </intent-filter>
380        </activity>
381        <activity
381-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:43:9-50:20
382            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
382-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:44:13-103
383            android:exported="true"
383-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:45:13-36
384            android:theme="@style/WhiteBackgroundDialogTheme" >
384-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:46:13-62
385            <intent-filter android:priority="-100" >
385-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:13-33:29
385-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:28-51
386                <category android:name="android.intent.category.LAUNCHER" />
386-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:17-77
386-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:27-74
387            </intent-filter>
388        </activity>
389
390        <provider
390-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
391            android:name="androidx.startup.InitializationProvider"
391-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
392            android:authorities="com.example.carnowx.androidx-startup"
392-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
393            android:exported="false" >
393-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
394            <meta-data
394-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
395                android:name="androidx.emoji2.text.EmojiCompatInitializer"
395-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
396                android:value="androidx.startup" />
396-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
397            <meta-data
397-->[androidx.lifecycle:lifecycle-process:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/de27bcac0ccd370803a68743b06afa7e/transformed/jetified-lifecycle-process-2.8.7/AndroidManifest.xml:29:13-31:52
398                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
398-->[androidx.lifecycle:lifecycle-process:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/de27bcac0ccd370803a68743b06afa7e/transformed/jetified-lifecycle-process-2.8.7/AndroidManifest.xml:30:17-78
399                android:value="androidx.startup" />
399-->[androidx.lifecycle:lifecycle-process:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/de27bcac0ccd370803a68743b06afa7e/transformed/jetified-lifecycle-process-2.8.7/AndroidManifest.xml:31:17-49
400            <meta-data
400-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:29:13-31:52
401                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
401-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:30:17-85
402                android:value="androidx.startup" />
402-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:31:17-49
403        </provider>
404
405        <uses-library
405-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/43af7710a47a79412d6bbc6a2fb6fb8c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:23:9-25:40
406            android:name="android.ext.adservices"
406-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/43af7710a47a79412d6bbc6a2fb6fb8c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:24:13-50
407            android:required="false" />
407-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/43af7710a47a79412d6bbc6a2fb6fb8c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:25:13-37
408
409        <receiver
409-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:34:9-52:20
410            android:name="androidx.profileinstaller.ProfileInstallReceiver"
410-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:35:13-76
411            android:directBootAware="false"
411-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:36:13-44
412            android:enabled="true"
412-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:37:13-35
413            android:exported="true"
413-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:38:13-36
414            android:permission="android.permission.DUMP" >
414-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:39:13-57
415            <intent-filter>
415-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:40:13-42:29
416                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
416-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:41:17-91
416-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:41:25-88
417            </intent-filter>
418            <intent-filter>
418-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:43:13-45:29
419                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
419-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:44:17-85
419-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:44:25-82
420            </intent-filter>
421            <intent-filter>
421-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:46:13-48:29
422                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
422-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:47:17-88
422-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:47:25-85
423            </intent-filter>
424            <intent-filter>
424-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:49:13-51:29
425                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
425-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:50:17-95
425-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:50:25-92
426            </intent-filter>
427        </receiver>
428
429        <service
429-->[com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:29:9-35:19
430            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
430-->[com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:30:13-103
431            android:exported="false" >
431-->[com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:31:13-37
432            <meta-data
432-->[com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:32:13-34:39
433                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
433-->[com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:33:17-94
434                android:value="cct" />
434-->[com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:34:17-36
435        </service>
436        <service
436-->[com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:26:9-30:19
437            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
437-->[com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:27:13-117
438            android:exported="false"
438-->[com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:28:13-37
439            android:permission="android.permission.BIND_JOB_SERVICE" >
439-->[com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:29:13-69
440        </service>
441
442        <receiver
442-->[com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:32:9-34:40
443            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
443-->[com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:33:13-132
444            android:exported="false" />
444-->[com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:34:13-37
445    </application>
446
447</manifest>
