-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:14:5-78:19
INJECTED from /Users/<USER>/mypro/carnow/android/app/src/debug/AndroidManifest.xml
MERGED from [:sentry_flutter] /Users/<USER>/mypro/carnow/build/sentry_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-14:19
MERGED from [:sentry_flutter] /Users/<USER>/mypro/carnow/build/sentry_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-14:19
MERGED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-33:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:28:5-73:19
MERGED from [:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] /Users/<USER>/mypro/carnow/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] /Users/<USER>/mypro/carnow/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [com.google.android.material:material:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bdecb973927d9c715ff6d17068a8ebbb/transformed/material-1.7.0/AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bdecb973927d9c715ff6d17068a8ebbb/transformed/material-1.7.0/AndroidManifest.xml:24:5-20
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:23:5-50:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:23:5-50:19
MERGED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd4db4106f18e531a05c8c3faee85543/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd4db4106f18e531a05c8c3faee85543/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:23:5-34:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/70cc921fa2d876bb27fc73001e00d1fd/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/70cc921fa2d876bb27fc73001e00d1fd/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d88b73ec39358a6bd35028a74b2d001/transformed/jetified-vision-common-17.3.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d88b73ec39358a6bd35028a74b2d001/transformed/jetified-vision-common-17.3.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ac3e0888502ad48b072e224658008b41/transformed/jetified-play-services-location-21.2.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ac3e0888502ad48b072e224658008b41/transformed/jetified-play-services-location-21.2.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/70785106f9c804659c7bfd6e133699aa/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/70785106f9c804659c7bfd6e133699aa/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-fido:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2e26807c8f25937a53c37fddbe6a55b6/transformed/jetified-play-services-fido-21.0.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2e26807c8f25937a53c37fddbe6a55b6/transformed/jetified-play-services-fido-21.0.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/67793af2c986633ea33943ae591078fb/transformed/jetified-play-services-auth-blockstore-16.4.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/67793af2c986633ea33943ae591078fb/transformed/jetified-play-services-auth-blockstore-16.4.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-analytics:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1377cb86361b4a7e0147429caeab8c37/transformed/jetified-firebase-analytics-21.5.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1377cb86361b4a7e0147429caeab8c37/transformed/jetified-firebase-analytics-21.5.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0adbd3c5f1c510d4d66cb53d1c56d0cd/transformed/jetified-play-services-measurement-sdk-21.5.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0adbd3c5f1c510d4d66cb53d1c56d0cd/transformed/jetified-play-services-measurement-sdk-21.5.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.play:integrity:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/72aee6f0e5b4c619a6e3b77a083d8ee7/transformed/jetified-integrity-1.1.0/AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/72aee6f0e5b4c619a6e3b77a083d8ee7/transformed/jetified-integrity-1.1.0/AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4ff043b69f72c0cb187a7044dee284d6/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4ff043b69f72c0cb187a7044dee284d6/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e30594788384c5d008298c14bdf327ea/transformed/jetified-firebase-common-ktx-20.4.2/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e30594788384c5d008298c14bdf327ea/transformed/jetified-firebase-common-ktx-20.4.2/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:22:5-39:19
MERGED from [io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:9:5-21:19
MERGED from [io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:9:5-21:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/2d724b320aa55e62302f92bce99e50d0/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/2d724b320aa55e62302f92bce99e50d0/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:26:5-51:19
MERGED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:26:5-51:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/de27bcac0ccd370803a68743b06afa7e/transformed/jetified-lifecycle-process-2.8.7/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/de27bcac0ccd370803a68743b06afa7e/transformed/jetified-lifecycle-process-2.8.7/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/43af7710a47a79412d6bbc6a2fb6fb8c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/43af7710a47a79412d6bbc6a2fb6fb8c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1dd85c30ebb98e7d9ec31c82e3f116a/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1dd85c30ebb98e7d9ec31c82e3f116a/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7386b0109621a5b16f658482ed280745/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7386b0109621a5b16f658482ed280745/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/066548ab0df7918275951c127e0ac3e3/transformed/jetified-play-services-measurement-base-21.5.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/066548ab0df7918275951c127e0ac3e3/transformed/jetified-play-services-measurement-base-21.5.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a21839de1896f98e8fc4b248682bea0/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a21839de1896f98e8fc4b248682bea0/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:30:5-20
MERGED from [androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:30:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/bca3722b042e7cfcfefb0e6cfdbd6187/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/bca3722b042e7cfcfefb0e6cfdbd6187/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/20c6fcb728555d6d2a487acdbb9e88a0/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/20c6fcb728555d6d2a487acdbb9e88a0/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:25:5-39:19
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/95a185fbfb96506c0cdec2a21ab0f739/transformed/uiautomator-2.2.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/95a185fbfb96506c0cdec2a21ab0f739/transformed/uiautomator-2.2.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7bc50ec0792ba64ee7b3c29f62b053b4/transformed/jetified-image-1.0.0-beta1/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7bc50ec0792ba64ee7b3c29f62b053b4/transformed/jetified-image-1.0.0-beta1/AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from /Users/<USER>/mypro/carnow/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:1:1-79:12
MERGED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:1:1-79:12
INJECTED from /Users/<USER>/mypro/carnow/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/mypro/carnow/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/mypro/carnow/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-15:12
MERGED from [:google_sign_in_android] /Users/<USER>/mypro/carnow/build/google_sign_in_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-9:12
MERGED from [:mobile_scanner] /Users/<USER>/mypro/carnow/build/mobile_scanner/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-13:12
MERGED from [:sentry_flutter] /Users/<USER>/mypro/carnow/build/sentry_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-16:12
MERGED from [:package_info_plus] /Users/<USER>/mypro/carnow/build/package_info_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:patrol] /Users/<USER>/mypro/carnow/build/patrol/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-35:12
MERGED from [:shared_preferences_android] /Users/<USER>/mypro/carnow/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:17:1-75:12
MERGED from [:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-15:12
MERGED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-34:12
MERGED from [:local_auth_android] /Users/<USER>/mypro/carnow/build/local_auth_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-9:12
MERGED from [:url_launcher_android] /Users/<USER>/mypro/carnow/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-14:12
MERGED from [:app_links] /Users/<USER>/mypro/carnow/build/app_links/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:connectivity_plus] /Users/<USER>/mypro/carnow/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-9:12
MERGED from [:dynamic_color] /Users/<USER>/mypro/carnow/build/dynamic_color/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/mypro/carnow/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_secure_storage] /Users/<USER>/mypro/carnow/build/flutter_secure_storage/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-10:12
MERGED from [:jni] /Users/<USER>/mypro/carnow/build/jni/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] /Users/<USER>/mypro/carnow/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] /Users/<USER>/mypro/carnow/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] /Users/<USER>/mypro/carnow/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bdecb973927d9c715ff6d17068a8ebbb/transformed/material-1.7.0/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:17:1-52:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8f7643e9ce3412d349249ecf1e5cf325/transformed/jetified-googleid-1.1.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/426e58cc6082e9ed0c5c00632bde216e/transformed/jetified-credentials-1.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/AndroidManifest.xml:17:1-29:12
MERGED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd4db4106f18e531a05c8c3faee85543/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-lifecycle:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/97751780a686aed19b8109dc8435b4f2/transformed/jetified-camera-lifecycle-1.4.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1265825b88f5d413d4b023436cf71467/transformed/preference-1.2.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2317eb6b3b283acb5034b744433cbaad/transformed/browser-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0db284c485392e6fb6deae59c158ac17/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/22f8d1b46fceeada679b30e0d4b62b3f/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3501f531dc4ea9bbe7f65da80a957cc5/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/70cc921fa2d876bb27fc73001e00d1fd/transformed/constraintlayout-2.0.1/AndroidManifest.xml:2:1-11:12
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7940825b9f61a429b8a2787bf4a1cc22/transformed/jetified-barcode-scanning-17.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0261c6a524650ab289def0cbaba8627f/transformed/jetified-barcode-scanning-common-17.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d88b73ec39358a6bd35028a74b2d001/transformed/jetified-vision-common-17.3.0/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:2:1-26:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9c2e912c4115effe7269e3307739bc17/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5c5bb3e97d4de44ccc93687f8b8f5ead/transformed/appcompat-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/18767ba4abbbadcd34370aae4dea35bc/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/79a7e00763360e2ac89c090ccc2803a1/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/10d681e17525fb9e0936812adb7e61f9/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8fcac208690cfa61207f7b78e5c74adb/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/343ca7c51fac9b900c0b3037e9a75673/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ac3e0888502ad48b072e224658008b41/transformed/jetified-play-services-location-21.2.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/70785106f9c804659c7bfd6e133699aa/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:15:1-25:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/bbbc1b56f8f7e0cb2b6a6d3fb4f6cb67/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/8.10.2/transforms/a74b65a88592fc5e6442cfde11fd85d0/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2e26807c8f25937a53c37fddbe6a55b6/transformed/jetified-play-services-fido-21.0.0/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/67793af2c986633ea33943ae591078fb/transformed/jetified-play-services-auth-blockstore-16.4.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02] /Users/<USER>/.gradle/caches/8.10.2/transforms/174a7963a46b128835574d0481740a1c/transformed/jetified-play-services-identity-credentials-16.0.0-alpha02/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-analytics:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1377cb86361b4a7e0147429caeab8c37/transformed/jetified-firebase-analytics-21.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:17:1-43:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0adbd3c5f1c510d4d66cb53d1c56d0cd/transformed/jetified-play-services-measurement-sdk-21.5.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/9d4f4d0260b6a795aa229c38bf285504/transformed/jetified-recaptcha-18.1.2/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.play:integrity:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/72aee6f0e5b4c619a6e3b77a083d8ee7/transformed/jetified-integrity-1.1.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4ff043b69f72c0cb187a7044dee284d6/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e30594788384c5d008298c14bdf327ea/transformed/jetified-firebase-common-ktx-20.4.2/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a9f04b8cd0eb0748ae058509a9d02d6b/transformed/jetified-vision-interfaces-16.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9408465326e055d51a07dde815d2e170/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:15:1-19:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb6f06ab6bb3f7919e9c83a8b6f1e30a/transformed/jetified-datastore-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/1cab4203751f902a965309ddb84dafe9/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/9296afa9e1b5a811643ca5924f227a27/transformed/jetified-datastore-release/AndroidManifest.xml:17:1-22:12
MERGED from [io.sentry:sentry-android:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6591ace77f1d233d99d7a07817248c44/transformed/jetified-sentry-android-8.13.2/AndroidManifest.xml:2:1-10:12
MERGED from [io.sentry:sentry-android-ndk:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e4dec9df466768ca1b6c0fe8867a3448/transformed/jetified-sentry-android-ndk-8.13.2/AndroidManifest.xml:2:1-7:12
MERGED from [io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:2:1-23:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/2d724b320aa55e62302f92bce99e50d0/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/217a8068f9af1228887eedcfc31fd7bb/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f11293702297dbe0fea3487456f23254/transformed/loader-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.8.8] /Users/<USER>/.gradle/caches/8.10.2/transforms/054c3f747961f1d602c2530c8635baf5/transformed/jetified-fragment-ktx-1.8.8/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9153c59c7c4c5f1fa385b86e08f41d0d/transformed/jetified-activity-ktx-1.10.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/80885beb63e9be082b5c54aa144e6c28/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/075bdc3bccdc27f5f5563a18053877ef/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.test.espresso:espresso-core:3.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/90ab35cc9e1413d64dcb951f5b657075/transformed/espresso-core-3.5.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:17:1-53:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/0640c43f099111f928e454752a7fb6fe/transformed/jetified-lifecycle-runtime-ktx-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/813edce8800945ebb9b8a333b3faaff6/transformed/lifecycle-viewmodel-2.8.7/AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/6f287fb969dac307f16ce999f3d65b8b/transformed/jetified-lifecycle-viewmodel-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/faf5d954f6f3643f1bb550392c49c52a/transformed/jetified-lifecycle-viewmodel-ktx-2.8.7/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/cd918d876ea62a720e25decd7af3a66e/transformed/jetified-lifecycle-livedata-core-ktx-2.8.7/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/db2ea3dc2a0d84cea214cbc2703e8624/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/de27bcac0ccd370803a68743b06afa7e/transformed/jetified-lifecycle-process-2.8.7/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/09cbf207ecb23c6d5ce22bdaf305313e/transformed/jetified-lifecycle-viewmodel-savedstate-2.8.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/d44557fd3fcd01b5949c8275f9576c47/transformed/lifecycle-livedata-core-2.8.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/73b4f21c15d0e8c5e626392077c96b39/transformed/jetified-lifecycle-runtime-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/a06344f1903f76c692e391c200503979/transformed/lifecycle-livedata-2.8.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/8e9f3ff22d1b9b1c40bad5685ba3cd2b/transformed/jetified-ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/43af7710a47a79412d6bbc6a2fb6fb8c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1dd85c30ebb98e7d9ec31c82e3f116a/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7386b0109621a5b16f658482ed280745/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/066548ab0df7918275951c127e0ac3e3/transformed/jetified-play-services-measurement-base-21.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a21839de1896f98e8fc4b248682bea0/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.8.8] /Users/<USER>/.gradle/caches/8.10.2/transforms/8b232af6dcd07b3c63984dcb9099d32b/transformed/fragment-1.8.8/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/b5b892ac6fd4d689f34954535c6cc7e2/transformed/jetified-activity-1.10.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0a6c962a5f0f9fa6149c4608c844f151/transformed/jetified-core-ktx-1.16.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/480fafcade925c318e0c8b2febad853d/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d65ad9051ec7bcf73278ad1fc74a4f2d/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/********************************/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/80072d5616d3a0625bff4b85bf440935/transformed/transition-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/8.10.2/transforms/2d8882f3f856eabacfabe1afbd73d31b/transformed/jetified-security-crypto-1.1.0-alpha06/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/d5578b07bcf4c37cb91acbdad61eb3a8/transformed/exifinterface-1.3.7/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:17:1-32:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3b10234edc83330a442bb971a9570f60/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/f43380ecfa20bab6888ff0110a55f466/transformed/jetified-firebase-components-17.1.5/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/*******************************a/transformed/jetified-tracing-ktx-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.test.services:storage:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/3ad48909929b63f79a145f6e78a92ab3/transformed/jetified-storage-1.4.2/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:monitor:1.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0143467a28ea1523acab788d5b4dd47b/transformed/monitor-1.6.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/bca3722b042e7cfcfefb0e6cfdbd6187/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f19da4c9804ec9ac9357b9fc4e425d2/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9f557a42ac16953d9cc312a11881024f/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1a3add375948800e269c996def321353/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/20c6fcb728555d6d2a487acdbb9e88a0/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.test:annotation:1.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/b5d6bf43b6200ef83fe0e661f9e5d3e9/transformed/jetified-annotation-1.0.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ac120d70dc6484d3d2df6c94d1988db8/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:2.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6217faa1e343f153ff43561591bdd51b/transformed/jetified-transport-api-2.2.1/AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ad05fccccdd931ea504c27384e2044af/transformed/jetified-firebase-encoders-json-17.1.0/AndroidManifest.xml:15:1-23:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9f9711f99a589e1e3084138010e9dc7b/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0de8aa3b548e65f45935f51b78c32699/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b6dafd0c966752fa3a89e43c3f435cc9/transformed/jetified-core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5cc1b4312c4b21b82b7804332a3ad4d2/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05674c615c0e8536f19298aaef49aacc/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/028e4321e0c193f72ac8e60c04ba6464/transformed/jetified-core-viewtree-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [io.sentry:sentry-android-replay:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f537ec426b832fd9b04ae434a4c02c2/transformed/jetified-sentry-android-replay-8.13.2/AndroidManifest.xml:2:1-7:12
MERGED from [org.microg:safe-parcel:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bacbdd5f368d93357ed30e12a0446605/transformed/jetified-safe-parcel-1.7.0/AndroidManifest.xml:6:1-14:12
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/95a185fbfb96506c0cdec2a21ab0f739/transformed/uiautomator-2.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb37b92be2d8ff50b2ed7002d2137a41/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.test.espresso:espresso-idling-resource:3.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/155b51c9373965fabd9abf5ae09eb283/transformed/espresso-idling-resource-3.5.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7bc50ec0792ba64ee7b3c29f62b053b4/transformed/jetified-image-1.0.0-beta1/AndroidManifest.xml:2:1-9:12
MERGED from [io.sentry:sentry-native-ndk:0.8.4] /Users/<USER>/.gradle/caches/8.10.2/transforms/60806ab4e2d37bf08016e0cfab931bab/transformed/jetified-sentry-native-ndk-0.8.4/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/mypro/carnow/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/mypro/carnow/android/app/src/debug/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/mypro/carnow/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:4:5-67
MERGED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:4:5-67
MERGED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:4:5-67
MERGED from [:google_sign_in_android] /Users/<USER>/mypro/carnow/build/google_sign_in_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] /Users/<USER>/mypro/carnow/build/google_sign_in_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:25:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:22:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/9d4f4d0260b6a795aa229c38bf285504/transformed/jetified-recaptcha-18.1.2/AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/9d4f4d0260b6a795aa229c38bf285504/transformed/jetified-recaptcha-18.1.2/AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:7:5-67
MERGED from [io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:26:5-67
	android:name
		ADDED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:4:22-64
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:7:5-79
	android:name
		ADDED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:8:5-81
	android:name
		ADDED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:8:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:11:5-77
	android:name
		ADDED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:11:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_LOCATION
ADDED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:12:5-86
	android:name
		ADDED from /Users/<USER>/mypro/carnow/android/app/src/main/AndroidManifest.xml:12:22-83
uses-sdk
INJECTED from /Users/<USER>/mypro/carnow/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/mypro/carnow/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/mypro/carnow/android/app/src/debug/AndroidManifest.xml
MERGED from [:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] /Users/<USER>/mypro/carnow/build/google_sign_in_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] /Users/<USER>/mypro/carnow/build/google_sign_in_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:mobile_scanner] /Users/<USER>/mypro/carnow/build/mobile_scanner/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:mobile_scanner] /Users/<USER>/mypro/carnow/build/mobile_scanner/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sentry_flutter] /Users/<USER>/mypro/carnow/build/sentry_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:sentry_flutter] /Users/<USER>/mypro/carnow/build/sentry_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:package_info_plus] /Users/<USER>/mypro/carnow/build/package_info_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] /Users/<USER>/mypro/carnow/build/package_info_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:patrol] /Users/<USER>/mypro/carnow/build/patrol/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:patrol] /Users/<USER>/mypro/carnow/build/patrol/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/mypro/carnow/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/mypro/carnow/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:21:5-23:64
MERGED from [:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:local_auth_android] /Users/<USER>/mypro/carnow/build/local_auth_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:local_auth_android] /Users/<USER>/mypro/carnow/build/local_auth_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/mypro/carnow/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/mypro/carnow/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:app_links] /Users/<USER>/mypro/carnow/build/app_links/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:app_links] /Users/<USER>/mypro/carnow/build/app_links/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] /Users/<USER>/mypro/carnow/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] /Users/<USER>/mypro/carnow/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:dynamic_color] /Users/<USER>/mypro/carnow/build/dynamic_color/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:dynamic_color] /Users/<USER>/mypro/carnow/build/dynamic_color/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/mypro/carnow/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/mypro/carnow/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_secure_storage] /Users/<USER>/mypro/carnow/build/flutter_secure_storage/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-8:53
MERGED from [:flutter_secure_storage] /Users/<USER>/mypro/carnow/build/flutter_secure_storage/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-8:53
MERGED from [:jni] /Users/<USER>/mypro/carnow/build/jni/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:jni] /Users/<USER>/mypro/carnow/build/jni/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/mypro/carnow/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/mypro/carnow/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/mypro/carnow/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/mypro/carnow/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/mypro/carnow/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/mypro/carnow/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bdecb973927d9c715ff6d17068a8ebbb/transformed/material-1.7.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bdecb973927d9c715ff6d17068a8ebbb/transformed/material-1.7.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:21:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8f7643e9ce3412d349249ecf1e5cf325/transformed/jetified-googleid-1.1.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8f7643e9ce3412d349249ecf1e5cf325/transformed/jetified-googleid-1.1.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/426e58cc6082e9ed0c5c00632bde216e/transformed/jetified-credentials-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/426e58cc6082e9ed0c5c00632bde216e/transformed/jetified-credentials-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd4db4106f18e531a05c8c3faee85543/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd4db4106f18e531a05c8c3faee85543/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/97751780a686aed19b8109dc8435b4f2/transformed/jetified-camera-lifecycle-1.4.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/97751780a686aed19b8109dc8435b4f2/transformed/jetified-camera-lifecycle-1.4.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1265825b88f5d413d4b023436cf71467/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1265825b88f5d413d4b023436cf71467/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2317eb6b3b283acb5034b744433cbaad/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2317eb6b3b283acb5034b744433cbaad/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0db284c485392e6fb6deae59c158ac17/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0db284c485392e6fb6deae59c158ac17/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/22f8d1b46fceeada679b30e0d4b62b3f/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/22f8d1b46fceeada679b30e0d4b62b3f/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3501f531dc4ea9bbe7f65da80a957cc5/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3501f531dc4ea9bbe7f65da80a957cc5/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/70cc921fa2d876bb27fc73001e00d1fd/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/70cc921fa2d876bb27fc73001e00d1fd/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7940825b9f61a429b8a2787bf4a1cc22/transformed/jetified-barcode-scanning-17.3.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7940825b9f61a429b8a2787bf4a1cc22/transformed/jetified-barcode-scanning-17.3.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0261c6a524650ab289def0cbaba8627f/transformed/jetified-barcode-scanning-common-17.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0261c6a524650ab289def0cbaba8627f/transformed/jetified-barcode-scanning-common-17.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d88b73ec39358a6bd35028a74b2d001/transformed/jetified-vision-common-17.3.0/AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d88b73ec39358a6bd35028a74b2d001/transformed/jetified-vision-common-17.3.0/AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9c2e912c4115effe7269e3307739bc17/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9c2e912c4115effe7269e3307739bc17/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5c5bb3e97d4de44ccc93687f8b8f5ead/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5c5bb3e97d4de44ccc93687f8b8f5ead/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/18767ba4abbbadcd34370aae4dea35bc/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/18767ba4abbbadcd34370aae4dea35bc/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/79a7e00763360e2ac89c090ccc2803a1/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/79a7e00763360e2ac89c090ccc2803a1/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/10d681e17525fb9e0936812adb7e61f9/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/10d681e17525fb9e0936812adb7e61f9/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8fcac208690cfa61207f7b78e5c74adb/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8fcac208690cfa61207f7b78e5c74adb/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/343ca7c51fac9b900c0b3037e9a75673/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/343ca7c51fac9b900c0b3037e9a75673/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ac3e0888502ad48b072e224658008b41/transformed/jetified-play-services-location-21.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ac3e0888502ad48b072e224658008b41/transformed/jetified-play-services-location-21.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/70785106f9c804659c7bfd6e133699aa/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/70785106f9c804659c7bfd6e133699aa/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/bbbc1b56f8f7e0cb2b6a6d3fb4f6cb67/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/bbbc1b56f8f7e0cb2b6a6d3fb4f6cb67/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/8.10.2/transforms/a74b65a88592fc5e6442cfde11fd85d0/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/8.10.2/transforms/a74b65a88592fc5e6442cfde11fd85d0/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2e26807c8f25937a53c37fddbe6a55b6/transformed/jetified-play-services-fido-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2e26807c8f25937a53c37fddbe6a55b6/transformed/jetified-play-services-fido-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/67793af2c986633ea33943ae591078fb/transformed/jetified-play-services-auth-blockstore-16.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/67793af2c986633ea33943ae591078fb/transformed/jetified-play-services-auth-blockstore-16.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02] /Users/<USER>/.gradle/caches/8.10.2/transforms/174a7963a46b128835574d0481740a1c/transformed/jetified-play-services-identity-credentials-16.0.0-alpha02/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02] /Users/<USER>/.gradle/caches/8.10.2/transforms/174a7963a46b128835574d0481740a1c/transformed/jetified-play-services-identity-credentials-16.0.0-alpha02/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1377cb86361b4a7e0147429caeab8c37/transformed/jetified-firebase-analytics-21.5.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1377cb86361b4a7e0147429caeab8c37/transformed/jetified-firebase-analytics-21.5.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0adbd3c5f1c510d4d66cb53d1c56d0cd/transformed/jetified-play-services-measurement-sdk-21.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0adbd3c5f1c510d4d66cb53d1c56d0cd/transformed/jetified-play-services-measurement-sdk-21.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/9d4f4d0260b6a795aa229c38bf285504/transformed/jetified-recaptcha-18.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/9d4f4d0260b6a795aa229c38bf285504/transformed/jetified-recaptcha-18.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/72aee6f0e5b4c619a6e3b77a083d8ee7/transformed/jetified-integrity-1.1.0/AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/72aee6f0e5b4c619a6e3b77a083d8ee7/transformed/jetified-integrity-1.1.0/AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4ff043b69f72c0cb187a7044dee284d6/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4ff043b69f72c0cb187a7044dee284d6/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e30594788384c5d008298c14bdf327ea/transformed/jetified-firebase-common-ktx-20.4.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e30594788384c5d008298c14bdf327ea/transformed/jetified-firebase-common-ktx-20.4.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:19:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a9f04b8cd0eb0748ae058509a9d02d6b/transformed/jetified-vision-interfaces-16.3.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a9f04b8cd0eb0748ae058509a9d02d6b/transformed/jetified-vision-interfaces-16.3.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9408465326e055d51a07dde815d2e170/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9408465326e055d51a07dde815d2e170/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb6f06ab6bb3f7919e9c83a8b6f1e30a/transformed/jetified-datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb6f06ab6bb3f7919e9c83a8b6f1e30a/transformed/jetified-datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/1cab4203751f902a965309ddb84dafe9/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/1cab4203751f902a965309ddb84dafe9/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/9296afa9e1b5a811643ca5924f227a27/transformed/jetified-datastore-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/9296afa9e1b5a811643ca5924f227a27/transformed/jetified-datastore-release/AndroidManifest.xml:20:5-44
MERGED from [io.sentry:sentry-android:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6591ace77f1d233d99d7a07817248c44/transformed/jetified-sentry-android-8.13.2/AndroidManifest.xml:6:5-8:57
MERGED from [io.sentry:sentry-android:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6591ace77f1d233d99d7a07817248c44/transformed/jetified-sentry-android-8.13.2/AndroidManifest.xml:6:5-8:57
MERGED from [io.sentry:sentry-android-ndk:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e4dec9df466768ca1b6c0fe8867a3448/transformed/jetified-sentry-android-ndk-8.13.2/AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-ndk:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e4dec9df466768ca1b6c0fe8867a3448/transformed/jetified-sentry-android-ndk-8.13.2/AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/2d724b320aa55e62302f92bce99e50d0/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/2d724b320aa55e62302f92bce99e50d0/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/217a8068f9af1228887eedcfc31fd7bb/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/217a8068f9af1228887eedcfc31fd7bb/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f11293702297dbe0fea3487456f23254/transformed/loader-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f11293702297dbe0fea3487456f23254/transformed/loader-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.8.8] /Users/<USER>/.gradle/caches/8.10.2/transforms/054c3f747961f1d602c2530c8635baf5/transformed/jetified-fragment-ktx-1.8.8/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.8] /Users/<USER>/.gradle/caches/8.10.2/transforms/054c3f747961f1d602c2530c8635baf5/transformed/jetified-fragment-ktx-1.8.8/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9153c59c7c4c5f1fa385b86e08f41d0d/transformed/jetified-activity-ktx-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9153c59c7c4c5f1fa385b86e08f41d0d/transformed/jetified-activity-ktx-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/80885beb63e9be082b5c54aa144e6c28/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/80885beb63e9be082b5c54aa144e6c28/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/075bdc3bccdc27f5f5563a18053877ef/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/075bdc3bccdc27f5f5563a18053877ef/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.test.espresso:espresso-core:3.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/90ab35cc9e1413d64dcb951f5b657075/transformed/espresso-core-3.5.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/90ab35cc9e1413d64dcb951f5b657075/transformed/espresso-core-3.5.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/0640c43f099111f928e454752a7fb6fe/transformed/jetified-lifecycle-runtime-ktx-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/0640c43f099111f928e454752a7fb6fe/transformed/jetified-lifecycle-runtime-ktx-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/813edce8800945ebb9b8a333b3faaff6/transformed/lifecycle-viewmodel-2.8.7/AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/813edce8800945ebb9b8a333b3faaff6/transformed/lifecycle-viewmodel-2.8.7/AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/6f287fb969dac307f16ce999f3d65b8b/transformed/jetified-lifecycle-viewmodel-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/6f287fb969dac307f16ce999f3d65b8b/transformed/jetified-lifecycle-viewmodel-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/faf5d954f6f3643f1bb550392c49c52a/transformed/jetified-lifecycle-viewmodel-ktx-2.8.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/faf5d954f6f3643f1bb550392c49c52a/transformed/jetified-lifecycle-viewmodel-ktx-2.8.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/cd918d876ea62a720e25decd7af3a66e/transformed/jetified-lifecycle-livedata-core-ktx-2.8.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/cd918d876ea62a720e25decd7af3a66e/transformed/jetified-lifecycle-livedata-core-ktx-2.8.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/db2ea3dc2a0d84cea214cbc2703e8624/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/db2ea3dc2a0d84cea214cbc2703e8624/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/de27bcac0ccd370803a68743b06afa7e/transformed/jetified-lifecycle-process-2.8.7/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/de27bcac0ccd370803a68743b06afa7e/transformed/jetified-lifecycle-process-2.8.7/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/09cbf207ecb23c6d5ce22bdaf305313e/transformed/jetified-lifecycle-viewmodel-savedstate-2.8.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/09cbf207ecb23c6d5ce22bdaf305313e/transformed/jetified-lifecycle-viewmodel-savedstate-2.8.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/d44557fd3fcd01b5949c8275f9576c47/transformed/lifecycle-livedata-core-2.8.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/d44557fd3fcd01b5949c8275f9576c47/transformed/lifecycle-livedata-core-2.8.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/73b4f21c15d0e8c5e626392077c96b39/transformed/jetified-lifecycle-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/73b4f21c15d0e8c5e626392077c96b39/transformed/jetified-lifecycle-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/a06344f1903f76c692e391c200503979/transformed/lifecycle-livedata-2.8.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/a06344f1903f76c692e391c200503979/transformed/lifecycle-livedata-2.8.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/8e9f3ff22d1b9b1c40bad5685ba3cd2b/transformed/jetified-ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/8e9f3ff22d1b9b1c40bad5685ba3cd2b/transformed/jetified-ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/43af7710a47a79412d6bbc6a2fb6fb8c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/43af7710a47a79412d6bbc6a2fb6fb8c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1dd85c30ebb98e7d9ec31c82e3f116a/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1dd85c30ebb98e7d9ec31c82e3f116a/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7386b0109621a5b16f658482ed280745/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7386b0109621a5b16f658482ed280745/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/066548ab0df7918275951c127e0ac3e3/transformed/jetified-play-services-measurement-base-21.5.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/066548ab0df7918275951c127e0ac3e3/transformed/jetified-play-services-measurement-base-21.5.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a21839de1896f98e8fc4b248682bea0/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a21839de1896f98e8fc4b248682bea0/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.8.8] /Users/<USER>/.gradle/caches/8.10.2/transforms/8b232af6dcd07b3c63984dcb9099d32b/transformed/fragment-1.8.8/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.8] /Users/<USER>/.gradle/caches/8.10.2/transforms/8b232af6dcd07b3c63984dcb9099d32b/transformed/fragment-1.8.8/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/b5b892ac6fd4d689f34954535c6cc7e2/transformed/jetified-activity-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/b5b892ac6fd4d689f34954535c6cc7e2/transformed/jetified-activity-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0a6c962a5f0f9fa6149c4608c844f151/transformed/jetified-core-ktx-1.16.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0a6c962a5f0f9fa6149c4608c844f151/transformed/jetified-core-ktx-1.16.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/480fafcade925c318e0c8b2febad853d/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/480fafcade925c318e0c8b2febad853d/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d65ad9051ec7bcf73278ad1fc74a4f2d/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d65ad9051ec7bcf73278ad1fc74a4f2d/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/********************************/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/********************************/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/80072d5616d3a0625bff4b85bf440935/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/80072d5616d3a0625bff4b85bf440935/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/8.10.2/transforms/2d8882f3f856eabacfabe1afbd73d31b/transformed/jetified-security-crypto-1.1.0-alpha06/AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/8.10.2/transforms/2d8882f3f856eabacfabe1afbd73d31b/transformed/jetified-security-crypto-1.1.0-alpha06/AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/d5578b07bcf4c37cb91acbdad61eb3a8/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/d5578b07bcf4c37cb91acbdad61eb3a8/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3b10234edc83330a442bb971a9570f60/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3b10234edc83330a442bb971a9570f60/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/f43380ecfa20bab6888ff0110a55f466/transformed/jetified-firebase-components-17.1.5/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/f43380ecfa20bab6888ff0110a55f466/transformed/jetified-firebase-components-17.1.5/AndroidManifest.xml:18:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/*******************************a/transformed/jetified-tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/*******************************a/transformed/jetified-tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.test.services:storage:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/3ad48909929b63f79a145f6e78a92ab3/transformed/jetified-storage-1.4.2/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.services:storage:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/3ad48909929b63f79a145f6e78a92ab3/transformed/jetified-storage-1.4.2/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0143467a28ea1523acab788d5b4dd47b/transformed/monitor-1.6.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0143467a28ea1523acab788d5b4dd47b/transformed/monitor-1.6.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/bca3722b042e7cfcfefb0e6cfdbd6187/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/bca3722b042e7cfcfefb0e6cfdbd6187/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f19da4c9804ec9ac9357b9fc4e425d2/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f19da4c9804ec9ac9357b9fc4e425d2/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9f557a42ac16953d9cc312a11881024f/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9f557a42ac16953d9cc312a11881024f/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1a3add375948800e269c996def321353/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1a3add375948800e269c996def321353/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/20c6fcb728555d6d2a487acdbb9e88a0/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/20c6fcb728555d6d2a487acdbb9e88a0/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:annotation:1.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/b5d6bf43b6200ef83fe0e661f9e5d3e9/transformed/jetified-annotation-1.0.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:annotation:1.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/b5d6bf43b6200ef83fe0e661f9e5d3e9/transformed/jetified-annotation-1.0.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ac120d70dc6484d3d2df6c94d1988db8/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ac120d70dc6484d3d2df6c94d1988db8/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6217faa1e343f153ff43561591bdd51b/transformed/jetified-transport-api-2.2.1/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6217faa1e343f153ff43561591bdd51b/transformed/jetified-transport-api-2.2.1/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ad05fccccdd931ea504c27384e2044af/transformed/jetified-firebase-encoders-json-17.1.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ad05fccccdd931ea504c27384e2044af/transformed/jetified-firebase-encoders-json-17.1.0/AndroidManifest.xml:19:5-21:41
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9f9711f99a589e1e3084138010e9dc7b/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9f9711f99a589e1e3084138010e9dc7b/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0de8aa3b548e65f45935f51b78c32699/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0de8aa3b548e65f45935f51b78c32699/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b6dafd0c966752fa3a89e43c3f435cc9/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b6dafd0c966752fa3a89e43c3f435cc9/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5cc1b4312c4b21b82b7804332a3ad4d2/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5cc1b4312c4b21b82b7804332a3ad4d2/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05674c615c0e8536f19298aaef49aacc/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05674c615c0e8536f19298aaef49aacc/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/028e4321e0c193f72ac8e60c04ba6464/transformed/jetified-core-viewtree-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/028e4321e0c193f72ac8e60c04ba6464/transformed/jetified-core-viewtree-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-replay:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f537ec426b832fd9b04ae434a4c02c2/transformed/jetified-sentry-android-replay-8.13.2/AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-replay:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f537ec426b832fd9b04ae434a4c02c2/transformed/jetified-sentry-android-replay-8.13.2/AndroidManifest.xml:5:5-44
MERGED from [org.microg:safe-parcel:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bacbdd5f368d93357ed30e12a0446605/transformed/jetified-safe-parcel-1.7.0/AndroidManifest.xml:10:5-12:41
MERGED from [org.microg:safe-parcel:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bacbdd5f368d93357ed30e12a0446605/transformed/jetified-safe-parcel-1.7.0/AndroidManifest.xml:10:5-12:41
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/95a185fbfb96506c0cdec2a21ab0f739/transformed/uiautomator-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/95a185fbfb96506c0cdec2a21ab0f739/transformed/uiautomator-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb37b92be2d8ff50b2ed7002d2137a41/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb37b92be2d8ff50b2ed7002d2137a41/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [androidx.test.espresso:espresso-idling-resource:3.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/155b51c9373965fabd9abf5ae09eb283/transformed/espresso-idling-resource-3.5.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-idling-resource:3.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/155b51c9373965fabd9abf5ae09eb283/transformed/espresso-idling-resource-3.5.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.odml:image:1.0.0-beta1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7bc50ec0792ba64ee7b3c29f62b053b4/transformed/jetified-image-1.0.0-beta1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7bc50ec0792ba64ee7b3c29f62b053b4/transformed/jetified-image-1.0.0-beta1/AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-native-ndk:0.8.4] /Users/<USER>/.gradle/caches/8.10.2/transforms/60806ab4e2d37bf08016e0cfab931bab/transformed/jetified-sentry-native-ndk-0.8.4/AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-native-ndk:0.8.4] /Users/<USER>/.gradle/caches/8.10.2/transforms/60806ab4e2d37bf08016e0cfab931bab/transformed/jetified-sentry-native-ndk-0.8.4/AndroidManifest.xml:5:5-44
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from /Users/<USER>/mypro/carnow/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/mypro/carnow/android/app/src/debug/AndroidManifest.xml
queries
ADDED from [:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:15
MERGED from [androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:24:5-28:15
MERGED from [androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:24:5-28:15
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-72
	android:name
		ADDED from [:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:21-69
data
ADDED from [:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-44
	android:mimeType
		ADDED from [:file_picker] /Users/<USER>/mypro/carnow/build/file_picker/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:19-41
uses-permission#android.permission.CAMERA
ADDED from [:mobile_scanner] /Users/<USER>/mypro/carnow/build/mobile_scanner/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-65
	android:name
		ADDED from [:mobile_scanner] /Users/<USER>/mypro/carnow/build/mobile_scanner/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-62
uses-feature#android.hardware.camera
ADDED from [:mobile_scanner] /Users/<USER>/mypro/carnow/build/mobile_scanner/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-11:36
	android:required
		ADDED from [:mobile_scanner] /Users/<USER>/mypro/carnow/build/mobile_scanner/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:9-33
	android:name
		ADDED from [:mobile_scanner] /Users/<USER>/mypro/carnow/build/mobile_scanner/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:9-47
provider#io.sentry.android.core.SentryInitProvider
ADDED from [:sentry_flutter] /Users/<USER>/mypro/carnow/build/sentry_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-13:35
REJECTED from [io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:12:9-15:40
	tools:node
		ADDED from [:sentry_flutter] /Users/<USER>/mypro/carnow/build/sentry_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-32
	android:authorities
		ADDED from [:sentry_flutter] /Users/<USER>/mypro/carnow/build/sentry_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-70
	android:exported
		ADDED from [:sentry_flutter] /Users/<USER>/mypro/carnow/build/sentry_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:sentry_flutter] /Users/<USER>/mypro/carnow/build/sentry_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-69
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-77
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-20:68
	android:resource
		ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:17-65
	android:name
		ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:17-67
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] /Users/<USER>/mypro/carnow/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:25-62
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:26:5-79
MERGED from [:connectivity_plus] /Users/<USER>/mypro/carnow/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] /Users/<USER>/mypro/carnow/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/9d4f4d0260b6a795aa229c38bf285504/transformed/jetified-recaptcha-18.1.2/AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/9d4f4d0260b6a795aa229c38bf285504/transformed/jetified-recaptcha-18.1.2/AndroidManifest.xml:8:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:39:27-75
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:53:13-63:29
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:66:9-72:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e30594788384c5d008298c14bdf327ea/transformed/jetified-firebase-common-ktx-20.4.2/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e30594788384c5d008298c14bdf327ea/transformed/jetified-firebase-common-ktx-20.4.2/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:67:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/200a71fb83c86ece155f01ce3245b423/transformed/jetified-firebase-auth-22.3.0/AndroidManifest.xml:70:17-109
service#com.baseflow.geolocator.GeolocatorLocationService
ADDED from [:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:56
	android:enabled
		ADDED from [:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-35
	android:exported
		ADDED from [:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-37
	android:foregroundServiceType
		ADDED from [:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-53
	android:name
		ADDED from [:geolocator_android] /Users/<USER>/mypro/carnow/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-77
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-82
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/mypro/carnow/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-63
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [:local_auth_android] /Users/<USER>/mypro/carnow/build/local_auth_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-72
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/AndroidManifest.xml:24:5-72
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/AndroidManifest.xml:24:5-72
	android:name
		ADDED from [:local_auth_android] /Users/<USER>/mypro/carnow/build/local_auth_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-69
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] /Users/<USER>/mypro/carnow/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] /Users/<USER>/mypro/carnow/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] /Users/<USER>/mypro/carnow/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] /Users/<USER>/mypro/carnow/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:35:13-76
activity#androidx.credentials.playservices.IdentityCredentialApiHiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:42:9-49:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:47:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:45:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:46:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:44:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:48:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/99a3288988f3a3b3cfce056382dc89c5/transformed/jetified-credentials-play-services-auth-1.5.0/AndroidManifest.xml:43:13-97
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8b321f32e745e9186ce3d960139d3ab2/transformed/biometric-1.1.0/AndroidManifest.xml:27:22-71
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd4db4106f18e531a05c8c3faee85543/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd4db4106f18e531a05c8c3faee85543/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:24:9-33:19
	android:enabled
		ADDED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:31:13-36
	android:exported
		ADDED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:32:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:33:13-75
	android:name
		ADDED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/95efa4860a4ee67edfb3e3f3d7d95526/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:30:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd4db4106f18e531a05c8c3faee85543/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd4db4106f18e531a05c8c3faee85543/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd4db4106f18e531a05c8c3faee85543/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:31:17-103
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1438d330fb81e2ab0144461b9b6a2403/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d88b73ec39358a6bd35028a74b2d001/transformed/jetified-vision-common-17.3.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d88b73ec39358a6bd35028a74b2d001/transformed/jetified-vision-common-17.3.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/afd741e0aca4136865a54a591eebeb26/transformed/jetified-play-services-mlkit-barcode-scanning-18.3.1/AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d88b73ec39358a6bd35028a74b2d001/transformed/jetified-vision-common-17.3.0/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d88b73ec39358a6bd35028a74b2d001/transformed/jetified-vision-common-17.3.0/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d88b73ec39358a6bd35028a74b2d001/transformed/jetified-vision-common-17.3.0/AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9bfd13f70b2781eda497e9639477184/transformed/jetified-common-18.11.0/AndroidManifest.xml:21:17-120
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/793724c2738b5056133e9a757953bfd3/transformed/jetified-play-services-auth-21.3.0/AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/673883e1e745606e3a2d7bf8006a6a16/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:19-85
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:25:22-65
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb76b5f8f09b39b20eb1fa0c997e78b7/transformed/jetified-play-services-measurement-21.5.0/AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01277d89a03da16913ac223b5c7bce57/transformed/jetified-play-services-measurement-impl-21.5.0/AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7386b0109621a5b16f658482ed280745/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7386b0109621a5b16f658482ed280745/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccfd42d462c8a6e104403ef32f4bd094/transformed/jetified-play-services-measurement-sdk-api-21.5.0/AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b924121c1be8606a644eba49955b60e8/transformed/jetified-play-services-measurement-api-21.5.0/AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65551fdb892d33d1f62e3f7adccb60fa/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e30594788384c5d008298c14bdf327ea/transformed/jetified-firebase-common-ktx-20.4.2/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e30594788384c5d008298c14bdf327ea/transformed/jetified-firebase-common-ktx-20.4.2/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/e30594788384c5d008298c14bdf327ea/transformed/jetified-firebase-common-ktx-20.4.2/AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a87f8807e59f87f67acb603e1b6e381/transformed/jetified-firebase-common-20.4.2/AndroidManifest.xml:36:17-109
provider#io.sentry.android.core.SentryPerformanceProvider
ADDED from [io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:16:9-20:39
	android:authorities
		ADDED from [io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:18:13-77
	android:exported
		ADDED from [io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:19:13-37
	android:initOrder
		ADDED from [io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:20:13-36
	android:name
		ADDED from [io.sentry:sentry-android-core:8.13.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6fb0d6a36f125719e49e9e6164355174/transformed/jetified-sentry-android-core-8.13.2/AndroidManifest.xml:17:13-76
uses-permission#android.permission.REORDER_TASKS
ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:24:22-69
activity#androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity
ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:27:9-34:20
	android:exported
		ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:29:13-36
	android:theme
		ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:30:13-56
	android:name
		ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:28:13-99
intent-filter#category:name:android.intent.category.LAUNCHER
ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:13-33:29
	android:priority
		ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:28-51
category#android.intent.category.LAUNCHER
ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:17-77
	android:name
		ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:27-74
activity#androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity
ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:35:9-42:20
	android:exported
		ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:37:13-36
	android:theme
		ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:38:13-56
	android:name
		ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:36:13-95
activity#androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity
ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:43:9-50:20
	android:exported
		ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:45:13-36
	android:theme
		ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:46:13-62
	android:name
		ADDED from [androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6003148592c678eb3610375ecc4edc80/transformed/jetified-core-1.5.0/AndroidManifest.xml:44:13-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/de27bcac0ccd370803a68743b06afa7e/transformed/jetified-lifecycle-process-2.8.7/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/de27bcac0ccd370803a68743b06afa7e/transformed/jetified-lifecycle-process-2.8.7/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/bca3722b042e7cfcfefb0e6cfdbd6187/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/bca3722b042e7cfcfefb0e6cfdbd6187/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c3d0e87e871bea048d5dbac31d2814a/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/de27bcac0ccd370803a68743b06afa7e/transformed/jetified-lifecycle-process-2.8.7/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/de27bcac0ccd370803a68743b06afa7e/transformed/jetified-lifecycle-process-2.8.7/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] /Users/<USER>/.gradle/caches/8.10.2/transforms/de27bcac0ccd370803a68743b06afa7e/transformed/jetified-lifecycle-process-2.8.7/AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/43af7710a47a79412d6bbc6a2fb6fb8c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/43af7710a47a79412d6bbc6a2fb6fb8c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/43af7710a47a79412d6bbc6a2fb6fb8c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4aade3a7a37e54c01875a6640a4f9b19/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
permission#com.example.carnowx.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
uses-permission#com.example.carnowx.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1731e9afd071f911e1ca414ed74898e/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
package#androidx.test.orchestrator
ADDED from [androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:25:9-62
	android:name
		ADDED from [androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:25:18-59
package#androidx.test.services
ADDED from [androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:26:9-58
	android:name
		ADDED from [androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:26:18-55
package#com.google.android.apps.common.testing.services
ADDED from [androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:27:9-83
	android:name
		ADDED from [androidx.test:runner:1.5.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/019ced679f79ef5ccb606c9fb09e862f/transformed/runner-1.5.1/AndroidManifest.xml:27:18-80
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73ee0c2113c97e170bb3b00739846e7d/transformed/jetified-profileinstaller-1.4.0/AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f21105b77070c137b03f09e1ea388cb5/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/df249df7686583e34624ea746a50f086/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml:33:13-132
