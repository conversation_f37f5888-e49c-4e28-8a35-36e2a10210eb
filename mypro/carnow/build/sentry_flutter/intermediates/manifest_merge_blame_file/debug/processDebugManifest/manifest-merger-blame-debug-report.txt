1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="io.sentry.flutter" >
5
6    <uses-sdk android:minSdkVersion="21" />
7
8    <application>
8-->/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/android/src/main/AndroidManifest.xml:4:5-10:19
9        <provider
9-->/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/android/src/main/AndroidManifest.xml:5:9-9:35
10            android:name="io.sentry.android.core.SentryInitProvider"
10-->/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/android/src/main/AndroidManifest.xml:6:13-69
11            android:authorities="${applicationId}.SentryInitProvider"
11-->/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/android/src/main/AndroidManifest.xml:7:13-70
12            android:exported="false"
12-->/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/android/src/main/AndroidManifest.xml:8:13-37
13            tools:node="remove" />
13-->/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/android/src/main/AndroidManifest.xml:9:13-32
14    </application>
15
16</manifest>
