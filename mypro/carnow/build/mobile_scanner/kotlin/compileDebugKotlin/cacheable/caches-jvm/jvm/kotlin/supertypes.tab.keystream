-dev.steenbakker.mobile_scanner.BarcodeHandler8dev.steenbakker.mobile_scanner.DeviceOrientationListener'dev.steenbakker.mobile_scanner.NoCamera-dev.steenbakker.mobile_scanner.AlreadyStarted-dev.steenbakker.mobile_scanner.AlreadyStopped,dev.steenbakker.mobile_scanner.AlreadyPaused*dev.steenbakker.mobile_scanner.CameraError.dev.steenbakker.mobile_scanner.ZoomWhenStopped-dev.steenbakker.mobile_scanner.ZoomNotInRange3dev.steenbakker.mobile_scanner.MobileScannerHandler?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener2dev.steenbakker.mobile_scanner.MobileScannerPlugin5dev.steenbakker.mobile_scanner.objects.BarcodeFormats5dev.steenbakker.mobile_scanner.objects.DetectionSpeed,dev.steenbakker.mobile_scanner.utils.YuvType                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                