  system_accent1_0 android.R.color  system_accent1_10 android.R.color  system_accent1_100 android.R.color  system_accent1_1000 android.R.color  system_accent1_200 android.R.color  system_accent1_300 android.R.color  system_accent1_400 android.R.color  system_accent1_50 android.R.color  system_accent1_500 android.R.color  system_accent1_600 android.R.color  system_accent1_700 android.R.color  system_accent1_800 android.R.color  system_accent1_900 android.R.color  system_accent2_0 android.R.color  system_accent2_10 android.R.color  system_accent2_100 android.R.color  system_accent2_1000 android.R.color  system_accent2_200 android.R.color  system_accent2_300 android.R.color  system_accent2_400 android.R.color  system_accent2_50 android.R.color  system_accent2_500 android.R.color  system_accent2_600 android.R.color  system_accent2_700 android.R.color  system_accent2_800 android.R.color  system_accent2_900 android.R.color  system_accent3_0 android.R.color  system_accent3_10 android.R.color  system_accent3_100 android.R.color  system_accent3_1000 android.R.color  system_accent3_200 android.R.color  system_accent3_300 android.R.color  system_accent3_400 android.R.color  system_accent3_50 android.R.color  system_accent3_500 android.R.color  system_accent3_600 android.R.color  system_accent3_700 android.R.color  system_accent3_800 android.R.color  system_accent3_900 android.R.color  system_neutral1_0 android.R.color  system_neutral1_10 android.R.color  system_neutral1_100 android.R.color  system_neutral1_1000 android.R.color  system_neutral1_200 android.R.color  system_neutral1_300 android.R.color  system_neutral1_400 android.R.color  system_neutral1_50 android.R.color  system_neutral1_500 android.R.color  system_neutral1_600 android.R.color  system_neutral1_700 android.R.color  system_neutral1_800 android.R.color  system_neutral1_900 android.R.color  system_neutral2_0 android.R.color  system_neutral2_10 android.R.color  system_neutral2_100 android.R.color  system_neutral2_1000 android.R.color  system_neutral2_200 android.R.color  system_neutral2_300 android.R.color  system_neutral2_400 android.R.color  system_neutral2_50 android.R.color  system_neutral2_500 android.R.color  system_neutral2_600 android.R.color  system_neutral2_700 android.R.color  system_neutral2_800 android.R.color  system_neutral2_900 android.R.color  Context android.content  	resources android.content.Context  	Resources android.content.res  getColor android.content.res.Resources  Build 
android.os  S android.os.Build.VERSION_CODES  RequiresApi androidx.annotation  
DynamicColors !com.google.android.material.color  isDynamicColorAvailable /com.google.android.material.color.DynamicColors  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  Build !io.material.plugins.dynamic_color  DynamicColorPlugin !io.material.plugins.dynamic_color  
DynamicColors !io.material.plugins.dynamic_color  
FlutterPlugin !io.material.plugins.dynamic_color  FlutterPluginBinding !io.material.plugins.dynamic_color  IntArray !io.material.plugins.dynamic_color  
MethodCall !io.material.plugins.dynamic_color  MethodCallHandler !io.material.plugins.dynamic_color  
MethodChannel !io.material.plugins.dynamic_color  RequiresApi !io.material.plugins.dynamic_color  	Resources !io.material.plugins.dynamic_color  Result !io.material.plugins.dynamic_color  android !io.material.plugins.dynamic_color  
intArrayOf !io.material.plugins.dynamic_color  Build 4io.material.plugins.dynamic_color.DynamicColorPlugin  
DynamicColors 4io.material.plugins.dynamic_color.DynamicColorPlugin  
MethodChannel 4io.material.plugins.dynamic_color.DynamicColorPlugin  android 4io.material.plugins.dynamic_color.DynamicColorPlugin  binding 4io.material.plugins.dynamic_color.DynamicColorPlugin  channel 4io.material.plugins.dynamic_color.DynamicColorPlugin  getCorePalette 4io.material.plugins.dynamic_color.DynamicColorPlugin  
intArrayOf 4io.material.plugins.dynamic_color.DynamicColorPlugin  IntArray kotlin  
intArrayOf kotlin                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     