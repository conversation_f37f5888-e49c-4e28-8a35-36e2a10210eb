package configs

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// Environment represents the deployment environment
type Environment string

const (
	Development Environment = "development"
	Staging     Environment = "staging"
	Production  Environment = "production"
	Testing     Environment = "testing"
)

// DeploymentConfig holds all configuration for different environments
type DeploymentConfig struct {
	Environment Environment      `json:"environment"`
	Server      ServerConfig     `json:"server"`
	Database    DatabaseConfig   `json:"database"`
	Auth        AuthConfig       `json:"auth"`
	Security    SecurityConfig   `json:"security"`
	Monitoring  MonitoringConfig `json:"monitoring"`
	Logging     LoggingConfig    `json:"logging"`
	Cache       CacheConfig      `json:"cache"`
	External    ExternalConfig   `json:"external"`
	Features    FeatureFlags     `json:"features"`
}

// ServerConfig holds server-specific configuration
type ServerConfig struct {
	Host            string        `json:"host"`
	Port            int           `json:"port"`
	ReadTimeout     time.Duration `json:"read_timeout"`
	WriteTimeout    time.Duration `json:"write_timeout"`
	IdleTimeout     time.Duration `json:"idle_timeout"`
	ShutdownTimeout time.Duration `json:"shutdown_timeout"`
	TLSEnabled      bool          `json:"tls_enabled"`
	CertFile        string        `json:"cert_file,omitempty"`
	KeyFile         string        `json:"key_file,omitempty"`
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	Host            string        `json:"host"`
	Port            int           `json:"port"`
	Name            string        `json:"name"`
	User            string        `json:"user"`
	Password        string        `json:"-"` // Excluded from JSON for security
	SSLMode         string        `json:"ssl_mode"`
	MaxOpenConns    int           `json:"max_open_conns"`
	MaxIdleConns    int           `json:"max_idle_conns"`
	ConnMaxLifetime time.Duration `json:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `json:"conn_max_idle_time"`
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	JWTSecret              string        `json:"-"` // Excluded from JSON for security
	JWTExpirationTime      time.Duration `json:"jwt_expiration_time"`
	RefreshTokenExpiration time.Duration `json:"refresh_token_expiration"`
	GoogleClientID         string        `json:"google_client_id"`
	GoogleClientSecret     string        `json:"-"` // Excluded from JSON for security
	SupabaseURL            string        `json:"supabase_url"`
	SupabaseAnonKey        string        `json:"-"` // Excluded from JSON for security
	SupabaseServiceKey     string        `json:"-"` // Excluded from JSON for security
	EnableGoogleAuth       bool          `json:"enable_google_auth"`
	EnableEmailAuth        bool          `json:"enable_email_auth"`
	EnableBiometricAuth    bool          `json:"enable_biometric_auth"`
}

// SecurityConfig holds security-related configuration
type SecurityConfig struct {
	EnableRateLimiting     bool          `json:"enable_rate_limiting"`
	RateLimitRequests      int           `json:"rate_limit_requests"`
	RateLimitWindow        time.Duration `json:"rate_limit_window"`
	EnableCORS             bool          `json:"enable_cors"`
	AllowedOrigins         []string      `json:"allowed_origins"`
	EnableSecurityHeaders  bool          `json:"enable_security_headers"`
	EnableAnomalyDetection bool          `json:"enable_anomaly_detection"`
	MaxLoginAttempts       int           `json:"max_login_attempts"`
	LoginAttemptWindow     time.Duration `json:"login_attempt_window"`
	BlockDuration          time.Duration `json:"block_duration"`
}

// MonitoringConfig holds monitoring configuration
type MonitoringConfig struct {
	EnableMetrics      bool   `json:"enable_metrics"`
	MetricsPort        int    `json:"metrics_port"`
	EnableHealthChecks bool   `json:"enable_health_checks"`
	HealthCheckPath    string `json:"health_check_path"`
	EnableTracing      bool   `json:"enable_tracing"`
	TracingEndpoint    string `json:"tracing_endpoint"`
	EnableAlerting     bool   `json:"enable_alerting"`
	SlackWebhookURL    string `json:"-"` // Excluded from JSON for security
	EmailSMTPHost      string `json:"email_smtp_host"`
	EmailSMTPPort      int    `json:"email_smtp_port"`
	EmailUsername      string `json:"email_username"`
	EmailPassword      string `json:"-"` // Excluded from JSON for security
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level               string `json:"level"`
	Format              string `json:"format"`
	EnableConsole       bool   `json:"enable_console"`
	EnableFile          bool   `json:"enable_file"`
	FilePath            string `json:"file_path"`
	MaxSize             int    `json:"max_size"`
	MaxBackups          int    `json:"max_backups"`
	MaxAge              int    `json:"max_age"`
	EnableStructured    bool   `json:"enable_structured"`
	EnableCorrelationID bool   `json:"enable_correlation_id"`
}

// CacheConfig holds cache configuration
type CacheConfig struct {
	EnableRedis      bool          `json:"enable_redis"`
	RedisHost        string        `json:"redis_host"`
	RedisPort        int           `json:"redis_port"`
	RedisPassword    string        `json:"-"` // Excluded from JSON for security
	RedisDB          int           `json:"redis_db"`
	DefaultTTL       time.Duration `json:"default_ttl"`
	EnableClustering bool          `json:"enable_clustering"`
	ClusterNodes     []string      `json:"cluster_nodes"`
}

// ExternalConfig holds external service configuration
type ExternalConfig struct {
	EnableAnalytics         bool   `json:"enable_analytics"`
	AnalyticsKey            string `json:"-"` // Excluded from JSON for security
	EnableCrashReporting    bool   `json:"enable_crash_reporting"`
	CrashReportingKey       string `json:"-"` // Excluded from JSON for security
	EnablePushNotifications bool   `json:"enable_push_notifications"`
	FCMServerKey            string `json:"-"` // Excluded from JSON for security
}

// FeatureFlags holds feature flag configuration
type FeatureFlags struct {
	EnhancedSecurity         bool `json:"enhanced_security"`
	RateLimiting             bool `json:"rate_limiting"`
	AdvancedMonitoring       bool `json:"advanced_monitoring"`
	ArabicLocalization       bool `json:"arabic_localization"`
	AccessibilityFeatures    bool `json:"accessibility_features"`
	BiometricAuth            bool `json:"biometric_auth"`
	GoogleAuth               bool `json:"google_auth"`
	EmailAuth                bool `json:"email_auth"`
	DebugLogging             bool `json:"debug_logging"`
	Analytics                bool `json:"analytics"`
	CrashReporting           bool `json:"crash_reporting"`
	OfflineMode              bool `json:"offline_mode"`
	DarkMode                 bool `json:"dark_mode"`
	HapticFeedback           bool `json:"haptic_feedback"`
	PushNotifications        bool `json:"push_notifications"`
	AutoBackup               bool `json:"auto_backup"`
	AdvancedValidation       bool `json:"advanced_validation"`
	PerformanceOptimizations bool `json:"performance_optimizations"`
	ExperimentalFeatures     bool `json:"experimental_features"`
	BetaFeatures             bool `json:"beta_features"`
}

// GetDeploymentConfig returns configuration for the specified environment
func GetDeploymentConfig(env Environment) (*DeploymentConfig, error) {
	switch env {
	case Development:
		return getDevelopmentConfig(), nil
	case Staging:
		return getStagingConfig(), nil
	case Production:
		return getProductionConfig(), nil
	case Testing:
		return getTestingConfig(), nil
	default:
		return nil, fmt.Errorf("unsupported environment: %s", env)
	}
}

// LoadFromEnvironment loads configuration from environment variables
func LoadFromEnvironment() (*DeploymentConfig, error) {
	// Load .env file if it exists
	_ = godotenv.Load()

	envStr := getEnvString("CARNOW_ENV", "production")
	env := Environment(strings.ToLower(envStr))

	config, err := GetDeploymentConfig(env)
	if err != nil {
		return nil, err
	}

	// Override with environment variables
	overrideFromEnv(config)

	// Validate configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return config, nil
}

// getDevelopmentConfig returns development environment configuration
func getDevelopmentConfig() *DeploymentConfig {
	return &DeploymentConfig{
		Environment: Development,
		Server: ServerConfig{
			Host:            "localhost",
			Port:            8080,
			ReadTimeout:     30 * time.Second,
			WriteTimeout:    30 * time.Second,
			IdleTimeout:     60 * time.Second,
			ShutdownTimeout: 10 * time.Second,
			TLSEnabled:      false,
		},
		Database: DatabaseConfig{
			Host:            "db.lpxtghyvxuenyyisrrro.supabase.co",
			Port:            5432,
			Name:            "postgres",
			User:            "postgres",
			SSLMode:         "require",
			MaxOpenConns:    25,
			MaxIdleConns:    5,
			ConnMaxLifetime: 5 * time.Minute,
			ConnMaxIdleTime: 5 * time.Minute,
		},
		Auth: AuthConfig{
			JWTSecret:              "cmk11gkJgFX6BKiIOioVpWfHY4ZZ7RRR6xTWyy7/wOlK6ZoRyFYlS+2hyQIS+OBt3ef5D1z9HVFYWoDJwwD4eQ==",
			JWTExpirationTime:      15 * time.Minute,
			RefreshTokenExpiration: 7 * 24 * time.Hour,
			SupabaseURL:            "https://lpxtghyvxuenyyisrrro.supabase.co",
			EnableGoogleAuth:       true,
			EnableEmailAuth:        true,
			EnableBiometricAuth:    true,
		},
		Security: SecurityConfig{
			EnableRateLimiting:     false,
			RateLimitRequests:      100,
			RateLimitWindow:        time.Minute,
			EnableCORS:             true,
			AllowedOrigins:         []string{"http://localhost:3000", "http://127.0.0.1:3000"},
			EnableSecurityHeaders:  true,
			EnableAnomalyDetection: true,
			MaxLoginAttempts:       10,
			LoginAttemptWindow:     15 * time.Minute,
			BlockDuration:          30 * time.Minute,
		},
		Monitoring: MonitoringConfig{
			EnableMetrics:      true,
			MetricsPort:        9090,
			EnableHealthChecks: true,
			HealthCheckPath:    "/health",
			EnableTracing:      true,
			EnableAlerting:     false,
			EmailSMTPHost:      "smtp.gmail.com",
			EmailSMTPPort:      587,
		},
		Logging: LoggingConfig{
			Level:               "debug",
			Format:              "json",
			EnableConsole:       true,
			EnableFile:          true,
			FilePath:            "./logs/carnow-dev.log",
			MaxSize:             100,
			MaxBackups:          3,
			MaxAge:              28,
			EnableStructured:    true,
			EnableCorrelationID: true,
		},
		Cache: CacheConfig{
			EnableRedis:      false,
			RedisHost:        "localhost",
			RedisPort:        6379,
			RedisDB:          0,
			DefaultTTL:       1 * time.Hour,
			EnableClustering: false,
		},
		External: ExternalConfig{
			EnableAnalytics:         false,
			EnableCrashReporting:    false,
			EnablePushNotifications: false,
		},
		Features: FeatureFlags{
			EnhancedSecurity:         true,
			RateLimiting:             false,
			AdvancedMonitoring:       true,
			ArabicLocalization:       true,
			AccessibilityFeatures:    true,
			BiometricAuth:            true,
			GoogleAuth:               true,
			EmailAuth:                true,
			DebugLogging:             true,
			Analytics:                false,
			CrashReporting:           false,
			OfflineMode:              false,
			DarkMode:                 true,
			HapticFeedback:           true,
			PushNotifications:        false,
			AutoBackup:               false,
			AdvancedValidation:       true,
			PerformanceOptimizations: true,
			ExperimentalFeatures:     true,
			BetaFeatures:             true,
		},
	}
}

// getStagingConfig returns staging environment configuration
func getStagingConfig() *DeploymentConfig {
	config := getDevelopmentConfig()
	config.Environment = Staging
	config.Auth.JWTSecret = "cmk11gkJgFX6BKiIOioVpWfHY4ZZ7RRR6xTWyy7/wOlK6ZoRyFYlS+2hyQIS+OBt3ef5D1z9HVFYWoDJwwD4eQ=="

	// Override staging-specific settings
	config.Server.Host = "0.0.0.0"
	config.Server.TLSEnabled = true
	config.Security.EnableRateLimiting = true
	config.Security.AllowedOrigins = []string{
		"https://staging-carnow.netlify.app",
		"https://staging.carnow.com",
	}
	config.Monitoring.EnableAlerting = true
	config.Logging.Level = "info"
	config.Cache.EnableRedis = true
	config.External.EnableAnalytics = true
	config.External.EnableCrashReporting = true
	config.Features.RateLimiting = true
	config.Features.Analytics = true
	config.Features.CrashReporting = true
	config.Features.ExperimentalFeatures = false

	return config
}

// getProductionConfig returns production environment configuration
func getProductionConfig() *DeploymentConfig {
	config := getStagingConfig()
	config.Environment = Production
	config.Auth.JWTSecret = "cmk11gkJgFX6BKiIOioVpWfHY4ZZ7RRR6xTWyy7/wOlK6ZoRyFYlS+2hyQIS+OBt3ef5D1z9HVFYWoDJwwD4eQ=="

	// Override production-specific settings
	config.Server.ReadTimeout = 15 * time.Second
	config.Server.WriteTimeout = 15 * time.Second
	config.Security.AllowedOrigins = []string{
		"https://carnow.netlify.app",
		"https://carnow.com",
		"https://www.carnow.com",
	}
	config.Security.MaxLoginAttempts = 5
	config.Security.BlockDuration = 1 * time.Hour
	config.Logging.Level = "warn"
	config.Logging.EnableConsole = false
	config.Database.MaxOpenConns = 100
	config.Database.MaxIdleConns = 20
	config.Features.DebugLogging = false
	config.Features.BetaFeatures = false

	return config
}

// getTestingConfig returns testing environment configuration
func getTestingConfig() *DeploymentConfig {
	config := getDevelopmentConfig()
	config.Environment = Testing
	config.Auth.JWTSecret = "cmk11gkJgFX6BKiIOioVpWfHY4ZZ7RRR6xTWyy7/wOlK6ZoRyFYlS+2hyQIS+OBt3ef5D1z9HVFYWoDJwwD4eQ=="

	// Override testing-specific settings
	config.Server.Port = 8081
	config.Database.Name = "carnow_test"
	config.Auth.JWTExpirationTime = 1 * time.Minute
	config.Auth.EnableGoogleAuth = false
	config.Security.EnableRateLimiting = false
	config.Security.EnableAnomalyDetection = false
	config.Monitoring.EnableMetrics = false
	config.Monitoring.EnableAlerting = false
	config.Cache.EnableRedis = false
	config.External.EnableAnalytics = false
	config.External.EnableCrashReporting = false
	config.Features.Analytics = false
	config.Features.CrashReporting = false
	config.Features.EnhancedSecurity = false
	config.Features.AdvancedMonitoring = false

	return config
}

// overrideFromEnv overrides configuration with environment variables
func overrideFromEnv(config *DeploymentConfig) {
	// Server configuration
	if host := getEnvString("SERVER_HOST", ""); host != "" {
		config.Server.Host = host
	}
	if port := getEnvInt("SERVER_PORT", 0); port != 0 {
		config.Server.Port = port
	}
	if timeout := getEnvDuration("SERVER_READ_TIMEOUT", 0); timeout != 0 {
		config.Server.ReadTimeout = timeout
	}
	if timeout := getEnvDuration("SERVER_WRITE_TIMEOUT", 0); timeout != 0 {
		config.Server.WriteTimeout = timeout
	}

	// Database configuration
	if host := getEnvString("DB_HOST", ""); host != "" {
		config.Database.Host = host
	}
	if port := getEnvInt("DB_PORT", 0); port != 0 {
		config.Database.Port = port
	}
	if name := getEnvString("DB_NAME", ""); name != "" {
		config.Database.Name = name
	}
	if user := getEnvString("DB_USER", ""); user != "" {
		config.Database.User = user
	}
	if password := getEnvString("DB_PASSWORD", ""); password != "" {
		config.Database.Password = password
	}

	// Auth configuration
	if secret := getEnvString("JWT_SECRET", ""); secret != "" {
		config.Auth.JWTSecret = secret
	}
	if clientID := getEnvString("GOOGLE_CLIENT_ID", ""); clientID != "" {
		config.Auth.GoogleClientID = clientID
	}
	if clientSecret := getEnvString("GOOGLE_CLIENT_SECRET", ""); clientSecret != "" {
		config.Auth.GoogleClientSecret = clientSecret
	}
	if url := getEnvString("SUPABASE_URL", ""); url != "" {
		config.Auth.SupabaseURL = url
	}
	if key := getEnvString("SUPABASE_ANON_KEY", ""); key != "" {
		config.Auth.SupabaseAnonKey = key
	}
	if key := getEnvString("SUPABASE_SERVICE_KEY", ""); key != "" {
		config.Auth.SupabaseServiceKey = key
	}

	// Monitoring configuration
	if url := getEnvString("SLACK_WEBHOOK_URL", ""); url != "" {
		config.Monitoring.SlackWebhookURL = url
	}
	if username := getEnvString("EMAIL_USERNAME", ""); username != "" {
		config.Monitoring.EmailUsername = username
	}
	if password := getEnvString("EMAIL_PASSWORD", ""); password != "" {
		config.Monitoring.EmailPassword = password
	}

	// Cache configuration
	if host := getEnvString("REDIS_HOST", ""); host != "" {
		config.Cache.RedisHost = host
	}
	if port := getEnvInt("REDIS_PORT", 0); port != 0 {
		config.Cache.RedisPort = port
	}
	if password := getEnvString("REDIS_PASSWORD", ""); password != "" {
		config.Cache.RedisPassword = password
	}
}

// Validate validates the configuration
func (c *DeploymentConfig) Validate() error {
	var errors []string

	// Validate server configuration
	if c.Server.Port <= 0 || c.Server.Port > 65535 {
		errors = append(errors, "invalid server port")
	}

	// Validate database configuration
	if c.Database.Host == "" {
		errors = append(errors, "database host is required")
	}
	if c.Database.Port <= 0 || c.Database.Port > 65535 {
		errors = append(errors, "invalid database port")
	}
	if c.Database.Name == "" {
		errors = append(errors, "database name is required")
	}
	if c.Database.User == "" {
		errors = append(errors, "database user is required")
	}

	// Validate auth configuration
	if c.Environment == Production && c.Auth.JWTSecret == "" {
		errors = append(errors, "JWT secret is required in production")
	}
	if c.Auth.EnableGoogleAuth && c.Auth.GoogleClientID == "" {
		errors = append(errors, "Google client ID is required when Google auth is enabled")
	}
	if c.Auth.SupabaseURL == "" {
		errors = append(errors, "Supabase URL is required")
	}

	// Validate monitoring configuration
	if c.Monitoring.EnableAlerting && c.Monitoring.SlackWebhookURL == "" {
		errors = append(errors, "Slack webhook URL is required when alerting is enabled")
	}

	if len(errors) > 0 {
		return fmt.Errorf("configuration validation errors: %s", strings.Join(errors, ", "))
	}

	return nil
}

// ToJSON converts configuration to JSON (excluding sensitive data)
func (c *DeploymentConfig) ToJSON() ([]byte, error) {
	return json.MarshalIndent(c, "", "  ")
}

// GetConnectionString returns the database connection string
func (c *DeploymentConfig) GetConnectionString() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Database.Host,
		c.Database.Port,
		c.Database.User,
		c.Database.Password,
		c.Database.Name,
		c.Database.SSLMode,
	)
}

// IsFeatureEnabled checks if a feature flag is enabled
func (c *DeploymentConfig) IsFeatureEnabled(feature string) bool {
	switch feature {
	case "enhanced_security":
		return c.Features.EnhancedSecurity
	case "rate_limiting":
		return c.Features.RateLimiting
	case "advanced_monitoring":
		return c.Features.AdvancedMonitoring
	case "arabic_localization":
		return c.Features.ArabicLocalization
	case "accessibility_features":
		return c.Features.AccessibilityFeatures
	case "biometric_auth":
		return c.Features.BiometricAuth
	case "google_auth":
		return c.Features.GoogleAuth
	case "email_auth":
		return c.Features.EmailAuth
	case "debug_logging":
		return c.Features.DebugLogging
	case "analytics":
		return c.Features.Analytics
	case "crash_reporting":
		return c.Features.CrashReporting
	default:
		return false
	}
}

// Helper functions for environment variable parsing
func getEnvString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}
