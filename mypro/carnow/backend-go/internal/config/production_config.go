package config

import (
	"fmt"
	"log"
	"os"
	"strings"
)

// ProductionConfigValidator validates production-specific configuration requirements
type ProductionConfigValidator struct {
	cfg *Config
}

// NewProductionConfigValidator creates a new production configuration validator
func NewProductionConfigValidator(cfg *Config) *ProductionConfigValidator {
	return &ProductionConfigValidator{cfg: cfg}
}

// ValidateProductionReadiness validates that all required configuration is set for production
func (v *ProductionConfigValidator) ValidateProductionReadiness() error {
	log.Printf("🚀 Validating Production Readiness for CarNow Unified Auth System...")

	// For local development, we can use config file values
	// For production deployment, environment variables should be set
	environment := os.Getenv("CARNOW_APP_ENVIRONMENT")
	if environment == "" {
		environment = "development"
	}

	// In development, we can skip environment variable validation
	// since values are loaded from config file
	if environment == "development" {
		log.Printf("✅ Production environment variables validation skipped for development (using config file)")
	} else {
		// Critical Environment Variables for Production
		requiredEnvVars := map[string]string{
			"CARNOW_SUPABASE_URL":            "Supabase project URL",
			"CARNOW_SUPABASE_ANON_KEY":       "Supabase anonymous key",
			"CARNOW_SUPABASE_JWT_SECRET":     "Supabase JWT secret",
			"CARNOW_JWT_SECRET":              "JWT signing secret",
			"CARNOW_SECURITY_ENCRYPTION_KEY": "Security encryption key",
		}

		// Check all required environment variables
		var missingVars []string
		for envVar, description := range requiredEnvVars {
			if os.Getenv(envVar) == "" {
				missingVars = append(missingVars, fmt.Sprintf("%s (%s)", envVar, description))
			}
		}

		if len(missingVars) > 0 {
			return fmt.Errorf("❌ Missing required environment variables for production:\n%s\n\nPlease set these in your Render.com dashboard",
				strings.Join(missingVars, "\n"))
		}
	}

	// Validate configuration values
	if err := v.validateAuthenticationConfig(); err != nil {
		return fmt.Errorf("❌ Authentication configuration validation failed: %w", err)
	}

	if err := v.validateSecurityConfig(); err != nil {
		return fmt.Errorf("❌ Security configuration validation failed: %w", err)
	}

	if err := v.validateSupabaseConfig(); err != nil {
		return fmt.Errorf("❌ Supabase configuration validation failed: %w", err)
	}

	log.Printf("✅ Production configuration validation passed!")
	return nil
}

// validateAuthenticationConfig validates authentication-specific configuration
func (v *ProductionConfigValidator) validateAuthenticationConfig() error {
	log.Printf("🔐 Validating Authentication Configuration...")

	// JWT Configuration
	if len(v.cfg.JWT.Secret) < 32 {
		return fmt.Errorf("JWT secret must be at least 32 characters for production security")
	}

	if v.cfg.JWT.Algorithm != "RS256" && v.cfg.JWT.Algorithm != "HS256" {
		return fmt.Errorf("JWT algorithm must be RS256 or HS256, got: %s", v.cfg.JWT.Algorithm)
	}

	if v.cfg.JWT.ExpiresIn.Minutes() < 5 || v.cfg.JWT.ExpiresIn.Hours() > 24 {
		return fmt.Errorf("JWT expires_in should be between 5 minutes and 24 hours for security")
	}

	if v.cfg.JWT.RefreshExpiresIn.Hours() < 24 || v.cfg.JWT.RefreshExpiresIn.Hours() > 720 { // 30 days max
		return fmt.Errorf("JWT refresh_expires_in should be between 24 hours and 30 days")
	}

	// Google OAuth Configuration (if enabled)
	if v.cfg.Google.ClientID != "" {
		if v.cfg.Google.ClientSecret == "" {
			return fmt.Errorf("Google OAuth client secret is required when client ID is set")
		}
		if len(v.cfg.Google.ClientSecret) < 20 {
			return fmt.Errorf("Google OAuth client secret appears to be invalid (too short)")
		}
	}

	log.Printf("✅ Authentication configuration is valid")
	return nil
}

// validateSecurityConfig validates security-specific configuration
func (v *ProductionConfigValidator) validateSecurityConfig() error {
	log.Printf("🛡️ Validating Security Configuration...")

	// Encryption Key
	if len(v.cfg.Security.EncryptionKey) < 32 {
		return fmt.Errorf("encryption key must be at least 32 characters for production security")
	}

	// Rate Limiting
	if v.cfg.Security.RateLimit.LoginAttempts < 3 || v.cfg.Security.RateLimit.LoginAttempts > 100 {
		return fmt.Errorf("login rate limit should be between 3 and 100 attempts")
	}

	if v.cfg.Security.RateLimit.RegisterAttempts < 1 || v.cfg.Security.RateLimit.RegisterAttempts > 50 {
		return fmt.Errorf("register rate limit should be between 1 and 50 attempts")
	}

	// CORS Configuration
	if v.cfg.Security.CORS.AllowedOrigins == nil || len(v.cfg.Security.CORS.AllowedOrigins) == 0 {
		log.Printf("⚠️ WARNING: No CORS origins configured. This may cause issues with frontend applications")
	}

	log.Printf("✅ Security configuration is valid")
	return nil
}

// validateSupabaseConfig validates Supabase-specific configuration
func (v *ProductionConfigValidator) validateSupabaseConfig() error {
	log.Printf("📊 Validating Supabase Configuration...")

	// URL validation
	if !strings.HasPrefix(v.cfg.Supabase.URL, "https://") {
		return fmt.Errorf("Supabase URL must use HTTPS in production")
	}

	if !strings.Contains(v.cfg.Supabase.URL, "supabase.co") {
		return fmt.Errorf("Supabase URL format appears invalid")
	}

	// Key validation
	if len(v.cfg.Supabase.AnonKey) < 100 {
		return fmt.Errorf("Supabase anon key appears to be invalid (too short)")
	}

	if len(v.cfg.Supabase.JWTSecret) < 32 {
		return fmt.Errorf("Supabase JWT secret appears to be invalid (too short)")
	}

	// Project reference validation
	if v.cfg.Supabase.ProjectRef == "" {
		log.Printf("⚠️ WARNING: Supabase project reference not set. Some features may not work correctly")
	}

	log.Printf("✅ Supabase configuration is valid")
	return nil
}

// GenerateProductionEnvTemplate generates a template for production environment variables
func GenerateProductionEnvTemplate() string {
	template := `# CarNow Backend - Production Environment Variables
# Copy this template and set the actual values in your Render.com dashboard

# Application Configuration
CARNOW_APP_ENVIRONMENT=production
CARNOW_APP_NAME=carnow-backend
CARNOW_APP_VERSION=1.0.0
PORT=8080

# Supabase Configuration (Forever Plan - Data Only)
CARNOW_SUPABASE_URL=https://lpxtghyvxuenyyisrrro.supabase.co
CARNOW_SUPABASE_ANON_KEY=your_supabase_anon_key_here
CARNOW_SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
CARNOW_SUPABASE_JWT_SECRET=your_supabase_jwt_secret_here
CARNOW_SUPABASE_PROJECT_REF=lpxtghyvxuenyyisrrro

# JWT Configuration (Go Backend Only)
CARNOW_JWT_SECRET=your_jwt_secret_here_generate_with_openssl_rand_base64_64
CARNOW_JWT_ISSUER=carnow-backend
CARNOW_JWT_AUDIENCE=carnow-app
CARNOW_JWT_EXPIRES_IN=15m
CARNOW_JWT_REFRESH_EXPIRES_IN=168h

# Security Configuration
CARNOW_SECURITY_ENCRYPTION_KEY=your_encryption_key_here_generate_with_openssl_rand_base64_64

# Google OAuth Configuration (Optional)
CARNOW_GOOGLE_CLIENT_ID=your_google_client_id_here
CARNOW_GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# Rate Limiting Configuration
CARNOW_SECURITY_RATE_LIMIT_LOGIN_ATTEMPTS=10
CARNOW_SECURITY_RATE_LIMIT_REGISTER_ATTEMPTS=5
CARNOW_SECURITY_RATE_LIMIT_WINDOW_MINUTES=15

# CORS Configuration
CARNOW_SECURITY_CORS_ALLOWED_ORIGINS=https://carnow.app,https://www.carnow.app

# Logging Configuration
CARNOW_LOGGING_LEVEL=info
CARNOW_LOGGING_FORMAT=json

# Forever Plan Features (Keep these disabled)
CARNOW_FEATURES_ENABLE_COMPLEX_AUTH=false
CARNOW_FEATURES_ENABLE_DUAL_DATABASE=false
CARNOW_FEATURES_ENABLE_SYNC_SERVICES=false
CARNOW_FEATURES_ENABLE_ENHANCED_FEATURES=false
`
	return template
}

// PrintProductionChecklist prints a checklist for production deployment
func PrintProductionChecklist() {
	log.Printf(`
🚀 CarNow Backend Production Deployment Checklist:

📋 Environment Variables:
   ✅ Set CARNOW_SUPABASE_URL in Render.com
   ✅ Set CARNOW_SUPABASE_ANON_KEY in Render.com
   ✅ Set CARNOW_SUPABASE_JWT_SECRET in Render.com
   ✅ Set CARNOW_JWT_SECRET in Render.com (generate with: openssl rand -base64 64)
   ✅ Set CARNOW_SECURITY_ENCRYPTION_KEY in Render.com (generate with: openssl rand -base64 64)
   ⚠️  Set CARNOW_GOOGLE_CLIENT_ID and CARNOW_GOOGLE_CLIENT_SECRET (optional)

🔐 Security:
   ✅ All secrets are properly masked in logs
   ✅ Rate limiting is configured
   ✅ CORS origins are set correctly
   ✅ JWT tokens use secure algorithms

📊 Supabase Configuration:
   ✅ Project ID: lpxtghyvxuenyyisrrro
   ✅ URL: https://lpxtghyvxuenyyisrrro.supabase.co
   ✅ Authentication enabled
   ✅ Row Level Security (RLS) policies configured

🏗️ Forever Plan Compliance:
   ✅ Flutter: UI Only (no business logic)
   ✅ Go Backend: All authentication logic
   ✅ Supabase: Data storage only
   ✅ No complex/enhanced features enabled

🚀 Deployment:
   ✅ Backend deployed to: https://backend-go-8klm.onrender.com
   ✅ Automatic deployment from GitHub
   ✅ Health checks configured
   ✅ Logging and monitoring enabled
`)
}
