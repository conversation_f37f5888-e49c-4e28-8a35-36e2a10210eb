package handlers

import (
	"fmt"
	"net/http"
	"strings"

	"carnow-backend/internal/config"
	"carnow-backend/internal/services"
	"carnow-backend/internal/shared/validation"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// MFAHandlers handles Multi-Factor Authentication endpoints
type MFAHandlers struct {
	config     *config.Config
	db         *gorm.DB
	logger     *zap.Logger
	mfaService *services.MFAService
	validator  *validation.CustomValidator
}

// MFA Request/Response structures
type SendOTPRequest struct {
	Method      string `json:"method" binding:"required" validate:"oneof=sms email"`
	PhoneNumber string `json:"phone_number,omitempty" validate:"required_if=Method sms"`
	Email       string `json:"email,omitempty" validate:"required_if=Method email,email"`
}

type VerifyOTPRequest struct {
	ChallengeID string `json:"challenge_id" binding:"required"`
	Code        string `json:"code" binding:"required" validate:"len=6,numeric"`
}

type SetupTOTPRequest struct {
	// No additional fields needed for initial setup
}

type ConfirmTOTPRequest struct {
	Code string `json:"code" binding:"required" validate:"len=6,numeric"`
}

type MFASettingsRequest struct {
	SMSEnabled   *bool   `json:"sms_enabled,omitempty"`
	EmailEnabled *bool   `json:"email_enabled,omitempty"`
	TOTPEnabled  *bool   `json:"totp_enabled,omitempty"`
	PhoneNumber  *string `json:"phone_number,omitempty"`
	Email        *string `json:"email,omitempty"`
}

type MFAResponse struct {
	Success     bool        `json:"success"`
	Message     string      `json:"message"`
	Data        interface{} `json:"data,omitempty"`
	ChallengeID string      `json:"challenge_id,omitempty"`
	QRCode      string      `json:"qr_code,omitempty"`
	Secret      string      `json:"secret,omitempty"`
	Error       string      `json:"error,omitempty"`
}

// NewMFAHandlers creates new MFA handlers
func NewMFAHandlers(config *config.Config, db *gorm.DB, logger *zap.Logger) *MFAHandlers {
	return &MFAHandlers{
		config:     config,
		db:         db,
		logger:     logger,
		mfaService: services.NewMFAService(config, db, logger),
		validator:  validation.NewCustomValidator(),
	}
}

// SendOTP sends OTP via SMS or Email
// POST /api/v1/auth/mfa/send-otp
func (h *MFAHandlers) SendOTP(c *gin.Context) {
	var req SendOTPRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid send OTP request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, MFAResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// Validate request
	if err := h.validator.Validate(req); err != nil {
		c.JSON(http.StatusBadRequest, MFAResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, MFAResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// Initialize MFA if not exists
	if err := h.mfaService.InitializeMFA(userIDStr); err != nil {
		h.logger.Error("Failed to initialize MFA",
			zap.String("user_id", userIDStr),
			zap.Error(err),
		)
		c.JSON(http.StatusInternalServerError, MFAResponse{
			Success: false,
			Error:   "Failed to initialize MFA",
		})
		return
	}

	var challenge *services.MFAChallenge
	var err error

	switch strings.ToLower(req.Method) {
	case "sms":
		challenge, err = h.mfaService.SendSMSOTP(userIDStr, req.PhoneNumber)
	case "email":
		challenge, err = h.mfaService.SendEmailOTP(userIDStr, req.Email)
	default:
		c.JSON(http.StatusBadRequest, MFAResponse{
			Success: false,
			Error:   "Invalid MFA method",
		})
		return
	}

	if err != nil {
		h.logger.Error("Failed to send OTP",
			zap.String("user_id", userIDStr),
			zap.String("method", req.Method),
			zap.Error(err),
		)
		c.JSON(http.StatusInternalServerError, MFAResponse{
			Success: false,
			Error:   "Failed to send OTP",
		})
		return
	}

	h.logger.Info("OTP sent successfully",
		zap.String("user_id", userIDStr),
		zap.String("method", req.Method),
		zap.String("challenge_id", challenge.ID),
	)

	c.JSON(http.StatusOK, MFAResponse{
		Success:     true,
		Message:     "OTP sent successfully",
		ChallengeID: challenge.ID,
	})
}

// VerifyOTP verifies OTP code
// POST /api/v1/auth/mfa/verify-otp
func (h *MFAHandlers) VerifyOTP(c *gin.Context) {
	var req VerifyOTPRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid verify OTP request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, MFAResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// Validate request
	if err := h.validator.Validate(req); err != nil {
		c.JSON(http.StatusBadRequest, MFAResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	// Verify OTP
	result, err := h.mfaService.VerifyOTP(req.ChallengeID, req.Code)
	if err != nil {
		h.logger.Error("Failed to verify OTP",
			zap.String("challenge_id", req.ChallengeID),
			zap.Error(err),
		)
		c.JSON(http.StatusInternalServerError, MFAResponse{
			Success: false,
			Error:   "Failed to verify OTP",
		})
		return
	}

	if !result.Success {
		h.logger.Warn("OTP verification failed",
			zap.String("challenge_id", req.ChallengeID),
			zap.String("error", result.Error),
		)

		statusCode := http.StatusBadRequest
		if result.RemainingAttempts == 0 {
			statusCode = http.StatusTooManyRequests
		}

		c.JSON(statusCode, MFAResponse{
			Success: false,
			Error:   result.Error,
			Data: map[string]interface{}{
				"remaining_attempts": result.RemainingAttempts,
				"cooldown_until":     result.CooldownUntil,
			},
		})
		return
	}

	h.logger.Info("OTP verified successfully",
		zap.String("challenge_id", req.ChallengeID),
	)

	c.JSON(http.StatusOK, MFAResponse{
		Success: true,
		Message: "OTP verified successfully",
		Data: map[string]interface{}{
			"method": result.Method,
		},
	})
}

// SetupTOTP generates TOTP secret and QR code
// POST /api/v1/auth/mfa/setup-totp
func (h *MFAHandlers) SetupTOTP(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, MFAResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// Get user email for TOTP label
	userEmail, _ := c.Get("user_email")
	userEmailStr := ""
	if userEmail != nil {
		userEmailStr = userEmail.(string)
	}

	// Generate TOTP secret
	secret, err := h.mfaService.GenerateTOTPSecret(userIDStr)
	if err != nil {
		h.logger.Error("Failed to generate TOTP secret",
			zap.String("user_id", userIDStr),
			zap.Error(err),
		)
		c.JSON(http.StatusInternalServerError, MFAResponse{
			Success: false,
			Error:   "Failed to generate TOTP secret",
		})
		return
	}

	// Generate QR code URL
	issuer := "CarNow"
	accountName := userEmailStr
	if accountName == "" {
		accountName = userIDStr
	}

	qrCodeURL := h.generateTOTPQRCodeURL(secret, accountName, issuer)

	h.logger.Info("TOTP setup initiated",
		zap.String("user_id", userIDStr),
	)

	c.JSON(http.StatusOK, MFAResponse{
		Success: true,
		Message: "TOTP secret generated successfully",
		Secret:  secret,
		QRCode:  qrCodeURL,
		Data: map[string]interface{}{
			"issuer":       issuer,
			"account_name": accountName,
		},
	})
}

// GetMFASettings retrieves user's MFA settings
// GET /api/v1/auth/mfa/settings
func (h *MFAHandlers) GetMFASettings(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, MFAResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// Get MFA settings
	settings, err := h.mfaService.GetMFASettings(userIDStr)
	if err != nil {
		h.logger.Error("Failed to get MFA settings",
			zap.String("user_id", userIDStr),
			zap.Error(err),
		)
		c.JSON(http.StatusInternalServerError, MFAResponse{
			Success: false,
			Error:   "Failed to get MFA settings",
		})
		return
	}

	c.JSON(http.StatusOK, MFAResponse{
		Success: true,
		Message: "MFA settings retrieved successfully",
		Data:    settings,
	})
}

// UpdateMFASettings updates user's MFA settings
// PUT /api/v1/auth/mfa/settings
func (h *MFAHandlers) UpdateMFASettings(c *gin.Context) {
	var req MFASettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid MFA settings request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, MFAResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, MFAResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// TODO: Implement MFA settings update
	// This would involve updating the MFASettings record

	h.logger.Info("MFA settings update requested",
		zap.String("user_id", userIDStr),
	)

	c.JSON(http.StatusOK, MFAResponse{
		Success: true,
		Message: "MFA settings updated successfully",
	})
}

// generateTOTPQRCodeURL generates a QR code URL for TOTP setup
func (h *MFAHandlers) generateTOTPQRCodeURL(secret, accountName, issuer string) string {
	return fmt.Sprintf("otpauth://totp/%s:%s?secret=%s&issuer=%s",
		issuer, accountName, secret, issuer)
}

// CleanupExpiredChallenges cleans up expired MFA challenges
// This should be called periodically by a background job
func (h *MFAHandlers) CleanupExpiredChallenges() error {
	return h.mfaService.CleanupExpiredChallenges()
}
