package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow-backend/internal/services"
)

// OrderHandler handles order-related HTTP requests following Forever Plan architecture
type OrderHandler struct {
	orderService *services.OrderService
	logger       *zap.Logger
}

// NewOrderHandler creates a new order handler instance
func NewOrderHandler(orderService *services.OrderService, logger *zap.Logger) *OrderHandler {
	return &OrderHandler{
		orderService: orderService,
		logger:       logger,
	}
}

// CreateOrderRequest represents the request body for creating an order
type CreateOrderRequest struct {
	ShippingAddress map[string]interface{} `json:"shipping_address" binding:"required"`
	PaymentMethod   string                 `json:"payment_method" binding:"required"`
	Notes           string                 `json:"notes"`
}

// CreateOrder handles POST /orders - Create order from cart
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Get user ID from JWT token
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid user ID"),
		})
		return
	}

	// Parse request body
	var req CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid request body: " + err.Error()),
		})
		return
	}

	// Validate payment method
	if !h.isValidPaymentMethod(req.PaymentMethod) {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid payment method. Supported: wallet"),
		})
		return
	}

	h.logger.Info("Creating order from cart",
		zap.String("user_id", userIDStr),
		zap.String("payment_method", req.PaymentMethod))

	// Create order request
	orderReq := &services.OrderRequest{
		UserID:          userIDStr,
		ShippingAddress: req.ShippingAddress,
		PaymentMethod:   req.PaymentMethod,
		Notes:           req.Notes,
	}

	// Create order using service
	orderResponse, err := h.orderService.CreateOrderFromCart(ctx, orderReq)
	if err != nil {
		h.logger.Error("Failed to create order", zap.Error(err), zap.String("user_id", userIDStr))

		// Handle specific error types
		var statusCode int
		var errorMessage string

		switch {
		case err.Error() == "cart is empty":
			statusCode = http.StatusBadRequest
			errorMessage = "العربة فارغة"
		case err.Error() == "wallet not found for user":
			statusCode = http.StatusBadRequest
			errorMessage = "المحفظة غير موجودة"
		case err.Error() == "insufficient wallet balance":
			statusCode = http.StatusBadRequest
			errorMessage = "رصيد المحفظة غير كافي"
		default:
			statusCode = http.StatusInternalServerError
			errorMessage = "فشل في إنشاء الطلب"
		}

		c.JSON(statusCode, APIResponse{
			Success: false,
			Error:   stringPtr(errorMessage + ": " + err.Error()),
		})
		return
	}

	c.JSON(http.StatusCreated, APIResponse{
		Success: true,
		Data:    orderResponse,
		Message: stringPtr("تم إنشاء الطلب بنجاح"),
	})
}

// GetUserOrders handles GET /orders - Get user's orders
func (h *OrderHandler) GetUserOrders(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get user ID from JWT token
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid user ID"),
		})
		return
	}

	h.logger.Info("Getting user orders", zap.String("user_id", userIDStr))

	// Get orders from database
	orders, err := h.getUserOrdersFromDB(ctx, userIDStr)
	if err != nil {
		h.logger.Error("Failed to get user orders", zap.Error(err))
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Error:   stringPtr("فشل في الحصول على الطلبات"),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data:    orders,
		Message: stringPtr("تم الحصول على الطلبات بنجاح"),
	})
}

// GetOrderDetails handles GET /orders/:id - Get specific order details
func (h *OrderHandler) GetOrderDetails(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get user ID from JWT token
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid user ID"),
		})
		return
	}

	// Get order ID from URL parameter
	orderID := c.Param("id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Order ID is required"),
		})
		return
	}

	h.logger.Info("Getting order details",
		zap.String("user_id", userIDStr),
		zap.String("order_id", orderID))

	// Get order details from database
	orderDetails, err := h.getOrderDetailsFromDB(ctx, userIDStr, orderID)
	if err != nil {
		h.logger.Error("Failed to get order details", zap.Error(err))
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Error:   stringPtr("فشل في الحصول على تفاصيل الطلب"),
		})
		return
	}

	if orderDetails == nil {
		c.JSON(http.StatusNotFound, APIResponse{
			Success: false,
			Error:   stringPtr("الطلب غير موجود"),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data:    orderDetails,
		Message: stringPtr("تم الحصول على تفاصيل الطلب بنجاح"),
	})
}

// isValidPaymentMethod validates the payment method
func (h *OrderHandler) isValidPaymentMethod(method string) bool {
	validMethods := []string{"wallet"} // Only wallet payment for now
	for _, valid := range validMethods {
		if method == valid {
			return true
		}
	}
	return false
}

// getUserOrdersFromDB gets user orders from database
func (h *OrderHandler) getUserOrdersFromDB(ctx context.Context, userID string) ([]map[string]interface{}, error) {
	// This would be implemented to fetch orders from database
	// For now, return empty slice
	return []map[string]interface{}{}, nil
}

// getOrderDetailsFromDB gets order details from database
func (h *OrderHandler) getOrderDetailsFromDB(ctx context.Context, userID, orderID string) (map[string]interface{}, error) {
	// This would be implemented to fetch order details from database
	// For now, return nil
	return nil, nil
}
