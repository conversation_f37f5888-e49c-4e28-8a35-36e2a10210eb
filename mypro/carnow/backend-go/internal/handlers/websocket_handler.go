package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow-backend/internal/services"
)

// WebSocketHandler handles WebSocket-related HTTP requests following Forever Plan architecture
type WebSocketHandler struct {
	wsService *services.WebSocketService
	logger    *zap.Logger
}

// NewWebSocketHandler creates a new WebSocket handler instance
func NewWebSocketHandler(wsService *services.WebSocketService, logger *zap.Logger) *WebSocketHandler {
	return &WebSocketHandler{
		wsService: wsService,
		logger:    logger,
	}
}

// HandleWebSocketConnection handles WebSocket connection upgrade
func (h *WebSocketHandler) HandleWebSocketConnection(c *gin.Context) {
	h.logger.Info("WebSocket connection request received")
	h.wsService.HandleWebSocket(c)
}

// GetWebSocketStats handles GET /ws/stats - Get WebSocket statistics
func (h *WebSocketHandler) GetWebSocketStats(c *gin.Context) {
	// Check if user is admin (optional - for monitoring)
	userRole, exists := c.Get("user_role")
	if exists && userRole == "admin" {
		stats := map[string]interface{}{
			"connected_clients": h.wsService.GetConnectedClients(),
			"service_status":    "active",
		}

		c.JSON(http.StatusOK, APIResponse{
			Success: true,
			Data:    stats,
			Message: stringPtr("WebSocket statistics retrieved successfully"),
		})
		return
	}

	// For regular users, just return basic status
	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: map[string]interface{}{
			"service_status": "active",
		},
		Message: stringPtr("WebSocket service is active"),
	})
}
