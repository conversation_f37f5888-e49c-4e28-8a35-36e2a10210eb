package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow-backend/internal/services"
)

// CheckoutAPI handles checkout-related HTTP requests for CarNow platform
type CheckoutAPI struct {
	checkoutService *services.CheckoutService
	logger          *zap.Logger
}

// NewCheckoutAPI creates a new checkout API handler
func NewCheckoutAPI(checkoutService *services.CheckoutService, logger *zap.Logger) *CheckoutAPI {
	return &CheckoutAPI{
		checkoutService: checkoutService,
		logger:          logger,
	}
}

// ProcessCheckout handles POST /api/v1/checkout
func (api *CheckoutAPI) ProcessCheckout(c *gin.Context) {
	var req services.CheckoutRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.logger.Error("Invalid checkout request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		api.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	req.UserID = userID.(string)

	// Validate required fields
	if req.CartID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Cart ID is required",
		})
		return
	}

	if req.Currency == "" {
		req.Currency = "USD" // Default currency
	}

	// Process checkout
	result, err := api.checkoutService.ProcessCheckout(c.Request.Context(), &req)
	if err != nil {
		api.logger.Error("Checkout processing failed", 
			zap.Error(err),
			zap.String("user_id", req.UserID),
			zap.String("cart_id", req.CartID))

		if result != nil && !result.Success {
			c.JSON(http.StatusBadRequest, result)
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Internal server error during checkout",
			})
		}
		return
	}

	api.logger.Info("Checkout processed successfully",
		zap.String("user_id", req.UserID),
		zap.String("order_id", result.OrderID),
		zap.String("payment_intent_id", result.PaymentIntentID))

	c.JSON(http.StatusOK, result)
}

// ConfirmCheckout handles POST /api/v1/checkout/confirm
func (api *CheckoutAPI) ConfirmCheckout(c *gin.Context) {
	var req struct {
		PaymentIntentID string `json:"payment_intent_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		api.logger.Error("Invalid confirm checkout request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		api.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Confirm checkout
	result, err := api.checkoutService.ConfirmCheckout(c.Request.Context(), req.PaymentIntentID)
	if err != nil {
		api.logger.Error("Checkout confirmation failed",
			zap.Error(err),
			zap.String("user_id", userID.(string)),
			zap.String("payment_intent_id", req.PaymentIntentID))

		if result != nil && !result.Success {
			c.JSON(http.StatusBadRequest, result)
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Internal server error during checkout confirmation",
			})
		}
		return
	}

	api.logger.Info("Checkout confirmed successfully",
		zap.String("user_id", userID.(string)),
		zap.String("payment_intent_id", req.PaymentIntentID))

	c.JSON(http.StatusOK, result)
}

// GetCheckoutStatus handles GET /api/v1/checkout/status/:payment_intent_id
func (api *CheckoutAPI) GetCheckoutStatus(c *gin.Context) {
	paymentIntentID := c.Param("payment_intent_id")
	if paymentIntentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Payment intent ID is required",
		})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		api.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// TODO: Implement get checkout status
	// For now, return a placeholder response
	api.logger.Info("Getting checkout status",
		zap.String("user_id", userID.(string)),
		zap.String("payment_intent_id", paymentIntentID))

	c.JSON(http.StatusOK, gin.H{
		"payment_intent_id": paymentIntentID,
		"status":           "pending", // TODO: Get actual status
		"message":          "Checkout status retrieved successfully",
	})
}

// RegisterCheckoutRoutes registers all checkout routes
func (api *CheckoutAPI) RegisterCheckoutRoutes(router *gin.RouterGroup) {
	checkout := router.Group("/checkout")
	{
		checkout.POST("", api.ProcessCheckout)
		checkout.POST("/confirm", api.ConfirmCheckout)
		checkout.GET("/status/:payment_intent_id", api.GetCheckoutStatus)
	}
}

// CheckoutHealthCheck handles GET /api/v1/checkout/health
func (api *CheckoutAPI) CheckoutHealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"service": "checkout",
		"status":  "healthy",
		"message": "CarNow checkout service is running",
		"features": []string{
			"multi_product_support",
			"cars_clothes_electronics",
			"payment_processing",
			"order_management",
		},
	})
}
